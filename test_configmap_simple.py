#!/usr/bin/env python3
"""
简化的 ConfigMap 解析测试 - 不依赖 IAM 连接
"""

import re
import json
import yaml

def parse_php_credentials(php_content):
    """解析 PHP 配置文件中的 IAM 凭据"""
    credentials = {}
    
    # 定义匹配模式
    patterns = {
        'ak': r'\$(\w+)_ak\s*=\s*[\'"]([^\'"]+)[\'"]',
        'sk': r'\$(\w+)_sk\s*=\s*[\'"]([^\'"]+)[\'"]',
        'password': r'\$(\w+)_passwd\s*=\s*[\'"]([^\'"]+)[\'"]',
        'userId': r'\$(\w+)_uid\s*=\s*[\'"]([^\'"]+)[\'"]'
    }
    
    # 收集所有产品的凭据
    products = {}
    for field, pattern in patterns.items():
        matches = re.findall(pattern, php_content, re.IGNORECASE)
        for product, value in matches:
            if product not in products:
                products[product] = {}
            products[product][field] = value
    
    # 只返回包含 ak 和 sk 的完整凭据
    for product, creds in products.items():
        if 'ak' in creds and 'sk' in creds:
            credentials[product] = creds
    
    return credentials

def parse_yaml_credentials(yaml_content):
    """解析 YAML 配置文件中的 IAM 凭据"""
    try:
        data = yaml.safe_load(yaml_content)
        credentials = {}
        
        # 查找 iam 配置
        if isinstance(data, dict) and 'iam' in data:
            iam_config = data['iam']
            for product, creds in iam_config.items():
                if isinstance(creds, dict) and 'ak' in creds and 'sk' in creds:
                    credentials[product] = {
                        'ak': str(creds.get('ak', '')),
                        'sk': str(creds.get('sk', '')),
                        'password': str(creds.get('password', '')),
                        'userId': str(creds.get('userId', ''))
                    }
        
        return credentials
    except Exception as e:
        print(f"YAML 解析错误: {e}")
        return {}

def parse_json_credentials(json_content):
    """解析 JSON 配置文件中的 IAM 凭据"""
    try:
        data = json.loads(json_content)
        credentials = {}
        
        # 查找 iam 配置
        if isinstance(data, dict) and 'iam' in data:
            iam_config = data['iam']
            for product, creds in iam_config.items():
                if isinstance(creds, dict) and 'ak' in creds and 'sk' in creds:
                    credentials[product] = {
                        'ak': str(creds.get('ak', '')),
                        'sk': str(creds.get('sk', '')),
                        'password': str(creds.get('password', '')),
                        'userId': str(creds.get('userId', ''))
                    }
        
        return credentials
    except Exception as e:
        print(f"JSON 解析错误: {e}")
        return {}

def test_real_configmap():
    """测试真实的 ConfigMap 数据"""
    print("=== 测试真实 ConfigMap 数据 ===")
    
    # 模拟真实的 waf-meta ConfigMap
    real_php_content = """<?php
    // BSS 配置
    $bss_ak = 'f90daa8e8af94f6bb2da26c380e0a9cb'; //bss access key
    $bss_sk = 'd90b6603d4414151a0dac95409dcd815'; //bss secret key
    $bss_uid = '1ee85f66f3f84121ba055126e0a6a3e6';//bss sandbox
    $bss_username = 'BSS'; //bss username
    $bss_passwd = 'TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5'; //bss password
    
    // 其他配置
    $app_name = 'waf-meta';
    $debug = false;
    ?>"""
    
    print("PHP 内容:")
    print(real_php_content)
    print("\n解析结果:")
    
    credentials = parse_php_credentials(real_php_content)
    if credentials:
        for product, creds in credentials.items():
            print(f"产品: {product}")
            print(f"  AK: {creds.get('ak')}")
            print(f"  SK: {creds.get('sk')}")
            print(f"  Password: {creds.get('password', 'N/A')}")
            print(f"  UserId: {creds.get('userId', 'N/A')}")
    else:
        print("未找到凭据")

def test_multiple_products():
    """测试多产品配置"""
    print("\n=== 测试多产品配置 ===")
    
    multi_php_content = """<?php
    // BSS 配置
    $bss_ak = 'bss-access-key-123';
    $bss_sk = 'bss-secret-key-456';
    $bss_passwd = 'bss-password-789';
    
    // 支付配置
    $payment_ak = 'payment-access-key-abc';
    $payment_sk = 'payment-secret-key-def';
    $payment_passwd = 'payment-password-ghi';
    
    // 订单配置
    $order_ak = 'order-access-key-xyz';
    $order_sk = 'order-secret-key-uvw';
    ?>"""
    
    print("多产品 PHP 内容:")
    print(multi_php_content)
    print("\n解析结果:")
    
    credentials = parse_php_credentials(multi_php_content)
    if credentials:
        print(f"找到 {len(credentials)} 个产品的凭据:")
        for product, creds in credentials.items():
            print(f"\n产品: {product}")
            print(f"  AK: {creds.get('ak')}")
            print(f"  SK: {creds.get('sk')}")
            print(f"  Password: {creds.get('password', 'N/A')}")
    else:
        print("未找到凭据")

def test_yaml_format():
    """测试 YAML 格式"""
    print("\n=== 测试 YAML 格式 ===")
    
    yaml_content = """app:
  name: billing-service

iam:
  payment:
    ak: payment-ak-12345
    sk: payment-sk-67890
    password: PaymentPassword123
    userId: payment-user-id-abc
  order:
    ak: order-ak-54321
    sk: order-sk-09876
    password: OrderPassword456"""
    
    print("YAML 内容:")
    print(yaml_content)
    print("\n解析结果:")
    
    credentials = parse_yaml_credentials(yaml_content)
    if credentials:
        print(f"找到 {len(credentials)} 个产品的凭据:")
        for product, creds in credentials.items():
            print(f"\n产品: {product}")
            print(f"  AK: {creds.get('ak')}")
            print(f"  SK: {creds.get('sk')}")
            print(f"  Password: {creds.get('password', 'N/A')}")
            print(f"  UserId: {creds.get('userId', 'N/A')}")
    else:
        print("未找到凭据")

def main():
    """主函数"""
    print("ConfigMap aksk 解析测试")
    print("=" * 50)
    
    test_real_configmap()
    test_multiple_products()
    test_yaml_format()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print("✅ PHP 格式解析正常")
    print("✅ 多产品凭据解析正常")
    print("✅ YAML 格式解析正常")
    print("✅ 只解析包含完整 ak/sk 的凭据")

if __name__ == "__main__":
    main()
