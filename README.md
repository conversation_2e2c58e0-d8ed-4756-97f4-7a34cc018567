# IAM-Operator

基于混合模式设计的 IAM 凭据管理 Operator，支持从 ConfigMap 和 CRD 两种输入源自动管理 IAM 凭据。

## 项目结构

```
iam-operator/
├── README.md                    # 项目说明
├── requirements.txt             # Python 依赖
├── Dockerfile                   # 容器镜像构建
├── src/                         # 源代码
│   ├── __init__.py
│   ├── main.py                  # 主入口
│   ├── handlers/                # 事件处理器
│   │   ├── __init__.py
│   │   ├── configmap_handler.py # ConfigMap 处理
│   │   └── crd_handler.py       # CRD 处理
│   ├── processors/              # 业务处理
│   │   ├── __init__.py
│   │   ├── credential_processor.py # 凭据处理引擎
│   │   └── parser.py            # 配置解析器
│   ├── clients/                 # 外部客户端
│   │   ├── __init__.py
│   │   └── iam_client.py        # IAM API 客户端
│   └── utils/                   # 工具函数
│       ├── __init__.py
│       ├── config.py            # 配置管理
│       ├── metrics.py           # 监控指标
│       └── exceptions.py        # 异常定义
├── deploy/                      # 部署文件
│   ├── crds/                    # CRD 定义
│   │   └── iam-service-account-crd.yaml
│   ├── rbac/                    # 权限配置
│   │   ├── cluster-role.yaml
│   │   ├── cluster-role-binding.yaml
│   │   └── service-account.yaml
│   ├── configmaps/              # 配置文件
│   │   └── operator-config.yaml
│   └── deployment.yaml          # Operator 部署
└── examples/                    # 使用示例
    ├── configmap-example.yaml   # ConfigMap 示例
    └── crd-example.yaml         # CRD 使用示例
```

## 快速开始

### 方式 1：Helm 部署（推荐）

#### 1. 使用 Helm 部署
```bash
# 部署到默认配置
./scripts/helm-deploy.sh deploy

# 或者指定参数
./scripts/helm-deploy.sh deploy iam-operator base 1.0.0

# 使用生产环境配置
./scripts/helm-deploy.sh deploy iam-operator base 1.0.0 helm/iam-operator/values-production.yaml
```

#### 2. 验证部署
```bash
./scripts/helm-deploy.sh status
./scripts/helm-deploy.sh test
```

### 方式 2：原生 Kubernetes 部署

#### 1. 部署 CRD 和 RBAC
```bash
kubectl apply -f deploy/crds/
kubectl apply -f deploy/rbac/
```

#### 2. 部署配置和 Operator
```bash
kubectl apply -f deploy/configmaps/
kubectl apply -f deploy/deployment.yaml
```

#### 3. 验证部署
```bash
kubectl get pods -n base | grep iam-operator
kubectl logs -f deployment/iam-operator -n base
```

## 使用方式

### 方式 1：CRD 方式（推荐）
```yaml
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: waf-meta-iam
  namespace: security
spec:
  serviceName: waf-meta
  products:
    bss:
      ak: f90daa8e8af94f6bb2da26c380e0a9cb
      sk: d90b6603d4414151a0dac95409dcd815
      password: TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5
      userId: 1ee85f66f3f84121ba055126e0a6a3e6
```

### 方式 2：ConfigMap 方式（兼容现有）
Operator 会自动检测包含 IAM 凭据的 ConfigMap 并处理。

## 监控

Operator 提供 Prometheus 指标：
- `iam_credentials_processed_total`: 处理的凭据总数
- `iam_processing_duration_seconds`: 处理耗时
- `iam_active_credentials`: 活跃凭据数量

## 开发

### 本地开发
```bash
# 安装依赖
pip install -r requirements.txt

# 运行 Operator
python src/main.py
```

### 构建镜像
```bash
# 使用 Helm 脚本构建
./scripts/helm-deploy.sh build 1.0.0

# 或者直接使用 Docker
docker build -t iam-operator:latest .
```

## Helm Chart

### Chart 结构
```
helm/iam-operator/
├── Chart.yaml                  # Chart 元数据
├── values.yaml                 # 默认配置
├── values-production.yaml      # 生产环境配置
├── crds/                       # CRD 定义
│   └── iam-service-account-crd.yaml
└── templates/                  # Kubernetes 模板
    ├── deployment.yaml         # Operator 部署
    ├── service.yaml            # 服务
    ├── configmap.yaml          # 配置
    ├── serviceaccount.yaml     # 服务账户
    ├── clusterrole.yaml        # 集群角色
    ├── clusterrolebinding.yaml # 角色绑定
    ├── hpa.yaml                # 自动扩缩容
    ├── pdb.yaml                # Pod 中断预算
    ├── servicemonitor.yaml     # Prometheus 监控
    └── networkpolicy.yaml      # 网络策略
```

### 部署阶段

IAM-Operator 属于 **Day 1 - 基础服务阶段** 部署：

```mermaid
graph TD
    A[Day 0: 基础设施] --> B[Day 1: 基础服务]
    B --> C[部署 IAM-Operator]
    B --> D[部署 XDB-Operator]
    C --> E[Day 2: 业务服务]
    D --> E
    E --> F[部署 waf-meta 等业务服务]
```

### 配置说明

#### 基本配置
```yaml
# values.yaml
image:
  repository: abc-stack/iam-operator
  tag: "1.0.0"

iamApi:
  url: "http://iam-openapi.console.svc.cluster.local:8480"

targetNamespaces:
  - security
  - console
  - billing
```

#### 生产环境配置
```yaml
# values-production.yaml
replicaCount: 2
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
podDisruptionBudget:
  enabled: true
monitoring:
  serviceMonitor:
    enabled: true
```
