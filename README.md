# [注意](https://ku.baidu-int.com/d/h-BSEqiwYzoOQM)
- 保存服务号`ak/sk`的`keys.json`对应的`SQL文件`通过`chart`包`xdb-data-init-bce-iam`挂载`configmap`的方式获取并执行（详见 `values.yaml` 中的 `bce-iam-aksk` CONFIG_MAP 变量）  
- `keys.json`对应`SQL文件`执行时机是: 所有服务号 `ak/sk` 的`模版数据`对应的`SQL`已经执行，比如 `V1.0.0.4__bce_iam__init_service_resource_account.sql` 等文件已由 `flyway` 执行  
- 所以，定义如下规则：  
  - 后续追加服务号、资源账号`ak/sk`统一在`V1.0.0.17`和`V1.0.0.18`文件中  
  - `keys.json`对应的`SQL`文件名固定为`V1.0.0.19__bce_iam__init_ak_sk.sql`  
此时会保证所有服务号的`ak/sk`都会刷新成`keys.json`中的（详见`dockerfile`目录中的`preinstall.sh`）  

如上为临时方案，可以保证 0-1 的自动化部署，并且`keys.json`的`SQL`只会执行一次（基于`flyway`的版本控制）可以满足 det 部署要求。  
但针对后续存量项目新增服务号，需要和 `charm` 包一样手动操作，但此时 det 已无需关注，所以暂时先这样弄。  
最终方案可以采用 `operator`：https://ku.baidu-int.com/d/pt0u1Vr4UAUQ7i

# [chart流水线](https://console.cloud.baidu-int.com/devops/ipipe/workspaces/415957/pipelines/list)
1. DockerFile 使用各产品代码库的 pcd/feature/23930 分支  
2. chart 使用 product-chart/iam 代码库的 master 分支  
3. 没有特殊情况，chart 包版本和镜像版本保持一致  
4. 后置动作暂时放在了 `iam-ueba` 模块  

# 参考
[Chart 接入指南](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/ZIeThHHOUA/j8KBbTBLvEM4Kw)  
[Chart 详细规范](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/ZIeThHHOUA/37afe342b79746)  
[xdb-init DDL规范](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/12eg8wo0pG/iA4-l4pqccdE27)  
[xdb-init DML规范](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/12eg8wo0pG/c-Z0k1JL-Fkbh-)  
[xdb-init 多 configmap 挂载](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/12eg8wo0pG/gbBh3onxyBF5ew)  
[服务配置文件引用 values.yaml 规范](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/uxbk9pSdsw/c0f17445e3604b)

如流群：9725209

# 打平记录
为方便后续检查xdb是否遗留，记录上次打平commitId
1. 323合入331版本 
2025/7/23 xdb-schema-init-bce-iam/xdb-data-init-bce-iam打平至charm包commitId: 842b705
