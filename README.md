Xdb Operator
===
云底座数据库xdb的operator，相比charm包下，需要基于苍竹后置任务执行脚本，完成DB、User的创建。本项目实现了对上述的资源的CRD转化，可以按需申请对应CR。
更加符合云原生。更加便捷直观的管理资源。

| Version | Features                                    | Mode     |
|---------|---------------------------------------------|----------| 
| 2.0     | xdb backup <br> mysqlsingle                 | Operator |
| 1.0     | database management <br> Account management | Operator |
| 0.1     | Cluster management                          | Helm     |

快速开始
---
如何构建、安装、运行
> 环境准备

1. 请下载安装miniconda3的Python环境，会避免很多的环境依赖问题，该项目最新使用Python3.10开发测试；只要python版本包支持
   kopf、kubernetes 即可。同时依赖gcc8.2(因为使用了xdb 的atum工具管理xdb)
   本项目基于 kopf框架 开发operator 开源地址：https://github.com/nolar/kopf  为什么不用go 的kubebuild? 原因是因为xdb
   的管理是基于有大量的 python2的脚本+二进制工具。采用GO完全重构开发周期会很长。


2. 下载项目

   https://console.cloud.baidu-int.com/devops/icode/repos/baidu/cloudbed/xdb-operator/tree/master

   目录结构

        ├── CHANGELOG.md
        ├── README.md
        ├── build.sh
        ├── ci.yml
        ├── deploy                      
        │   ├── deploy-crds.yaml
        │   └── deploy-operator.yaml
        ├── helm
        │   └── xdb-operator
        │       ├── Chart.yaml
        │       ├── charts
        │       ├── crds
        │       │   └── crd.yaml
        │       ├── templates
        │       │   ├── cluster_kopf_keepering.yaml
        │       │   ├── cluster_role_binding_operator.yaml
        │       │   ├── cluster_role_operator.yaml
        │       │   ├── deployment.yaml
        │       │   ├── service.yaml
        │       │   └── service_account_operator.yaml
        │       ├── values-template.yaml
        │       └── values.yaml
        ├── images    # 容器镜像
        │   ├── xdb-cluster
        │   │   └── Dockerfile
        │   └── xdb-operator
        │       └── Dockerfile
        ├── requirements.txt
        ├── samples  #常见的crd 的例子
        │   ├── example-xdb-cluster.yaml
        │   ├── example-xdb-database.yaml
        │   └── example-xdb-user.yaml
        └── xdboperator  #具体逻辑
            ├── __init__.py
            ├── controller
            │   ├── __init__.py
            │   ├── api_utils.py
            │   ├── cluster
            │   │   ├── __init__.py
            │   │   ├── operato_cluster.py
            │   │   ├── xdbcluster_api.py
            │   │   ├── xdbcluster_controller.py
            │   │   └── xdbcluster_objects.py
            │   ├── config.py
            │   ├── consts.py
            │   ├── database
            │   │   ├── __init__.py
            │   │   ├── database_api.py
            │   │   ├── database_controller.py
            │   │   └── operator_database.py
            │   ├── diagnose.py
            │   ├── k8sobject.py
            │   ├── kubeutils.py
            │   ├── operator.py
            │   ├── user
            │   │   ├── __init__.py
            │   │   ├── operator_user.py
            │   │   ├── xdbuser_api.py
            │   │   └── xdbuser_controller.py
            │   └── utils.py
            └── lightxdb
                ├── __init__.py
                ├── account.py
                ├── atum
                ├── database.py
                ├── http_client.py
                ├── readme.txt
                ├── xagent.py
                └── zookeeper.py

测试
---
如何执行自动化测试

如何贡献
---
贡献patch流程及质量要求

版本信息
---
本项目的各版本信息和变更历史可以在[这里][changelog]查看。

维护者
---

### owners

* lixianzhu(<EMAIL>)

### committers

* lixianzhu(<EMAIL>)

讨论
---
百度Hi交流群：群号


[changelog]: http://icode.baidu.com/repos/baidu/personal-code/xdb-operator/blob/master:CHANGELOG.md
