package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

// Config holds the operator configuration
type Config struct {
	// IAM Service Discovery
	IAMServiceDiscoveryEnabled bool
	IAMServiceNamespace        string
	IAMServiceName             string

	// IAM API Configuration
	IAMAPIURLFallback string
	IAMAPITimeout     time.Duration
	IAMDomain         string
	IAMUsername       string
	IAMPassword       string

	// Processing Configuration
	ProcessingTimeout time.Duration
	RetryInterval     time.Duration
	MaxRetries        int

	// Monitoring Configuration
	MetricsPort     int
	HealthCheckPort int

	// Logging Configuration
	LogLevel string

	// Target Namespaces
	TargetNamespaces []string

	// State Management
	StateConfigMapName      string
	StateConfigMapNamespace string
}

// LoadConfig loads configuration from environment variables
func LoadConfig() *Config {
	return &Config{
		// IAM Service Discovery
		IAMServiceDiscoveryEnabled: getBoolEnv("IAM_SERVICE_DISCOVERY_ENABLED", true),
		IAMServiceNamespace:        getEnv("IAM_SERVICE_NAMESPACE", "console"),
		IAMServiceName:             getEnv("IAM_SERVICE_NAME", "iam-manage-xian"),

		// IAM API Configuration
		IAMAPIURLFallback: getEnv("IAM_API_URL_FALLBACK", "http://iam-manage-xian.console.svc.cluster.local:8468/v3"),
		IAMAPITimeout:     getDurationEnv("IAM_API_TIMEOUT", 30*time.Second),
		IAMDomain:         getEnv("IAM_DOMAIN", "Default"),
		IAMUsername:       getEnv("IAM_USERNAME", "proxy"),
		IAMPassword:       getEnv("IAM_PASSWORD", "default-password"),

		// Processing Configuration
		ProcessingTimeout: getDurationEnv("PROCESSING_TIMEOUT", 30*time.Second),
		RetryInterval:     getDurationEnv("RETRY_INTERVAL", 60*time.Second),
		MaxRetries:        getIntEnv("MAX_RETRIES", 3),

		// Monitoring Configuration
		MetricsPort:     getIntEnv("METRICS_PORT", 8080),
		HealthCheckPort: getIntEnv("HEALTH_CHECK_PORT", 8081),

		// Logging Configuration
		LogLevel: getEnv("LOG_LEVEL", "INFO"),

		// Target Namespaces
		TargetNamespaces: getStringSliceEnv("TARGET_NAMESPACES", []string{"security", "console", "billing"}),

		// State Management
		StateConfigMapName:      getEnv("STATE_CONFIGMAP_NAME", "iam-operator-state"),
		StateConfigMapNamespace: getEnv("STATE_CONFIGMAP_NAMESPACE", "base"),
	}
}

// IsTargetNamespace checks if the given namespace is in the target list
func (c *Config) IsTargetNamespace(namespace string) bool {
	for _, ns := range c.TargetNamespaces {
		if ns == namespace {
			return true
		}
	}
	return false
}

// GetIAMAPIURL returns the appropriate IAM API URL based on configuration
func (c *Config) GetIAMAPIURL() string {
	if c.IAMServiceDiscoveryEnabled {
		// Service discovery will be used, return empty for now
		return ""
	}
	return c.IAMAPIURLFallback
}

// Helper functions for environment variable parsing

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		// Try parsing as seconds first
		if seconds, err := strconv.Atoi(value); err == nil {
			return time.Duration(seconds) * time.Second
		}
		// Try parsing as duration string
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getStringSliceEnv(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.IAMAPIURLFallback == "" {
		return fmt.Errorf("IAM API URL fallback is required")
	}

	if c.IAMUsername == "" || c.IAMPassword == "" {
		return fmt.Errorf("IAM username and password are required")
	}

	if len(c.TargetNamespaces) == 0 {
		return fmt.Errorf("at least one target namespace is required")
	}

	return nil
}
