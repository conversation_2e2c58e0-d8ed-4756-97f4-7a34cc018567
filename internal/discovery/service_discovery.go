package discovery

import (
	"context"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

// ServiceDiscovery handles Kubernetes service discovery for IAM API
type ServiceDiscovery struct {
	client      kubernetes.Interface
	namespace   string
	serviceName string

	// Cache
	cachedEndpoint string
	cacheTime      time.Time
	cacheTTL       time.Duration
}

// NewServiceDiscovery creates a new service discovery client
func NewServiceDiscovery(client kubernetes.Interface, namespace, serviceName string) *ServiceDiscovery {
	return &ServiceDiscovery{
		client:      client,
		namespace:   namespace,
		serviceName: serviceName,
		cacheTTL:    5 * time.Minute, // 5分钟缓存
	}
}

// GetIAMEndpoint discovers and returns the IAM API endpoint
func (sd *ServiceDiscovery) GetIAMEndpoint(ctx context.Context) (string, error) {
	logger := log.FromContext(ctx)

	// Check cache first
	if sd.isCacheValid() {
		logger.Info("Using cached IAM endpoint", "endpoint", sd.cachedEndpoint)
		return sd.cachedEndpoint, nil
	}

	// Discover service
	service, err := sd.client.CoreV1().Services(sd.namespace).Get(ctx, sd.serviceName, metav1.GetOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to get service %s/%s: %w", sd.namespace, sd.serviceName, err)
	}

	// Get cluster IP
	clusterIP := service.Spec.ClusterIP
	if clusterIP == "" || clusterIP == "None" {
		return "", fmt.Errorf("service %s/%s has no cluster IP", sd.namespace, sd.serviceName)
	}

	// Find target port
	var targetPort int32
	for _, port := range service.Spec.Ports {
		if port.Name == "http" || port.Name == "api" || len(service.Spec.Ports) == 1 {
			targetPort = port.Port
			break
		}
	}

	if targetPort == 0 {
		return "", fmt.Errorf("no suitable port found for service %s/%s", sd.namespace, sd.serviceName)
	}

	// Build endpoint URL
	endpoint := fmt.Sprintf("http://%s:%d/v3", clusterIP, targetPort)

	// Update cache
	sd.cachedEndpoint = endpoint
	sd.cacheTime = time.Now()

	logger.Info("Discovered IAM endpoint",
		"service", fmt.Sprintf("%s/%s", sd.namespace, sd.serviceName),
		"endpoint", endpoint,
		"clusterIP", clusterIP,
		"port", targetPort)

	return endpoint, nil
}

// GetServiceInfo returns detailed service information for diagnostics
func (sd *ServiceDiscovery) GetServiceInfo(ctx context.Context) (*ServiceInfo, error) {
	service, err := sd.client.CoreV1().Services(sd.namespace).Get(ctx, sd.serviceName, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get service info: %w", err)
	}

	ports := make([]PortInfo, len(service.Spec.Ports))
	for i, port := range service.Spec.Ports {
		ports[i] = PortInfo{
			Name:     port.Name,
			Port:     port.Port,
			Protocol: string(port.Protocol),
		}
	}

	return &ServiceInfo{
		Name:      service.Name,
		Namespace: service.Namespace,
		ClusterIP: service.Spec.ClusterIP,
		Type:      string(service.Spec.Type),
		Ports:     ports,
	}, nil
}

// isCacheValid checks if the cached endpoint is still valid
func (sd *ServiceDiscovery) isCacheValid() bool {
	return sd.cachedEndpoint != "" && time.Since(sd.cacheTime) < sd.cacheTTL
}

// ClearCache clears the cached endpoint
func (sd *ServiceDiscovery) ClearCache() {
	sd.cachedEndpoint = ""
	sd.cacheTime = time.Time{}
}

// ServiceInfo contains detailed service information
type ServiceInfo struct {
	Name      string     `json:"name"`
	Namespace string     `json:"namespace"`
	ClusterIP string     `json:"cluster_ip"`
	Type      string     `json:"type"`
	Ports     []PortInfo `json:"ports"`
}

// PortInfo contains port information
type PortInfo struct {
	Name     string `json:"name"`
	Port     int32  `json:"port"`
	Protocol string `json:"protocol"`
}
