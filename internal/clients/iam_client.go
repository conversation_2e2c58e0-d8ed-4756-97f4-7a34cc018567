package clients

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"sigs.k8s.io/controller-runtime/pkg/log"
)

// IAMClient handles IAM API authentication and operations
type IAMClient struct {
	baseURL    string
	timeout    time.Duration
	httpClient *http.Client

	// Authentication cache
	authToken    string
	tokenExpires time.Time

	// Configuration
	domain   string
	username string
	password string
}

// NewIAMClient creates a new IAM client
func NewIAMClient(baseURL string, timeout time.Duration) *IAMClient {
	return &IAMClient{
		baseURL: baseURL,
		timeout: timeout,
		httpClient: &http.Client{
			Timeout: timeout,
		},
		// Default configuration - should be loaded from config
		domain:   "Default",
		username: "proxy",
		password: "default-password",
	}
}

// SetCredentials sets the authentication credentials
func (c *IAMClient) SetCredentials(domain, username, password string) {
	c.domain = domain
	c.username = username
	c.password = password
}

// AuthenticateAKSK validates AKSK credentials through IAM API
func (c *IAMClient) AuthenticateAKSK(ctx context.Context, accessKey, secretKey string) error {
	logger := log.FromContext(ctx)

	// Get auth token first
	token, err := c.getAuthToken(ctx)
	if err != nil {
		return fmt.Errorf("failed to get auth token: %w", err)
	}

	// Validate AKSK
	validateURL := fmt.Sprintf("%s/credentials/validate", c.baseURL)

	payload := map[string]interface{}{
		"access_key": accessKey,
		"secret_key": secretKey,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", validateURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Auth-Token", token)
	req.Header.Set("User-Agent", "IAM-Operator-Go/1.0")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		logger.Info("AKSK validation successful", "access_key", accessKey[:8]+"...")
		return nil
	}

	body, _ := io.ReadAll(resp.Body)
	return fmt.Errorf("AKSK validation failed: %d - %s", resp.StatusCode, string(body))
}

// getAuthToken obtains authentication token using OpenStack Keystone v3 format
func (c *IAMClient) getAuthToken(ctx context.Context) (string, error) {
	logger := log.FromContext(ctx)

	// Check if token is still valid
	if c.authToken != "" && time.Now().Before(c.tokenExpires) {
		return c.authToken, nil
	}

	// Prepare authentication request (OpenStack Keystone v3 format)
	authRequest := map[string]interface{}{
		"auth": map[string]interface{}{
			"identity": map[string]interface{}{
				"methods": []string{"password"},
				"password": map[string]interface{}{
					"user": map[string]interface{}{
						"domain":   map[string]string{"name": c.domain},
						"name":     c.username,
						"password": c.password,
					},
				},
			},
			"scope": map[string]interface{}{
				"domain": map[string]string{"id": "default"},
			},
		},
	}

	jsonData, err := json.Marshal(authRequest)
	if err != nil {
		return "", fmt.Errorf("failed to marshal auth request: %w", err)
	}

	authURL := fmt.Sprintf("%s/auth/tokens", c.baseURL)
	req, err := http.NewRequestWithContext(ctx, "POST", authURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create auth request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "IAM-Operator-Go/1.0")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make auth request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("authentication failed: %d - %s", resp.StatusCode, string(body))
	}

	// Get token from response header
	token := resp.Header.Get("X-Subject-Token")
	if token == "" {
		return "", fmt.Errorf("no X-Subject-Token in response")
	}

	// Cache token (default 1 hour expiry)
	c.authToken = token
	c.tokenExpires = time.Now().Add(time.Hour)

	logger.Info("IAM authentication successful")
	return token, nil
}

// HealthCheck performs a health check against the IAM API
func (c *IAMClient) HealthCheck(ctx context.Context) error {
	healthURL := fmt.Sprintf("%s/health", c.baseURL)

	req, err := http.NewRequestWithContext(ctx, "GET", healthURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create health check request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("health check request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("health check failed: status %d", resp.StatusCode)
	}

	return nil
}

// ClearAuthCache clears the cached authentication token
func (c *IAMClient) ClearAuthCache() {
	c.authToken = ""
	c.tokenExpires = time.Time{}
}
