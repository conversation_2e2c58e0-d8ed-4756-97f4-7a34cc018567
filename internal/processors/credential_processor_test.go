package processors

import (
	"context"
	"testing"

	"github.com/abc-stack/iam-operator/internal/config"
)

func TestExtractPHPCredentials(t *testing.T) {
	cp := &CredentialProcessor{}

	testCases := []struct {
		name     string
		content  string
		expected map[string]*Credential
	}{
		{
			name: "Valid PHP credentials",
			content: `<?php
$bss_ak = "AKIAIOSFODNN7EXAMPLE";
$bss_sk = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY";
$other_ak = "AKIAI44QH8DHBEXAMPLE";
$other_sk = "je7MtGbClwBF/2Zp9Utk/h3yCo8nvbEXAMPLEKEY";
?>`,
			expected: map[string]*Credential{
				"bss": {
					AccessKey: "AKIAIOSFODNN7EXAMPLE",
					SecretKey: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
					Product:   "bss",
				},
				"other": {
					AccessKey: "AKIAI44QH8DHBEXAMPLE",
					SecretKey: "je7MtGbClwBF/2Zp9Utk/h3yCo8nvbEXAMPLEKEY",
					Product:   "other",
				},
			},
		},
		{
			name: "Single quotes",
			content: `$test_ak = 'AKIATEST123';
$test_sk = 'secretkey456';`,
			expected: map[string]*Credential{
				"test": {
					AccessKey: "AKIATEST123",
					SecretKey: "secretkey456",
					Product:   "test",
				},
			},
		},
		{
			name:     "No credentials",
			content:  `<?php echo "Hello World"; ?>`,
			expected: map[string]*Credential{},
		},
		{
			name: "Mismatched credentials",
			content: `$bss_ak = "AKIATEST123";
$other_sk = "secretkey456";`,
			expected: map[string]*Credential{},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := cp.extractPHPCredentials(tc.content)

			if len(result) != len(tc.expected) {
				t.Errorf("Expected %d credentials, got %d", len(tc.expected), len(result))
				return
			}

			for product, expectedCred := range tc.expected {
				actualCred, exists := result[product]
				if !exists {
					t.Errorf("Expected credential for product %s not found", product)
					continue
				}

				if actualCred.AccessKey != expectedCred.AccessKey {
					t.Errorf("AccessKey mismatch for %s: expected %s, got %s",
						product, expectedCred.AccessKey, actualCred.AccessKey)
				}

				if actualCred.SecretKey != expectedCred.SecretKey {
					t.Errorf("SecretKey mismatch for %s: expected %s, got %s",
						product, expectedCred.SecretKey, actualCred.SecretKey)
				}

				if actualCred.Product != expectedCred.Product {
					t.Errorf("Product mismatch for %s: expected %s, got %s",
						product, expectedCred.Product, actualCred.Product)
				}
			}
		})
	}
}

func TestIsIAMConfigMap(t *testing.T) {
	testCases := []struct {
		name             string
		data             map[string]string
		namespace        string
		targetNamespaces []string
		expected         bool
	}{
		{
			name: "Valid IAM ConfigMap with PHP credentials",
			data: map[string]string{
				"config.php": `$bss_ak = "test"; $bss_sk = "test";`,
			},
			namespace:        "default",
			targetNamespaces: []string{"default", "kube-system"},
			expected:         true,
		},
		{
			name: "Valid IAM ConfigMap with YAML credentials",
			data: map[string]string{
				"config.yaml": `iam:
  ak: "test"
  sk: "test"`,
			},
			namespace:        "default",
			targetNamespaces: []string{"default", "kube-system"},
			expected:         true,
		},
		{
			name: "Non-target namespace",
			data: map[string]string{
				"config.php": `$bss_ak = "test"; $bss_sk = "test";`,
			},
			namespace:        "other",
			targetNamespaces: []string{"default", "kube-system"},
			expected:         false,
		},
		{
			name: "No IAM credentials",
			data: map[string]string{
				"config.txt": `some random config`,
			},
			namespace:        "default",
			targetNamespaces: []string{"default", "kube-system"},
			expected:         false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := IsIAMConfigMap(tc.data, tc.namespace, tc.targetNamespaces)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v", tc.expected, result)
			}
		})
	}
}

func TestProcessCredentials(t *testing.T) {
	// Skip this test since it requires a real IAM client
	// In a production environment, you would create a mock IAM client
	t.Skip("Skipping ProcessCredentials test - requires mock IAM client implementation")

	// Mock IAM client for testing
	mockConfig := &config.Config{
		TargetNamespaces: []string{"default"},
	}

	cp := &CredentialProcessor{
		config: mockConfig,
		// iamClient would be mocked in a real test
	}

	ctx := context.Background()

	testData := map[string]string{
		"config.php": `$bss_ak = "AKIATEST123"; $bss_sk = "secretkey456";`,
	}

	// This test would require a mock IAM client
	// For now, we just test that it doesn't panic
	err := cp.ProcessCredentials(ctx, "configmap", "test/config", testData)

	// Since we don't have a real IAM client, we expect an error
	if err == nil {
		t.Log("ProcessCredentials completed without error (expected with mock)")
	}
}
