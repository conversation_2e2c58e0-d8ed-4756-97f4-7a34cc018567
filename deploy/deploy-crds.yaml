apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: xdbfusionclusters.cloudbed.abcstack.com
spec:
  group: cloudbed.abcstack.com
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            apiVersion:
              type: string
            kind:
              type: string
            metadata:
              type: object
              properties:
                name:
                  type: string
                  maxLength: 64
            spec:
              type: object
              required: [ "instances","mysqlVersion","appID","clusterID","ZKDomain" ]
              properties:
                instances:
                  type: integer
                  description: "The number of XDB control plane instances"
                  default: 3
                mysqlVersion:
                  type: string
                  description: "mysql version"
                appID:
                  type: string
                  description: "clusterID Blueprint plans cluster ids"
                clusterID:
                  type: string
                  description: "clusterID Blueprint plans cluster ids"
                ZKDomain:
                  type: string
                  description: "The XDB control plane runs the dependent zookeeper server"
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
      subresources:
        status: { }
      additionalPrinterColumns:
        - name: Status
          type: string
          description: Status of the XDB Fusion Cluster
          jsonPath: .status.cluster.status
        - name: Online
          type: integer
          description: Number of ONLINE XDB Fusion Cluster instances
          jsonPath: .status.cluster.onlineInstances
        - name: Instances
          type: integer
          description: Number of XDB Fusion Cluster instances configured
          jsonPath: .spec.instances
        - name: clusterID
          type: string
          description: clusterID of XDB Fusion Cluster
          jsonPath: .spec.clusterID
        - name: Version
          type: string
          description: mysqlVersion of XDB Fusion Cluster
          jsonPath: .spec.mysqlVersion
        - name: Age
          type: date
          jsonPath: .metadata.creationTimestamp

  scope: Namespaced
  names:
    plural: xdbfusionclusters
    singular: xdbfusioncluster
    kind: XDBFusionCluster
    shortNames:
      - xdbfc
      - xdb
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: mysqlsingles.cloudbed.abcstack.com
spec:
  group: cloudbed.abcstack.com
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            apiVersion:
              type: string
            kind:
              type: string
            metadata:
              type: object
              properties:
                name:
                  type: string
                  maxLength: 64
            spec:
              type: object
              required: ["mysqlVersion","mysqlDataStorageSize"]
              properties:
                instances:
                  type: integer
                  description: "The number of XDB control plane instances"
                  default: 1
                  minimum: 1
                mysqlVersion:
                  type: string
                  description: "mysql version"
                  enum: [ "5.7"]
                mysqlDataStorageSize:
                  type: string
                  description: "mysql dir storage size"
                # appID:
                #   type: string
                #   description: "clusterID Blueprint plans cluster ids"
                # clusterID:
                #   type: string
                #   description: "clusterID Blueprint plans cluster ids"
                # ZKDomain:
                #   type: string
                #   description: "The XDB control plane runs the dependent zookeeper server"
                snapshotRef:
                  type: object
                  required: [ "name","namespace" ]
                  properties:
                    name:
                      type: string
                    namespace:
                      type: string 
                image:
                  type: string
                  description: "Custom image for XDB MySQL StatefulSet"
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
      subresources:
        status: { }
      additionalPrinterColumns:
        - name: Status
          type: string
          description: Status of the XDB Fusion Cluster
          jsonPath: .status.cluster.status
        - name: Online
          type: integer
          description: Number of ONLINE XDB Fusion Cluster instances
          jsonPath: .status.cluster.onlineInstances
        - name: Instances
          type: integer
          description: Number of XDB Fusion Cluster instances configured
          jsonPath: .spec.instances
        - name: Version
          type: string
          description: mysqlVersion of XDB Fusion Cluster
          jsonPath: .spec.mysqlVersion
        - name: Age
          type: date
          jsonPath: .metadata.creationTimestamp
  scope: Namespaced
  names:
    plural: mysqlsingles
    singular: mysqlsingle
    kind: MysqlSingle
    shortNames:
      - ms
      - mysqls
      - mysqlone
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: xdbproxys.cloudbed.abcstack.com
spec:
  group: cloudbed.abcstack.com
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            apiVersion:
              type: string
            kind:
              type: string
            metadata:
              type: object
              properties:
                name:
                  type: string
                  maxLength: 64
            spec:
              type: object
              required: [ "instances","clusterRef" ]
              properties:
                instances:
                  type: integer
                  description: "The number of XDB Proxy plane instances"
                  default: 3
                  minimum: 1
                clusterRef:
                  type: object
                  required: [ "name","namespace" ]
                  properties:
                    name:
                      type: string
                    namespace:
                      type: string
                version:
                  type: string
                  description: "proxy bin version"
                  default: "********"
                region:
                  type: string
                  description: "The region where the cluster is located"
                  default: "cn-beijing"
                storage:
                  type: object
                  properties:
                    xdbproxy:
                      type: object
                      properties:
                        storageClass:
                          type: string
                        size:
                          type: string
                        storageType:
                          type: string
                          enum: ["emptyDir", "persistentVolume"]
                          default: "emptyDir"
                    xagent:
                      type: object
                      properties:
                        storageClass:
                          type: string
                        size:
                          type: string
                        storageType:
                          type: string
                          enum: ["emptyDir", "persistentVolume"]
                          default: "emptyDir"
                affinity:
                  type: object
                  properties:
                    podAntiAffinity:
                      type: object
                      properties:
                        preferredDuringSchedulingIgnoredDuringExecution:
                          type: array
                          items:
                            type: object
                            properties:
                              weight:
                                type: integer
                              podAffinityTerm:
                                type: object
                                properties:
                                  labelSelector:
                                    type: object
                                    x-kubernetes-preserve-unknown-fields: true
                                  topologyKey:
                                    type: string
                        requiredDuringSchedulingIgnoredDuringExecution:
                          type: array
                          items:
                            type: object
                            properties:
                              labelSelector:
                                type: object
                                x-kubernetes-preserve-unknown-fields: true
                              topologyKey:
                                type: string
                              whenUnsatisfiable:
                                type: string
                    nodeAffinity:
                      type: object
                      properties:
                        requiredDuringSchedulingIgnoredDuringExecution:
                          type: object
                          properties:
                            nodeSelectorTerms:
                              type: array
                              items:
                                type: object
                                properties:
                                  matchExpressions:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        key:
                                          type: string
                                        operator:
                                          type: string
                                        values:
                                          type: array
                                          items:
                                            type: string
                resources:
                  type: object
                  properties:
                    requests:
                      type: object
                      properties:
                        cpu:
                          type: string
                        memory:
                          type: string
                    limits:
                      type: object
                      properties:
                        cpu:
                          type: string
                        memory:
                          type: string
                image:
                  description: "Custom image for XDBProxy StatefulSet"
                  type: string
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
      subresources:
        status: { }
      additionalPrinterColumns:
        - name: Status
          type: string
          description: Status of the XDB Fusion ClusterProxy
          jsonPath: .status.cluster.status
        # - name: READY
        #   type: string
        #   jsonPath: .status.ready
        #   description: "Indicates if the resource is ready"
        # - name: RESTARTS
        #   type: integer
        #   jsonPath: .status.restarts
        #   description: "Number of restarts for the resource"
        - name: Online
          type: integer
          description: Number of ONLINE XDB Fusion ClusterProxy instances
          jsonPath: .status.cluster.onlineInstances
        - name: Instances
          type: integer
          description: Number of XDB Fusion Cluster instances configured
          jsonPath: .spec.instances
        - name: clusterID
          type: string
          description: clusterID of Relation XDB Fusion Cluster
          jsonPath: .spec.clusterRef.name
        - name: Version
          type: string
          description: proxyVersion of XDB Proxy kernel
          jsonPath: .spec.Version
        - name: region
          type: string
          description: The region where the cluster is located
          jsonPath: .spec.region
        - name: Age
          type: date
          jsonPath: .metadata.creationTimestamp
  scope: Namespaced
  names:
    plural: xdbproxys
    singular: xdbproxy
    kind: XDBProxy
    shortNames:
      - xdbp
      - dbproxy
      - proxy
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: xdbdatabases.cloudbed.abcstack.com
spec:
  group: cloudbed.abcstack.com
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            apiVersion:
              type: string
            kind:
              type: string
            metadata:
              type: object
            spec:
              type: object
              required: [ "database","clusterRef" ]
              properties:
                database:
                  type: string
                clusterRef:
                  type: object
                  required: [ "name","namespace" ]
                  properties:
                    name:
                      type: string
                      maxLength: 64
                    namespace:
                      type: string
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
      subresources:
        status: { }
      additionalPrinterColumns:
        - name: Status
          type: string
          description: Status of the XDB Fusion Cluster
          jsonPath: .status.db.status
        - name: READY
          type: string
          description: DB Init Result
          jsonPath: .status.db.ready
        - name: DBName
          type: string
          description: DBName of XDB Fusion Cluster database
          jsonPath: .spec.database
        - name: XDBClusterName
          type: string
          description: XDBClusterName of xdbusers user
          jsonPath: .spec.clusterRef.name
        - name: Age
          type: date
          jsonPath: .metadata.creationTimestamp
  scope: Namespaced
  names:
    plural: xdbdatabases
    singular: xdbdatabase
    kind: XDBDataBase
    shortNames:
      - xdbdb
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: xdbusers.cloudbed.abcstack.com
spec:
  group: cloudbed.abcstack.com
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            apiVersion:
              type: string
            kind:
              type: string
            metadata:
              type: object
              properties:
                name:
                  type: string
                  maxLength: 64
            spec:
              type: object
              required: [ "user","password","clusterRef" ]
              properties:
                user:
                  type: string
                password:
                  type: object
                  required: [ "secretName","key" ]
                  properties:
                    secretName:
                      type: string
                    key:
                      type: string
                clusterRef:
                  type: object
                  required: [ "name","namespace" ]
                  properties:
                    name:
                      type: string
                    namespace:
                      type: string
                dbproxyUserMaxConnections:
                  type: integer
                  description: "Max connections for DBProxy layer users"
                mysqlUserMaxConnections:
                  type: integer
                  description: "Max connections for Mysql layer users"
                wrFlag:
                  type: integer
                  enum: [ 0,1,2 ]
                  description: "Read/write split switch, 0: enable read/write split, 1: read from slave library only, 2: read from master library only"
                allowedHosts:
                  type: array
                  description: "If host is specified, the user is created to use the specified host; if not, the default is the IP address of the dbproxy"
                  items:
                    type: string
                    enum: [ "localhost", "%" ]
                permissions:
                  type: array
                  items:
                    type: object
                    properties:
                      schema:
                        type: string
                      permissions:
                        type: string
                        enum: [ "ALL", "READ", "WRITE","all","write","read","monitor","DBA","MONITOR","dba"]
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
      subresources:
        status: { }
      additionalPrinterColumns:
        - name: Status
          type: string
          description: Status of the XDB Fusion Cluster
          jsonPath: .status.user.status
        - name: READY
          type: string
          description: User Init Result
          jsonPath: .status.user.ready
        - name: UserName
          type: string
          description: UserName of xdbusers user
          jsonPath: .spec.user
        - name: RWFlag
          type: integer
          description: Read write separation switch
          jsonPath: .spec.wrFlag
        - name: XDBClusterName
          type: string
          description: XDBClusterName of xdbusers user
          jsonPath: .spec.clusterRef.name
        - name: Age
          type: date
          jsonPath: .metadata.creationTimestamp
  scope: Namespaced
  names:
    plural: xdbusers
    singular: xdbuser
    kind: XDBUser
    shortNames:
      - xmuser
      - xdbuser
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: xdbbackups.cloudbed.abcstack.com
spec:
  group: cloudbed.abcstack.com
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            apiVersion:
              type: string
            kind:
              type: string
            metadata:
              type: object
            spec:
              type: object
              required: [ "clusterName","clusterNamespace","backupStrategyName","backupStrategyNamespace"]
              properties:
                remoteDeletePolicy:
                  type: string
                clusterName:
                  type: string
                clusterNamespace:
                  type: string
                backupStrategyName:
                   type: string
                backupStrategyNamespace:
                   type: string
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
      subresources:
        status: { }
      additionalPrinterColumns:
        - name: Status
          type: string
          description: Status of the snapshot task
          jsonPath: .status.snapshot.status
        - jsonPath: .status.snapshot.storageType
          description: Backup storage type
          name: StorageType
          type: string
          priority: 0
        - jsonPath: .status.snapshot.beginTime
          description: Backup begin time
          name: BeginTime
          type: string
          priority: 0
        - jsonPath: .status.snapshot.endTime
          description: Backup end time
          name: EndTime
          type: string
          priority: 1
        - jsonPath: .status.snapshot.NodeID
          description: Backup the node on which the task is running
          name: NodeID
          type: string
          priority: 0
        - jsonPath: .status.snapshot.originSize
          description: Backup origin size
          name: OriginSize
          type: string
          priority: 0
        - jsonPath: .status.snapshot.storageSize
          description: Backup storage size
          name: StorageSize
          type: string
          priority: 0
        - jsonPath: .status.snapshot.fileName
          description: Backup file name
          name: fileName
          type: string
          priority: 0
        - jsonPath: .status.snapshot.filePath
          description: Backup file path
          name: filePath
          type: string
          priority: 1
        - name: XDBClusterName
          type: string
          description: The cluster name to associate with
          jsonPath: .spec.clusterName
          priority: 0
        - name: backupStrategyName
          type: string
          description: Associated backup policy
          jsonPath: .spec.backupStrategyName
          priority: 1
        - name: backupStrategyNamespace
          type: string
          description: Associated backup policy
          jsonPath: .spec.backupStrategyNamespace
          priority: 1
  scope: Namespaced
  names:
    plural: xdbbackups
    singular: xdbbackup
    kind: XDBBackup
    shortNames:
      - xdbbk
      - xb
      - xbk

---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: xdbbackupstrategys.cloudbed.abcstack.com
spec:
  group: cloudbed.abcstack.com
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            apiVersion:
              type: string
            kind:
              type: string
            metadata:
              type: object
            spec:
              type: object
              required: [ "clusterName","clusterNamespace","expireDuration", "storageType", "schedule" ]
              properties:
                schedule:
                  type: string
                secretName:
                  type: string
                storageType:
                  type: string
                  enum: [ "fs", "s3" ]
                kind:
                  type: string
                  enum: [ "schedule", "single" ]
                expireDuration:
                  type: string
                clusterName:
                  type: string
                clusterNamespace:
                  type: string
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
      subresources:
        status: { }
      additionalPrinterColumns:
        - name: Status
          type: string
          description: Status of the strategy task
          jsonPath: .status.strategy.status
        - jsonPath: .spec.storageType
          description: Backup stroy Type
          name: StorageType
          type: string
          priority: 0
        - jsonPath: .spec.schedule
          description: Backup Schedule
          name: Schedule
          type: string
          priority: 0
        - jsonPath: .spec.kind
          description: Backup kind
          name: kind
          type: string
          priority: 0
        - jsonPath: .spec.expireDuration
          description: Backup expireDuration
          name: expireDuration
          type: string
          priority: 0
        - name: XDBName
          type: string
          description: XDBClusterName
          jsonPath: .spec.clusterName
          priority: 0
        - jsonPath: .status.strategy.nextRunTime
          description: If Backup is cron show Backup next run time
          name: NextRunTime
          type: string
          priority: 0
  scope: Namespaced
  names:
    plural: xdbbackupstrategys
    singular: xdbbackupstrategy
    kind: XDBBackupStrategy
    shortNames:
      - xdbbkps
      - xbs
      - xdbbks
      - xbks

---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: clusterkopfpeerings.kopf.dev
spec:
  scope: Cluster
  group: kopf.dev
  names:
    kind: ClusterKopfPeering
    plural: clusterkopfpeerings
    singular: clusterkopfpeering
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: kopfpeerings.kopf.dev
spec:
  scope: Namespaced
  group: kopf.dev
  names:
    kind: KopfPeering
    plural: kopfpeerings
    singular: kopfpeering
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            status:
              type: object
              x-kubernetes-preserve-unknown-fields: true
