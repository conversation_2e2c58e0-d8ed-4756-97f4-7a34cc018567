# The main role for the operator
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: xdb-operator
rules:
  - apiGroups: [ "" ]
    resources: [ "pods" ]
    verbs: [ "get", "list", "watch", "patch" ]
  - apiGroups: [ "" ]
    resources: [ "pods/status" ]
    verbs: [ "get", "patch", "update", "watch" ]
    # <PERSON><PERSON> needs patch on secrets or the sidecar will throw
    # The operator needs this verb to be able to pass it to the sidecar
  - apiGroups: [ "" ]
    resources: [ "secrets" ]
    verbs: [ "get", "create", "list", "watch", "patch" ]
  - apiGroups: [ "" ]
    resources: [ "configmaps" ]
    verbs: [ "get", "create", "update", "list", "watch", "patch", "delete" ]
  - apiGroups: [ "" ]
    resources: [ "services" ]
    verbs: [ "get", "create", "list", "update", "delete", "patch" ]
  - apiGroups: [ "" ]
    resources: [ "serviceaccounts" ]
    verbs: [ "get", "create", "patch" ]
  - apiGroups: [ "" ]
    resources: [ "events" ]
    verbs: [ "create", "patch", "update" ]
  - apiGroups: [ "rbac.authorization.k8s.io" ]
    resources: [ "rolebindings" ]
    verbs: [ "get", "create" ]
  - apiGroups: [ "policy" ]
    resources: [ "poddisruptionbudgets" ]
    verbs: [ "get", "create" ]
  - apiGroups: [ "batch" ]
    resources: [ "jobs" ]
    verbs: [ "create" ]
  - apiGroups: [ "batch" ]
    resources: [ "cronjobs" ]
    verbs: [ "get", "create", "update", "delete" ]
  - apiGroups: [ "apps" ]
    resources: [ "deployments", "statefulsets" ]
    verbs: [ "get", "create", "patch", "update", "watch", "delete" ]
  - apiGroups: [ "apps.kruise.io" ]
    resources: [ "statefulsets" ]
    verbs: [ "get", "list", "watch", "create", "update", "patch", "delete" ]
  - apiGroups: [ "cloudbed.abcstack.com" ]
    resources: [ "*" ]
    verbs: [ "*" ]
  # Framework: knowing which other operators are running (i.e. peering).
  - apiGroups: [ "kopf.dev" ]
    resources: [ "*" ]
    verbs: [ "get", "patch", "list", "watch" ]
  # Kopf: runtime observation of namespaces & CRDs (addition/deletion).
  - apiGroups: [ "apiextensions.k8s.io" ]
    resources: [ "customresourcedefinitions" ]
    verbs: [ "list", "watch" ]
  - apiGroups: [ "" ]
    resources: [ "namespaces" ]
    verbs: [ "list", "watch" ]
  - apiGroups: [ "monitoring.coreos.com" ]
    resources: [ "servicemonitors" ]
    verbs: [ "get", "create", "patch", "update", "delete" ]
  - apiGroups: [ "" ]
    resources: [ pods/exec ]
    verbs: [ "*" ]
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [ "apps.kruise.io" ]
    resources: [ "statefulsets" ]
    verbs: [ "get", "list", "watch", "create", "update", "patch", "delete" ]
  - apiGroups: ["directpv.min.io"]
    resources: ["directpvvolumes"]
    verbs: ["get", "list", "watch"]


---
# Give access to the operator
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: xdb-operator-rolebinding
subjects:
  - kind: ServiceAccount
    name: xdb-operator-sa
    namespace: xdb-operator
roleRef:
  kind: ClusterRole
  name: xdb-operator
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: kopf.dev/v1
kind: ClusterKopfPeering
metadata:
  name: xdb-operator
---
apiVersion: v1
kind: Namespace
metadata:
  name: xdb-operator
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: xdb-operator-sa
  namespace: xdb-operator
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: xdb-operator
  namespace: xdb-operator
  labels:
    version: "v1.0"
    app.kubernetes.io/name: xdb-operator
    app.kubernetes.io/instance: xdb-operator
    app.kubernetes.io/version: "v1.0"
    app.kubernetes.io/component: controller
    app.kubernetes.io/managed-by: xdb-operator
    app.kubernetes.io/created-by: xdb-operator
spec:
  replicas: 1
  selector:
    matchLabels:
      name: xdb-operator
  template:
    metadata:
      labels:
        name: xdb-operator
    spec:
      containers:
        - name: xdb-operator
          image: registry.dev7.abcstackint.com:5000/abc-stack/xdb-operator:1.0.0.20
          imagePullPolicy: IfNotPresent
          args: [ "/usr/local/bin/kopf", "run","/xdb/xdboperator/controller/operator.py", "-A", "--verbose", "--liveness=http://0.0.0.0:8080/healthz" ]
          env:
            - name: GLOBAL_RESOURCE_DELETE_POLICY
              value: retain
            - name: DEFAULT_RESOURCE_DELETE_POLICY
              value: retain
            - name: XDB_OPERATOR_DEFAULT_REPOSITORY
              value: abc-stack/xdb-operator
          readinessProbe:
            exec:
              command:
                - cat
                - /tmp/xdb-operator-ready
            initialDelaySeconds: 1
            periodSeconds: 3
          livenessProbe:
            timeoutSeconds: 10
            initialDelaySeconds: 60
            periodSeconds: 60
            failureThreshold: 10
            successThreshold: 1
            httpGet:
              path: /healthz
              port: 8080
          volumeMounts:
            - mountPath: /opt
              name: opt
          securityContext:
            runAsUser: 0
            allowPrivilegeEscalation: true
            privileged: true
            readOnlyRootFilesystem: false
      volumes:
        - hostPath:
            path: /opt
            type: ""
          name: opt
      serviceAccountName: xdb-operator-sa
