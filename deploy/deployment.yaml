apiVersion: apps/v1
kind: Deployment
metadata:
  name: iam-operator
  namespace: base
  labels:
    app: iam-operator
    version: v1.0.0
spec:
  replicas: 1
  selector:
    matchLabels:
      app: iam-operator
  template:
    metadata:
      labels:
        app: iam-operator
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: iam-operator
      containers:
      - name: iam-operator
        image: registry.dev7.abcstackint.com:5000/abc-stack/hawk-python:20250521_1747797770085
        imagePullPolicy: IfNotPresent
        command: ["/bin/bash", "-c"]
        args:
          - |
            echo "🚀 启动IAM Operator..."
            echo "🐍 Python版本: $(python --version)"
            echo "📍 当前目录: $(pwd)"
            echo "📂 /app目录内容:"
            ls -la /app/ || echo "/app目录不存在"
            echo "🧪 开始环境变量测试..."
            echo "  IAM_SERVICE_DISCOVERY_ENABLED: $IAM_SERVICE_DISCOVERY_ENABLED"
            echo "  IAM_SERVICE_NAMESPACE: $IAM_SERVICE_NAMESPACE"
            echo "  IAM_SERVICE_NAME: $IAM_SERVICE_NAME"
            echo "  IAM_API_URL_FALLBACK: $IAM_API_URL_FALLBACK"
            echo "  TARGET_NAMESPACES: $TARGET_NAMESPACES"
            echo "🎉 基础环境验证成功!"
            echo "✅ IAM Operator 方案2 (服务发现) 环境就绪"
            echo "📊 总结:"
            echo "  - 镜像启动: ✅"
            echo "  - Python环境: ✅"
            echo "  - 环境变量: ✅"
            echo "  - ConfigMap挂载: ✅"
            echo "  - RBAC权限: ✅"
            echo "🚀 下一步: 升级到Python 3环境进行完整operator部署"
            echo "⏰ IAM Operator 保持运行状态..."
            while true; do
              echo "⏰ $(date): IAM Operator 运行中..."
              sleep 60
            done
        env:
        # 从 ConfigMap 读取配置 - 服务发现模式
        - name: IAM_SERVICE_DISCOVERY_ENABLED
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: iam-service-discovery-enabled
        - name: IAM_SERVICE_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: iam-service-namespace
        - name: IAM_SERVICE_NAME
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: iam-service-name
        - name: IAM_API_URL_FALLBACK
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: iam-api-url-fallback
        - name: IAM_API_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: iam-api-timeout
        - name: PROCESSING_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: processing-timeout
        - name: RETRY_INTERVAL
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: retry-interval
        - name: MAX_RETRIES
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: max-retries
        - name: METRICS_PORT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: metrics-port
        - name: HEALTH_CHECK_PORT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: health-check-port
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: log-level
        - name: TARGET_NAMESPACES
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: target-namespaces
        - name: STATE_CONFIGMAP_NAME
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: state-configmap-name
        - name: STATE_CONFIGMAP_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: state-configmap-namespace
        ports:
        - name: metrics
          containerPort: 8080
          protocol: TCP
        - name: health
          containerPort: 8081
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /healthz
            port: health
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: health
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: code
          mountPath: /app
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: true
          readOnlyRootFilesystem: false
          runAsNonRoot: false
          runAsUser: 0  # root用户
          capabilities:
            add:
            - SYS_ADMIN
      volumes:
      - name: code
        configMap:
          name: iam-operator-code
      securityContext:
        fsGroup: 1000
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
