# 真实场景测试ConfigMaps - 验证IAM Operator的AKSK解析功能

---
# 场景1: 直接键值对格式 (测试用例)
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-direct-credentials
  namespace: default
  labels:
    test-scenario: "direct-key-value"
data:
  access_key: "test-access-key-123"
  secret_key: "test-secret-key-456"
  app_name: "test-application"

---
# 场景2: PHP配置文件格式 (生产环境最常见)
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-php-credentials
  namespace: default
  labels:
    test-scenario: "php-config-file"
data:
  config.php: |
    <?php
    // BSS 产品配置
    $bss_ak = 'f90daa8e8af94f6bb2da26c380e0a9cb';
    $bss_sk = 'd90b6603d4414151a0dac95409dcd815';
    $bss_passwd = 'TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5';
    
    // WAF 产品配置
    $waf_ak = 'waf123456789abcdef';
    $waf_sk = 'waf987654321fedcba';
    ?>
  app_config: "database_url=postgresql://localhost:5432/app"

---
# 场景3: YAML嵌套格式 (结构化配置)
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-yaml-credentials
  namespace: default
  labels:
    test-scenario: "yaml-nested-config"
data:
  application.yaml: |
    app:
      name: "security-service"
      version: "1.0.0"
    
    iam:
      bss:
        ak: "yaml-bss-access-key"
        sk: "yaml-bss-secret-key"
        password: "yaml-bss-password"
      waf:
        ak: "yaml-waf-access-key"
        sk: "yaml-waf-secret-key"
    
    database:
      host: "localhost"
      port: 5432

---
# 场景4: 配置文件格式 (key=value)
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-config-file-credentials
  namespace: default
  labels:
    test-scenario: "config-file-format"
data:
  app.conf: |
    # 应用基础配置
    app_name=security-gateway
    app_version=2.1.0
    
    # BSS产品凭据
    bss_ak=conf-bss-access-key-789
    bss_sk=conf-bss-secret-key-012
    bss_endpoint=https://bss.example.com
    
    # WAF产品凭据
    waf_ak=conf-waf-access-key-345
    waf_sk=conf-waf-secret-key-678
    
    # 数据库配置
    db_host=localhost
    db_port=5432

---
# 场景5: 连字符格式
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-hyphen-credentials
  namespace: default
  labels:
    test-scenario: "hyphen-format"
data:
  access-key: "hyphen-access-key"
  secret-key: "hyphen-secret-key"
  service-name: "test-service"

---
# 场景6: 负面测试 - 不包含IAM凭据的ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-no-credentials
  namespace: default
  labels:
    test-scenario: "negative-test"
data:
  app_name: "normal-application"
  database_url: "postgresql://localhost:5432/app"
  redis_url: "redis://localhost:6379"
  log_level: "INFO"
  feature_flags: "feature1=true,feature2=false"
