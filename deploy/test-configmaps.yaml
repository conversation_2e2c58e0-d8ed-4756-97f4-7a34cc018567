# 真实场景测试ConfigMaps - 验证IAM Operator的AKSK解析功能

---
# 场景1: 直接键值对格式 (测试用例)
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-direct-credentials
  namespace: default
  labels:
    test-scenario: "direct-key-value"
data:
  access_key: "test-access-key-123"
  secret_key: "test-secret-key-456"
  app_name: "test-application"

---
# 场景2: PHP配置文件格式 (生产环境最常见)
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-php-credentials
  namespace: default
  labels:
    test-scenario: "php-config-file"
data:
  config.php: |
    <?php
    // BSS 产品配置
    $bss_ak = 'f90daa8e8af94f6bb2da26c380e0a9cb';
    $bss_sk = 'd90b6603d4414151a0dac95409dcd815';
    $bss_passwd = 'TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5';
    
    // WAF 产品配置
    $waf_ak = 'waf123456789abcdef';
    $waf_sk = 'waf987654321fedcba';
    ?>
  app_config: "database_url=postgresql://localhost:5432/app"

---
# 场景3: YAML嵌套格式 (结构化配置)
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-yaml-credentials
  namespace: default
  labels:
    test-scenario: "yaml-nested-config"
data:
  application.yaml: |
    app:
      name: "security-service"
      version: "1.0.0"
    
    iam:
      bss:
        ak: "yaml-bss-access-key"
        sk: "yaml-bss-secret-key"
        password: "yaml-bss-password"
      waf:
        ak: "yaml-waf-access-key"
        sk: "yaml-waf-secret-key"
    
    database:
      host: "localhost"
      port: 5432

---
# 场景4: 配置文件格式 (key=value)
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-config-file-credentials
  namespace: default
  labels:
    test-scenario: "config-file-format"
data:
  app.conf: |
    # 应用基础配置
    app_name=security-gateway
    app_version=2.1.0
    
    # BSS产品凭据
    bss_ak=conf-bss-access-key-789
    bss_sk=conf-bss-secret-key-012
    bss_endpoint=https://bss.example.com
    
    # WAF产品凭据
    waf_ak=conf-waf-access-key-345
    waf_sk=conf-waf-secret-key-678
    
    # 数据库配置
    db_host=localhost
    db_port=5432

---
# 场景5: 混合格式 (多种格式混合)
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-mixed-credentials
  namespace: default
  labels:
    test-scenario: "mixed-formats"
data:
  # 直接键值对
  access-key: "mixed-direct-ak"
  secret-key: "mixed-direct-sk"
  
  # PHP配置
  legacy.php: |
    <?php
    $legacy_ak = 'mixed-php-ak';
    $legacy_sk = 'mixed-php-sk';
    ?>
  
  # 配置文件
  service.conf: |
    service_ak=mixed-conf-ak
    service_sk=mixed-conf-sk

---
# 场景6: 负面测试 - 不包含IAM凭据的ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-no-credentials
  namespace: default
  labels:
    test-scenario: "negative-test"
data:
  app_name: "normal-application"
  database_url: "postgresql://localhost:5432/app"
  redis_url: "redis://localhost:6379"
  log_level: "INFO"
  feature_flags: "feature1=true,feature2=false"

---
# 场景7: 边界测试 - 包含ak/sk但不是凭据
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-false-positive
  namespace: default
  labels:
    test-scenario: "boundary-test"
data:
  readme.txt: |
    This application requires ak and sk configuration.
    Please set your access_key and secret_key in the environment.
    
    Example:
    - Make sure your ak is valid
    - Verify your sk is correct
  
  help_text: "Contact support if you have issues with ak/sk setup"

---
# 场景8: 真实生产格式 - 基于实际ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-production-like
  namespace: console
  labels:
    test-scenario: "production-like"
    app: "iam-test"
data:
  config.php: |
    <?php
    // 基于真实生产环境的格式
    $bss_ak = 'prod-like-bss-access-key-123456';
    $bss_sk = 'prod-like-bss-secret-key-789012';
    $bss_passwd = 'prod-like-bss-password-345678';
    $bss_uid = 'prod-like-bss-user-id-901234';
    
    // 数据库配置
    $db_host = 'mysql.console.svc.cluster.local';
    $db_port = 3306;
    $db_name = 'iam_production';
    ?>
  
  application.properties: |
    # Spring Boot配置
    spring.application.name=iam-service
    spring.profiles.active=production
    
    # IAM配置
    iam.bss.ak=prop-bss-access-key
    iam.bss.sk=prop-bss-secret-key
    iam.endpoint=http://iam-manage-xian.console.svc.cluster.local:8468
