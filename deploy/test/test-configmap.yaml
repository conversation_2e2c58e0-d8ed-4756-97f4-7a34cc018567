apiVersion: v1
kind: ConfigMap
metadata:
  name: test-aksk-config
  namespace: iam-operator-test-v2
  labels:
    app: test-application
    managed-by: iam-operator
  annotations:
    iam-operator.example.com/managed: "true"
    iam-operator.example.com/user-id: "test-user-123"
    description: "Test ConfigMap for AKSK parsing validation"
data:
  # 应用基本信息
  app-name: "test-application"
  environment: "test"
  
  # AKSK字段 - 这些应该被operator填充
  access-key: ""
  secret-key: ""
  
  # 其他配置保持不变
  database-url: "postgresql://localhost:5432/testdb"
  redis-url: "redis://localhost:6379"
  log-level: "INFO"
