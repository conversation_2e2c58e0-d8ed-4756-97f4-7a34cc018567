# IAM Operator 测试部署影响评估

## 🎯 测试目标
- 验证ConfigMap AKSK解析功能
- 验证Kubernetes服务发现机制
- 验证IAM API调用流程

## 📊 对dev7 K8s环境的影响分析

### ✅ 无影响的方面
- **现有工作负载**：不影响任何现有的Pod、Service、Deployment
- **现有配置**：不修改任何现有的ConfigMap、Secret
- **现有网络**：不修改网络策略或Service配置
- **现有存储**：不使用或修改任何持久化存储

### ⚠️ 有限影响的方面

#### 1. 集群级别资源
**影响**：
- 创建 `ClusterRole: iam-operator-test`
- 创建 `ClusterRoleBinding: iam-operator-test`

**权限范围**：
```yaml
# 只读权限，限制范围
- resources: ["services"]
  verbs: ["get", "list"]
  resourceNames: ["iam-manage-xian", "iam-openapi"]
- resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
  namespaces: ["iam-operator-test"]
```

**缓解措施**：
- 最小权限原则
- 测试后立即清理
- 明确的资源命名（包含test标识）

#### 2. 跨命名空间访问
**影响**：
- 读取 `console` 命名空间的特定Services
- 不修改任何资源，只读取

**访问范围**：
- `console/iam-manage-xian` Service
- `console/iam-openapi` Service

**缓解措施**：
- 只读操作，无修改风险
- 限制具体的资源名称

#### 3. 网络流量
**影响**：
- 测试Pod到console命名空间Services的网络请求
- 可能的IAM API认证请求

**流量特征**：
- 低频率（测试期间）
- 小数据量
- 短时间（< 10分钟）

#### 4. 资源占用
**影响**：
- 1个测试Pod
- 少量CPU/内存资源

**资源限制**：
```yaml
resources:
  requests:
    memory: "128Mi"
    cpu: "100m"
  limits:
    memory: "256Mi"
    cpu: "200m"
```

## 🛡️ 安全保障措施

### 1. 命名空间隔离
```yaml
namespace: iam-operator-test
```
- 所有测试资源在独立命名空间
- 不与生产资源混合

### 2. 资源标识
```yaml
labels:
  app: iam-operator
  environment: test
annotations:
  description: "Test deployment - safe to delete"
```
- 明确标识测试资源
- 便于识别和清理

### 3. 权限限制
- 最小必要权限
- 限制资源名称和命名空间
- 只读操作为主

### 4. 测试模式
```yaml
test-mode: "true"
dry-run: "true"
mock-mode: "true"  # 可选：避免真实API调用
```

## 🧹 清理机制

### 自动清理
```bash
./scripts/test-deploy.sh cleanup
```

### 清理内容
- 删除 `iam-operator-test` 命名空间（包含所有资源）
- 删除 `ClusterRole: iam-operator-test`
- 删除 `ClusterRoleBinding: iam-operator-test`

### 验证清理
```bash
kubectl get namespace iam-operator-test  # 应该不存在
kubectl get clusterrole iam-operator-test  # 应该不存在
kubectl get clusterrolebinding iam-operator-test  # 应该不存在
```

## 📋 部署选项对比

| 选项 | 安全性 | 功能完整性 | 对环境影响 |
|------|--------|------------|------------|
| **标准模式** | 🟡 中等 | 🟢 完整 | 🟡 有限影响 |
| **Mock模式** | 🟢 高 | 🟡 部分 | 🟢 最小影响 |
| **只读模式** | 🟢 高 | 🟡 部分 | 🟢 最小影响 |

## 💡 推荐方案

### 方案1：渐进式测试（推荐）
1. **第一阶段**：Mock模式 - 验证服务发现
2. **第二阶段**：只读模式 - 验证ConfigMap解析
3. **第三阶段**：标准模式 - 完整功能测试

### 方案2：最小影响测试
- 使用 `safer-rbac.yaml`
- 使用 `mock-configmap.yaml`
- 避免真实IAM API调用

## 🚨 风险评估总结

**总体风险等级：🟡 低-中等**

**主要风险**：
1. 集群级别权限创建
2. 跨命名空间资源访问
3. 可能的IAM API调用

**风险缓解**：
1. 最小权限原则
2. 完整的清理机制
3. 测试模式配置
4. 明确的资源标识

## ✅ 部署建议

**可以安全部署，但建议**：
1. 在非业务高峰期进行
2. 提前通知相关团队
3. 准备回滚方案
4. 测试完立即清理
5. 监控测试过程

**部署前检查**：
```bash
# 确认测试命名空间不存在
kubectl get namespace iam-operator-test

# 确认没有同名的集群资源
kubectl get clusterrole iam-operator-test
kubectl get clusterrolebinding iam-operator-test
```
