---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: iam-operator-test
  namespace: iam-operator-test
  labels:
    app: iam-operator
    environment: test
  annotations:
    description: "Test service account for IAM operator - safer version"

---
# 更安全的ClusterRole - 限制访问范围
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: iam-operator-test-safer
  labels:
    app: iam-operator
    environment: test
  annotations:
    description: "Safer test cluster role - limited scope"
rules:
# 只读取console命名空间的Services (用于服务发现)
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list"]
  resourceNames: ["iam-manage-xian", "iam-openapi"]  # 限制具体服务名
  
# 只读取测试命名空间的ConfigMaps
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
  namespaces: ["iam-operator-test"]  # 限制命名空间

# 只读取测试命名空间的Secrets
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]
  namespaces: ["iam-operator-test"]  # 限制命名空间

# 创建Events (仅在测试命名空间)
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
  namespaces: ["iam-operator-test"]  # 限制命名空间

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: iam-operator-test-safer
  labels:
    app: iam-operator
    environment: test
  annotations:
    description: "Safer test cluster role binding"
subjects:
- kind: ServiceAccount
  name: iam-operator-test
  namespace: iam-operator-test
roleRef:
  kind: ClusterRole
  name: iam-operator-test-safer
  apiGroup: rbac.authorization.k8s.io

---
# 额外的Role用于命名空间内操作
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: iam-operator-test-local
  namespace: iam-operator-test
  labels:
    app: iam-operator
    environment: test
rules:
# 在测试命名空间内的完整权限
- apiGroups: [""]
  resources: ["configmaps", "secrets", "events"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: iam-operator-test-local
  namespace: iam-operator-test
  labels:
    app: iam-operator
    environment: test
subjects:
- kind: ServiceAccount
  name: iam-operator-test
  namespace: iam-operator-test
roleRef:
  kind: Role
  name: iam-operator-test-local
  apiGroup: rbac.authorization.k8s.io
