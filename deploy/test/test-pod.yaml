apiVersion: v1
kind: Pod
metadata:
  name: iam-test-pod
  namespace: iam-operator-test-v2
  labels:
    app: iam-operator
    environment: test
spec:
  restartPolicy: Never
  containers:
  - name: test-container
    image: python:3.9-slim
    command: ["/bin/bash"]
    args: ["-c", "sleep 3600"]
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 200m
        memory: 256Mi
