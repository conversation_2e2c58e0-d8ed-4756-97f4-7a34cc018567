apiVersion: v1
kind: ConfigMap
metadata:
  name: iam-operator-test-config
  namespace: iam-operator-test
  labels:
    app: iam-operator
    environment: test
  annotations:
    description: "Test configuration for IAM operator with service discovery enabled"
data:
  # 基础配置
  log-level: "INFO"
  metrics-port: "8080"
  
  # IAM API 配置 (fallback)
  iam-api-url: "http://iam-manage-xian.console.svc.cluster.local:8468"
  iam-api-timeout: "30"
  
  # IAM 服务发现配置 (启用)
  iam-service-discovery-enabled: "true"
  iam-service-namespace: "console"
  iam-service-name: "iam-manage-xian"
  
  # IAM 认证配置 (基于实际验证)
  iam-domain: "Default"
  iam-scope-domain: "default"
  
  # 测试模式配置
  test-mode: "true"
  dry-run: "true"  # 只读模式，不实际修改资源
