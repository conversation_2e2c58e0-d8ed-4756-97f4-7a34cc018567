apiVersion: batch/v1
kind: Job
metadata:
  name: iam-operator-test-job
  namespace: iam-operator-test
  labels:
    app: iam-operator
    environment: test
  annotations:
    description: "Test job for IAM operator validation - no custom image needed"
spec:
  template:
    metadata:
      labels:
        app: iam-operator
        environment: test
    spec:
      serviceAccountName: iam-operator-test
      restartPolicy: Never
      containers:
      - name: iam-test
        image: python:3.9-slim  # 使用官方Python镜像
        command: ["/bin/bash"]
        args:
          - -c
          - |
            echo "🚀 开始IAM Operator测试"
            
            # 安装依赖
            pip install requests kubernetes
            
            # 创建测试脚本
            cat > /tmp/test_service_discovery.py << 'EOF'
            import requests
            import json
            import os
            from kubernetes import client, config as k8s_config
            
            def test_k8s_connection():
                print("🔗 测试Kubernetes连接...")
                try:
                    k8s_config.load_incluster_config()
                    v1 = client.CoreV1Api()
                    namespaces = v1.list_namespace()
                    print(f"✅ 成功连接，发现 {len(namespaces.items)} 个命名空间")
                    return True
                except Exception as e:
                    print(f"❌ 连接失败: {e}")
                    return False
            
            def test_service_discovery():
                print("🔍 测试服务发现...")
                try:
                    v1 = client.CoreV1Api()
                    
                    # 查找IAM服务
                    services = [
                        ("console", "iam-manage-xian"),
                        ("console", "iam-openapi")
                    ]
                    
                    discovered = []
                    for namespace, service_name in services:
                        try:
                            service = v1.read_namespaced_service(
                                name=service_name,
                                namespace=namespace
                            )
                            
                            cluster_ip = service.spec.cluster_ip
                            port = service.spec.ports[0].port
                            endpoint = f"http://{cluster_ip}:{port}/v3"
                            
                            discovered.append({
                                'name': service_name,
                                'namespace': namespace,
                                'endpoint': endpoint,
                                'cluster_ip': cluster_ip,
                                'port': port
                            })
                            
                            print(f"✅ 发现服务: {service_name} -> {endpoint}")
                            
                        except Exception as e:
                            print(f"❌ 服务 {namespace}/{service_name} 不存在: {e}")
                    
                    return discovered
                    
                except Exception as e:
                    print(f"❌ 服务发现失败: {e}")
                    return []
            
            def test_configmap_parsing():
                print("📋 测试ConfigMap解析...")
                try:
                    v1 = client.CoreV1Api()
                    
                    # 读取测试ConfigMap
                    cm = v1.read_namespaced_config_map(
                        name="test-aksk-config",
                        namespace="iam-operator-test"
                    )
                    
                    print("✅ 成功读取测试ConfigMap")
                    print(f"   数据键: {list(cm.data.keys())}")
                    
                    # 检查AKSK字段
                    aksk_fields = ['access-key', 'secret-key']
                    for field in aksk_fields:
                        if field in cm.data:
                            print(f"   {field}: {'已设置' if cm.data[field] else '空值'}")
                        else:
                            print(f"   {field}: 不存在")
                    
                    return True
                    
                except Exception as e:
                    print(f"❌ ConfigMap解析失败: {e}")
                    return False
            
            def main():
                print("=" * 50)
                print("🧪 IAM Operator 功能测试")
                print("=" * 50)
                
                results = {
                    'k8s_connection': test_k8s_connection(),
                    'service_discovery': test_service_discovery(),
                    'configmap_parsing': test_configmap_parsing()
                }
                
                print("\n" + "=" * 50)
                print("📊 测试结果总结")
                print("=" * 50)
                
                for test, result in results.items():
                    status = "✅ 通过" if result else "❌ 失败"
                    print(f"{test}: {status}")
                
                # 保存结果
                with open('/tmp/test_results.json', 'w') as f:
                    json.dump(results, f, indent=2)
                
                print(f"\n📄 详细结果已保存到 /tmp/test_results.json")
                
                return all(results.values())
            
            if __name__ == '__main__':
                success = main()
                exit(0 if success else 1)
            EOF
            
            # 运行测试
            python /tmp/test_service_discovery.py
            
            # 显示结果
            echo "🎯 测试完成"
            if [ -f /tmp/test_results.json ]; then
                echo "📋 测试结果:"
                cat /tmp/test_results.json
            fi
            
            # 保持容器运行一段时间以便查看日志
            echo "⏰ 等待60秒后结束..."
            sleep 60
        env:
        # 从ConfigMap读取配置
        - name: IAM_SERVICE_DISCOVERY_ENABLED
          value: "true"
        - name: IAM_SERVICE_NAMESPACE
          value: "console"
        - name: IAM_SERVICE_NAME
          value: "iam-manage-xian"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
  backoffLimit: 1
