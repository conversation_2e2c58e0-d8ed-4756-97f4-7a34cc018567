apiVersion: apps/v1
kind: Deployment
metadata:
  name: iam-operator-test
  namespace: iam-operator-test
  labels:
    app: iam-operator
    environment: test
  annotations:
    description: "Test deployment for IAM operator validation"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: iam-operator
      environment: test
  template:
    metadata:
      labels:
        app: iam-operator
        environment: test
      annotations:
        description: "IAM operator test pod"
    spec:
      serviceAccountName: iam-operator-test
      containers:
      - name: iam-operator
        image: iam-operator:latest  # 需要构建镜像
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
          name: metrics
          protocol: TCP
        env:
        # 从ConfigMap读取配置
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: log-level
        - name: METRICS_PORT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: metrics-port
        - name: IAM_API_URL
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: iam-api-url
        - name: IAM_API_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: iam-api-timeout
        - name: IAM_SERVICE_DISCOVERY_ENABLED
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: iam-service-discovery-enabled
        - name: IAM_SERVICE_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: iam-service-namespace
        - name: IAM_SERVICE_NAME
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: iam-service-name
        - name: IAM_DOMAIN
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: iam-domain
        - name: IAM_SCOPE_DOMAIN
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: iam-scope-domain
        - name: TEST_MODE
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: test-mode
        - name: DRY_RUN
          valueFrom:
            configMapKeyRef:
              name: iam-operator-test-config
              key: dry-run
        # 从Secret读取认证凭据
        - name: IAM_USERNAME
          valueFrom:
            secretKeyRef:
              name: iam-operator-test-credentials
              key: iam-username
        - name: IAM_PASSWORD
          valueFrom:
            secretKeyRef:
              name: iam-operator-test-credentials
              key: iam-password
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config
          mountPath: /etc/iam-operator
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: iam-operator-test-config
      restartPolicy: Always
