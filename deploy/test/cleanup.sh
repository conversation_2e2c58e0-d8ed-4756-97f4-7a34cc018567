#!/bin/bash
# IAM Operator 测试环境清理脚本

set -e

echo "🧹 开始清理IAM Operator测试环境..."

# 删除测试命名空间（会级联删除所有命名空间内资源）
echo "1. 删除测试命名空间..."
kubectl delete namespace iam-operator-test-v2 --ignore-not-found=true

# 删除集群级别资源
echo "2. 删除集群级别资源..."
kubectl delete clusterrole iam-operator-test --ignore-not-found=true
kubectl delete clusterrolebinding iam-operator-test --ignore-not-found=true

# 验证清理结果
echo "3. 验证清理结果..."
echo "   检查命名空间..."
if kubectl get namespace iam-operator-test-v2 2>/dev/null; then
    echo "   ⚠️  命名空间仍存在，可能正在删除中..."
else
    echo "   ✅ 命名空间已删除"
fi

echo "   检查集群资源..."
if kubectl get clusterrole iam-operator-test 2>/dev/null; then
    echo "   ⚠️  ClusterRole仍存在"
else
    echo "   ✅ ClusterRole已删除"
fi

if kubectl get clusterrolebinding iam-operator-test 2>/dev/null; then
    echo "   ⚠️  ClusterRoleBinding仍存在"
else
    echo "   ✅ ClusterRoleBinding已删除"
fi

echo ""
echo "🎉 IAM Operator测试环境清理完成！"
echo ""
echo "💡 如果命名空间显示'Terminating'状态，请等待几分钟后自动完成删除"
