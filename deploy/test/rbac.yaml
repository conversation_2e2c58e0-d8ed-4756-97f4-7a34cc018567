---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: iam-operator-test
  namespace: iam-operator-test
  labels:
    app: iam-operator
    environment: test
  annotations:
    description: "Test service account for IAM operator"

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: iam-operator-test
  labels:
    app: iam-operator
    environment: test
  annotations:
    description: "Test cluster role for IAM operator - minimal permissions"
rules:
# 读取ConfigMaps (用于解析AKSK)
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]

# 读取Services (用于服务发现)
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list"]

# 读取Secrets (如果需要)
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]

# 创建Events (用于日志记录)
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]

# CRD相关权限 (如果使用CRD)
- apiGroups: ["iam.example.com"]
  resources: ["iamserviceaccounts"]
  verbs: ["get", "list", "watch", "update", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: iam-operator-test
  labels:
    app: iam-operator
    environment: test
  annotations:
    description: "Test cluster role binding for IAM operator"
subjects:
- kind: ServiceAccount
  name: iam-operator-test
  namespace: iam-operator-test
roleRef:
  kind: ClusterRole
  name: iam-operator-test
  apiGroup: rbac.authorization.k8s.io
