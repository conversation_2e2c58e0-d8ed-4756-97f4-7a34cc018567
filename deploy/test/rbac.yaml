---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: iam-operator-test
  namespace: iam-operator-test-v2
  labels:
    app: iam-operator
    environment: test

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: iam-operator-test
  labels:
    app: iam-operator
    environment: test
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: iam-operator-test
  labels:
    app: iam-operator
    environment: test
subjects:
- kind: ServiceAccount
  name: iam-operator-test
  namespace: iam-operator-test-v2
roleRef:
  kind: ClusterRole
  name: iam-operator-test
  apiGroup: rbac.authorization.k8s.io
