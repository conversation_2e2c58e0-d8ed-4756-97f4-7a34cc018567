---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: iam-operator-test
  namespace: iam-operator-test-v2
  labels:
    app: iam-operator
    environment: test

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: iam-operator-test
  labels:
    app: iam-operator
    environment: test
rules:
# 读取ConfigMaps (用于解析AKSK)
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch", "update", "patch"]

# 读取Services (用于服务发现)
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list"]

# 读取Secrets (如果需要)
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]

# 创建Events (用于日志记录)
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: iam-operator-test
  labels:
    app: iam-operator
    environment: test
subjects:
- kind: ServiceAccount
  name: iam-operator-test
  namespace: iam-operator-test-v2
roleRef:
  kind: ClusterRole
  name: iam-operator-test
  apiGroup: rbac.authorization.k8s.io
