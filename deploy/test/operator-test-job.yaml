apiVersion: batch/v1
kind: Job
metadata:
  name: iam-operator-test-job
  namespace: iam-operator-test-v2
  labels:
    app: iam-operator
    environment: test
  annotations:
    description: "One-time test job for IAM operator functionality"
spec:
  template:
    metadata:
      labels:
        app: iam-operator
        environment: test
    spec:
      serviceAccountName: iam-operator-test
      restartPolicy: Never
      containers:
      - name: iam-operator-test
        image: registry.dev7.abcstackint.com:5000/abc-stack/hawk-python:20250521_1747797770085
        command: ["/bin/bash"]
        args:
          - -c
          - |
            echo "🚀 IAM Operator 功能测试开始"
            echo "时间: $(date)"
            echo "主机: $(hostname)"
            echo "命名空间: iam-operator-test-v2"
            echo ""
            
            # 安装依赖
            echo "📦 安装Python依赖..."
            pip install requests kubernetes --quiet
            
            # 运行完整测试
            echo "🧪 开始功能测试..."
            cd /app
            python3 -c "
            from kubernetes import client, config
            import requests
            import json
            import sys
            
            print('=' * 60)
            print('🎯 IAM Operator 功能验证测试')
            print('=' * 60)
            
            results = {}
            
            # 1. 测试K8s连接
            print('\n1️⃣  测试Kubernetes连接...')
            try:
                config.load_incluster_config()
                v1 = client.CoreV1Api()
                namespaces = v1.list_namespace()
                print(f'✅ Kubernetes连接成功，发现 {len(namespaces.items)} 个命名空间')
                results['k8s_connection'] = True
            except Exception as e:
                print(f'❌ Kubernetes连接失败: {e}')
                results['k8s_connection'] = False
                sys.exit(1)
            
            # 2. 测试服务发现
            print('\n2️⃣  测试IAM服务发现...')
            discovered_services = []
            services_to_test = [
                ('console', 'iam-manage-xian'),
                ('console', 'iam-openapi')
            ]
            
            for namespace, service_name in services_to_test:
                try:
                    service = v1.read_namespaced_service(service_name, namespace)
                    cluster_ip = service.spec.cluster_ip
                    port = service.spec.ports[0].port
                    endpoint = f'http://{cluster_ip}:{port}/v3'
                    
                    discovered_services.append({
                        'name': service_name,
                        'namespace': namespace,
                        'endpoint': endpoint,
                        'cluster_ip': cluster_ip,
                        'port': port,
                        'type': service.spec.type
                    })
                    
                    print(f'✅ 发现服务 {service_name}: {endpoint}')
                    
                except Exception as e:
                    print(f'❌ 服务 {namespace}/{service_name} 发现失败: {e}')
            
            results['service_discovery'] = len(discovered_services) > 0
            results['discovered_services'] = discovered_services
            
            # 3. 测试ConfigMap读取和解析
            print('\n3️⃣  测试ConfigMap AKSK解析...')
            try:
                cm = v1.read_namespaced_config_map('test-aksk-config', 'iam-operator-test-v2')
                print(f'✅ ConfigMap读取成功')
                print(f'   数据键: {list(cm.data.keys())}')
                
                # 检查AKSK字段
                aksk_fields = ['access-key', 'secret-key']
                for field in aksk_fields:
                    if field in cm.data:
                        value = cm.data[field]
                        status = '已设置' if value else '空值'
                        print(f'   {field}: {status}')
                    else:
                        print(f'   {field}: 不存在')
                
                results['configmap_parsing'] = True
                
            except Exception as e:
                print(f'❌ ConfigMap读取失败: {e}')
                results['configmap_parsing'] = False
            
            # 4. 总结测试结果
            print('\n' + '=' * 60)
            print('📊 测试结果总结')
            print('=' * 60)
            
            success_count = sum([
                results['k8s_connection'],
                results['service_discovery'],
                results['configmap_parsing']
            ])
            
            print(f'✅ 核心功能测试: {success_count}/3 通过')
            print(f'🔍 发现的IAM服务: {len(discovered_services)} 个')
            
            # 显示发现的服务详情
            if discovered_services:
                print('\n🎯 发现的IAM服务详情:')
                for service in discovered_services:
                    print(f'   - {service[\"name\"]}: {service[\"endpoint\"]}')
            
            # 保存详细结果
            with open('/tmp/test_results.json', 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f'\n📄 详细结果已保存到 /tmp/test_results.json')
            
            if success_count >= 2:
                print('\n🎉 IAM Operator 核心功能验证成功！')
                print('✅ 服务发现机制正常工作')
                print('✅ ConfigMap解析功能正常')
                print('\n💡 下一步可以部署完整的operator进行端到端测试')
                sys.exit(0)
            else:
                print('\n⚠️  部分功能需要进一步检查')
                sys.exit(1)
            "
            
            echo ""
            echo "🏁 IAM Operator测试任务完成"
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: code
          mountPath: /app
        env:
        - name: IAM_SERVICE_DISCOVERY_ENABLED
          value: "true"
        - name: IAM_SERVICE_NAMESPACE
          value: "console"
        - name: IAM_SERVICE_NAME
          value: "iam-manage-xian"
      volumes:
      - name: code
        configMap:
          name: iam-operator-code
  backoffLimit: 1
