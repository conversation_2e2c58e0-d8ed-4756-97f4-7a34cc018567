apiVersion: v1
kind: ConfigMap
metadata:
  name: iam-operator-test-config-mock
  namespace: iam-operator-test
  labels:
    app: iam-operator
    environment: test
    mode: mock
  annotations:
    description: "Mock configuration - no real IAM API calls"
data:
  # 基础配置
  log-level: "DEBUG"  # 更详细的日志
  metrics-port: "8080"
  
  # IAM API 配置 (fallback)
  iam-api-url: "http://iam-manage-xian.console.svc.cluster.local:8468"
  iam-api-timeout: "30"
  
  # IAM 服务发现配置 (启用)
  iam-service-discovery-enabled: "true"
  iam-service-namespace: "console"
  iam-service-name: "iam-manage-xian"
  
  # IAM 认证配置
  iam-domain: "Default"
  iam-scope-domain: "default"
  
  # 测试模式配置 - 更安全
  test-mode: "true"
  dry-run: "true"
  mock-mode: "true"  # 启用模拟模式，不调用真实API
  
  # 模拟响应配置
  mock-iam-token: "mock-token-12345"
  mock-iam-endpoint: "http://**************:8468/v3"
