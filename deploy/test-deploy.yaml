apiVersion: v1
kind: Namespace
metadata:
  name: iam-operator-system
  labels:
    control-plane: controller-manager
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: iam-operator-controller-manager
  namespace: iam-operator-system
  labels:
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: iam-operator-manager-role
  labels:
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
rules:
# ConfigMap permissions for credential processing (read-only)
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
# Service permissions for service discovery
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch"]
# Event permissions for logging
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: iam-operator-manager-rolebinding
  labels:
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: iam-operator-manager-role
subjects:
- kind: ServiceAccount
  name: iam-operator-controller-manager
  namespace: iam-operator-system
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: iam-operator-config
  namespace: iam-operator-system
data:
  # IAM Service Discovery Configuration
  IAM_SERVICE_DISCOVERY_ENABLED: "true"
  IAM_SERVICE_NAMESPACE: "console"
  IAM_SERVICE_NAME: "iam-manage-xian"
  
  # IAM API Configuration
  IAM_API_URL_FALLBACK: "http://iam.xian.dev7.abcstackint.com:35357"
  IAM_API_TIMEOUT: "30s"
  IAM_DOMAIN: "default"
  IAM_USERNAME: "proxy"
  IAM_PASSWORD: "proxy"
  
  # Target Namespaces (comma-separated)
  TARGET_NAMESPACES: "default,console,kube-system"
  
  # Retry Configuration
  RETRY_INTERVAL: "5m"
  
  # Log Level
  LOG_LEVEL: "info"
---
apiVersion: v1
kind: Secret
metadata:
  name: iam-operator-secret
  namespace: iam-operator-system
type: Opaque
data:
  # Base64 encoded credentials
  # echo -n "proxy" | base64
  IAM_USERNAME: cHJveHk=
  IAM_PASSWORD: cHJveHk=
---
# Test Job to verify permissions and configuration
apiVersion: batch/v1
kind: Job
metadata:
  name: iam-operator-test
  namespace: iam-operator-system
spec:
  template:
    spec:
      serviceAccountName: iam-operator-controller-manager
      restartPolicy: Never
      containers:
      - name: test
        image: registry.dev7.abcstackint.com:5000/abc-stack/hawk-python:20250521_1747797770085
        command: ["/bin/bash"]
        args:
        - -c
        - |
          echo "=== IAM Operator Permission Test ==="
          echo "1. Testing ConfigMap access..."
          kubectl get configmaps -n default --no-headers | head -3
          kubectl get configmaps -n console --no-headers | head -3
          echo "2. Testing Service access..."
          kubectl get services -n console iam-manage-xian
          echo "3. Testing configuration..."
          echo "IAM_SERVICE_DISCOVERY_ENABLED: $IAM_SERVICE_DISCOVERY_ENABLED"
          echo "IAM_SERVICE_NAMESPACE: $IAM_SERVICE_NAMESPACE"
          echo "IAM_SERVICE_NAME: $IAM_SERVICE_NAME"
          echo "TARGET_NAMESPACES: $TARGET_NAMESPACES"
          echo "4. Testing IAM API connectivity..."
          curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" "$IAM_API_URL_FALLBACK/v3" || echo "IAM API not accessible"
          echo "=== Test completed ==="
          sleep 10
        env:
        - name: IAM_SERVICE_DISCOVERY_ENABLED
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: IAM_SERVICE_DISCOVERY_ENABLED
        - name: IAM_SERVICE_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: IAM_SERVICE_NAMESPACE
        - name: IAM_SERVICE_NAME
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: IAM_SERVICE_NAME
        - name: IAM_API_URL_FALLBACK
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: IAM_API_URL_FALLBACK
        - name: TARGET_NAMESPACES
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: TARGET_NAMESPACES
