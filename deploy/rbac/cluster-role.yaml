apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: iam-operator
  labels:
    app: iam-operator
rules:
# 读取输入资源
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]

- apiGroups: ["iam.example.com"]
  resources: ["iamserviceaccounts"]
  verbs: ["get", "list", "watch"]

# 更新 CRD 状态
- apiGroups: ["iam.example.com"]
  resources: ["iamserviceaccounts/status"]
  verbs: ["get", "update", "patch"]

# 管理状态存储
- apiGroups: [""]
  resources: ["configmaps"]
  resourceNames: ["iam-operator-state"]
  verbs: ["create", "update", "patch"]

# 创建状态 ConfigMap（如果不存在）
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["create"]
  resourceNames: ["iam-operator-state"]

# 创建输出 Secret（可选功能）
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["create", "update", "patch"]

# 事件记录
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]

# 节点信息（用于调试）
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list"]
