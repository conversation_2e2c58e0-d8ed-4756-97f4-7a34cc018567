#!/usr/bin/env python3
"""
使用项目中的 IAM Client 进行测试
"""

import sys
import os

# 添加项目路径到 Python 路径
sys.path.insert(0, 'iam-operator/src')

try:
    from clients.iam_client import IAMClient
    from clients.mock_iam_client import MockIAMClient
    from utils.config import config
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

def test_mock_client():
    """测试 Mock IAM Client"""
    print("=== 测试 Mock IAM Client ===")
    
    try:
        client = MockIAMClient()
        
        # 健康检查
        health = client.health_check()
        print(f"健康检查: {health}")
        
        # 创建服务凭据
        result = client.create_service_credentials(
            "test-service",
            "test-product", 
            {"ak": "test-ak", "sk": "test-sk"}
        )
        print(f"创建凭据: {result}")
        
        # 列出所有凭据
        all_creds = client.list_all_credentials()
        print(f"所有凭据: {all_creds}")
        
        return True
        
    except Exception as e:
        print(f"Mock 测试失败: {e}")
        return False

def test_real_client():
    """测试真实 IAM Client"""
    print("\n=== 测试真实 IAM Client ===")
    
    # 设置环境变量
    os.environ['IAM_API_URL'] = 'http://iam-openapi.console.svc.cluster.local:8480'
    os.environ['IAM_USERNAME'] = input("请输入 IAM 用户名 (默认: test-user): ") or 'test-user'
    os.environ['IAM_PASSWORD'] = input("请输入 IAM 密码 (默认: test-password): ") or 'test-password'
    os.environ['IAM_DOMAIN'] = input("请输入 IAM 域 (默认: default): ") or 'default'
    
    try:
        client = IAMClient()
        
        # 健康检查
        print("1. 健康检查...")
        health = client.health_check()
        print(f"   结果: {health}")
        
        if not health:
            print("❌ IAM 服务不可用")
            return False
        
        # 认证测试
        print("2. 认证测试...")
        try:
            token = client._get_auth_token()
            print(f"   ✅ 认证成功，Token: {token[:20]}...")
        except Exception as e:
            print(f"   ❌ 认证失败: {e}")
            return False
        
        # 用户查询测试
        print("3. 用户查询测试...")
        try:
            user = client.get_user_by_name("test-user", "default")
            print(f"   用户信息: {user}")
        except Exception as e:
            print(f"   用户查询失败: {e}")
        
        # 创建服务凭据测试（谨慎）
        if input("是否测试创建服务凭据？(y/N): ").lower() == 'y':
            print("4. 创建服务凭据测试...")
            try:
                result = client.create_service_credentials(
                    "test-service",
                    "test-product",
                    {"ak": "test-ak", "sk": "test-sk"}
                )
                print(f"   创建结果: {result}")
            except Exception as e:
                print(f"   创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"真实 IAM 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("IAM Client 测试工具")
    print("=" * 40)
    
    # 测试 Mock Client
    mock_success = test_mock_client()
    
    # 询问是否测试真实 Client
    if input("\n是否测试真实 IAM Client？(y/N): ").lower() == 'y':
        real_success = test_real_client()
    else:
        real_success = None
    
    # 总结
    print("\n" + "=" * 40)
    print("测试总结:")
    print(f"Mock Client: {'✅ 成功' if mock_success else '❌ 失败'}")
    if real_success is not None:
        print(f"Real Client: {'✅ 成功' if real_success else '❌ 失败'}")
    
    print("\n开发建议:")
    if mock_success:
        print("- Mock Client 工作正常，可以用于开发和单元测试")
    if real_success:
        print("- 真实 IAM API 连接正常，可以进行集成测试")
    elif real_success is False:
        print("- 真实 IAM API 连接有问题，请检查:")
        print("  1. 网络连接是否正常")
        print("  2. IAM 服务是否运行")
        print("  3. 认证信息是否正确")

if __name__ == "__main__":
    main()
