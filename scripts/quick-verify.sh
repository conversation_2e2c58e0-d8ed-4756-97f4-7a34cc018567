#!/bin/bash

# IAM-Operator 快速验证脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 k8s 连接
check_k8s_connection() {
    log_info "检查 Kubernetes 连接..."
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    local cluster_info=$(kubectl cluster-info | head -1)
    log_success "已连接到集群: $cluster_info"
    
    # 显示当前上下文
    local current_context=$(kubectl config current-context)
    log_info "当前上下文: $current_context"
    
    # 检查权限
    log_info "检查基本权限..."
    if kubectl auth can-i get configmaps &> /dev/null; then
        log_success "有 ConfigMap 读权限"
    else
        log_warning "没有 ConfigMap 读权限"
    fi
    
    if kubectl auth can-i create crd &> /dev/null; then
        log_success "有 CRD 创建权限"
    else
        log_warning "没有 CRD 创建权限"
    fi
}

# 检查现有的 waf-meta 资源
check_existing_resources() {
    log_info "检查现有的 waf-meta 相关资源..."
    
    # 检查 waf-meta ConfigMap
    log_info "查找 waf-meta ConfigMap..."
    local waf_configmaps=$(kubectl get configmap -n security | grep waf-meta || echo "")
    if [ -n "$waf_configmaps" ]; then
        log_success "找到 waf-meta ConfigMap:"
        echo "$waf_configmaps"
        
        # 检查是否包含 IAM 凭据
        local configmap_name=$(echo "$waf_configmaps" | awk '{print $1}' | head -1)
        log_info "检查 ConfigMap $configmap_name 的内容..."
        
        local iam_content=$(kubectl get configmap "$configmap_name" -n security -o yaml | grep -E '\$.*_ak|\$.*_sk' || echo "")
        if [ -n "$iam_content" ]; then
            log_success "发现 IAM 凭据配置!"
            echo "$iam_content" | head -3
        else
            log_warning "未发现 IAM 凭据配置"
        fi
    else
        log_warning "未找到 waf-meta ConfigMap"
    fi
    
    # 检查 waf-meta Pod
    log_info "查找 waf-meta Pod..."
    local waf_pods=$(kubectl get pods -n security | grep waf-meta || echo "")
    if [ -n "$waf_pods" ]; then
        log_success "找到 waf-meta Pod:"
        echo "$waf_pods"
    else
        log_warning "未找到 waf-meta Pod"
    fi
}

# 检查 IAM API 连通性
check_iam_api() {
    log_info "检查 IAM API 连通性..."
    
    # 查找 IAM API 服务
    local iam_service=$(kubectl get svc -n console | grep iam-openapi || echo "")
    if [ -n "$iam_service" ]; then
        log_success "找到 IAM OpenAPI 服务:"
        echo "$iam_service"
        
        # 尝试从集群内访问
        log_info "测试 IAM API 连通性..."
        local test_pod=$(kubectl get pods -n console | grep iam-openapi | awk '{print $1}' | head -1)
        if [ -n "$test_pod" ]; then
            local health_check=$(kubectl exec -n console "$test_pod" -- curl -s -f http://localhost:8480/health 2>/dev/null || echo "failed")
            if [ "$health_check" != "failed" ]; then
                log_success "IAM API 健康检查通过"
            else
                log_warning "IAM API 健康检查失败"
            fi
        fi
    else
        log_warning "未找到 IAM OpenAPI 服务"
    fi
}

# 创建最小测试
create_minimal_test() {
    log_info "创建最小测试资源..."
    
    # 创建测试命名空间
    kubectl create namespace iam-operator-test --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建测试 ConfigMap
    kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: minimal-test-config
  namespace: iam-operator-test
  labels:
    test: iam-operator
data:
  config.php: |
    <?php
    \$test_ak = 'minimal-test-ak-12345';
    \$test_sk = 'minimal-test-sk-67890';
    ?>
EOF

    log_success "最小测试资源创建完成"
    
    # 显示创建的资源
    kubectl get configmap minimal-test-config -n iam-operator-test -o yaml
}

# 清理测试资源
cleanup_test() {
    log_info "清理测试资源..."
    
    kubectl delete configmap minimal-test-config -n iam-operator-test --ignore-not-found=true
    kubectl delete namespace iam-operator-test --ignore-not-found=true
    
    log_success "测试资源清理完成"
}

# 显示下一步建议
show_next_steps() {
    echo ""
    log_info "=== 下一步建议 ==="
    echo ""
    echo "1. 如果要进行本地测试："
    echo "   ./scripts/local-test.sh setup"
    echo "   ./scripts/local-test.sh test"
    echo "   ./scripts/local-test.sh run"
    echo ""
    echo "2. 如果要部署到集群："
    echo "   ./scripts/helm-deploy.sh deploy"
    echo ""
    echo "3. 如果要查看现有 waf-meta 配置："
    echo "   kubectl get configmap -n security -o yaml | grep -A 10 -B 5 '_ak\\|_sk'"
    echo ""
    echo "4. 如果要监控 Operator 日志："
    echo "   kubectl logs -f deployment/iam-operator -n base"
}

# 主函数
main() {
    local command=${1:-"all"}
    
    case $command in
        "k8s")
            check_k8s_connection
            ;;
        "resources")
            check_existing_resources
            ;;
        "iam")
            check_iam_api
            ;;
        "test")
            create_minimal_test
            ;;
        "cleanup")
            cleanup_test
            ;;
        "all")
            log_info "=== IAM-Operator 快速验证 ==="
            echo ""
            check_k8s_connection
            echo ""
            check_existing_resources
            echo ""
            check_iam_api
            echo ""
            create_minimal_test
            echo ""
            show_next_steps
            ;;
        *)
            echo "用法: $0 [k8s|resources|iam|test|cleanup|all]"
            echo ""
            echo "  k8s       - 检查 Kubernetes 连接"
            echo "  resources - 检查现有资源"
            echo "  iam       - 检查 IAM API"
            echo "  test      - 创建测试资源"
            echo "  cleanup   - 清理测试资源"
            echo "  all       - 执行所有检查（默认）"
            ;;
    esac
}

# 执行主函数
main "$@"
