#!/bin/bash

# IAM-Operator 本地测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查本地环境..."
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查 kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    # 检查 k8s 连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 安装依赖
install_dependencies() {
    log_info "安装 Python 依赖..."
    
    cd "$(dirname "$0")/.."
    
    # 创建虚拟环境（如果不存在）
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    pip install -r requirements.txt
    
    log_success "依赖安装完成"
}

# 创建测试 CRD
create_test_crd() {
    log_info "创建测试 CRD..."
    
    kubectl apply -f deploy/crds/iam-service-account-crd.yaml
    
    # 等待 CRD 就绪
    kubectl wait --for condition=established --timeout=60s crd/iamserviceaccounts.iam.example.com
    
    log_success "CRD 创建完成"
}

# 创建测试 RBAC（简化版）
create_test_rbac() {
    log_info "创建测试 RBAC..."
    
    # 创建 ServiceAccount
    kubectl apply -f - <<EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: iam-operator-test
  namespace: default
EOF

    # 创建简化的 ClusterRole（只读权限，用于测试）
    kubectl apply -f - <<EOF
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: iam-operator-test
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["iam.example.com"]
  resources: ["iamserviceaccounts"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["iam.example.com"]
  resources: ["iamserviceaccounts/status"]
  verbs: ["get", "update", "patch"]
EOF

    # 创建 ClusterRoleBinding
    kubectl apply -f - <<EOF
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: iam-operator-test
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: iam-operator-test
subjects:
- kind: ServiceAccount
  name: iam-operator-test
  namespace: default
EOF

    log_success "RBAC 创建完成"
}

# 创建测试 ConfigMap
create_test_configmap() {
    log_info "创建测试 ConfigMap..."
    
    kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-iam-config
  namespace: security
data:
  config.php: |
    <?php
    \$test_ak = 'test-access-key-12345';
    \$test_sk = 'test-secret-key-67890';
    \$test_passwd = 'test-password';
    \$test_uid = 'test-user-id';
    ?>
EOF

    log_success "测试 ConfigMap 创建完成"
}

# 创建测试 CRD 实例
create_test_crd_instance() {
    log_info "创建测试 CRD 实例..."
    
    kubectl apply -f - <<EOF
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: test-service-iam
  namespace: default
spec:
  serviceName: test-service
  products:
    test-product:
      ak: test-access-key-12345
      sk: test-secret-key-67890
      password: test-password
      userId: test-user-id
EOF

    log_success "测试 CRD 实例创建完成"
}

# 运行本地 Operator
run_local_operator() {
    log_info "启动本地 IAM-Operator..."
    
    cd "$(dirname "$0")/.."
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 设置环境变量
    export USE_MOCK_IAM="true"
    export IAM_API_URL="http://iam-openapi.console.svc.cluster.local:8480"
    export LOG_LEVEL="DEBUG"
    export TARGET_NAMESPACES="default,security"
    export STATE_CONFIGMAP_NAME="iam-operator-test-state"
    export STATE_CONFIGMAP_NAMESPACE="default"
    
    log_info "环境变量设置："
    echo "  USE_MOCK_IAM: $USE_MOCK_IAM"
    echo "  IAM_API_URL: $IAM_API_URL"
    echo "  LOG_LEVEL: $LOG_LEVEL"
    echo "  TARGET_NAMESPACES: $TARGET_NAMESPACES"
    
    log_warning "Operator 将在前台运行，按 Ctrl+C 停止"
    sleep 3
    
    # 运行 Operator
    python -m src.main
}

# 清理测试资源
cleanup() {
    log_info "清理测试资源..."
    
    kubectl delete iamserviceaccount test-service-iam --ignore-not-found=true
    kubectl delete configmap test-iam-config -n security --ignore-not-found=true
    kubectl delete clusterrolebinding iam-operator-test --ignore-not-found=true
    kubectl delete clusterrole iam-operator-test --ignore-not-found=true
    kubectl delete serviceaccount iam-operator-test --ignore-not-found=true
    kubectl delete crd iamserviceaccounts.iam.example.com --ignore-not-found=true
    
    log_success "清理完成"
}

# 显示帮助
show_help() {
    echo "IAM-Operator 本地测试脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  setup     - 设置测试环境（安装依赖、创建 CRD、RBAC）"
    echo "  test      - 创建测试资源"
    echo "  run       - 运行本地 Operator"
    echo "  cleanup   - 清理测试资源"
    echo "  all       - 执行完整测试流程"
    echo "  help      - 显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 setup    # 设置环境"
    echo "  $0 test     # 创建测试资源"
    echo "  $0 run      # 运行 Operator（在另一个终端）"
    echo "  $0 cleanup  # 清理资源"
}

# 主函数
main() {
    local command=${1:-"help"}
    
    case $command in
        "setup")
            check_environment
            install_dependencies
            create_test_crd
            create_test_rbac
            log_success "测试环境设置完成！"
            ;;
        "test")
            create_test_configmap
            create_test_crd_instance
            log_success "测试资源创建完成！"
            echo ""
            echo "现在可以在另一个终端运行："
            echo "  $0 run"
            ;;
        "run")
            check_environment
            run_local_operator
            ;;
        "cleanup")
            cleanup
            ;;
        "all")
            check_environment
            install_dependencies
            create_test_crd
            create_test_rbac
            create_test_configmap
            create_test_crd_instance
            log_success "完整测试环境准备完成！"
            echo ""
            echo "现在运行 Operator："
            echo "  $0 run"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
