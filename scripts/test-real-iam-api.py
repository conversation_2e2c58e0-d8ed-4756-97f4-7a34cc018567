#!/usr/bin/env python3
"""
测试真实的IAM API调用 - 基于实际验证的流程
"""
import requests
import json
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_iam_auth_flow():
    """测试完整的IAM认证流程"""
    print("🔐 测试IAM认证流程...")
    
    # 基于你验证的实际端点
    base_url = "http://iam.xian.dev7.abcstackint.com:35357"
    
    # 1. 获取认证Token
    print("\n1️⃣  获取认证Token...")
    
    auth_payload = {
        "auth": {
            "identity": {
                "methods": ["password"],
                "password": {
                    "user": {
                        "domain": {"name": "Default"},
                        "name": "proxy",  # 你验证中使用的用户名
                        "password": "nV5WgQMDdzjBS7PwCP1sqVy5Q0STwAa8"  # 实际密码
                    }
                }
            },
            "scope": {
                "domain": {"id": "default"}
            }
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/v3/auth/tokens",
            json=auth_payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 201:
            token = response.headers.get('X-Subject-Token')
            if token:
                print(f"   ✅ 获取Token成功: {token[:20]}...")
                return token
            else:
                print("   ❌ 响应中没有X-Subject-Token")
                return None
        else:
            print(f"   ❌ 认证失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 认证请求异常: {e}")
        return None


def test_iam_api_call(token):
    """使用Token调用IAM API"""
    print("\n2️⃣  使用Token调用IAM API...")
    
    if not token:
        print("   ❌ 没有有效的Token，跳过API调用")
        return False
    
    base_url = "http://iam.xian.dev7.abcstackint.com:35357"
    
    # 测试几个常用的API端点
    test_endpoints = [
        "/v3/users",  # 列出用户
        "/v3/domains",  # 列出域
        "/v3/projects",  # 列出项目
    ]
    
    headers = {
        'X-Auth-Token': token,  # 或者 X-Subject-Token
        'Content-Type': 'application/json'
    }
    
    success_count = 0
    
    for endpoint in test_endpoints:
        try:
            print(f"\n   测试端点: {endpoint}")
            response = requests.get(
                f"{base_url}{endpoint}",
                headers=headers,
                timeout=10
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 调用成功")
                # 打印部分响应内容
                try:
                    data = response.json()
                    if isinstance(data, dict):
                        for key in list(data.keys())[:3]:  # 只显示前3个key
                            print(f"   响应包含: {key}")
                except:
                    print(f"   响应长度: {len(response.text)} 字符")
                success_count += 1
            else:
                print(f"   ❌ 调用失败: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
    
    print(f"\n   📊 API调用成功率: {success_count}/{len(test_endpoints)}")
    return success_count > 0


def test_service_discovery_vs_direct():
    """对比服务发现和直接调用"""
    print("\n3️⃣  对比服务发现 vs 直接调用...")
    
    # 我们发现的服务端点
    discovered_endpoints = [
        "http://**************:8468/v3",  # iam-manage-xian
        "http://**************:8480/v3",  # iam-openapi
    ]
    
    # 实际验证的端点
    actual_endpoint = "http://iam.xian.dev7.abcstackint.com:35357/v3"
    
    print(f"   🔍 服务发现的端点:")
    for endpoint in discovered_endpoints:
        print(f"     - {endpoint}")
    
    print(f"   🎯 实际验证的端点:")
    print(f"     - {actual_endpoint}")
    
    print(f"\n   💡 分析:")
    print(f"     - 服务发现找到的是内部ClusterIP")
    print(f"     - 实际验证的是外部LoadBalancer域名")
    print(f"     - 两者都指向同一个IAM服务集群")
    print(f"     - 在K8s集群内应该使用ClusterIP")
    print(f"     - 在集群外应该使用LoadBalancer域名")


def test_connectivity_to_discovered_services():
    """测试到发现的服务的连通性"""
    print("\n4️⃣  测试到发现服务的连通性...")
    
    # 我们发现的服务端点
    services = [
        {"name": "iam-manage-xian", "endpoint": "http://**************:8468"},
        {"name": "iam-openapi", "endpoint": "http://**************:8480"},
    ]
    
    for service in services:
        print(f"\n   测试服务: {service['name']}")
        print(f"   端点: {service['endpoint']}")
        
        # 测试基本连通性
        try:
            response = requests.get(
                f"{service['endpoint']}/health",
                timeout=5
            )
            print(f"   健康检查: HTTP {response.status_code}")
        except Exception as e:
            print(f"   健康检查失败: {e}")
        
        # 测试v3端点
        try:
            response = requests.get(
                f"{service['endpoint']}/v3",
                timeout=5
            )
            print(f"   v3端点: HTTP {response.status_code}")
        except Exception as e:
            print(f"   v3端点失败: {e}")


def main():
    """主测试函数"""
    print("🚀 真实IAM API调用测试")
    print("=" * 50)
    
    # 1. 测试认证流程
    token = test_iam_auth_flow()
    
    # 2. 测试API调用
    api_success = test_iam_api_call(token)
    
    # 3. 对比分析
    test_service_discovery_vs_direct()
    
    # 4. 测试发现的服务连通性
    test_connectivity_to_discovered_services()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    
    if token:
        print("✅ IAM认证流程正常")
    else:
        print("❌ IAM认证流程失败")
    
    if api_success:
        print("✅ IAM API调用正常")
    else:
        print("❌ IAM API调用失败")
    
    print("\n💡 下一步建议:")
    if token and api_success:
        print("1. 验证我们的服务发现找到的是正确的服务")
        print("2. 更新我们的IAM客户端使用正确的认证流程")
        print("3. 测试在K8s集群内的连通性")
    else:
        print("1. 检查网络连通性")
        print("2. 验证认证凭据")
        print("3. 确认IAM服务状态")


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
