#!/bin/bash

# 模拟苍竹部署系统：values.yaml → ConfigMap 的完整流程
# 作者：基于 IAM Operator 项目经验
# 日期：2025-08-01

set -e

echo "🚀 模拟苍竹部署系统：values.yaml → ConfigMap 流程"
echo "=================================================="

# 创建示例 values.yaml
echo "📝 1. 创建示例 values.yaml 文件"
cat > /tmp/example-values.yaml << 'EOF'
# 示例 values.yaml - 模拟程广连维护的蓝图配置
app:
  name: "my-service"
  version: "1.0.0"

# IAM 凭据配置（多种格式）
iam:
  bss:
    ak: "AKIAIOSFODNN7EXAMPLE"
    sk: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
  waf:
    ak: "AKIAI44QH8DHBEXAMPLE"
    sk: "je7MtGbClwBF/2Zp9Utk/h3yCo8nvbEXAMPLEKEY"

# PHP 格式配置
php_config: |
  <?php
  $bss_ak = "AKIAIOSFODNN7EXAMPLE";
  $bss_sk = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY";
  $waf_ak = "AKIAI44QH8DHBEXAMPLE";
  $waf_sk = "je7MtGbClwBF/2Zp9Utk/h3yCo8nvbEXAMPLEKEY";
  ?>

# 配置文件格式
config_file: |
  bss_ak=AKIAIOSFODNN7EXAMPLE
  bss_sk=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
  waf_ak=AKIAI44QH8DHBEXAMPLE
  waf_sk=je7MtGbClwBF/2Zp9Utk/h3yCo8nvbEXAMPLEKEY
EOF

echo "✅ values.yaml 创建完成"
echo ""

# 方法1：直接从 values.yaml 创建 ConfigMap
echo "📦 2. 方法1：直接从 values.yaml 创建 ConfigMap"
echo "命令：kubectl create configmap iam-config --from-file=values.yaml"
kubectl create configmap iam-config-from-file \
  --from-file=values.yaml=/tmp/example-values.yaml \
  --dry-run=client -o yaml > /tmp/configmap-from-file.yaml

echo "✅ ConfigMap YAML 生成完成：/tmp/configmap-from-file.yaml"
echo ""

# 方法2：从键值对创建 ConfigMap（模拟解析后的结果）
echo "📦 3. 方法2：从键值对创建 ConfigMap（模拟苍竹解析后）"
echo "命令：kubectl create configmap --from-literal"
kubectl create configmap iam-credentials \
  --from-literal=access_key="AKIAIOSFODNN7EXAMPLE" \
  --from-literal=secret_key="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY" \
  --from-literal=bss_ak="AKIAIOSFODNN7EXAMPLE" \
  --from-literal=bss_sk="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY" \
  --dry-run=client -o yaml > /tmp/configmap-from-literals.yaml

echo "✅ ConfigMap YAML 生成完成：/tmp/configmap-from-literals.yaml"
echo ""

# 方法3：模拟 Helm 模板渲染
echo "📦 4. 方法3：模拟 Helm 模板渲染"
echo "创建简单的 Helm Chart 模板..."

# 创建临时 Chart 目录
mkdir -p /tmp/helm-chart/templates

# 创建 Chart.yaml
cat > /tmp/helm-chart/Chart.yaml << 'EOF'
apiVersion: v2
name: iam-service
description: IAM Service Helm Chart
version: 0.1.0
appVersion: "1.0"
EOF

# 创建 ConfigMap 模板
cat > /tmp/helm-chart/templates/configmap.yaml << 'EOF'
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.app.name }}-config
  namespace: default
data:
  # 直接键值对格式
  access_key: "{{ .Values.iam.bss.ak }}"
  secret_key: "{{ .Values.iam.bss.sk }}"
  
  # PHP 格式
  php_config.php: |
{{ .Values.php_config | indent 4 }}
  
  # 配置文件格式
  app.conf: |
{{ .Values.config_file | indent 4 }}
  
  # YAML 格式
  iam_config.yaml: |
    iam:
      bss:
        ak: "{{ .Values.iam.bss.ak }}"
        sk: "{{ .Values.iam.bss.sk }}"
      waf:
        ak: "{{ .Values.iam.waf.ak }}"
        sk: "{{ .Values.iam.waf.sk }}"
EOF

# 使用 Helm 渲染模板
echo "命令：helm template iam-service /tmp/helm-chart --values /tmp/example-values.yaml"
if command -v helm &> /dev/null; then
    helm template iam-service /tmp/helm-chart --values /tmp/example-values.yaml > /tmp/helm-rendered.yaml
    echo "✅ Helm 模板渲染完成：/tmp/helm-rendered.yaml"
else
    echo "⚠️  Helm 未安装，跳过模板渲染"
fi
echo ""

# 显示生成的文件
echo "📋 5. 生成的文件预览"
echo "===================="

echo "📄 方法1 - 从文件创建的 ConfigMap："
echo "---"
head -20 /tmp/configmap-from-file.yaml
echo "..."
echo ""

echo "📄 方法2 - 从键值对创建的 ConfigMap："
echo "---"
head -20 /tmp/configmap-from-literals.yaml
echo "..."
echo ""

if [ -f /tmp/helm-rendered.yaml ]; then
    echo "📄 方法3 - Helm 渲染的 ConfigMap："
    echo "---"
    head -30 /tmp/helm-rendered.yaml
    echo "..."
fi
echo ""

# 模拟 IAM Operator 检测
echo "🔍 6. 模拟 IAM Operator 检测过程"
echo "================================="

echo "检测 ConfigMap 中的 IAM 凭据格式："
echo ""

# 检测直接键值对
echo "✅ 检测到直接键值对格式："
echo "   - access_key: AKIAIOSFODNN7EXAMPLE"
echo "   - secret_key: wJalrXUtnFEMI/K7MDENG/..."
echo ""

# 检测 PHP 格式
echo "✅ 检测到 PHP 格式："
echo "   - \$bss_ak = \"AKIAIOSFODNN7EXAMPLE\";"
echo "   - \$bss_sk = \"wJalrXUtnFEMI/K7MDENG/...\";"
echo ""

# 检测配置文件格式
echo "✅ 检测到配置文件格式："
echo "   - bss_ak=AKIAIOSFODNN7EXAMPLE"
echo "   - bss_sk=wJalrXUtnFEMI/K7MDENG/..."
echo ""

# 检测 YAML 格式
echo "✅ 检测到 YAML 嵌套格式："
echo "   - iam.bss.ak: AKIAIOSFODNN7EXAMPLE"
echo "   - iam.bss.sk: wJalrXUtnFEMI/K7MDENG/..."
echo ""

echo "🎯 总结"
echo "======"
echo "1. values.yaml → ConfigMap 的转换可以通过多种方式实现"
echo "2. Helm 是客户端工具，在 Day0/Day1 阶段工作"
echo "3. IAM Operator 在 Day1/Day2 阶段监听 ConfigMap 变化"
echo "4. 支持多种 IAM 凭据格式的自动检测"
echo ""
echo "📁 生成的文件位置："
echo "   - /tmp/example-values.yaml"
echo "   - /tmp/configmap-from-file.yaml"
echo "   - /tmp/configmap-from-literals.yaml"
if [ -f /tmp/helm-rendered.yaml ]; then
    echo "   - /tmp/helm-rendered.yaml"
fi
echo ""
echo "🚀 模拟完成！您可以查看这些文件了解完整的转换过程。"
