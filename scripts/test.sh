#!/bin/bash

# IAM-Operator 测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试 CRD 功能
test_crd() {
    log_info "测试 CRD 功能..."
    
    # 创建测试 CRD 实例
    kubectl apply -f - <<EOF
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: test-service-iam
  namespace: default
spec:
  serviceName: test-service
  products:
    test-product:
      ak: test-access-key-12345
      sk: test-secret-key-67890
      password: test-password
      userId: test-user-id
EOF
    
    # 等待处理
    sleep 5
    
    # 检查状态
    local status=$(kubectl get iamserviceaccount test-service-iam -o jsonpath='{.status.conditions[0].status}' 2>/dev/null || echo "Unknown")
    
    if [ "$status" = "True" ]; then
        log_success "CRD 测试通过"
    else
        log_warning "CRD 状态: $status"
    fi
    
    # 清理测试资源
    kubectl delete iamserviceaccount test-service-iam --ignore-not-found=true
}

# 测试 ConfigMap 功能
test_configmap() {
    log_info "测试 ConfigMap 功能..."
    
    # 创建测试 ConfigMap
    kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-config
  namespace: security
data:
  config.php: |
    <?php
    \$test_ak = 'test-access-key-12345';
    \$test_sk = 'test-secret-key-67890';
    \$test_passwd = 'test-password';
    \$test_uid = 'test-user-id';
    ?>
EOF
    
    # 等待处理
    sleep 5
    
    # 检查日志
    local pod_name=$(kubectl get pods -n base -l app=iam-operator -o jsonpath='{.items[0].metadata.name}')
    local log_output=$(kubectl logs -n base "$pod_name" --tail=20 | grep -i "test-config" || echo "")
    
    if [ -n "$log_output" ]; then
        log_success "ConfigMap 测试通过"
    else
        log_warning "ConfigMap 可能未被处理"
    fi
    
    # 清理测试资源
    kubectl delete configmap test-config -n security --ignore-not-found=true
}

# 测试健康检查
test_health() {
    log_info "测试健康检查..."
    
    local pod_name=$(kubectl get pods -n base -l app=iam-operator -o jsonpath='{.items[0].metadata.name}')
    
    # 测试存活探针
    if kubectl exec -n base "$pod_name" -- curl -f http://localhost:8081/healthz &> /dev/null; then
        log_success "存活探针测试通过"
    else
        log_error "存活探针测试失败"
        return 1
    fi
    
    # 测试就绪探针
    if kubectl exec -n base "$pod_name" -- curl -f http://localhost:8081/ready &> /dev/null; then
        log_success "就绪探针测试通过"
    else
        log_warning "就绪探针测试失败（可能是 IAM API 不可用）"
    fi
}

# 测试指标
test_metrics() {
    log_info "测试指标..."
    
    local pod_name=$(kubectl get pods -n base -l app=iam-operator -o jsonpath='{.items[0].metadata.name}')
    
    # 获取指标
    local metrics=$(kubectl exec -n base "$pod_name" -- curl -s http://localhost:8080/metrics | grep "iam_" | head -5)
    
    if [ -n "$metrics" ]; then
        log_success "指标测试通过"
        echo "$metrics"
    else
        log_warning "未找到 IAM 相关指标"
    fi
}

# 查看日志
view_logs() {
    log_info "查看 Operator 日志..."
    
    kubectl logs -n base -l app=iam-operator --tail=50 -f
}

# 运行所有测试
run_all_tests() {
    log_info "运行所有测试..."
    
    # 检查 Operator 是否运行
    local pod_status=$(kubectl get pods -n base -l app=iam-operator -o jsonpath='{.items[0].status.phase}' 2>/dev/null || echo "NotFound")
    
    if [ "$pod_status" != "Running" ]; then
        log_error "Operator 未运行，状态: $pod_status"
        exit 1
    fi
    
    test_health
    test_metrics
    test_crd
    test_configmap
    
    log_success "所有测试完成"
}

# 显示状态
show_status() {
    log_info "显示 IAM-Operator 状态..."
    
    echo ""
    echo "=== Pods ==="
    kubectl get pods -n base -l app=iam-operator
    
    echo ""
    echo "=== Services ==="
    kubectl get svc -n base -l app=iam-operator
    
    echo ""
    echo "=== CRDs ==="
    kubectl get crd iamserviceaccounts.iam.example.com
    
    echo ""
    echo "=== IAMServiceAccounts ==="
    kubectl get iamserviceaccounts -A
    
    echo ""
    echo "=== Recent Events ==="
    kubectl get events -n base --sort-by='.lastTimestamp' | grep iam-operator | tail -5
}

# 显示帮助
show_help() {
    echo "IAM-Operator 测试脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  test        - 运行所有测试"
    echo "  crd         - 测试 CRD 功能"
    echo "  configmap   - 测试 ConfigMap 功能"
    echo "  health      - 测试健康检查"
    echo "  metrics     - 测试指标"
    echo "  logs        - 查看日志"
    echo "  status      - 显示状态"
    echo "  help        - 显示帮助"
}

# 主函数
main() {
    local command=${1:-"help"}
    
    case $command in
        "test")
            run_all_tests
            ;;
        "crd")
            test_crd
            ;;
        "configmap")
            test_configmap
            ;;
        "health")
            test_health
            ;;
        "metrics")
            test_metrics
            ;;
        "logs")
            view_logs
            ;;
        "status")
            show_status
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
