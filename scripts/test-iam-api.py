#!/usr/bin/env python3
"""
IAM API 连接测试脚本
"""
import os
import sys
import logging

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.clients.iam_client import IAMClient
from src.clients.mock_iam_client import MockIAMClient
from src.utils.exceptions import IAMAPIError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_mock_iam():
    """测试 Mock IAM 客户端"""
    print("\n=== 测试 Mock IAM 客户端 ===")
    
    try:
        client = MockIAMClient()
        
        # 1. 健康检查
        health = client.health_check()
        print(f"✅ 健康检查: {health}")
        
        # 2. 创建凭据
        result = client.create_service_credentials(
            "test-service", 
            "test-product", 
            {"ak": "test-ak", "sk": "test-sk"}
        )
        print(f"✅ 创建凭据: {result}")
        
        # 3. 列出所有凭据
        all_creds = client.list_all_credentials()
        print(f"✅ 所有凭据: {all_creds}")
        
        return True
        
    except Exception as e:
        print(f"❌ Mock IAM 测试失败: {e}")
        return False


def test_real_iam():
    """测试真实 IAM API"""
    print("\n=== 测试真实 IAM API ===")
    
    # 设置测试环境变量
    os.environ['IAM_API_URL'] = 'http://iam-manage-xian.console.svc.cluster.local:8468'
    os.environ['IAM_USERNAME'] = 'test-user'
    os.environ['IAM_PASSWORD'] = 'test-password'
    os.environ['IAM_DOMAIN'] = 'default'
    os.environ['IAM_DOMAIN_ID'] = 'default'
    
    try:
        client = IAMClient()
        
        # 1. 健康检查
        print("1. 测试健康检查...")
        health = client.health_check()
        print(f"   健康检查结果: {health}")
        
        if not health:
            print("❌ IAM API 不可用，跳过后续测试")
            return False
        
        # 2. 测试认证
        print("2. 测试认证...")
        try:
            token = client._get_auth_token()
            print(f"   认证成功，Token: {token[:20]}...")
        except Exception as e:
            print(f"   认证失败: {e}")
            return False
        
        # 3. 测试用户查询
        print("3. 测试用户查询...")
        try:
            user = client.get_user_by_name("test-user", "default")
            print(f"   用户查询结果: {user}")
        except Exception as e:
            print(f"   用户查询失败: {e}")
        
        # 4. 测试创建服务凭据（谨慎测试）
        print("4. 测试创建服务凭据...")
        try:
            result = client.create_service_credentials(
                "iam-operator-test",
                "test-product", 
                {"ak": "test-ak", "sk": "test-sk"}
            )
            print(f"   创建凭据结果: {result}")
        except Exception as e:
            print(f"   创建凭据失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实 IAM 测试失败: {e}")
        return False


def test_iam_endpoints():
    """测试 IAM 端点连通性"""
    print("\n=== 测试 IAM 端点连通性 ===")
    
    import requests
    
    endpoints = [
        "http://iam-manage-xian.console.svc.cluster.local:8468/v3/health",
        "http://iam-manage-xian.console.svc.cluster.local:8468/v3/auth/tokens",
        "http://nmg02-bce-test6.nmg02.baidu.com:8468/health",
        "http://nmg02-bce-test6.nmg02.baidu.com:8468/v3/auth/tokens"
    ]
    
    for endpoint in endpoints:
        try:
            print(f"测试端点: {endpoint}")
            
            if endpoint.endswith('/health'):
                response = requests.get(endpoint, timeout=5)
            else:
                # 测试认证端点（只发送 HEAD 请求）
                response = requests.head(endpoint, timeout=5)
            
            print(f"   状态码: {response.status_code}")
            if response.status_code < 400:
                print(f"   ✅ 端点可访问")
            else:
                print(f"   ⚠️ 端点返回错误状态")
                
        except requests.exceptions.ConnectTimeout:
            print(f"   ❌ 连接超时")
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接错误")
        except Exception as e:
            print(f"   ❌ 其他错误: {e}")


def main():
    """主函数"""
    print("IAM API 连接测试")
    print("=" * 50)
    
    # 1. 测试 Mock IAM
    mock_success = test_mock_iam()
    
    # 2. 测试端点连通性
    test_iam_endpoints()
    
    # 3. 测试真实 IAM（如果用户确认）
    if input("\n是否测试真实 IAM API？(y/N): ").lower() == 'y':
        real_success = test_real_iam()
    else:
        print("跳过真实 IAM API 测试")
        real_success = None
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"Mock IAM: {'✅ 成功' if mock_success else '❌ 失败'}")
    if real_success is not None:
        print(f"真实 IAM: {'✅ 成功' if real_success else '❌ 失败'}")
    
    print("\n建议:")
    if mock_success:
        print("- Mock IAM 工作正常，可以继续开发和测试")
    if real_success is False:
        print("- 真实 IAM API 连接有问题，请检查网络和认证配置")
    elif real_success is True:
        print("- 真实 IAM API 连接正常，可以切换到生产模式")


if __name__ == '__main__':
    main()
