#!/bin/bash

# IAM-Operator 部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        log_error "docker 未安装"
        exit 1
    fi
    
    # 检查 kubectl 连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 构建镜像
build_image() {
    local image_tag=${1:-"iam-operator:latest"}
    
    log_info "构建 Docker 镜像: $image_tag"
    
    cd "$(dirname "$0")/.."
    
    docker build -t "$image_tag" .
    
    log_success "镜像构建完成: $image_tag"
}

# 部署 CRD
deploy_crds() {
    log_info "部署 CRD..."
    
    kubectl apply -f deploy/crds/
    
    # 等待 CRD 就绪
    log_info "等待 CRD 就绪..."
    kubectl wait --for condition=established --timeout=60s crd/iamserviceaccounts.iam.example.com
    
    log_success "CRD 部署完成"
}

# 部署 RBAC
deploy_rbac() {
    log_info "部署 RBAC..."
    
    kubectl apply -f deploy/rbac/
    
    log_success "RBAC 部署完成"
}

# 部署配置
deploy_config() {
    log_info "部署配置..."
    
    kubectl apply -f deploy/configmaps/
    
    log_success "配置部署完成"
}

# 部署 Operator
deploy_operator() {
    local image_tag=${1:-"iam-operator:latest"}
    
    log_info "部署 IAM-Operator..."
    
    # 更新镜像标签
    sed "s|image: iam-operator:latest|image: $image_tag|g" deploy/deployment.yaml | kubectl apply -f -
    
    # 等待部署就绪
    log_info "等待 Operator 就绪..."
    kubectl rollout status deployment/iam-operator -n base --timeout=300s
    
    log_success "IAM-Operator 部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查 Pod 状态
    local pod_status=$(kubectl get pods -n base -l app=iam-operator -o jsonpath='{.items[0].status.phase}')
    if [ "$pod_status" != "Running" ]; then
        log_error "Operator Pod 状态异常: $pod_status"
        kubectl describe pods -n base -l app=iam-operator
        exit 1
    fi
    
    # 检查日志
    log_info "检查 Operator 日志..."
    kubectl logs -n base -l app=iam-operator --tail=10
    
    # 检查健康状态
    local pod_name=$(kubectl get pods -n base -l app=iam-operator -o jsonpath='{.items[0].metadata.name}')
    if kubectl exec -n base "$pod_name" -- curl -f http://localhost:8081/healthz &> /dev/null; then
        log_success "健康检查通过"
    else
        log_warning "健康检查失败，但 Pod 正在运行"
    fi
    
    log_success "部署验证完成"
}

# 清理部署
cleanup() {
    log_info "清理 IAM-Operator..."
    
    kubectl delete -f deploy/deployment.yaml --ignore-not-found=true
    kubectl delete -f deploy/configmaps/ --ignore-not-found=true
    kubectl delete -f deploy/rbac/ --ignore-not-found=true
    kubectl delete -f deploy/crds/ --ignore-not-found=true
    
    log_success "清理完成"
}

# 显示帮助
show_help() {
    echo "IAM-Operator 部署脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  deploy [image_tag]  - 完整部署 (默认: iam-operator:latest)"
    echo "  build [image_tag]   - 只构建镜像"
    echo "  cleanup             - 清理部署"
    echo "  verify              - 验证部署"
    echo "  help                - 显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 deploy                    # 使用默认镜像标签部署"
    echo "  $0 deploy iam-operator:v1.0  # 使用指定镜像标签部署"
    echo "  $0 build iam-operator:v1.0   # 构建指定标签的镜像"
    echo "  $0 cleanup                   # 清理所有资源"
}

# 主函数
main() {
    local command=${1:-"help"}
    local image_tag=${2:-"iam-operator:latest"}
    
    case $command in
        "deploy")
            check_dependencies
            build_image "$image_tag"
            deploy_crds
            deploy_rbac
            deploy_config
            deploy_operator "$image_tag"
            verify_deployment
            log_success "IAM-Operator 部署成功！"
            ;;
        "build")
            check_dependencies
            build_image "$image_tag"
            ;;
        "cleanup")
            cleanup
            ;;
        "verify")
            verify_deployment
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
