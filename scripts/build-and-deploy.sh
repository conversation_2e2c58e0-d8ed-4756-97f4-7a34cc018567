#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="iam-operator"
IMAGE_TAG="latest"
REGISTRY="registry.dev7.abcstackint.com:5000/abc-stack"
FULL_IMAGE="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"

echo -e "${GREEN}🚀 Building and Deploying IAM Operator (Go Version)${NC}"

# Step 1: Build the binary
echo -e "${YELLOW}📦 Building Go binary...${NC}"
go build -o bin/manager cmd/main.go
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Binary built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build binary${NC}"
    exit 1
fi

# Step 2: Create Dockerfile if it doesn't exist
if [ ! -f Dockerfile ]; then
    echo -e "${YELLOW}📝 Creating Dockerfile...${NC}"
    cat > Dockerfile << 'EOF'
# Build the manager binary
FROM golang:1.21 AS builder
ARG TARGETOS
ARG TARGETARCH

WORKDIR /workspace
# Copy the Go Modules manifests
COPY go.mod go.mod
COPY go.sum go.sum
# cache deps before building and copying source so that we don't need to re-download as much
# and so that source changes don't invalidate our downloaded layer
RUN go mod download

# Copy the go source
COPY cmd/main.go cmd/main.go
COPY internal/ internal/

# Build
# the GOARCH has not a default value to allow the binary be built according to the host where the command
# was called. For example, if we call make docker-build in a local env which has the Apple Silicon M1 SO
# the docker BUILDPLATFORM arg will be linux/arm64 when for Apple x86 it will be linux/amd64. Therefore,
# by leaving it empty we can ensure that the container and binary shipped on it will have the same platform.
RUN CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH} go build -a -o manager cmd/main.go

# Use distroless as minimal base image to package the manager binary
# Refer to https://github.com/GoogleContainerTools/distroless for more details
FROM gcr.io/distroless/static:nonroot
WORKDIR /
COPY --from=builder /workspace/manager .
USER 65532:65532

ENTRYPOINT ["/manager"]
EOF
    echo -e "${GREEN}✅ Dockerfile created${NC}"
fi

# Step 3: Build Docker image
echo -e "${YELLOW}🐳 Building Docker image...${NC}"
docker build -t ${IMAGE_NAME}:${IMAGE_TAG} .
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build Docker image${NC}"
    exit 1
fi

# Step 4: Tag and push to registry (optional)
read -p "Do you want to push to registry ${REGISTRY}? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}📤 Pushing to registry...${NC}"
    docker tag ${IMAGE_NAME}:${IMAGE_TAG} ${FULL_IMAGE}
    docker push ${FULL_IMAGE}
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Image pushed successfully${NC}"
        # Update deployment to use registry image
        sed -i.bak "s|image: iam-operator:latest|image: ${FULL_IMAGE}|g" deploy/simple-deploy.yaml
    else
        echo -e "${RED}❌ Failed to push image${NC}"
        exit 1
    fi
fi

# Step 5: Deploy to Kubernetes
read -p "Do you want to deploy to Kubernetes? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}☸️  Deploying to Kubernetes...${NC}"
    
    # Check if deployment already exists
    if kubectl get deployment iam-operator-controller-manager -n iam-operator-system >/dev/null 2>&1; then
        echo -e "${YELLOW}🔄 Updating existing deployment...${NC}"
        kubectl apply -f deploy/simple-deploy.yaml
    else
        echo -e "${YELLOW}🆕 Creating new deployment...${NC}"
        kubectl apply -f deploy/simple-deploy.yaml
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Deployment successful${NC}"
        
        # Wait for deployment to be ready
        echo -e "${YELLOW}⏳ Waiting for deployment to be ready...${NC}"
        kubectl wait --for=condition=available --timeout=300s deployment/iam-operator-controller-manager -n iam-operator-system
        
        # Show status
        echo -e "${GREEN}📊 Deployment Status:${NC}"
        kubectl get pods -n iam-operator-system -l control-plane=controller-manager
        
        echo -e "${GREEN}📋 To check logs:${NC}"
        echo "kubectl logs -f deployment/iam-operator-controller-manager -n iam-operator-system"
        
        echo -e "${GREEN}🧪 To test with a sample ConfigMap:${NC}"
        echo "kubectl create configmap test-iam-config --from-literal=config.php='\$bss_ak=\"test_ak\"; \$bss_sk=\"test_sk\";' -n default"
        
    else
        echo -e "${RED}❌ Deployment failed${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}🎉 Build and deploy process completed!${NC}"
