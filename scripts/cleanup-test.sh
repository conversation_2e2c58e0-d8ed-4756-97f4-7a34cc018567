#!/bin/bash
set -e

# IAM Operator 测试环境完整清理脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查资源是否存在
check_resource_exists() {
    local resource_type=$1
    local resource_name=$2
    local namespace=${3:-""}
    
    if [ -n "$namespace" ]; then
        kubectl get $resource_type $resource_name -n $namespace &>/dev/null
    else
        kubectl get $resource_type $resource_name &>/dev/null
    fi
}

# 清理命名空间级别资源
cleanup_namespace_resources() {
    log_info "清理命名空间级别资源..."
    
    local namespace="iam-operator-test"
    
    if check_resource_exists "namespace" $namespace; then
        log_info "发现测试命名空间: $namespace"
        
        # 显示即将删除的资源
        echo "即将删除的资源:"
        kubectl get all,configmaps,secrets -n $namespace 2>/dev/null || true
        
        # 删除整个命名空间（会级联删除所有资源）
        log_info "删除测试命名空间..."
        kubectl delete namespace $namespace --timeout=60s
        
        # 等待命名空间完全删除
        log_info "等待命名空间完全删除..."
        local count=0
        while check_resource_exists "namespace" $namespace && [ $count -lt 30 ]; do
            sleep 2
            count=$((count + 1))
            echo -n "."
        done
        echo ""
        
        if check_resource_exists "namespace" $namespace; then
            log_warning "命名空间删除超时，可能需要手动清理"
        else
            log_success "命名空间已完全删除"
        fi
    else
        log_info "测试命名空间不存在，跳过"
    fi
}

# 清理集群级别资源
cleanup_cluster_resources() {
    log_info "清理集群级别资源..."
    
    # 清理ClusterRoleBinding
    local crb_name="iam-operator-test"
    if check_resource_exists "clusterrolebinding" $crb_name; then
        log_info "删除ClusterRoleBinding: $crb_name"
        kubectl delete clusterrolebinding $crb_name
        log_success "ClusterRoleBinding已删除"
    else
        log_info "ClusterRoleBinding不存在，跳过"
    fi
    
    # 清理ClusterRole
    local cr_name="iam-operator-test"
    if check_resource_exists "clusterrole" $cr_name; then
        log_info "删除ClusterRole: $cr_name"
        kubectl delete clusterrole $cr_name
        log_success "ClusterRole已删除"
    else
        log_info "ClusterRole不存在，跳过"
    fi
    
    # 清理更安全版本的集群资源（如果存在）
    local safer_crb="iam-operator-test-safer"
    local safer_cr="iam-operator-test-safer"
    
    if check_resource_exists "clusterrolebinding" $safer_crb; then
        log_info "删除安全版本ClusterRoleBinding: $safer_crb"
        kubectl delete clusterrolebinding $safer_crb
    fi
    
    if check_resource_exists "clusterrole" $safer_cr; then
        log_info "删除安全版本ClusterRole: $safer_cr"
        kubectl delete clusterrole $safer_cr
    fi
}

# 清理Docker镜像（可选）
cleanup_docker_images() {
    log_info "清理Docker镜像（可选）..."
    
    if command -v docker &> /dev/null; then
        # 检查是否存在测试镜像
        if docker images | grep -q "iam-operator.*latest"; then
            read -p "是否删除Docker镜像 iam-operator:latest? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                docker rmi iam-operator:latest 2>/dev/null || log_warning "Docker镜像删除失败或不存在"
                log_success "Docker镜像已删除"
            else
                log_info "保留Docker镜像"
            fi
        else
            log_info "未发现测试Docker镜像"
        fi
    else
        log_info "Docker未安装，跳过镜像清理"
    fi
}

# 验证清理结果
verify_cleanup() {
    log_info "验证清理结果..."
    
    local cleanup_success=true
    
    # 检查命名空间
    if check_resource_exists "namespace" "iam-operator-test"; then
        log_error "❌ 命名空间仍然存在"
        cleanup_success=false
    else
        log_success "✅ 命名空间已清理"
    fi
    
    # 检查ClusterRole
    if check_resource_exists "clusterrole" "iam-operator-test"; then
        log_error "❌ ClusterRole仍然存在"
        cleanup_success=false
    else
        log_success "✅ ClusterRole已清理"
    fi
    
    # 检查ClusterRoleBinding
    if check_resource_exists "clusterrolebinding" "iam-operator-test"; then
        log_error "❌ ClusterRoleBinding仍然存在"
        cleanup_success=false
    else
        log_success "✅ ClusterRoleBinding已清理"
    fi
    
    if $cleanup_success; then
        log_success "🎉 所有测试资源已完全清理！"
        return 0
    else
        log_error "⚠️  部分资源清理失败，请手动检查"
        return 1
    fi
}

# 显示清理前的资源状态
show_resources_before_cleanup() {
    log_info "清理前的资源状态："
    
    echo "=== 命名空间 ==="
    kubectl get namespace iam-operator-test 2>/dev/null || echo "命名空间不存在"
    
    echo -e "\n=== 命名空间内资源 ==="
    kubectl get all,configmaps,secrets -n iam-operator-test 2>/dev/null || echo "命名空间内无资源"
    
    echo -e "\n=== 集群级别资源 ==="
    kubectl get clusterrole,clusterrolebinding | grep iam-operator-test || echo "无相关集群资源"
    
    echo -e "\n=== Docker镜像 ==="
    if command -v docker &> /dev/null; then
        docker images | grep iam-operator || echo "无相关Docker镜像"
    else
        echo "Docker未安装"
    fi
}

# 主函数
main() {
    echo "🧹 IAM Operator 测试环境清理工具"
    echo "=================================="
    
    case "${1:-all}" in
        "check")
            show_resources_before_cleanup
            ;;
        "namespace")
            cleanup_namespace_resources
            verify_cleanup
            ;;
        "cluster")
            cleanup_cluster_resources
            verify_cleanup
            ;;
        "docker")
            cleanup_docker_images
            ;;
        "all")
            show_resources_before_cleanup
            echo ""
            read -p "确认清理所有测试资源? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                cleanup_namespace_resources
                cleanup_cluster_resources
                cleanup_docker_images
                verify_cleanup
            else
                log_info "清理已取消"
                exit 0
            fi
            ;;
        "force")
            log_warning "强制清理模式（无确认）"
            cleanup_namespace_resources
            cleanup_cluster_resources
            verify_cleanup
            ;;
        *)
            echo "用法: $0 {all|check|namespace|cluster|docker|force}"
            echo ""
            echo "命令说明:"
            echo "  all       - 清理所有资源（默认，需要确认）"
            echo "  check     - 只检查资源状态，不清理"
            echo "  namespace - 只清理命名空间级别资源"
            echo "  cluster   - 只清理集群级别资源"
            echo "  docker    - 只清理Docker镜像"
            echo "  force     - 强制清理所有资源（无确认）"
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'log_warning "清理被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
