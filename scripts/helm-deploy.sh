#!/bin/bash

# IAM-Operator Helm 部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
CHART_PATH="helm/iam-operator"
RELEASE_NAME="iam-operator"
NAMESPACE="base"
VALUES_FILE=""
IMAGE_TAG="1.0.0"

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v helm &> /dev/null; then
        log_error "helm 未安装"
        exit 1
    fi
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    # 检查 kubectl 连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 构建镜像
build_image() {
    local image_tag=${1:-$IMAGE_TAG}
    
    log_info "构建 Docker 镜像: abc-stack/iam-operator:$image_tag"
    
    cd "$(dirname "$0")/.."
    
    docker build -t "abc-stack/iam-operator:$image_tag" .
    
    log_success "镜像构建完成: abc-stack/iam-operator:$image_tag"
}

# 验证 Helm Chart
validate_chart() {
    log_info "验证 Helm Chart..."
    
    cd "$(dirname "$0")/.."
    
    # Lint Chart
    helm lint "$CHART_PATH"
    
    # 模板渲染测试
    helm template test-release "$CHART_PATH" --debug --dry-run > /dev/null
    
    log_success "Chart 验证通过"
}

# 部署 IAM-Operator
deploy_operator() {
    local release_name=${1:-$RELEASE_NAME}
    local namespace=${2:-$NAMESPACE}
    local image_tag=${3:-$IMAGE_TAG}
    local values_file=${4:-$VALUES_FILE}
    
    log_info "部署 IAM-Operator..."
    
    cd "$(dirname "$0")/.."
    
    # 创建命名空间（如果不存在）
    kubectl create namespace "$namespace" --dry-run=client -o yaml | kubectl apply -f -
    
    # 构建 Helm 命令
    local helm_cmd="helm upgrade --install $release_name $CHART_PATH"
    helm_cmd="$helm_cmd --namespace $namespace"
    helm_cmd="$helm_cmd --set image.tag=$image_tag"
    
    if [ -n "$values_file" ] && [ -f "$values_file" ]; then
        helm_cmd="$helm_cmd --values $values_file"
    fi
    
    # 执行部署
    eval "$helm_cmd"
    
    # 等待部署就绪
    log_info "等待 Operator 就绪..."
    kubectl rollout status deployment/$release_name -n "$namespace" --timeout=300s
    
    log_success "IAM-Operator 部署完成"
}

# 升级 IAM-Operator
upgrade_operator() {
    local release_name=${1:-$RELEASE_NAME}
    local namespace=${2:-$NAMESPACE}
    local image_tag=${3:-$IMAGE_TAG}
    local values_file=${4:-$VALUES_FILE}
    
    log_info "升级 IAM-Operator..."
    
    # 检查 Release 是否存在
    if ! helm list -n "$namespace" | grep -q "$release_name"; then
        log_error "Release $release_name 不存在，请先部署"
        exit 1
    fi
    
    deploy_operator "$release_name" "$namespace" "$image_tag" "$values_file"
}

# 卸载 IAM-Operator
uninstall_operator() {
    local release_name=${1:-$RELEASE_NAME}
    local namespace=${2:-$NAMESPACE}
    
    log_info "卸载 IAM-Operator..."
    
    # 卸载 Helm Release
    helm uninstall "$release_name" -n "$namespace"
    
    # 清理 CRD（可选）
    log_warning "CRD 不会自动删除，如需删除请手动执行："
    echo "kubectl delete crd iamserviceaccounts.iam.example.com"
    
    log_success "IAM-Operator 卸载完成"
}

# 查看状态
show_status() {
    local release_name=${1:-$RELEASE_NAME}
    local namespace=${2:-$NAMESPACE}
    
    log_info "显示 IAM-Operator 状态..."
    
    echo ""
    echo "=== Helm Release ==="
    helm list -n "$namespace" | grep "$release_name" || echo "Release not found"
    
    echo ""
    echo "=== Pods ==="
    kubectl get pods -n "$namespace" -l "app.kubernetes.io/name=iam-operator"
    
    echo ""
    echo "=== Services ==="
    kubectl get svc -n "$namespace" -l "app.kubernetes.io/name=iam-operator"
    
    echo ""
    echo "=== CRDs ==="
    kubectl get crd iamserviceaccounts.iam.example.com 2>/dev/null || echo "CRD not found"
    
    echo ""
    echo "=== IAMServiceAccounts ==="
    kubectl get iamserviceaccounts -A 2>/dev/null || echo "No IAMServiceAccounts found"
}

# 查看日志
view_logs() {
    local release_name=${1:-$RELEASE_NAME}
    local namespace=${2:-$NAMESPACE}
    
    log_info "查看 IAM-Operator 日志..."
    
    kubectl logs -n "$namespace" -l "app.kubernetes.io/name=iam-operator" --tail=50 -f
}

# 测试部署
test_deployment() {
    local release_name=${1:-$RELEASE_NAME}
    local namespace=${2:-$NAMESPACE}
    
    log_info "测试 IAM-Operator 部署..."
    
    # 检查 Pod 状态
    local pod_name=$(kubectl get pods -n "$namespace" -l "app.kubernetes.io/name=iam-operator" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$pod_name" ]; then
        log_error "未找到 IAM-Operator Pod"
        exit 1
    fi
    
    local pod_status=$(kubectl get pod "$pod_name" -n "$namespace" -o jsonpath='{.status.phase}')
    if [ "$pod_status" != "Running" ]; then
        log_error "Pod 状态异常: $pod_status"
        kubectl describe pod "$pod_name" -n "$namespace"
        exit 1
    fi
    
    # 测试健康检查
    if kubectl exec -n "$namespace" "$pod_name" -- curl -f http://localhost:8081/healthz &> /dev/null; then
        log_success "健康检查通过"
    else
        log_warning "健康检查失败"
    fi
    
    # 测试指标
    local metrics=$(kubectl exec -n "$namespace" "$pod_name" -- curl -s http://localhost:8080/metrics | grep "iam_" | head -3)
    if [ -n "$metrics" ]; then
        log_success "指标测试通过"
    else
        log_warning "未找到 IAM 相关指标"
    fi
    
    log_success "部署测试完成"
}

# 显示帮助
show_help() {
    echo "IAM-Operator Helm 部署脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  deploy [release] [namespace] [image_tag] [values_file]  - 部署 Operator"
    echo "  upgrade [release] [namespace] [image_tag] [values_file] - 升级 Operator"
    echo "  uninstall [release] [namespace]                        - 卸载 Operator"
    echo "  build [image_tag]                                      - 构建镜像"
    echo "  validate                                               - 验证 Chart"
    echo "  status [release] [namespace]                           - 显示状态"
    echo "  logs [release] [namespace]                             - 查看日志"
    echo "  test [release] [namespace]                             - 测试部署"
    echo "  help                                                   - 显示帮助"
    echo ""
    echo "默认值:"
    echo "  release: $RELEASE_NAME"
    echo "  namespace: $NAMESPACE"
    echo "  image_tag: $IMAGE_TAG"
    echo ""
    echo "示例:"
    echo "  $0 deploy                                    # 使用默认配置部署"
    echo "  $0 deploy my-iam base 1.1.0                 # 指定参数部署"
    echo "  $0 deploy iam-operator base 1.0.0 values.yaml # 使用自定义 values 文件"
    echo "  $0 upgrade                                   # 升级到最新版本"
    echo "  $0 uninstall                                 # 卸载"
}

# 主函数
main() {
    local command=${1:-"help"}
    
    case $command in
        "deploy")
            check_dependencies
            validate_chart
            build_image "${4:-$IMAGE_TAG}"
            deploy_operator "$2" "$3" "$4" "$5"
            test_deployment "$2" "$3"
            log_success "IAM-Operator 部署成功！"
            ;;
        "upgrade")
            check_dependencies
            validate_chart
            build_image "${4:-$IMAGE_TAG}"
            upgrade_operator "$2" "$3" "$4" "$5"
            test_deployment "$2" "$3"
            log_success "IAM-Operator 升级成功！"
            ;;
        "uninstall")
            uninstall_operator "$2" "$3"
            ;;
        "build")
            build_image "$2"
            ;;
        "validate")
            validate_chart
            ;;
        "status")
            show_status "$2" "$3"
            ;;
        "logs")
            view_logs "$2" "$3"
            ;;
        "test")
            test_deployment "$2" "$3"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
