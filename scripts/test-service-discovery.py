#!/usr/bin/env python3
"""
测试 IAM 服务发现功能
"""
import sys
import os
import json
import logging
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.clients.iam_service_discovery import IAMServiceDiscoveryClient
from src.utils.config import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_service_discovery_basic():
    """基础服务发现测试"""
    print("=" * 60)
    print("1. 基础服务发现测试")
    print("=" * 60)
    
    try:
        # 测试默认配置
        client = IAMServiceDiscoveryClient()
        result = client.test_service_discovery()
        
        print(f"✅ 服务发现结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        return result['service_discovery']
        
    except Exception as e:
        print(f"❌ 基础服务发现测试失败: {e}")
        return False


def test_different_service_names():
    """测试不同的服务名称"""
    print("\n" + "=" * 60)
    print("2. 测试不同服务名称")
    print("=" * 60)
    
    service_names = [
        "iam-manage-xian",  # 从文档中看到的实际服务名
        "iam-nginx",        # 方案中提到的服务名
        "iam-manage",       # 可能的服务名
        "iam-openapi"       # 另一个可能的服务
    ]
    
    results = {}
    
    for service_name in service_names:
        print(f"\n测试服务: {service_name}")
        try:
            client = IAMServiceDiscoveryClient(service_name=service_name)
            result = client.test_service_discovery()
            
            if result['service_discovery']:
                print(f"  ✅ 发现服务: {result['endpoint']}")
                print(f"  📋 服务信息: {result['service_info']['cluster_ip']}:{result['service_info']['ports'][0]['port']}")
                results[service_name] = result
            else:
                print(f"  ❌ 未发现服务: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
    
    return results


def test_endpoint_construction():
    """测试端点构建逻辑"""
    print("\n" + "=" * 60)
    print("3. 测试端点构建逻辑")
    print("=" * 60)
    
    try:
        client = IAMServiceDiscoveryClient()
        
        # 手动测试服务发现
        endpoint = client._discover_iam_endpoint()
        print(f"✅ 发现的端点: {endpoint}")
        
        # 验证端点格式
        if endpoint.startswith('http://') and '/v3' in endpoint:
            print("✅ 端点格式正确")
            return True
        else:
            print("❌ 端点格式不正确")
            return False
            
    except Exception as e:
        print(f"❌ 端点构建测试失败: {e}")
        return False


def test_health_check():
    """测试健康检查"""
    print("\n" + "=" * 60)
    print("4. 测试健康检查")
    print("=" * 60)
    
    try:
        client = IAMServiceDiscoveryClient()
        
        # 测试健康检查
        is_healthy = client.health_check()
        
        if is_healthy:
            print("✅ IAM 服务健康检查通过")
        else:
            print("⚠️  IAM 服务健康检查失败（可能是正常的，取决于服务配置）")
        
        return is_healthy
        
    except Exception as e:
        print(f"❌ 健康检查测试失败: {e}")
        return False


def test_minimal_connectivity():
    """最小连通性测试"""
    print("\n" + "=" * 60)
    print("5. 最小连通性测试")
    print("=" * 60)
    
    try:
        client = IAMServiceDiscoveryClient()
        endpoint = client._discover_iam_endpoint()
        
        # 尝试简单的 HTTP 请求
        import requests
        
        # 测试根路径
        try:
            response = requests.get(
                endpoint.replace('/v3', ''),
                timeout=5
            )
            print(f"✅ 根路径连通性: HTTP {response.status_code}")
        except Exception as e:
            print(f"⚠️  根路径连通性失败: {e}")
        
        # 测试 v3 路径
        try:
            response = requests.get(
                endpoint,
                timeout=5
            )
            print(f"✅ v3 路径连通性: HTTP {response.status_code}")
        except Exception as e:
            print(f"⚠️  v3 路径连通性失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连通性测试失败: {e}")
        return False


def generate_implementation_code():
    """生成实现代码示例"""
    print("\n" + "=" * 60)
    print("6. 生成实现代码示例")
    print("=" * 60)
    
    code_example = '''
# 方案2实现示例：通过K8s Service发现调用iam-manage API

from kubernetes import client, config as k8s_config
from src.clients.iam_service_discovery import IAMServiceDiscoveryClient

# 1. 初始化服务发现客户端
iam_client = IAMServiceDiscoveryClient(
    iam_namespace="console",
    service_name="iam-manage-xian"  # 根据实际部署调整
)

# 2. 自动发现服务端点
try:
    endpoint = iam_client._discover_iam_endpoint()
    print(f"发现的IAM端点: {endpoint}")
    
    # 3. 进行健康检查
    if iam_client.health_check():
        print("IAM服务可用")
        
        # 4. 使用服务（示例）
        # token = iam_client._get_auth_token()
        # ... 其他API调用
    else:
        print("IAM服务不可用")
        
except Exception as e:
    print(f"服务发现失败: {e}")
'''
    
    print(code_example)
    
    # 保存到文件
    with open('service_discovery_example.py', 'w', encoding='utf-8') as f:
        f.write(code_example)
    
    print("✅ 实现代码示例已保存到 service_discovery_example.py")


def main():
    """主测试函数"""
    print("🚀 IAM 服务发现测试开始")
    print(f"📋 当前配置:")
    print(f"   - IAM API URL: {config.iam_api_url}")
    print(f"   - 超时时间: {config.iam_api_timeout}s")
    
    results = {
        'basic_discovery': False,
        'service_names': {},
        'endpoint_construction': False,
        'health_check': False,
        'connectivity': False
    }
    
    # 执行测试
    results['basic_discovery'] = test_service_discovery_basic()
    results['service_names'] = test_different_service_names()
    results['endpoint_construction'] = test_endpoint_construction()
    results['health_check'] = test_health_check()
    results['connectivity'] = test_minimal_connectivity()
    
    # 生成实现代码
    generate_implementation_code()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    success_count = sum([
        results['basic_discovery'],
        bool(results['service_names']),
        results['endpoint_construction'],
        results['health_check'],
        results['connectivity']
    ])
    
    print(f"✅ 成功测试: {success_count}/5")
    
    if results['service_names']:
        print(f"🎯 发现的可用服务:")
        for name, info in results['service_names'].items():
            print(f"   - {name}: {info['endpoint']}")
    
    # 建议
    print("\n💡 实施建议:")
    if results['basic_discovery']:
        print("   ✅ 服务发现功能正常，可以实施方案2")
    else:
        print("   ⚠️  服务发现存在问题，需要检查服务部署状态")
    
    if results['health_check']:
        print("   ✅ 健康检查通过，服务状态良好")
    else:
        print("   ⚠️  健康检查失败，可能需要调整健康检查逻辑")
    
    return results


if __name__ == '__main__':
    try:
        results = main()
        
        # 根据测试结果设置退出码
        if results['basic_discovery'] and results['endpoint_construction']:
            sys.exit(0)  # 成功
        else:
            sys.exit(1)  # 失败
            
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期的错误: {e}")
        sys.exit(1)
