#!/usr/bin/env python3
"""
简化的服务发现测试 - 专注于核心功能验证
"""
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from kubernetes import client, config as k8s_config
from kubernetes.client.rest import ApiException


def test_kubernetes_connection():
    """测试Kubernetes连接"""
    print("🔗 测试Kubernetes连接...")
    try:
        # 加载配置
        try:
            k8s_config.load_incluster_config()
            print("✅ 加载集群内配置成功")
        except k8s_config.ConfigException:
            k8s_config.load_kube_config()
            print("✅ 加载本地配置成功")
        
        # 测试连接
        v1 = client.CoreV1Api()
        namespaces = v1.list_namespace()
        print(f"✅ 成功连接到Kubernetes，发现 {len(namespaces.items)} 个命名空间")
        return True
        
    except Exception as e:
        print(f"❌ Kubernetes连接失败: {e}")
        return False


def test_service_discovery():
    """测试服务发现核心功能"""
    print("\n🔍 测试服务发现核心功能...")
    
    try:
        v1 = client.CoreV1Api()
        
        # 测试目标服务
        services_to_test = [
            ("console", "iam-manage-xian"),
            ("console", "iam-openapi"),
            ("console", "iam-nginx")
        ]
        
        discovered_services = []
        
        for namespace, service_name in services_to_test:
            try:
                print(f"\n📍 查找服务: {namespace}/{service_name}")
                
                # 获取服务信息
                service = v1.read_namespaced_service(
                    name=service_name,
                    namespace=namespace
                )
                
                cluster_ip = service.spec.cluster_ip
                if not cluster_ip or cluster_ip == "None":
                    print(f"⚠️  服务 {service_name} 没有ClusterIP")
                    continue
                
                # 获取端口
                if not service.spec.ports:
                    print(f"⚠️  服务 {service_name} 没有端口配置")
                    continue
                
                port = service.spec.ports[0].port
                endpoint = f"http://{cluster_ip}:{port}/v3"
                
                service_info = {
                    'name': service_name,
                    'namespace': namespace,
                    'cluster_ip': cluster_ip,
                    'port': port,
                    'endpoint': endpoint,
                    'type': service.spec.type,
                    'ports': [{'name': p.name, 'port': p.port, 'protocol': p.protocol} 
                             for p in service.spec.ports]
                }
                
                discovered_services.append(service_info)
                
                print(f"✅ 发现服务: {endpoint}")
                print(f"   类型: {service.spec.type}")
                print(f"   端口: {[f'{p.name}:{p.port}' for p in service.spec.ports]}")
                
            except ApiException as e:
                if e.status == 404:
                    print(f"❌ 服务 {namespace}/{service_name} 不存在")
                else:
                    print(f"❌ 获取服务 {namespace}/{service_name} 失败: {e}")
            except Exception as e:
                print(f"❌ 处理服务 {namespace}/{service_name} 时出错: {e}")
        
        return discovered_services
        
    except Exception as e:
        print(f"❌ 服务发现测试失败: {e}")
        return []


def test_endpoint_construction():
    """测试端点构建逻辑"""
    print("\n🔧 测试端点构建逻辑...")
    
    # 模拟服务发现结果
    test_cases = [
        {
            'cluster_ip': '**************',
            'port': 8468,
            'expected': 'http://**************:8468/v3'
        },
        {
            'cluster_ip': '**************',
            'port': 8480,
            'expected': 'http://**************:8480/v3'
        }
    ]
    
    success_count = 0
    for i, case in enumerate(test_cases, 1):
        endpoint = f"http://{case['cluster_ip']}:{case['port']}/v3"
        if endpoint == case['expected']:
            print(f"✅ 测试用例 {i}: {endpoint}")
            success_count += 1
        else:
            print(f"❌ 测试用例 {i}: 期望 {case['expected']}, 实际 {endpoint}")
    
    print(f"📊 端点构建测试: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def validate_approach_feasibility(discovered_services):
    """验证方案可行性"""
    print("\n🎯 验证方案2可行性...")
    
    # 检查关键服务
    iam_manage_found = any(s['name'] == 'iam-manage-xian' for s in discovered_services)
    iam_openapi_found = any(s['name'] == 'iam-openapi' for s in discovered_services)
    
    print(f"📋 发现的服务数量: {len(discovered_services)}")
    print(f"🎯 iam-manage-xian: {'✅ 发现' if iam_manage_found else '❌ 未发现'}")
    print(f"🎯 iam-openapi: {'✅ 发现' if iam_openapi_found else '❌ 未发现'}")
    
    # 分析可行性
    if iam_manage_found:
        iam_manage_service = next(s for s in discovered_services if s['name'] == 'iam-manage-xian')
        print(f"\n💡 推荐配置 (iam-manage-xian):")
        print(f"   服务名称: {iam_manage_service['name']}")
        print(f"   命名空间: {iam_manage_service['namespace']}")
        print(f"   端点: {iam_manage_service['endpoint']}")
        
        feasible = True
        recommendation = "✅ 方案2可行，推荐使用 iam-manage-xian 服务"
    elif iam_openapi_found:
        iam_openapi_service = next(s for s in discovered_services if s['name'] == 'iam-openapi')
        print(f"\n💡 备选配置 (iam-openapi):")
        print(f"   服务名称: {iam_openapi_service['name']}")
        print(f"   命名空间: {iam_openapi_service['namespace']}")
        print(f"   端点: {iam_openapi_service['endpoint']}")
        
        feasible = True
        recommendation = "⚠️  方案2可行，但建议使用 iam-openapi 作为备选"
    else:
        feasible = False
        recommendation = "❌ 方案2不可行，未发现合适的IAM服务"
    
    return feasible, recommendation


def generate_implementation_config(discovered_services):
    """生成实施配置"""
    print("\n⚙️  生成实施配置...")
    
    if not discovered_services:
        print("❌ 没有发现服务，无法生成配置")
        return
    
    # 优先选择 iam-manage-xian
    target_service = None
    for service in discovered_services:
        if service['name'] == 'iam-manage-xian':
            target_service = service
            break
    
    if not target_service and discovered_services:
        target_service = discovered_services[0]
    
    if target_service:
        config_yaml = f"""
# Helm values.yaml 配置
iamApi:
  serviceDiscovery:
    enabled: true
    namespace: "{target_service['namespace']}"
    serviceName: "{target_service['name']}"

# 环境变量配置
env:
- name: IAM_SERVICE_DISCOVERY_ENABLED
  value: "true"
- name: IAM_SERVICE_NAMESPACE
  value: "{target_service['namespace']}"
- name: IAM_SERVICE_NAME
  value: "{target_service['name']}"
"""
        
        print("📄 推荐配置:")
        print(config_yaml)
        
        # 保存配置到文件
        with open('recommended_config.yaml', 'w') as f:
            f.write(config_yaml.strip())
        print("✅ 配置已保存到 recommended_config.yaml")


def main():
    """主函数"""
    print("🚀 IAM服务发现简化测试")
    print("=" * 50)
    
    # 1. 测试Kubernetes连接
    if not test_kubernetes_connection():
        print("\n❌ 无法连接到Kubernetes，测试终止")
        return 1
    
    # 2. 测试服务发现
    discovered_services = test_service_discovery()
    
    # 3. 测试端点构建
    endpoint_test_passed = test_endpoint_construction()
    
    # 4. 验证可行性
    feasible, recommendation = validate_approach_feasibility(discovered_services)
    
    # 5. 生成配置
    if feasible:
        generate_implementation_config(discovered_services)
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    print(f"🔗 Kubernetes连接: ✅")
    print(f"🔍 服务发现: {'✅' if discovered_services else '❌'} ({len(discovered_services)} 个服务)")
    print(f"🔧 端点构建: {'✅' if endpoint_test_passed else '❌'}")
    print(f"🎯 方案可行性: {'✅' if feasible else '❌'}")
    print(f"\n💡 结论: {recommendation}")
    
    # 保存详细结果
    results = {
        'kubernetes_connection': True,
        'discovered_services': discovered_services,
        'endpoint_construction': endpoint_test_passed,
        'feasible': feasible,
        'recommendation': recommendation
    }
    
    with open('simple_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"📄 详细结果已保存到 simple_test_results.json")
    
    return 0 if feasible else 1


if __name__ == '__main__':
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        sys.exit(1)
