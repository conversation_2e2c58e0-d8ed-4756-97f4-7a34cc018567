#!/bin/bash
set -e

# IAM Operator 测试部署脚本
# 用于安全地部署和测试operator功能

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_NAMESPACE="iam-operator-test"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        log_error "docker 未安装"
        exit 1
    fi
    
    # 检查kubectl连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像..."
    
    cd "$PROJECT_ROOT"
    
    # 构建镜像
    docker build -t iam-operator:latest .
    
    if [ $? -eq 0 ]; then
        log_success "Docker镜像构建成功"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
}

# 部署测试环境
deploy_test() {
    log_info "部署测试环境..."
    
    cd "$PROJECT_ROOT"
    
    # 1. 创建命名空间
    log_info "创建测试命名空间..."
    kubectl apply -f deploy/test/namespace.yaml
    
    # 2. 部署RBAC
    log_info "部署RBAC配置..."
    kubectl apply -f deploy/test/rbac.yaml
    
    # 3. 部署ConfigMap
    log_info "部署ConfigMap..."
    kubectl apply -f deploy/test/configmap.yaml
    
    # 4. 部署Secret
    log_info "部署Secret..."
    kubectl apply -f deploy/test/secret.yaml
    
    # 5. 部署测试用的AKSK ConfigMap
    log_info "部署测试AKSK ConfigMap..."
    kubectl apply -f deploy/test/test-aksk-configmap.yaml
    
    # 6. 部署Operator
    log_info "部署IAM Operator..."
    kubectl apply -f deploy/test/deployment.yaml
    
    log_success "测试环境部署完成"
}

# 等待部署就绪
wait_for_ready() {
    log_info "等待Operator就绪..."
    
    # 等待Pod启动
    kubectl wait --for=condition=Ready pod -l app=iam-operator,environment=test -n $TEST_NAMESPACE --timeout=300s
    
    if [ $? -eq 0 ]; then
        log_success "Operator已就绪"
    else
        log_error "Operator启动超时"
        return 1
    fi
}

# 验证功能
verify_functionality() {
    log_info "验证Operator功能..."
    
    # 1. 检查Pod状态
    log_info "检查Pod状态..."
    kubectl get pods -n $TEST_NAMESPACE -l app=iam-operator
    
    # 2. 检查日志
    log_info "检查Operator日志..."
    POD_NAME=$(kubectl get pods -n $TEST_NAMESPACE -l app=iam-operator -o jsonpath='{.items[0].metadata.name}')
    
    if [ -n "$POD_NAME" ]; then
        echo "=== Operator日志 (最近50行) ==="
        kubectl logs -n $TEST_NAMESPACE $POD_NAME --tail=50
        echo "=========================="
    fi
    
    # 3. 检查服务发现
    log_info "验证服务发现功能..."
    kubectl exec -n $TEST_NAMESPACE $POD_NAME -- python3 -c "
import sys
sys.path.append('/app')
from src.clients.iam_service_discovery import IAMServiceDiscoveryClient
try:
    client = IAMServiceDiscoveryClient()
    endpoint = client._discover_iam_endpoint()
    print(f'✅ 服务发现成功: {endpoint}')
except Exception as e:
    print(f'❌ 服务发现失败: {e}')
    sys.exit(1)
" || log_warning "服务发现验证失败"
    
    # 4. 检查ConfigMap解析
    log_info "验证ConfigMap解析功能..."
    kubectl get configmap test-aksk-config -n $TEST_NAMESPACE -o yaml
    
    log_success "功能验证完成"
}

# 显示测试结果
show_results() {
    log_info "测试结果总结..."
    
    echo "================================"
    echo "🎯 测试环境信息"
    echo "================================"
    echo "命名空间: $TEST_NAMESPACE"
    echo "Operator镜像: iam-operator:latest"
    echo ""
    
    echo "📋 部署的资源:"
    kubectl get all -n $TEST_NAMESPACE
    echo ""
    
    echo "⚙️  ConfigMaps:"
    kubectl get configmaps -n $TEST_NAMESPACE
    echo ""
    
    echo "🔐 Secrets:"
    kubectl get secrets -n $TEST_NAMESPACE
    echo ""
    
    echo "🔍 验证命令:"
    echo "  查看日志: kubectl logs -f deployment/iam-operator-test -n $TEST_NAMESPACE"
    echo "  进入Pod: kubectl exec -it deployment/iam-operator-test -n $TEST_NAMESPACE -- /bin/bash"
    echo "  查看配置: kubectl get configmap iam-operator-test-config -n $TEST_NAMESPACE -o yaml"
    echo ""
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    
    # 删除整个测试命名空间
    kubectl delete namespace $TEST_NAMESPACE --ignore-not-found=true
    
    # 删除ClusterRole和ClusterRoleBinding
    kubectl delete clusterrole iam-operator-test --ignore-not-found=true
    kubectl delete clusterrolebinding iam-operator-test --ignore-not-found=true
    
    log_success "测试环境已清理"
}

# 主函数
main() {
    case "${1:-deploy}" in
        "deploy")
            log_info "开始测试部署..."
            check_dependencies
            build_image
            deploy_test
            wait_for_ready
            verify_functionality
            show_results
            log_success "测试部署完成！"
            ;;
        "verify")
            log_info "验证现有部署..."
            verify_functionality
            show_results
            ;;
        "cleanup")
            cleanup
            ;;
        "logs")
            POD_NAME=$(kubectl get pods -n $TEST_NAMESPACE -l app=iam-operator -o jsonpath='{.items[0].metadata.name}')
            if [ -n "$POD_NAME" ]; then
                kubectl logs -f -n $TEST_NAMESPACE $POD_NAME
            else
                log_error "未找到Operator Pod"
            fi
            ;;
        *)
            echo "用法: $0 {deploy|verify|cleanup|logs}"
            echo ""
            echo "命令说明:"
            echo "  deploy  - 完整部署测试环境"
            echo "  verify  - 验证现有部署"
            echo "  cleanup - 清理测试环境"
            echo "  logs    - 查看Operator日志"
            exit 1
            ;;
    esac
}

# 捕获中断信号，确保清理
trap 'log_warning "脚本被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
