# IAM-Operator Helm Chart Values

# 镜像配置
image:
  repository: abc-stack/iam-operator
  tag: "1.0.0"
  pullPolicy: IfNotPresent

# 副本数
replicaCount: 1

# 命名空间配置
namespaceOverride: "base"

# 服务账户配置
serviceAccount:
  create: true
  name: iam-operator
  annotations: {}

# RBAC 配置
rbac:
  create: true

# 资源配置
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

# 节点选择器
nodeSelector: {}

# 容忍度
tolerations: []

# 亲和性
affinity: {}

# 安全上下文
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 1000

# Pod 安全上下文
podSecurityContext:
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
      - ALL

# 服务配置
service:
  type: ClusterIP
  metricsPort: 8080
  healthPort: 8081

# 探针配置
probes:
  liveness:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 30
    timeoutSeconds: 5
    failureThreshold: 3
  readiness:
    enabled: true
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

# IAM API 配置
iamApi:
  url: "http://iam-manage-xian.console.svc.cluster.local:8468"
  timeout: 30

# 处理配置
processing:
  timeout: 30
  retryInterval: 60
  maxRetries: 3

# 监控配置
metrics:
  enabled: true
  port: 8080
  path: /metrics

# 健康检查配置
health:
  port: 8081
  livenessPath: /healthz
  readinessPath: /ready

# 日志配置
logging:
  level: INFO

# 目标命名空间配置
targetNamespaces:
  - security
  - console
  - billing

# 状态存储配置
stateStorage:
  configMapName: iam-operator-state
  configMapNamespace: base

# CRD 配置
crd:
  create: true

# 监控集成
monitoring:
  serviceMonitor:
    enabled: false
    namespace: monitoring
    interval: 30s
    scrapeTimeout: 10s
    labels: {}

# Pod 注解
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8080"
  prometheus.io/path: "/metrics"

# Pod 标签
podLabels: {}

# 环境变量
env: []

# 额外的环境变量（从 ConfigMap 或 Secret）
envFrom: []

# 挂载卷
volumes: []
volumeMounts: []

# 初始化容器
initContainers: []

# Sidecar 容器
sidecars: []

# 终止宽限期
terminationGracePeriodSeconds: 30

# 更新策略
updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 1
    maxSurge: 1

# 自动扩缩容（HPA）
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Pod 中断预算（PDB）
podDisruptionBudget:
  enabled: false
  minAvailable: 1

# 网络策略
networkPolicy:
  enabled: false
