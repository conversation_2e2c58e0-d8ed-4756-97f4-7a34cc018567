apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "iam-operator.fullname" . }}-config
  namespace: {{ include "iam-operator.namespace" . }}
  labels:
    {{- include "iam-operator.labels" . | nindent 4 }}
data:
  # IAM API 配置
  iam-api-url: {{ .Values.iamApi.url | quote }}
  iam-api-timeout: {{ .Values.iamApi.timeout | quote }}

  # IAM 服务发现配置
  iam-service-discovery-enabled: {{ .Values.iamApi.serviceDiscovery.enabled | quote }}
  iam-service-namespace: {{ .Values.iamApi.serviceDiscovery.namespace | quote }}
  iam-service-name: {{ .Values.iamApi.serviceDiscovery.serviceName | quote }}
  
  # 处理配置
  processing-timeout: {{ .Values.processing.timeout | quote }}
  retry-interval: {{ .Values.processing.retryInterval | quote }}
  max-retries: {{ .Values.processing.maxRetries | quote }}
  
  # 监控配置
  metrics-port: {{ .Values.metrics.port | quote }}
  health-check-port: {{ .Values.health.port | quote }}
  
  # 日志配置
  log-level: {{ .Values.logging.level | quote }}
  
  # 目标命名空间（逗号分隔）
  target-namespaces: {{ join "," .Values.targetNamespaces | quote }}
  
  # 状态存储配置
  state-configmap-name: {{ .Values.stateStorage.configMapName | quote }}
  state-configmap-namespace: {{ .Values.stateStorage.configMapNamespace | quote }}
