## 问题定义

### 抽象定义

1. 通过前置的输入，各产品的aksk/password 的几元组，动态的更新IAM 部署在k8s 的xdb 数据库表，用户通过前置（事先约定）的认证信息使用各种产品

## 输入、输出、中间过程

1. 输入
    1. configMap，由蓝图（程广连）维护values.yaml 文件，经过苍竹的部署和映射后生成各种configMap
    2. CRD
    3. **当前模式 (ConfigMap 被动监听)**
        1. ConfigMap 变化 → Controller 检测 → 处理所有 ConfigMaps → 过滤 IAM 凭据 → 验证
        2. 用户创建 IAMCredential → Controller 监听 → 精确获取指定凭据 → 验证 → 状态更新
2. 输出：更新iam xdb 的AKSK password
3. 中间过程，operator 的处理

## 困难

1. k8s → 转Golang 后，二进制文件打不进去，其他方案
2. 运行后的部署情况 → 简单，依赖，测试
3. iam - service 的接口与本地运行问题
4. 监听 namespaces 的配置

## 前置知识

1. k8s - operator what how why
2. 系统设计部分
3. 幂等
4. 混合输入源 → 渐进式迁移

## 其他侧面

1. 是否我有没有遇到的坑？
2. 要部署的几个环境？

## TODO

1. iam- manage 的本地运行（数据库，redis），接口开发
2. operator 的部署环节
3. DDL

---

**补充文档**：详细的设计思路与验证流程补充请参考 `docs/设计思路与验证流程补充.md`