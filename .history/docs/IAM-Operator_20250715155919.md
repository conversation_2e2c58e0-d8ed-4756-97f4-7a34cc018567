# 背景：[产品使用ak、sk需要动态注入iam数据库问题](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/ZIeThHHOUA/pt0u1Vr4UAUQ7i?t=mention&mt=doc&dt=doc)

每个云产品都会用到 ak/sk/password 去完成服务间调用，这些 ak/sk/password 在初次交付时由  蓝图平台 和 IAM 统一分配给各个产品方，当前存在两种交付形式，分别为 charm 和 chart 包。本次改造主要关注新云底座 chart 包部署交付时 ak/sk 分配的自动化。

# chart 现状：[自动注入 keys.json 对应 SQL](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/vSZMZk-ELQ/h-BSEqiwYzoOQM?t=mention&mt=doc&dt=doc)

每个产品用到的 ak/sk 明文事先会由蓝图统一生成，并注入到每个产品的 values.yaml 中；

然后蓝图会提供这些 ak/sk 对应的 SQL 交给沧竹平台创建对应的 configmap；

IAM 则基于这个 configmap 拿到 SQL 文件注入到 iam 数据库，保证蓝图分配给产品方的 ak/sk 可用；

针对已交付的环境，如果有新增产品，依然由蓝图产出明文 ak/sk/password，此时不会在产出 SQL 文件，此时完全由 IAM 侧负责生成 ak/sk/password 对应的 SQL，并注入到 IAM 数据库

所以云底座同学提供了 Operator 方案，基于 IAM-Operator ，让产品方事先声明要用到的服务号，同时 IAM 提供注入 ak/sk/password 的接口，在升级服务时请求 IAM 这些接口进行 ak/sk 的注入，省去之前的一系列 SQL 动作

# 其它 Operator 参考实现

[基于Operator 实现XDB账户、DB动态注册方案](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/ZIeThHHOUA/vZZxibiHrmrzw9?t=mention&mt=doc&dt=doc)