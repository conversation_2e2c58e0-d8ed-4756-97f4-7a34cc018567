# IAM-Operator 架构分析与技术方案

## 背景

基于对现有 Kubernetes 集群中 XDB-Operator 和 IAM 服务的调研，本文档分析 IAM-Operator 的技术架构和实现方案。

## 现状调研结果

### 1. XDB-Operator 运行状态

通过 `kubectl get pod -A | grep operator` 确认：

```bash
base               xdb-operator-xa-0                1/1     Running
base               xdb-operator-xa-1                1/1     Running  
base               xdb-operator-xa-2                1/1     Running
```

**关键发现**：
- XDB-Operator **确实在运行**，部署在 `base` 命名空间
- 采用 3 副本高可用部署（StatefulSet）
- 基于 Python Kopf 框架实现
- `xdb-operator` 命名空间存在但为空，用于权限隔离

### 2. XDB-Operator 功能边界

**管理的资源类型**：
- `XDBFusionCluster`：XDB 集群管理
- `XDBUser`：数据库用户管理
- `XDBDatabase`：数据库管理
- `XDBBackup`、`XDBBackupStrategy`：备份管理
- `XDBProxy`：代理管理

**典型 XDBUser 资源结构**：
```yaml
spec:
  allowedHosts: ["%"]
  clusterRef:
    name: xdb-global-default
    namespace: base
  password:
    key: password
    secretName: xdb-global-default-iam-manage-xian
  permissions:
  - permissions: ALL
    schema: bce_iam
  user: iam_manage_xian
  wrFlag: 2
```

### 3. IAM 服务部署状态

IAM 相关服务已部署在 `console` 命名空间：
- `iam-manage-xian`：IAM 管理服务
- `iam-openapi`：IAM OpenAPI 服务  
- `iam-proxy-xian`：IAM 代理服务

**服务暴露方式**：
```bash
iam-manage-xian     LoadBalancer   **************   ************    8468:32688/TCP
iam-openapi         LoadBalancer   **************   ************    8480:32707/TCP
```

### 4. IAM 数据库结构分析

通过 `bce-iam-aksk` ConfigMap 分析得出 IAM 数据库主要表结构：

**`user` 表**：
- `id`：用户 ID（UUID 格式）
- `password`：加密后的密码

**`credential` 表**：
- `id`：凭据 ID
- `user_id`：关联的用户 ID
- `project_id`：项目 ID
- `blob`：JSON 格式的凭据信息
  - `access`：Access Key (AK)
  - `secret_encrypted`：加密后的 Secret Key (SK)
  - `trust_id`：信任 ID
- `type`：凭据类型（如 "ec2"）
- `modify_time`：修改时间

**重要发现**：IAM 数据库（`bce_iam`）运行在 XDB 集群上，由 XDB-Operator 管理。

## 技术方案对比

### 方案一：直接操作数据库（不推荐）

**技术可行性**：
```python
# 技术上可行的实现
def update_credentials_directly():
    connection = mysql.connect(
        host="xdb-global-default.base.svc.cluster.local",
        user="iam_manage_xian", 
        password="from-secret",
        database="bce_iam"
    )
    
    cursor.execute("UPDATE user SET password = %s WHERE id = %s", (new_password, user_id))
    cursor.execute("INSERT INTO credential (...) VALUES (...)")
```

**主要劣势**：

1. **架构违反原则**
   - 违反微服务边界：IAM 数据库应该只由 IAM 服务操作
   - 绕过业务逻辑：IAM 服务中可能有复杂的业务逻辑、验证、审计等
   - 破坏数据一致性：可能与 IAM 服务的操作产生冲突

2. **安全风险**
   - 权限过大：Operator 需要数据库直接访问权限
   - 审计缺失：绕过了 IAM 服务的审计日志
   - 加密问题：直接操作可能无法正确处理加密逻辑

3. **维护问题**
   - 紧耦合：Operator 与数据库表结构强耦合
   - 版本兼容：数据库 schema 变更时 Operator 需要同步更新
   - 调试困难：问题排查时需要同时检查 Operator 和 IAM 服务

### 方案二：调用 IAM API 服务（推荐）

**架构图**：
```mermaid
graph TD
    A[IAM-Operator] --> B[IAM API 服务]
    B --> C[bce_iam 数据库 on XDB]
    
    D[XDB-Operator] --> E[XDB 集群管理]
    E --> C
    
    F[服务升级事件] --> A
    G[服务号配置] --> A
```

**实现方式**：
```python
class IAMClient:
    def __init__(self):
        self.base_url = "http://iam-openapi.console.svc.cluster.local:8480"
        self.manage_url = "http://iam-manage-xian.console.svc.cluster.local:8468"
    
    def create_service_credentials(self, service_name: str, permissions: list):
        """创建服务凭据"""
        payload = {
            "service_name": service_name,
            "permissions": permissions,
            "action": "create"
        }
        response = requests.post(f"{self.base_url}/api/v1/credentials", json=payload)
        return response.json()
    
    def get_credentials(self, service_name: str):
        """获取服务凭据"""
        response = requests.get(f"{self.base_url}/api/v1/credentials/{service_name}")
        return response.json()
```

**主要优势**：
1. **职责分离**：XDB-Operator 管理集群，IAM 服务管理业务逻辑
2. **API 稳定性**：IAM API 比数据库 schema 更稳定
3. **业务完整性**：通过 IAM 服务确保所有业务规则得到执行
4. **安全性**：遵循最小权限原则，通过 API 认证
5. **可维护性**：松耦合，便于独立演进

## 推荐架构方案

### 1. IAM-Operator 部署架构

**部署位置**：`base` 命名空间（与 XDB-Operator 同命名空间）
**高可用**：3 副本部署
**权限模型**：使用 ClusterRole 和 ClusterRoleBinding
**存储**：使用 PVC 存储配置数据

### 2. IAM-Operator 核心功能

**监听事件**：
- Kubernetes 中的 Deployment/StatefulSet 更新事件
- 自定义资源变化事件

**处理流程**：
1. 监听服务升级事件
2. 读取服务号预声明配置
3. 调用 IAM API 获取/创建 ak/sk/password
4. 将凭据注入到服务的 Secret 中
5. 触发服务重启以使用新凭据

### 3. 与现有系统的集成

**与 XDB-Operator 的关系**：
- 独立运行，各自负责不同的资源管理
- XDB-Operator：管理 XDB 集群和业务数据库用户
- IAM-Operator：管理 IAM 认证凭据

**与 IAM 服务的关系**：
- 通过 HTTP REST API 调用已部署的 IAM 服务
- 利用 Kubernetes 内部服务发现机制
- 无需额外的网络配置或权限

### 4. 新增资源定义

**IAMServiceAccount CRD**：
```yaml
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: my-service-account
  namespace: default
spec:
  serviceName: my-service
  permissions:
    - resource: "database"
      actions: ["read", "write"]
  credentialType: "ec2"
```

**IAMCredentialInjection CRD**：
```yaml
apiVersion: iam.example.com/v1
kind: IAMCredentialInjection
metadata:
  name: my-service-injection
  namespace: default
spec:
  serviceAccountRef:
    name: my-service-account
  targetSecret:
    name: my-service-credentials
    keys:
      accessKey: "ACCESS_KEY"
      secretKey: "SECRET_KEY"
      password: "PASSWORD"
```

## 实施建议

1. **第一阶段**：实现基本的 IAM API 调用功能
2. **第二阶段**：添加服务升级事件监听
3. **第三阶段**：实现自动凭据注入和轮换
4. **第四阶段**：添加监控、告警和审计功能

## 总结

基于对现有系统的深入分析，推荐采用调用 IAM API 服务的架构方案。虽然直接操作数据库在技术上可行，但考虑到架构原则、安全性和可维护性，通过 IAM API 服务是更好的选择。这种方案既能满足业务需求，又能保持系统的整体架构清晰和可维护性。
