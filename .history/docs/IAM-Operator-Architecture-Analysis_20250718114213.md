# IAM-Operator 架构分析与技术方案

## 背景

基于对现有 Kubernetes 集群中 XDB-Operator 和 IAM 服务的调研，本文档分析 IAM-Operator 的技术架构和实现方案。

## 现状调研结果

### 1. XDB-Operator 运行状态

通过 `kubectl get pod -A | grep operator` 确认：

```bash
base               xdb-operator-xa-0                1/1     Running
base               xdb-operator-xa-1                1/1     Running  
base               xdb-operator-xa-2                1/1     Running
```

**关键发现**：
- XDB-Operator **确实在运行**，部署在 `base` 命名空间
- 采用 3 副本高可用部署（StatefulSet）
- 基于 Python Kopf 框架实现
- `xdb-operator` 命名空间存在但为空，用于权限隔离

### 2. XDB-Operator 功能边界

**管理的资源类型**：
- `XDBFusionCluster`：XDB 集群管理
- `XDBUser`：数据库用户管理
- `XDBDatabase`：数据库管理
- `XDBBackup`、`XDBBackupStrategy`：备份管理
- `XDBProxy`：代理管理

**典型 XDBUser 资源结构**：
```yaml
spec:
  allowedHosts: ["%"]
  clusterRef:
    name: xdb-global-default
    namespace: base
  password:
    key: password
    secretName: xdb-global-default-iam-manage-xian
  permissions:
  - permissions: ALL
    schema: bce_iam
  user: iam_manage_xian
  wrFlag: 2
```

### 3. IAM 服务部署状态

IAM 相关服务已部署在 `console` 命名空间：
- `iam-manage-xian`：IAM 管理服务
- `iam-openapi`：IAM OpenAPI 服务  
- `iam-proxy-xian`：IAM 代理服务

**服务暴露方式**：
```bash
iam-manage-xian     LoadBalancer   **************   ************    8468:32688/TCP
iam-openapi         LoadBalancer   **************   ************    8480:32707/TCP
```

### 4. IAM 数据库结构分析

通过 `bce-iam-aksk` ConfigMap 分析得出 IAM 数据库主要表结构：

**`user` 表**：
- `id`：用户 ID（UUID 格式）
- `password`：加密后的密码

**`credential` 表**：
- `id`：凭据 ID
- `user_id`：关联的用户 ID
- `project_id`：项目 ID
- `blob`：JSON 格式的凭据信息
  - `access`：Access Key (AK)
  - `secret_encrypted`：加密后的 Secret Key (SK)
  - `trust_id`：信任 ID
- `type`：凭据类型（如 "ec2"）
- `modify_time`：修改时间

**重要发现**：IAM 数据库（`bce_iam`）运行在 XDB 集群上，由 XDB-Operator 管理。

## 技术方案对比

### 方案一：直接操作数据库（不推荐）

**技术可行性**：
```python
# 技术上可行的实现
def update_credentials_directly():
    connection = mysql.connect(
        host="xdb-global-default.base.svc.cluster.local",
        user="iam_manage_xian", 
        password="from-secret",
        database="bce_iam"
    )
    
    cursor.execute("UPDATE user SET password = %s WHERE id = %s", (new_password, user_id))
    cursor.execute("INSERT INTO credential (...) VALUES (...)")
```

**主要劣势**：

1. **架构违反原则**
   - 违反微服务边界：IAM 数据库应该只由 IAM 服务操作
   - 绕过业务逻辑：IAM 服务中可能有复杂的业务逻辑、验证、审计等
   - 破坏数据一致性：可能与 IAM 服务的操作产生冲突

2. **安全风险**
   - 权限过大：Operator 需要数据库直接访问权限
   - 审计缺失：绕过了 IAM 服务的审计日志
   - 加密问题：直接操作可能无法正确处理加密逻辑

3. **维护问题**
   - 紧耦合：Operator 与数据库表结构强耦合
   - 版本兼容：数据库 schema 变更时 Operator 需要同步更新
   - 调试困难：问题排查时需要同时检查 Operator 和 IAM 服务

### 方案二：调用 IAM API 服务（推荐）

**架构图**：
```mermaid
graph TD
    A[IAM-Operator] --> B[IAM API 服务]
    B --> C[bce_iam 数据库 on XDB]
    
    D[XDB-Operator] --> E[XDB 集群管理]
    E --> C
    
    F[服务升级事件] --> A
    G[服务号配置] --> A
```

**实现方式**：
```python
class IAMClient:
    def __init__(self):
        self.base_url = "http://iam-openapi.console.svc.cluster.local:8480"
        self.manage_url = "http://iam-manage-xian.console.svc.cluster.local:8468"
    
    def create_service_credentials(self, service_name: str, permissions: list):
        """创建服务凭据"""
        payload = {
            "service_name": service_name,
            "permissions": permissions,
            "action": "create"
        }
        response = requests.post(f"{self.base_url}/api/v1/credentials", json=payload)
        return response.json()
    
    def get_credentials(self, service_name: str):
        """获取服务凭据"""
        response = requests.get(f"{self.base_url}/api/v1/credentials/{service_name}")
        return response.json()
```

**主要优势**：
1. **职责分离**：XDB-Operator 管理集群，IAM 服务管理业务逻辑
2. **API 稳定性**：IAM API 比数据库 schema 更稳定
3. **业务完整性**：通过 IAM 服务确保所有业务规则得到执行
4. **安全性**：遵循最小权限原则，通过 API 认证
5. **可维护性**：松耦合，便于独立演进

## 推荐架构方案

### 1. IAM-Operator 部署架构

**部署位置**：`base` 命名空间（与 XDB-Operator 同命名空间）
**高可用**：3 副本部署
**权限模型**：使用 ClusterRole 和 ClusterRoleBinding
**存储**：使用 PVC 存储配置数据

### 2. IAM-Operator 核心功能

**监听事件**：
- Kubernetes 中的 Deployment/StatefulSet 更新事件
- 自定义资源变化事件

**处理流程**：
1. 监听服务升级事件
2. 读取服务号预声明配置
3. 调用 IAM API 获取/创建 ak/sk/password
4. 将凭据注入到服务的 Secret 中
5. 触发服务重启以使用新凭据

### 3. 与现有系统的集成

**与 XDB-Operator 的关系**：
- 独立运行，各自负责不同的资源管理
- XDB-Operator：管理 XDB 集群和业务数据库用户
- IAM-Operator：管理 IAM 认证凭据

**与 IAM 服务的关系**：
- 通过 HTTP REST API 调用已部署的 IAM 服务
- 利用 Kubernetes 内部服务发现机制
- 无需额外的网络配置或权限

### 4. 新增资源定义

**IAMServiceAccount CRD**：
```yaml
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: my-service-account
  namespace: default
spec:
  serviceName: my-service
  permissions:
    - resource: "database"
      actions: ["read", "write"]
  credentialType: "ec2"
```

**IAMCredentialInjection CRD**：
```yaml
apiVersion: iam.example.com/v1
kind: IAMCredentialInjection
metadata:
  name: my-service-injection
  namespace: default
spec:
  serviceAccountRef:
    name: my-service-account
  targetSecret:
    name: my-service-credentials
    keys:
      accessKey: "ACCESS_KEY"
      secretKey: "SECRET_KEY"
      password: "PASSWORD"
```

## 实施建议

1. **第一阶段**：实现基本的 IAM API 调用功能
2. **第二阶段**：添加服务升级事件监听
3. **第三阶段**：实现自动凭据注入和轮换
4. **第四阶段**：添加监控、告警和审计功能

## Operator 部署方式和触发机制

### 1. Operator 部署方式

**当前 XDB-Operator 的部署方式**：
```yaml
# StatefulSet 部署
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: xdb-operator-xa
  namespace: base
spec:
  replicas: 3
  serviceName: xdb-operator-xa
  template:
    spec:
      containers:
      - name: xdb-operator
        image: xdb-operator:latest
        command: ["/usr/local/bin/kopf", "run", "/xdb/xdboperator/controller/operator.py", "-A", "--verbose"]
        ports:
        - containerPort: 8080
          name: healthz
```

**常见 Operator 部署方式**：

1. **Deployment 部署**（最常见）
   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: iam-operator
     namespace: base
   spec:
     replicas: 1  # 通常单副本，避免并发冲突
     selector:
       matchLabels:
         app: iam-operator
     template:
       spec:
         containers:
         - name: iam-operator
           image: iam-operator:v1.0.0
           env:
           - name: IAM_API_URL
             value: "http://iam-openapi.console.svc.cluster.local:8480"
   ```

2. **StatefulSet 部署**（需要持久化状态时）
   - 适用于需要持久化存储的场景
   - 如 XDB-Operator 需要存储备份数据

3. **DaemonSet 部署**（节点级别操作）
   - 适用于需要在每个节点运行的场景
   - 如网络、存储相关的 Operator

### 2. Operator 触发方式

**基于事件驱动的触发机制**：

1. **Watch API 触发**（最常见）
   ```python
   import kopf

   @kopf.on.create('apps', 'v1', 'deployments')
   def deployment_created(spec, name, namespace, **kwargs):
       """当 Deployment 创建时触发"""
       if has_iam_annotation(spec):
           create_service_credentials(name, namespace)

   @kopf.on.update('apps', 'v1', 'deployments')
   def deployment_updated(spec, name, namespace, **kwargs):
       """当 Deployment 更新时触发"""
       if has_iam_annotation(spec):
           update_service_credentials(name, namespace)
   ```

2. **自定义资源触发**
   ```python
   @kopf.on.create('iam.example.com', 'v1', 'iamserviceaccounts')
   def iam_service_account_created(spec, name, namespace, **kwargs):
       """当 IAMServiceAccount 创建时触发"""
       service_name = spec.get('serviceName')
       permissions = spec.get('permissions', [])
       create_iam_credentials(service_name, permissions)
   ```

3. **定时触发**
   ```python
   @kopf.timer('iam.example.com', 'v1', 'iamserviceaccounts', interval=3600)
   def rotate_credentials(spec, name, namespace, **kwargs):
       """每小时检查并轮换凭据"""
       if should_rotate_credentials(name):
           rotate_service_credentials(name, namespace)
   ```

4. **Webhook 触发**
   ```python
   # 通过 Admission Controller 在资源创建/更新时触发
   @kopf.on.mutate('apps', 'v1', 'deployments')
   def inject_iam_credentials(spec, **kwargs):
       """在 Deployment 创建时注入 IAM 凭据"""
       if has_iam_annotation(spec):
           inject_credentials_to_spec(spec)
   ```

### 3. 服务号概念澄清

基于你的新理解，**服务号 = 服务 + 账号**：

**服务号的本质**：
- 各产品/服务在 IAM 系统中的身份标识
- 包含 AK/SK、STS Token 等认证凭据
- 通过 IAM 统一管控和分发

**典型场景**：
```yaml
# 产品服务的服务号配置
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: billing-service-account
  namespace: billing
spec:
  serviceName: "bce-billing"           # 产品服务名
  serviceNumber: "SN-BILLING-001"      # 服务号标识
  credentialTypes:
    - "aksk"      # Access Key / Secret Key
    - "sts"       # Security Token Service
    - "password"  # 数据库密码
  permissions:
    - resource: "database:bce_billing"
      actions: ["read", "write"]
    - resource: "storage:billing-bucket"
      actions: ["read", "write", "delete"]
  rotationPolicy:
    enabled: true
    interval: "30d"  # 30天轮换一次
```

### 4. IAM-Operator 触发场景示例

**场景一：服务部署时自动创建服务号**
```python
@kopf.on.create('apps', 'v1', 'deployments')
def on_deployment_create(spec, name, namespace, **kwargs):
    # 检查是否有 IAM 注解
    annotations = spec.get('template', {}).get('metadata', {}).get('annotations', {})
    if 'iam.example.com/service-account' in annotations:
        service_account_name = annotations['iam.example.com/service-account']

        # 调用 IAM API 创建服务号
        iam_client = IAMClient()
        credentials = iam_client.create_service_credentials(
            service_name=name,
            service_number=f"SN-{name.upper()}-001",
            permissions=get_permissions_from_annotation(annotations)
        )

        # 创建 Secret 存储凭据
        create_credential_secret(name, namespace, credentials)
```

**场景二：定时轮换服务号凭据**
```python
@kopf.timer('iam.example.com', 'v1', 'iamserviceaccounts', interval=86400)  # 每天检查
def rotate_service_credentials(spec, name, namespace, **kwargs):
    rotation_policy = spec.get('rotationPolicy', {})
    if not rotation_policy.get('enabled', False):
        return

    last_rotation = get_last_rotation_time(name, namespace)
    interval_days = parse_interval(rotation_policy.get('interval', '30d'))

    if should_rotate(last_rotation, interval_days):
        # 调用 IAM API 轮换凭据
        iam_client = IAMClient()
        new_credentials = iam_client.rotate_service_credentials(
            service_number=spec.get('serviceNumber')
        )

        # 更新 Secret
        update_credential_secret(name, namespace, new_credentials)

        # 触发服务重启
        restart_related_deployments(name, namespace)
```

### 5. Demo 示例

**完整的 IAM-Operator Demo**：

```python
import kopf
import requests
from kubernetes import client, config

class IAMOperator:
    def __init__(self):
        self.iam_api_url = "http://iam-openapi.console.svc.cluster.local:8480"
        config.load_incluster_config()
        self.k8s_client = client.ApiClient()

    def create_service_credentials(self, service_name, service_number, permissions):
        """调用 IAM API 创建服务号凭据"""
        payload = {
            "service_name": service_name,
            "service_number": service_number,
            "permissions": permissions,
            "credential_types": ["aksk", "password"]
        }

        response = requests.post(
            f"{self.iam_api_url}/api/v1/service-accounts",
            json=payload
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Failed to create credentials: {response.text}")

# Operator 事件处理
iam_operator = IAMOperator()

@kopf.on.create('iam.example.com', 'v1', 'iamserviceaccounts')
def create_iam_service_account(spec, name, namespace, **kwargs):
    """创建 IAM 服务号"""
    try:
        service_name = spec.get('serviceName')
        service_number = spec.get('serviceNumber')
        permissions = spec.get('permissions', [])

        # 调用 IAM API
        credentials = iam_operator.create_service_credentials(
            service_name, service_number, permissions
        )

        # 创建 Kubernetes Secret
        secret_data = {
            'access_key': credentials['access_key'],
            'secret_key': credentials['secret_key'],
            'password': credentials['password']
        }

        create_secret(name + '-credentials', namespace, secret_data)

        kopf.info(f"Successfully created IAM service account: {service_number}")

    except Exception as e:
        kopf.error(f"Failed to create IAM service account: {str(e)}")
        raise

def create_secret(name, namespace, data):
    """创建 Kubernetes Secret"""
    v1 = client.CoreV1Api()
    secret = client.V1Secret(
        metadata=client.V1ObjectMeta(name=name, namespace=namespace),
        string_data=data
    )
    v1.create_namespaced_secret(namespace=namespace, body=secret)
```

**使用示例**：
```yaml
# 1. 创建 IAM 服务号资源
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: billing-service
  namespace: billing
spec:
  serviceName: "bce-billing"
  serviceNumber: "SN-BILLING-001"
  permissions:
    - resource: "database:bce_billing"
      actions: ["read", "write"]

---
# 2. 在 Deployment 中使用凭据
apiVersion: apps/v1
kind: Deployment
metadata:
  name: billing-service
  namespace: billing
spec:
  template:
    spec:
      containers:
      - name: billing
        image: billing:latest
        env:
        - name: ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: billing-service-credentials
              key: access_key
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: billing-service-credentials
              key: secret_key
```

这样的设计让服务号的管理变得自动化和标准化，产品团队只需要声明需要什么权限，IAM-Operator 会自动处理凭据的创建、分发和轮换。

## 总结

基于对现有系统的深入分析，推荐采用调用 IAM API 服务的架构方案。虽然直接操作数据库在技术上可行，但考虑到架构原则、安全性和可维护性，通过 IAM API 服务是更好的选择。这种方案既能满足业务需求，又能保持系统的整体架构清晰和可维护性。
