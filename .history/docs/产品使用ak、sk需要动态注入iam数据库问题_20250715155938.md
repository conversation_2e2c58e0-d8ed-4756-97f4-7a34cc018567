### 一、背景：

在charm包部署的时候，每次新项目部署的时候，在xdb-init-iam部署完成后，需要执行后置操作操作往 bce_iam 数据库注入 ak/sk，同时生成 keys.json 放到沧竹规划中。

### 当前现状

- *已经对齐操作：**keys.json 由蓝图托管，根据业务接入chart改造，由蓝图注入到 最终生成的value.yaml文件中。业务只需要获取使用即可，参考下图

[](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=8df136296aff4cb8a9f250adf23d7f77&docGuid=pt0u1Vr4UAUQ7i)

[](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=4f86ee8e69be4794899603382ff868a2&docGuid=pt0u1Vr4UAUQ7i)

- *未对齐操作：**针对经过蓝图转化加密后的 账户信息，需要注册到iam的数据库中，才能供给业务方使用，目前该细节几方没有对齐清楚。

**各方诉求：**

1. 由于苍竹已经没有后置脚本任务，需要自动化的完整当前初始化操作
2. 需要与xdb-data-init 做好联动，完成数据灌入

二、大致方案思路

[](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=6c91c48a619b4f6ba45f20c0ef98dcae&docGuid=pt0u1Vr4UAUQ7i)

可能存在的问题：

第二个方案 挂载到pod内，需要解决每次sql文件的幂等性问题。

```
UPDATE user SET password = "$6$rounds=40000$ekZHQbQ0$CXBupWGFE0xrOg59XOReS7lvQEAqj3IFWdz.scWrn.VVaz3GXfeVu.et8Mwi.ouVaAtQr1mvwplNryYnb7zB50" WHERE (id = "00cd46532a024cf8a91feb6dfb83e5fa");
DELETE FROM credential WHERE (user_id = "00cd46532a024cf8a91feb6dfb83e5fa");
INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time) VALUES ("bdb06936340d1b8441a248323d62af9551a6bb2adbcf17889c1100f936ad73e6", "00cd46532a024cf8a91feb6dfb83e5fa", "0557fd0d0d244cf7801dba4cadf214c5", "\\"{\\\\\\"trust_id\\\\\\":null,\\\\\\"access\\\\\\":\\\\\\"c55ef31dd8434693ae46656d744382a5\\\\\\",\\\\\\"secret_encrypted\\\\\\":\\\\\\"0fe7a268b7479d19858252c2e59ea57b7e9c9a9fdd02e8eefe160848697abab2774c482bdaea58b390d5080f3dcfe089748a23b0a16f6058763f10d52ef9ef4e\\\\\\"}\\"", "ec2", "{}", "2024-07-22 10:50:00");

```

方案三：需要开发operator 暂时不考虑