### 一、背景：

目前 xdb 初步完成了chart 改造，使用sts类型完成实现了集群的搭建。但是xdb配套功能 例如 DB的创建，用户的创建。依旧在云原生下没有很好的方案。在charm 包版本下，是通过 xdb部署后的后置步骤根据多个脚本完成初始化（创建脚本+用户访问探测脚本）。并且无法直观查看用户、DB资源。当访问不通的时候，需要DBA介入排查。

目标：通过开发xdb-operator 实现 xdb的账户、DB的创建管理。

详细代码：[文件页：baidu/cloudbed/xdb-operator  *master](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/cloudbed/xdb-operator/tree/master?t=mention&mt=doc&dt=sdk)

[](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=0982835535154142b9141c465f438ce6&docGuid=vZZxibiHrmrzw9)

注意！注意！注意！

1、在k8s中 CR的资源名称，只能下中划线，不能有下划线。

2、蓝图规划的应用名称都是以中划线隔离的

3、xdb规划的DB、user，只能是下划线，不能有中划线。蓝图

**所以结论：**

**1、使用 应用名 + DB Name  或者作为CR name的时候，需要对DB name的 下划线转为中划线，生成唯一的 CR Name。**

**2、对于CR中Spec属性中的  用户名、DB 名，需要保持下划线，与蓝图渲染保持一致。**

---

### 二、CR案例一：XDB 融合版本集群

注意：目前版本operator 暂未实现 xdb集群的自动化创建 申请的CR 只做信息存储，供给其他CR 查询，最终集群的搭建依赖helm chart 的安装结果

```
apiVersion: cloudbed.abcstack.com/v1
kind: XDBFusionCluster   #xdb 融合模式，dbproxy、mysql、zm、xagent 等是混布在一个pod中
metadata:
  name: xdb-lxz-a        #与helm chart的 app_name 对应
  namespace: xa-xdb
spec:
  instances: 3
  mysqlVersion: 5.7     #注意这里是 number
  appID: xdb_init_lxz
  clusterID: xdb_init_lxz_0000
  ZKDomain: zookeeper-a.dev6.abcstackint.com:2181

```

- *需求：**需要在XDB 的Chart 包中增加 Extension 文件夹，里面存放各产品扩展自定义的模板。在使用 chart-tool 工具的 bulid 时候，将本chart的文件 mv到 总的template的目录中，以便在helm chart 安装的时候 能够顺利加载运行自定义资源。[@史孝开](https://ku.baidu-int.com/?t=mention&mt=contact&id=6c69ecb0-3f66-11ef-8004-4bdc0287123d)
- *案例：**使用蓝图规划的app_name 作为内部appID、clusterId

```
{{- if eq .Chart.Name "xdb" }}
{{- $namespace := .Values.appspace.namespace }}
{{- $clusterName := .Values.appspace.charts.platformConfigVals.appName }}
{{- $clusterInstances := .Values.appspace.replica }}
{{- $existInK8s := (lookup "cloudbed.abcstack.com/v1" "XDBFusionCluster" $namespace $clusterName) }}
{{- if $existInK8s }}
{{- else }}
---
apiVersion: cloudbed.abcstack.com/v1
kind: XDBFusionCluster      # xdb 融合模式，dbproxy\\mysql\\zm\\xagent 等是混布在一个pod中
metadata:
  name: {{ $clusterName }}        # 与 Helm chart 的 app_name 对应
  namespace: {{ $namespace }}
  annotations:
    xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy: retain # When the MysqlUser is deleted, the MySQL user will be preserved
    helm.sh/resource-policy: keep
spec:
  instances: {{ $clusterInstances }}
  {{- $replaceAppName := $clusterName | replace "-" "" }}
  mysqlVersion: {{ .Values.appspace.charts.configVals.mysql_version }}
  appID: {{ $replaceAppName }}
  clusterID: {{ printf "%s_0000" $replaceAppName }}
  ZKDomain: {{ .Values.appspace.charts.configVals.zookeeper_svc_domain }}:{{ .Values.appspace.charts.configVals.zookeeper_port }}
{{- end }}
{{- end }}

```

[](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=94a6cd2739914ec99ad946c7077ccc65&docGuid=vZZxibiHrmrzw9)

### 三、CR案例二：XDB 创建DB

```
apiVersion: cloudbed.abcstack.com/v1
kind: XDBDataBase
metadata:
  name: {xdbcluster_name}-{db_name}    #注意：由于可能多套xdb 都部署在同一个ns。多个集群中有同名的DB,所以这里名称需要带上集群名称保证唯一
  namespace: base   #注意：经过讨论，按产品划分ns后，cr的资源也放入当前的产品的 ns中
  annotations:
      xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy: retain # When the MysqlUDB is deleted, the MySQL DB will be preserved
spec:
  database: bce_rds        #注意：当前集群的需要创建的db名称
  clusterRef:              #注意：当前db创建的关联的XDB集群信息
    name: xdb-lxz-a        #注意： 集群名称，是XDBFusionCluster的 CR 中 name 字段
    namespace: xa-xdb      #注意： 集群所属的ns，是XDBFusionCluster的 CR 中 ns 字段

```

需求：需要在chart-tools的 common/template/ 中新增 例如 xdb-db-user.yaml（待讨论） 文件，需要各业务在chart改造的时候，如果有声明db，就需要转化对应的CR， 提交执行。

案例：

### 三、CR案例三：XDB 创建 User

```

apiVersion: v1
kind: Secret
metadata:
  name: {xdbcluster_name}-{app_name}  #注意：同用户CR名称即可
  namespace: {product-app.namespace}  #注意：同下面user的 cr 解释
data:
  PASSWORD: bXlzcWwtcGFzc3dvcmQtZm9yLXVzZXI=   #注意：蓝图规划密码，转化为base64字符串

---
apiVersion: cloudbed.abcstack.com/v1
kind: XDBUser
metadata:
  name: {xdbcluster_name}-{app_name} #注意： 按照约束，不同业务访问同db，要求按产品划分账户，所以这里名称最好是按照 xdb集群名称+ 产品名称生成
  namespace: {product-app.namespace} #注意：经过讨论，按产品划分ns后，cr的资源也放入当前的产品的 ns中
  annotations:
      xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy: retain # When the MysqlUser is deleted, the MySQL user will be preserved
spec:
  user: xdb_admin     #注意：具体的用户名称
  password:
    secretName: {xdbcluster_name}-{app_name}        #注意：关联的Secret 资源的name
    key: PASSWORD                       #注意：关联的Secret 资源的name的所加密的key
  clusterRef:                           #注意：当前user创建的关联的XDB集群信息
    name: lxzxdb                        #注意：集群名称，是XDBFusionCluster的 CR 中 name 字段
    namespace: xdb-operator             #注意：集群所属的ns，是XDBFusionCluster的 CR 中 ns 字段
  permissions:                          #注意：db 权限列表，支持为空或者多个，暂不支持修改
    - schema: db1                       #注意：db name, 从业务value 中获取
      permissions: write                #注意：写权限，charm包下都为 write
    - schema: db2
      permissions: read
    - schema: db3
      permissions: all                   #charm包逻辑当前为 all

```

需求：需要在chart-tools的 common/template/ 中新增 例如 xdb-db-user.yaml（待讨论） 文件，需要各业务在chart改造的时候，如果有声明账户，就需要转化对应的CR， 提交执行。

```
appspace:
 charts:
  middleware:
    redis:
      clusterName: '{{ redis_cluster_name }}'      # 必填，redis 集群名称对应redis app名称，业务定义redis集群名称，也是service名称,建议redis-产品名称或者redis-应用名称,string
      domain: '{{ redis_domain }}'                 # 必填，redis 访问域名
      password: '{{ redis_password }}'             # 必填，redis 访问密码
      version: 5                                   # 选填，支持 Redis 的版本，默认使用 redis 5版本，可选4、5、6
      port: 6379
    xdb:
      bce_dns:
        clusterName: '{{ bce_dns_cluster_name }}'
        user: '{{ bce_dns_user }}'
        password: '{{ bce_dns_password }}'
        domain: '{{ bce_dns_domain }}'
        port: 6203
      bce_da:
        clusterName: '{{ bce_da_cluster_name }}'
        user: '{{ bce_da_user }}'
        password: '{{ bce_da_password }}'
        domain: '{{ bce_da_domain }}'
        port: 6203

```

举个例子：根据values-advance.yaml 生成的cr 配置

```

---
# Source: bp-charging/templates/pod-availability.yaml
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: docker-bp-charging-pdb
  namespace: console
  labels:
    karrier.abcstack.com/component: bp-charging
    karrier.abcstack.com/app: docker-bp-charging
spec:
  maxUnavailable: 25%
  selector:
    matchLabels:
      karrier.abcstack.com/app: docker-bp-charging
---
# Source: bp-charging/templates/service-account.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: docker-bp-charging
  namespace: console
  labels:
    karrier.abcstack.com/component: bp-charging
    karrier.abcstack.com/app: docker-bp-charging
---
# Source: bp-charging/templates/xdb-user.yaml
apiVersion: v1
kind: Secret
metadata:
  #注意：同用户CR名称即可
  name: xdb-console-bce-billing-w
    #注意：同下面user的 cr 解释
  namespace: console
  annotations:
    helm.sh/resource-policy: keep
data:
  password: dnA0anFjMHhicnF3QEFiQzM0Njg0   #注意：蓝图规划密码，转化为base64字符串
---
# Source: bp-charging/templates/xdb-user.yaml
apiVersion: v1
kind: Secret
metadata:
  #注意：同用户CR名称即可
  name: xdb-console-u-34ca005e
    #注意：同下面user的 cr 解释
  namespace: console
  annotations:
    helm.sh/resource-policy: keep
data:
  password: dnA0anFjMHhicnF3QEFiQzE2NDIw   #注意：蓝图规划密码，转化为base64字符串
---
# Source: bp-charging/templates/configmaps.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: docker-bp-charging
  namespace: console
data:
   bp-charging: |-
    #!/usr/bin/env sh

    ##############################################################################
    ##
    ##  bp-charging start up script for UN*X
    ##
    ##############################################################################

    # Attempt to set APP_HOME
    # Resolve links: $0 may be a link
    PRG="$0"
    # Need this for relative symlinks.
    while [ -h "$PRG" ] ; do
        ls=`ls -ld "$PRG"`
        link=`expr "$ls" : '.*-> \\(.*\\)$'`
        if expr "$link" : '/.*' > /dev/null; then
            PRG="$link"
        else
            PRG=`dirname "$PRG"`"/$link"
        fi
    done
    SAVED="`pwd`"
    cd "`dirname \\"$PRG\\"`/.." >/dev/null
    APP_HOME="`pwd -P`"
    cd "$SAVED" >/dev/null

    APP_NAME="bp-charging"
    APP_BASE_NAME=`basename "$0"`

    # Add default JVM options here. You can also use JAVA_OPTS to pass JVM options to this script.
    DEFAULT_JVM_OPTS='"-Xmx15G" "-XX:+UseG1GC" "-XX:+PrintGCDetails" "-XX:+PrintGCDateStamps" "-Xloggc:log/gc.log" "-XX:+UseGCLogFileRotation" "-XX:NumberOfGCLogFiles=100" "-XX:GCLogFileSize=2M"'

    # Use the maximum available, or set MAX_FD != -1 to use that value.
    MAX_FD="maximum"

    warn () {
        echo "$*"
    }

    die () {
        echo
        echo "$*"
        echo
        exit 1
    }

    # OS specific support (must be 'true' or 'false').
    cygwin=false
    msys=false
    darwin=false
    nonstop=false
    case "`uname`" in
      CYGWIN* )
        cygwin=true
        ;;
      Darwin* )
        darwin=true
        ;;
      MINGW* )
        msys=true
        ;;
      NONSTOP* )
        nonstop=true
        ;;
    esac

    CLASSPATH=$APP_HOME/lib/bp-charging-2.0.jar

    # Determine the Java command to use to start the JVM.
    if [ -n "$JAVA_HOME" ] ; then
        if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
            # IBM's JDK on AIX uses strange locations for the executables
            JAVACMD="$JAVA_HOME/jre/sh/java"
        else
            JAVACMD="$JAVA_HOME/bin/java"
        fi
        if [ ! -x "$JAVACMD" ] ; then
            die "ERROR: JAVA_HOME is set to an invalid directory: $JAVA_HOME
    Please set the JAVA_HOME variable in your environment to match the
    location of your Java installation."
        fi
    else
        JAVACMD="java"
        which java >/dev/null 2>&1 || die "ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.
    Please set the JAVA_HOME variable in your environment to match the
    location of your Java installation."
    fi

    # Increase the maximum file descriptors if we can.
    if [ "$cygwin" = "false" -a "$darwin" = "false" -a "$nonstop" = "false" ] ; then
        MAX_FD_LIMIT=`ulimit -H -n`
        if [ $? -eq 0 ] ; then
            if [ "$MAX_FD" = "maximum" -o "$MAX_FD" = "max" ] ; then
                MAX_FD="$MAX_FD_LIMIT"
            fi
            ulimit -n $MAX_FD
            if [ $? -ne 0 ] ; then
                warn "Could not set maximum file descriptor limit: $MAX_FD"
            fi
        else
            warn "Could not query maximum file descriptor limit: $MAX_FD_LIMIT"
        fi
    fi

    # Escape application args
    save () {
        for i do printf %s\\\\n "$i" | sed "s/'/'\\\\\\\\''/g;1s/^/'/;\\$s/\\$/' \\\\\\\\/" ; done
        echo " "
    }
    APP_ARGS=$(save "$@")
    # add huatuo tracing #
    APPLICATION="bp-charging.jar"
    user=`whoami`
    if [[ $user == "root" ]];then
        deploy_path="/root/huatuo-java-agent"
    else
        deploy_path="/home/<USER>/huatuo-java-agent"
    fi
    if [ -f $deploy_path/bin/trace_func.sh ];then
        source $deploy_path/bin/trace_func.sh
    fi
    sed -i "s/>..\\/log\\/huatuo_trace_log/>.\\/log\\/huatuo_trace_log/" $deploy_path/data/$APP_NAME/huatuo_trace_agent.xml
    sed -i "s/file.path: ..\\/log\\/huatuo_trace_log/file.path: .\\/log\\/huatuo_trace_log/" $deploy_path/data/$APP_NAME/huatuo_trace_agent.yml
    # end add huatuo tracing #
    # add jacoco agent
    JACOCO_AGENT=""
    if [ -n "$JACOCO_HOME" ] && [ -n "$JACOCO_PORT" ];then
        hostname=`hostname`
        JACOCO_AGENT="-javaagent:$JACOCO_HOME/jacocoagent.jar=output=tcpserver,port=$JACOCO_PORT,address=$hostname"
    fi
    # end add jacoco agent
    # Collect all arguments for the java command, following the shell quoting and substitution rules
    eval set -- $DEFAULT_JVM_OPTS $JAVA_OPTS ${WITH_TRACE_AGENT} ${JACOCO_AGENT} -jar "$CLASSPATH" "$APP_ARGS"

    # by default we should be in the correct project dir, but when run from Finder on Mac, the cwd is wrong
    if [ "$(uname)" = "Darwin" ] && [ "$HOME" = "$PWD" ]; then
      cd "$(dirname "$0")"
    fi

    exec "$JAVACMD" "$@"
---
# Source: bp-charging/templates/logrotate-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: docker-bp-charging-logrotate-configmap
  namespace: console
data:
  logrotate.yaml: |-
    filePaths:
    dirPaths:
---
# Source: bp-charging/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: docker-bp-charging-hs
  namespace: console
  labels:
    karrier.abcstack.com/component: bp-charging
    karrier.abcstack.com/app: docker-bp-charging
spec:
  clusterIP: None
  selector:
      karrier.abcstack.com/app: docker-bp-charging
  ports:
    - name: 8685-tcp
      protocol: TCP
      port: 8685
      targetPort: 8685
---
# Source: bp-charging/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: docker-bp-charging
  namespace: console
  labels:
    karrier.abcstack.com/component: bp-charging
    karrier.abcstack.com/app: docker-bp-charging
    region: xa
  annotations:
    helm.sh/resource-policy: keep
    service.beta.kubernetes.io/stack-load-balancer-health-check-retry: "3"
    service.beta.kubernetes.io/stack-load-balancer-health-check-timeout: "10"
    service.beta.kubernetes.io/stack-load-balancer-health-check-interval: "20"
    service.beta.kubernetes.io/stack-load-balancer-schname: "srch"
    service.beta.kubernetes.io/stack-load-balancer-vip-type: underlay
spec:
  type: LoadBalancer
  ports:
    - name: 8685-tcp
      protocol: TCP
      # Service 的 ClusterIP（或外部负载均衡器的 IP）上公开的端口
      port: 8685
      # 将流量转发到后端 Pod 的端口
      targetPort: 8685
  selector:
      karrier.abcstack.com/app: docker-bp-charging
---
# Source: bp-charging/templates/workload-statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: docker-bp-charging
  namespace: console
  annotations:

    karrier.abcstack.com/app-version: 0.1.0
    karrier.abcstack.com/subsystem: Billing
  labels:
    karrier.abcstack.com/component: bp-charging
    karrier.abcstack.com/app: docker-bp-charging
    helm.sh/chart: bp-charging-0.1.0
spec:
  replicas: 2
  selector:
    matchLabels:
      karrier.abcstack.com/app: docker-bp-charging
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%

  podManagementPolicy: Parallel
  serviceName: docker-bp-charging-hs
  template:
    metadata:
      annotations:

        karrier.abcstack.com/app-version: 0.1.0
        karrier.abcstack.com/subsystem: Billing
      labels:
        cce.baidubce.com/fixedip: "true"

        karrier.abcstack.com/subsystem: Billing
        karrier.abcstack.com/component: bp-charging
        karrier.abcstack.com/app: docker-bp-charging
    spec:
      enableServiceLinks: false
      serviceAccountName: docker-bp-charging
      restartPolicy: Always
      tolerations:
        - effect: NoSchedule
          key: node.kubernetes.io/disk-pressure
          operator: Exists
        - effect: NoSchedule
          key: node.kubernetes.io/memory-pressure
          operator: Exists
        - effect: NoSchedule
          key: node.kubernetes.io/pid-pressure
          operator: Exists
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchLabels:
                  karrier.abcstack.com/component: bp-charging
              topologyKey: kubernetes.io/hostname
            weight: 100
      volumes:

        - name: appspace-localtime
          hostPath:
            path: /etc/localtime
            type:
        - name: appspace-shanghai
          hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai
            type:
        - name: appspace-tools
          hostPath:
            path: /home/<USER>/tools
            type:
        - name: appspace-scripts
          hostPath:
            path: /home/<USER>/scripts
            type:
        - name: bp-charging
          configMap:
            name: docker-bp-charging
            defaultMode: 0777
        - name: logrotate
          configMap:
            name: docker-bp-charging-logrotate-configmap
            defaultMode: 0777
      terminationGracePeriodSeconds: 15
      containers:
        - name: bp-charging
          image: registry.dev6.abcstackint.com:5000/abc-stack/bp-charging:20.188.1232682.3
          imagePullPolicy: IfNotPresent
          command:
            - tail
            - -f
          ports:
            - name: "8685-tcp"
              containerPort: 8685
              protocol: TCP
          tty: true
          env:
            - name: APPSPACE_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: APPSPACE_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: APPSPACE_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: APPSPACE_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: APPSPACE_NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: APPSPACE_APP_NAME
              value: docker-bp-charging
            - name: APPSPACE_POD_FQDN
              value: $(APPSPACE_POD_NAME).$(APPSPACE_APP_NAME)-hs.$(APPSPACE_POD_NAMESPACE).svc.cluster.local
            - name: APPSPACE_HEADLESS_SVC
              value: $(APPSPACE_APP_NAME)-hs.$(APPSPACE_POD_NAMESPACE).svc.cluster.local
            - name: APPSPACE_POD_FQDNS
              value: "docker-bp-charging-0.docker-bp-charging-hs.console.svc.cluster.local,docker-bp-charging-1.docker-bp-charging-hs.console.svc.cluster.local"
          resources:
            limits:
              cpu: "500m"
              memory: "4000Mi"
            requests:
              cpu: "500m"
              memory: "4000Mi"
          volumeMounts:

            - name: bp-charging
              mountPath: /home/<USER>/bp-charging/conf/application.properties
              subPath: application.properties
            - name: bp-charging
              mountPath: /home/<USER>/bp-charging/conf/endpoint.json
              subPath: endpoint.json
            - name: bp-charging
              mountPath: /home/<USER>/bp-charging/init_nfs.sh
              subPath: init_nfs.sh
            - name: bp-charging
              mountPath: /home/<USER>/bp-charging/bin/bp-charging
              subPath: bp-charging
            - name: appspace-localtime
              mountPath: /etc/localtime
            - name: appspace-shanghai
              mountPath: /usr/share/zoneinfo/Asia/Shanghai
            - name: appspace-tools
              mountPath: /home/<USER>/tools
            - name: appspace-scripts
              mountPath: /home/<USER>/script
        - name: logrotate
          image: registry.dev6.abcstackint.com:5000/abc-stack/logrotate:latest
          imagePullPolicy: IfNotPresent
          tty: true
          env:
            - name: APPSPACE_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: APPSPACE_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: APPSPACE_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: APPSPACE_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: APPSPACE_NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: APPSPACE_APP_NAME
              value: docker-bp-charging
            - name: APPSPACE_POD_FQDN
              value: $(APPSPACE_POD_NAME).$(APPSPACE_APP_NAME)-hs.$(APPSPACE_POD_NAMESPACE).svc.cluster.local
            - name: APPSPACE_HEADLESS_SVC
              value: $(APPSPACE_APP_NAME)-hs.$(APPSPACE_POD_NAMESPACE).svc.cluster.local
            - name: APPSPACE_POD_FQDNS
              value: "docker-bp-charging-0.docker-bp-charging-hs.console.svc.cluster.local,docker-bp-charging-1.docker-bp-charging-hs.console.svc.cluster.local"
          resources:
            limits:
              cpu: "750m"
              memory: "6000Mi"
            requests:
              cpu: "500m"
              memory: "4000Mi"
          volumeMounts:

            - name: logrotate
              mountPath: /opdir/log-rotate/conf/
---
# Source: bp-charging/templates/noahee.yaml
apiVersion: inf.baidu.com/v1
kind: NoahEEService
metadata:
  name: docker-bp-charging-noahee
  namespace: console
  annotations:
    helm.sh/resource-policy: keep
spec:
  appName: bp-charging                                                                     # noahee 的 appname
  productName: abc-stack                         # noahee 的 product-name,私有云下默认abc-stack
  systemName: Billing                                               # 子系统，英文
  systemNameCn: 交易系统Billing                                           # 子系统，中文
  runUser: root                                      # docker 镜像启动的用户名，默认root
  headlessServiceName: docker-bp-charging-hs
  selector:
    matchLabels:
      karrier.abcstack.com/app: docker-bp-charging                                        # pod selector 用于筛选需要注册的 pod
    # 尝试罗列监控的服务端口，可选
  ports:
    - name: 8685-tcp
      port: 8685
---
# Source: bp-charging/templates/xdb-db.yaml
apiVersion: cloudbed.abcstack.com/v1
kind: XDBDataBase
metadata:
  name: xdb-console-bce-billing   #注意：由于可能多套xdb 都部署在同一个ns。多个集群中有同名的DB,所以这里名称需要带上集群名称保证唯一
  namespace: console                        #注意：经过讨论，按产品划分ns后，cr的资源也放入当前的产品的 ns中
  annotations:
    xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy: retain # When the MysqlUDB is deleted, the MySQL DB will be preserved
    helm.sh/resource-policy: keep
spec:
  # 注意：当前集群的需要创建的db名称
  database: bce_billing
  clusterRef:
    # 注意：当前db创建的关联的XDB集群信息
    name: xdb-console
    #注意： 集群所属的ns，是XDBFusionCluster的 CR 中 ns 字段
    namespace: base
---
# Source: bp-charging/templates/xdb-db.yaml
apiVersion: cloudbed.abcstack.com/v1
kind: XDBDataBase
metadata:
  name: xdb-console-bce-billing-context   #注意：由于可能多套xdb 都部署在同一个ns。多个集群中有同名的DB,所以这里名称需要带上集群名称保证唯一
  namespace: console                        #注意：经过讨论，按产品划分ns后，cr的资源也放入当前的产品的 ns中
  annotations:
    xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy: retain # When the MysqlUDB is deleted, the MySQL DB will be preserved
    helm.sh/resource-policy: keep
spec:
  # 注意：当前集群的需要创建的db名称
  database: bce_billing_context
  clusterRef:
    # 注意：当前db创建的关联的XDB集群信息
    name: xdb-console
    #注意： 集群所属的ns，是XDBFusionCluster的 CR 中 ns 字段
    namespace: base
---
# Source: bp-charging/templates/xdb-user.yaml
apiVersion: cloudbed.abcstack.com/v1
kind: XDBUser
metadata:
  name: xdb-console-bce-billing-w #注意： 按照约束，不同业务访问同db，要求按产品划分账户，所以这里名称最好是按照 xdb集群名称+ 产品名称生成
  namespace: console #注意：经过讨论，按产品划分ns后，cr的资源也放入当前的产品的 ns中
  annotations:
    xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy: retain # When the MysqlUser is deleted, the MySQL user will be preserved
    helm.sh/resource-policy: keep
spec:
  user: bce_billing_w     #注意：具体的用户名称,蓝图规划的名称
  password:
    secretName: xdb-console-bce-billing-w        #注意：关联的Secret 资源的name
    key: password                       #注意：关联的Secret 资源的name的所加密的key
  clusterRef: #注意：当前user创建的关联的XDB集群信息
    name: xdb-console                       #注意：集群名称，是XDBFusionCluster的 CR 中 name 字段
    namespace: base                  #注意：集群所属的ns，是XDBFusionCluster的 CR 中 ns 字段
  permissions: #注意：db 权限列表，支持为空或者多个，暂不支持修改
    - schema: bce_billing                       #注意：db name, 从业务value 中获取
      permissions: ALL                #注意：写权限，charm包下都为 write
    - schema: bce_billing
      permissions: ALL
---
# Source: bp-charging/templates/xdb-user.yaml
apiVersion: cloudbed.abcstack.com/v1
kind: XDBUser
metadata:
  name: xdb-console-u-34ca005e #注意： 按照约束，不同业务访问同db，要求按产品划分账户，所以这里名称最好是按照 xdb集群名称+ 产品名称生成
  namespace: console #注意：经过讨论，按产品划分ns后，cr的资源也放入当前的产品的 ns中
  annotations:
    xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy: retain # When the MysqlUser is deleted, the MySQL user will be preserved
    helm.sh/resource-policy: keep
spec:
  user: u_34ca005e     #注意：具体的用户名称,蓝图规划的名称
  password:
    secretName: xdb-console-u-34ca005e        #注意：关联的Secret 资源的name
    key: password                       #注意：关联的Secret 资源的name的所加密的key
  clusterRef: #注意：当前user创建的关联的XDB集群信息
    name: xdb-console                       #注意：集群名称，是XDBFusionCluster的 CR 中 name 字段
    namespace: base                  #注意：集群所属的ns，是XDBFusionCluster的 CR 中 ns 字段
  permissions: #注意：db 权限列表，支持为空或者多个，暂不支持修改
    - schema: bce_billing_context                       #注意：db name, 从业务value 中获取
      permissions: ALL                #注意：写权限，charm包下都为 write
    - schema: bce_billing_context
      permissions: ALL

```

[附件]
[附件]