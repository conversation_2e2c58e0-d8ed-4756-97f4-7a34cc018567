# IAM-Operator 混合模式设计方案

## 设计原则

- **边界清晰**：明确定义输入、处理、输出边界
- **没有冗余**：避免重复处理和资源浪费
- **保持最小**：最小化复杂度和依赖
- **逻辑合理**：符合 Kubernetes 生态和运维习惯
- **符合规范**：遵循 Operator 和 Kubernetes 最佳实践

## 架构概览

```mermaid
graph TD
    A[Values.yaml 输入] --> B[Helm 部署]
    B --> C[ConfigMap 资源]
    
    D[CRD 输入] --> E[IAMServiceAccount 资源]
    
    F[IAM-Operator] --> G[统一处理引擎]
    C --> G
    E --> G
    
    G --> H[IAM API 调用]
    H --> I[IAM 数据库更新]
    
    G --> J[Kubernetes Secret 创建]
```

## 核心组件设计

### 1. 输入边界

#### 输入源 A：ConfigMap（现有方式）
```python
@kopf.on.create('v1', 'configmaps')
@kopf.on.update('v1', 'configmaps')
def handle_configmap_input(body, name, namespace, **kwargs):
    """处理从 values.yaml 转换来的 ConfigMap"""
    
    # 边界检查：只处理包含 IAM 配置的 ConfigMap
    if not is_iam_configmap(body, name, namespace):
        return
    
    # 解析并标准化
    credentials = parse_configmap_credentials(body)
    if credentials:
        process_credentials(
            source_type='configmap',
            source_name=f"{namespace}/{name}",
            credentials=credentials
        )

def is_iam_configmap(body, name, namespace):
    """边界判断：是否为 IAM 相关的 ConfigMap"""
    # 明确的判断条件，避免误处理
    return (
        namespace in ['security', 'console', 'billing'] and
        any('$bss_ak' in str(data) or '$.*_ak' in str(data) 
            for data in body.get('data', {}).values())
    )
```

#### 输入源 B：CRD（新方式）
```python
@kopf.on.create('iam.example.com', 'v1', 'iamserviceaccounts')
@kopf.on.update('iam.example.com', 'v1', 'iamserviceaccounts')
def handle_crd_input(spec, name, namespace, **kwargs):
    """处理 CRD 输入"""
    
    # 直接处理结构化数据，无需解析
    process_credentials(
        source_type='crd',
        source_name=f"{namespace}/{name}",
        credentials=spec.get('products', {})
    )
```

### 2. 处理边界

#### 统一处理引擎
```python
def process_credentials(source_type, source_name, credentials):
    """统一的凭据处理逻辑 - 避免重复代码"""
    
    for product_name, creds in credentials.items():
        # 边界检查：验证凭据完整性
        if not is_valid_credentials(creds):
            kopf.warn(f"跳过无效凭据: {source_name}/{product_name}")
            continue
        
        # 幂等性检查：避免重复处理
        if not needs_update(source_name, product_name, creds):
            kopf.info(f"凭据无变化，跳过: {source_name}/{product_name}")
            continue
        
        # 调用 IAM API
        try:
            update_iam_credentials(source_name, product_name, creds)
            record_success(source_name, product_name)
        except Exception as e:
            record_failure(source_name, product_name, str(e))
            raise

def is_valid_credentials(creds):
    """最小验证：只检查必需字段"""
    return (
        isinstance(creds, dict) and
        creds.get('ak') and 
        creds.get('sk')
    )

def needs_update(source_name, product_name, creds):
    """幂等性检查：避免重复处理"""
    last_processed = get_last_processed_hash(source_name, product_name)
    current_hash = hash_credentials(creds)
    return last_processed != current_hash
```

### 3. 输出边界

#### IAM API 调用
```python
class IAMClient:
    """IAM API 客户端 - 单一职责"""
    
    def __init__(self):
        self.base_url = "http://iam-openapi.console.svc.cluster.local:8480"
        self.timeout = 30
    
    def update_service_credentials(self, service_name, product_name, credentials):
        """更新服务凭据 - 明确的 API 边界"""
        
        payload = {
            "service_name": service_name,
            "product_name": product_name,
            "credentials": {
                "access_key": credentials['ak'],
                "secret_key": credentials['sk'],
                "password": credentials.get('password', ''),
                "user_id": credentials.get('userId', '')
            }
        }
        
        response = requests.post(
            f"{self.base_url}/api/v1/service-credentials",
            json=payload,
            timeout=self.timeout
        )
        
        if not response.ok:
            raise IAMAPIError(f"API 调用失败: {response.status_code} - {response.text}")
        
        return response.json()
```

## CRD 定义

### 最小化的 CRD 设计
```yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: iamserviceaccounts.iam.example.com
spec:
  group: iam.example.com
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              serviceName:
                type: string
                description: "服务名称"
              products:
                type: object
                description: "产品凭据映射"
                additionalProperties:
                  type: object
                  properties:
                    ak:
                      type: string
                    sk:
                      type: string
                    password:
                      type: string
                    userId:
                      type: string
                  required: ["ak", "sk"]
            required: ["serviceName", "products"]
          status:
            type: object
            properties:
              lastUpdated:
                type: string
              conditions:
                type: array
                items:
                  type: object
```

## 部署策略

### 阶段 1：现有方式支持
```bash
# 部署 IAM-Operator（支持 ConfigMap 输入）
kubectl apply -f iam-operator-deployment.yaml

# 现有服务继续正常工作
helm install waf-meta ./chart -f waf-meta-values.yaml
```

### 阶段 2：CRD 支持
```bash
# 安装 CRD 定义
kubectl apply -f iam-service-account-crd.yaml

# 用户可选择使用 CRD 方式
kubectl apply -f waf-meta-iam-service-account.yaml
```

### 阶段 3：Helm Hook 集成
```yaml
# 在 Helm Chart 中添加可选的 Hook
{{- if .Values.iam.enableOperator }}
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: {{ .Chart.Name }}-iam
  annotations:
    "helm.sh/hook": post-install,post-upgrade
spec:
  serviceName: {{ .Chart.Name }}
  products:
    {{- toYaml .Values.iam | nindent 4 }}
{{- end }}
```

## 错误处理和监控

### 边界清晰的错误处理
```python
class IAMOperatorError(Exception):
    """IAM Operator 基础异常"""
    pass

class ConfigParseError(IAMOperatorError):
    """配置解析错误"""
    pass

class IAMAPIError(IAMOperatorError):
    """IAM API 调用错误"""
    pass

@kopf.on.create('v1', 'configmaps')
def handle_configmap_with_error_boundary(body, **kwargs):
    try:
        handle_configmap_input(body, **kwargs)
    except ConfigParseError as e:
        kopf.warn(f"配置解析失败: {e}")
        # 不重试，记录警告
    except IAMAPIError as e:
        kopf.error(f"IAM API 调用失败: {e}")
        # 重试机制
        raise kopf.TemporaryError(f"IAM API 暂时不可用: {e}", delay=60)
```

### 最小化的状态管理
```python
def record_processing_state(source_name, product_name, status, error=None):
    """记录处理状态 - 用于幂等性和监控"""
    
    state = {
        'timestamp': datetime.utcnow().isoformat(),
        'status': status,  # 'success' | 'failed' | 'skipped'
        'error': error
    }
    
    # 存储在 ConfigMap 中，避免引入额外存储
    update_state_configmap(f"iam-operator-state", {
        f"{source_name}/{product_name}": json.dumps(state)
    })
```
