# IAM Operator dev7 部署技术细节

## 🏗️ 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    dev7 Kubernetes Cluster                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌──────────────────────────────────┐ │
│  │  iam-operator   │    │         console namespace        │ │
│  │   (iaas5 node)  │◄──►│    iam-manage-xian service      │ │
│  │                 │    │   (**************:8468)         │ │
│  └─────────────────┘    └──────────────────────────────────┘ │
│           │                                                  │
│           ▼                                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Target Namespaces                          │ │
│  │        default, console, kube-system                    │ │
│  │              (ConfigMap Monitoring)                     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 关键技术实现

### 1. 服务发现机制

**实现原理：**
```go
// 通过Kubernetes Service API发现IAM端点
func (sd *ServiceDiscovery) GetIAMEndpoint(ctx context.Context) (string, error) {
    service, err := sd.client.CoreV1().Services(sd.namespace).Get(ctx, sd.serviceName, metav1.GetOptions{})
    if err != nil {
        return "", fmt.Errorf("failed to get service %s/%s: %w", sd.namespace, sd.serviceName, err)
    }
    
    clusterIP := service.Spec.ClusterIP
    port := service.Spec.Ports[0].Port
    endpoint := fmt.Sprintf("http://%s:%d/v3", clusterIP, port)
    
    return endpoint, nil
}
```

**发现结果：**
- **服务名称：** `console/iam-manage-xian`
- **集群IP：** `**************`
- **端口：** `8468`
- **完整端点：** `http://**************:8468/v3`

### 2. ConfigMap监控逻辑

**Controller实现：**
```go
func (r *ConfigMapReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    log := log.FromContext(ctx)
    
    // 获取ConfigMap
    var configMap corev1.ConfigMap
    if err := r.Get(ctx, req.NamespacedName, &configMap); err != nil {
        return ctrl.Result{}, client.IgnoreNotFound(err)
    }
    
    // 检查是否包含IAM凭据
    if !r.containsIAMCredentials(&configMap) {
        log.V(1).Info("ConfigMap does not contain IAM credentials, skipping")
        return ctrl.Result{}, nil
    }
    
    // 处理IAM凭据验证逻辑
    return r.processIAMCredentials(ctx, &configMap)
}
```

**凭据检测逻辑：**
```go
func (r *ConfigMapReconciler) containsIAMCredentials(cm *corev1.ConfigMap) bool {
    _, hasAccessKey := cm.Data["access_key"]
    _, hasSecretKey := cm.Data["secret_key"]
    return hasAccessKey && hasSecretKey
}
```

### 3. 权限模型设计

**RBAC最小权限原则：**
```yaml
# 只读ConfigMap权限
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]  # 仅读取，无修改权限

# 服务发现权限
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch"]

# 事件记录权限
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]

# Leader选举权限
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
```

## 🐳 容器化部署细节

### 1. Dockerfile优化

**最终Dockerfile：**
```dockerfile
# 使用集群可访问的基础镜像
FROM registry.dev7.abcstackint.com:5000/abc-stack/golang:1.22

# 复制编译好的二进制文件
COPY bin/manager-linux-amd64 /usr/local/bin/iam-operator

# 设置执行权限
RUN chmod +x /usr/local/bin/iam-operator

# 设置工作目录
WORKDIR /

# 暴露健康检查端口
EXPOSE 8081

# 设置入口点
ENTRYPOINT ["/usr/local/bin/iam-operator"]
```

**关键优化点：**
- 使用集群内可访问的基础镜像
- 单阶段构建，避免复杂的多阶段构建
- 直接使用预编译的linux/amd64二进制文件

### 2. .dockerignore配置

**修复前（有问题）：**
```
bin/  # 忽略整个bin目录，导致找不到二进制文件
```

**修复后（正确）：**
```
# 忽略本地编译的二进制，但保留linux版本
bin/manager
!bin/manager-linux-amd64
```

### 3. 镜像传输流程

```bash
# 1. 本地构建
docker build --platform linux/amd64 -t iam-operator:local .

# 2. 导出镜像
docker save -o iam-operator-local-fixed.tar iam-operator:local

# 3. 传输到集群节点
scp -o ProxyJump=跳板机 iam-operator-local-fixed.tar <EMAIL>:/tmp/iam-operator-image/

# 4. 在节点导入到containerd
ctr -n k8s.io images import /tmp/iam-operator-image/iam-operator-local-fixed.tar

# 5. 验证导入
ctr -n k8s.io images list | grep iam-operator
```

## 🔍 问题诊断与解决

### 1. 信号处理器重复调用问题

**错误现象：**
```
panic: close of closed channel
goroutine 1 [running]:
sigs.k8s.io/controller-runtime/pkg/manager/signals.SetupSignalHandler()
```

**根本原因：**
`ctrl.SetupSignalHandler()` 在代码中被调用了两次：
- 第一次：服务发现时调用
- 第二次：启动manager时调用

**解决方案：**
```go
// 修复：只调用一次，复用context
ctx := ctrl.SetupSignalHandler()
endpoint, err := serviceDiscovery.GetIAMEndpoint(ctx)
// ...
if err := mgr.Start(ctx); err != nil {
```

### 2. Leader Election权限问题

**错误现象：**
```
error retrieving resource lock iam-operator-system/c46850f8.abcstackint.com: 
leases.coordination.k8s.io "c46850f8.abcstackint.com" is forbidden
```

**解决方案：**
添加coordination.k8s.io/leases资源的完整权限。

### 3. Docker构建找不到文件问题

**错误现象：**
```
ERROR: failed to solve: failed to compute cache key: 
"/bin/manager-linux-amd64": not found
```

**根本原因：**
.dockerignore文件配置错误，忽略了整个bin/目录。

## 📊 性能与监控数据

### 1. 资源使用情况

**镜像信息：**
```
docker.io/library/iam-operator:local
Size: 320.6 MiB
Platform: linux/amd64
```

**Pod资源使用：**
```bash
kubectl top pod -n iam-operator-system
# NAME                                               CPU(cores)   MEMORY(bytes)
# iam-operator-controller-manager-59d475665b-q2zwz   1m           22Mi
```

### 2. 健康检查配置

**健康探针端点：**
```
Health Probe Server: [::]:8081
Readiness Probe: /readyz
Liveness Probe: /healthz
```

### 3. 日志级别配置

**当前配置：**
```yaml
LOG_LEVEL: "info"
```

**日志输出示例：**
```
2025-07-30T13:53:43Z	INFO	Discovered IAM endpoint
2025-07-30T13:53:43Z	INFO	setup	starting manager
2025-07-30T13:55:49Z	INFO	Reconciling ConfigMap
2025-07-30T13:55:49Z	DEBUG	ConfigMap does not contain IAM credentials, skipping
```

## 🔐 安全考虑

### 1. 权限最小化原则

**实施措施：**
- 移除所有ConfigMap的update/patch权限
- 仅保留必要的读取权限
- 限制目标命名空间范围

### 2. 网络安全

**网络策略：**
- 仅允许访问console命名空间的iam-manage-xian服务
- 使用集群内部IP通信，避免外部网络暴露

### 3. 凭据处理

**安全措施：**
- 不在日志中输出敏感信息
- 使用Kubernetes Secret存储敏感配置
- 实现凭据验证而非存储

## 🚀 扩展性设计

### 1. 多节点部署准备

**当前限制：**
```yaml
nodeSelector:
  kubernetes.io/hostname: iaas5-kangding.dev7.abcstackint.com
```

**扩展方案：**
1. 移除nodeSelector限制
2. 将镜像分发到所有worker节点
3. 配置Pod反亲和性确保高可用

### 2. 配置热更新

**实现思路：**
- 监控ConfigMap变更事件
- 实现配置重载机制
- 支持不重启Pod的配置更新

### 3. 多集群支持

**架构扩展：**
- 支持跨集群服务发现
- 实现集群间IAM凭据同步
- 配置多集群权限管理

---

**文档版本：** v1.0  
**最后更新：** 2025-07-30  
**维护者：** IAM Operator Team
