apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
  name: manager-role
rules:
# ConfigMap permissions for credential processing
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch", "update", "patch"]
# Service permissions for service discovery
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch"]
# Event permissions for logging
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
