package main

import (
	"fmt"
	"strings"

	"github.com/abc-stack/iam-operator/internal/processors"
)

func main() {
	fmt.Println("🧪 测试IAM ConfigMap检测逻辑")
	fmt.Println("=" * 50)

	targetNamespaces := []string{"default", "console", "kube-system"}

	// 测试用例
	testCases := []struct {
		name      string
		namespace string
		data      map[string]string
		expected  bool
	}{
		{
			name:      "场景1: 直接键值对格式",
			namespace: "default",
			data: map[string]string{
				"access_key": "test-ak",
				"secret_key": "test-sk",
				"app_name":   "test-app",
			},
			expected: true,
		},
		{
			name:      "场景2: PHP配置文件格式",
			namespace: "default",
			data: map[string]string{
				"config.php": `<?php
$bss_ak = 'f90daa8e8af94f6bb2da26c380e0a9cb';
$bss_sk = 'd90b6603d4414151a0dac95409dcd815';
?>`,
			},
			expected: true,
		},
		{
			name:      "场景3: YAML嵌套格式",
			namespace: "default",
			data: map[string]string{
				"app.yaml": `
iam:
  bss:
    ak: yaml-bss-access-key
    sk: yaml-bss-secret-key
`,
			},
			expected: true,
		},
		{
			name:      "场景4: 配置文件格式",
			namespace: "default",
			data: map[string]string{
				"app.conf": `
bss_ak=conf-access-key
bss_sk=conf-secret-key
db_host=localhost
`,
			},
			expected: true,
		},
		{
			name:      "场景5: 连字符格式",
			namespace: "default",
			data: map[string]string{
				"access-key": "test-ak",
				"secret-key": "test-sk",
			},
			expected: true,
		},
		{
			name:      "场景6: 负面测试 - 无凭据",
			namespace: "default",
			data: map[string]string{
				"app_name":     "normal-app",
				"database_url": "postgresql://localhost:5432/app",
				"log_level":    "INFO",
			},
			expected: false,
		},
		{
			name:      "场景7: 边界测试 - 包含ak/sk但不是凭据",
			namespace: "default",
			data: map[string]string{
				"readme.txt": "This app requires ak and sk configuration",
				"help_text":  "Contact support for ak/sk issues",
			},
			expected: false,
		},
		{
			name:      "场景8: 非目标命名空间",
			namespace: "other-namespace",
			data: map[string]string{
				"access_key": "test-ak",
				"secret_key": "test-sk",
			},
			expected: false,
		},
	}

	// 执行测试
	passed := 0
	total := len(testCases)

	for i, tc := range testCases {
		fmt.Printf("\n%d. %s\n", i+1, tc.name)
		fmt.Printf("   命名空间: %s\n", tc.namespace)
		fmt.Printf("   数据键: %v\n", getKeys(tc.data))

		result := processors.IsIAMConfigMap(tc.data, tc.namespace, targetNamespaces)
		status := "❌ FAIL"
		if result == tc.expected {
			status = "✅ PASS"
			passed++
		}

		fmt.Printf("   期望: %v, 实际: %v %s\n", tc.expected, result, status)

		// 显示数据内容（截断）
		for key, value := range tc.data {
			preview := value
			if len(preview) > 50 {
				preview = preview[:50] + "..."
			}
			preview = strings.ReplaceAll(preview, "\n", "\\n")
			fmt.Printf("   %s: %s\n", key, preview)
		}
	}

	fmt.Printf("\n" + "=" * 50)
	fmt.Printf("\n🎯 测试结果: %d/%d 通过", passed, total)
	if passed == total {
		fmt.Printf(" 🎉 全部通过！")
	}
	fmt.Println()
}

func getKeys(data map[string]string) []string {
	keys := make([]string, 0, len(data))
	for k := range data {
		keys = append(keys, k)
	}
	return keys
}
