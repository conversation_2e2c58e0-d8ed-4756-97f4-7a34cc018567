package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/abc-stack/iam-operator/internal/clients"
	"github.com/abc-stack/iam-operator/internal/config"
	"github.com/abc-stack/iam-operator/internal/processors"
)

func main() {
	fmt.Println("🧪 IAM Operator Component Testing")
	fmt.Println("==================================")

	// Test 1: Configuration Loading
	fmt.Println("\n1️⃣ Testing Configuration Loading...")
	testConfig()

	// Test 2: Credential Processing
	fmt.Println("\n2️⃣ Testing Credential Processing...")
	testCredentialProcessing()

	// Test 3: IAM Client (Mock)
	fmt.Println("\n3️⃣ Testing IAM Client (Mock)...")
	testIAMClient()

	fmt.Println("\n🎉 All component tests completed!")
}

func testConfig() {
	// Set test environment variables
	os.Setenv("IAM_SERVICE_DISCOVERY_ENABLED", "true")
	os.Setenv("IAM_SERVICE_NAMESPACE", "default")
	os.Setenv("IAM_SERVICE_NAME", "iam-manage")
	os.Setenv("IAM_API_URL_FALLBACK", "http://iam.xian.dev7.abcstackint.com:35357")
	os.Setenv("IAM_API_TIMEOUT", "30s")
	os.Setenv("IAM_DOMAIN", "default")
	os.Setenv("IAM_USERNAME", "proxy")
	os.Setenv("IAM_PASSWORD", "proxy")
	os.Setenv("TARGET_NAMESPACES", "default,kube-system,abc-stack")
	os.Setenv("RETRY_INTERVAL", "5m")

	cfg := config.LoadConfig()

	fmt.Printf("   ✅ Service Discovery Enabled: %v\n", cfg.IAMServiceDiscoveryEnabled)
	fmt.Printf("   ✅ Service Namespace: %s\n", cfg.IAMServiceNamespace)
	fmt.Printf("   ✅ Service Name: %s\n", cfg.IAMServiceName)
	fmt.Printf("   ✅ API URL Fallback: %s\n", cfg.IAMAPIURLFallback)
	fmt.Printf("   ✅ API Timeout: %v\n", cfg.IAMAPITimeout)
	fmt.Printf("   ✅ IAM Domain: %s\n", cfg.IAMDomain)
	fmt.Printf("   ✅ IAM Username: %s\n", cfg.IAMUsername)
	fmt.Printf("   ✅ Target Namespaces: %v\n", cfg.TargetNamespaces)
	fmt.Printf("   ✅ Retry Interval: %v\n", cfg.RetryInterval)

	if err := cfg.Validate(); err != nil {
		log.Fatalf("   ❌ Configuration validation failed: %v", err)
	}
	fmt.Println("   ✅ Configuration validation passed")
}

func testCredentialProcessing() {
	// Test data with various credential formats
	testCases := []struct {
		name string
		data map[string]string
	}{
		{
			name: "PHP Style Credentials",
			data: map[string]string{
				"config.php": `<?php
$bss_ak = "AKIAIOSFODNN7EXAMPLE";
$bss_sk = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY";
$vpc_ak = "AKIAI44QH8DHBEXAMPLE";
$vpc_sk = "je7MtGbClwBF/2Zp9Utk/h3yCo8nvbEXAMPLEKEY";
?>`,
			},
		},
		{
			name: "YAML Style Credentials",
			data: map[string]string{
				"config.yaml": `iam:
  ak: "AKIATEST123"
  sk: "secretkey456"`,
			},
		},
		{
			name: "Mixed Format",
			data: map[string]string{
				"php-config.php": `$test_ak = "AKIATEST111"; $test_sk = "secret111";`,
				"yaml-config.yml": `iam:
  ak: "AKIATEST222"
  sk: "secret222"`,
			},
		},
		{
			name: "No Credentials",
			data: map[string]string{
				"regular-config.txt": "some regular configuration without credentials",
			},
		},
	}

	cfg := &config.Config{
		TargetNamespaces: []string{"default", "test"},
	}

	for _, tc := range testCases {
		fmt.Printf("   🔍 Testing: %s\n", tc.name)

		// Test IsIAMConfigMap
		isIAM := processors.IsIAMConfigMap(tc.data, "default", cfg.TargetNamespaces)
		fmt.Printf("      - IsIAMConfigMap: %v\n", isIAM)

		if isIAM {
			// ConfigMap contains IAM credentials
			fmt.Printf("      - ✅ ConfigMap identified as containing IAM credentials\n")
		} else {
			fmt.Printf("      - ℹ️  ConfigMap does not contain IAM credentials\n")
		}
	}
}

func testIAMClient() {
	// Create IAM client with test configuration
	iamClient := clients.NewIAMClient("http://iam.xian.dev7.abcstackint.com:35357", 30*time.Second)
	iamClient.SetCredentials("default", "proxy", "proxy")

	fmt.Println("   ✅ IAM Client created successfully")
	fmt.Printf("   ✅ Endpoint configured\n")
	fmt.Printf("   ✅ Credentials set\n")

	// Test health check (this will likely fail without real connection, but tests the code path)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	fmt.Println("   🔍 Testing health check (may fail without real IAM service)...")
	err := iamClient.HealthCheck(ctx)
	if err != nil {
		fmt.Printf("   ⚠️  Health check failed (expected): %v\n", err)
		fmt.Println("   ℹ️  This is normal when testing without real IAM service")
	} else {
		fmt.Println("   ✅ Health check passed")
	}

	// Test AKSK authentication (this will also likely fail without real connection)
	fmt.Println("   🔍 Testing AKSK authentication (may fail without real IAM service)...")
	err = iamClient.AuthenticateAKSK(ctx, "test_ak", "test_sk")
	if err != nil {
		fmt.Printf("   ⚠️  AKSK authentication failed (expected): %v\n", err)
		fmt.Println("   ℹ️  This is normal when testing without real IAM service")
	} else {
		fmt.Println("   ✅ AKSK authentication passed")
	}
}
