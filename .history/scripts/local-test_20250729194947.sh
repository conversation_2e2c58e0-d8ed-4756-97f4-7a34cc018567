#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🧪 Running Local Tests for IAM Operator (Go Version)${NC}"

# Function to print test section
print_section() {
    echo -e "\n${BLUE}==================== $1 ====================${NC}"
}

# Function to run command with status
run_test() {
    local cmd="$1"
    local desc="$2"
    
    echo -e "${YELLOW}📋 $desc${NC}"
    echo -e "${BLUE}Command: $cmd${NC}"
    
    if eval "$cmd"; then
        echo -e "${GREEN}✅ $desc - PASSED${NC}"
        return 0
    else
        echo -e "${RED}❌ $desc - FAILED${NC}"
        return 1
    fi
}

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0

# Step 1: Go Module and Dependencies
print_section "Go Module and Dependencies"

run_test "go mod tidy" "Tidy Go modules"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ $? -eq 0 ]; then PASSED_TESTS=$((PASSED_TESTS + 1)); fi

run_test "go mod verify" "Verify Go modules"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ $? -eq 0 ]; then PASSED_TESTS=$((PASSED_TESTS + 1)); fi

# Step 2: Code Compilation
print_section "Code Compilation"

run_test "go build -o bin/manager cmd/main.go" "Build main binary"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ $? -eq 0 ]; then PASSED_TESTS=$((PASSED_TESTS + 1)); fi

# Step 3: Unit Tests
print_section "Unit Tests"

run_test "go test ./internal/config/... -v" "Config package tests"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ $? -eq 0 ]; then PASSED_TESTS=$((PASSED_TESTS + 1)); fi

run_test "go test ./internal/processors/... -v" "Processors package tests"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ $? -eq 0 ]; then PASSED_TESTS=$((PASSED_TESTS + 1)); fi

# Step 4: Code Quality Checks
print_section "Code Quality Checks"

# Check if gofmt is needed
if [ -n "$(gofmt -l .)" ]; then
    echo -e "${RED}❌ Code formatting issues found:${NC}"
    gofmt -l .
    echo -e "${YELLOW}💡 Run 'gofmt -w .' to fix formatting${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    echo -e "${GREEN}✅ Code formatting - PASSED${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Check if go vet passes
run_test "go vet ./..." "Go vet static analysis"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ $? -eq 0 ]; then PASSED_TESTS=$((PASSED_TESTS + 1)); fi

# Step 5: Integration Test (Mock)
print_section "Integration Test (Mock)"

# Create a simple integration test
cat > /tmp/integration_test.go << 'EOF'
package main

import (
	"fmt"
	"os"

	"github.com/abc-stack/iam-operator/internal/config"
	"github.com/abc-stack/iam-operator/internal/processors"
)

func main() {
	// Test configuration loading
	fmt.Println("🔧 Testing configuration loading...")
	
	// Set test environment variables
	os.Setenv("IAM_SERVICE_DISCOVERY_ENABLED", "false")
	os.Setenv("IAM_API_URL_FALLBACK", "http://test-iam:8080")
	os.Setenv("IAM_DOMAIN", "test")
	os.Setenv("IAM_USERNAME", "testuser")
	os.Setenv("IAM_PASSWORD", "testpass")
	os.Setenv("TARGET_NAMESPACES", "default,test")
	
	cfg := config.LoadConfig()
	if err := cfg.Validate(); err != nil {
		fmt.Printf("❌ Configuration validation failed: %v\n", err)
		os.Exit(1)
	}
	fmt.Println("✅ Configuration loaded and validated successfully")
	
	// Test credential processing
	fmt.Println("🔍 Testing credential processing...")
	
	testData := map[string]string{
		"config.php": `<?php
$bss_ak = "AKIAIOSFODNN7EXAMPLE";
$bss_sk = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY";
$vpc_ak = "AKIAI44QH8DHBEXAMPLE";
$vpc_sk = "je7MtGbClwBF/2Zp9Utk/h3yCo8nvbEXAMPLEKEY";
?>`,
		"config.yaml": `iam:
  ak: "AKIATEST123"
  sk: "secretkey456"`,
	}
	
	// Test IsIAMConfigMap function
	if processors.IsIAMConfigMap(testData, "default", cfg.TargetNamespaces) {
		fmt.Println("✅ ConfigMap correctly identified as containing IAM credentials")
	} else {
		fmt.Println("❌ ConfigMap not identified as containing IAM credentials")
		os.Exit(1)
	}
	
	fmt.Println("🎉 Integration test completed successfully!")
}
EOF

# Copy the integration test to the project and run it
cp /tmp/integration_test.go ./test_integration.go
run_test "go run test_integration.go" "Integration test (mock)"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ $? -eq 0 ]; then PASSED_TESTS=$((PASSED_TESTS + 1)); fi

# Clean up
rm -f test_integration.go

# Step 6: Binary Functionality Test
print_section "Binary Functionality Test"

if [ -f "bin/manager" ]; then
    echo -e "${YELLOW}📋 Testing binary help output${NC}"
    timeout 5s ./bin/manager --help > /dev/null 2>&1 || true
    echo -e "${GREEN}✅ Binary executes without crashing${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Binary not found${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

# Step 7: Docker Build Test (Optional)
print_section "Docker Build Test (Optional)"

if command -v docker &> /dev/null; then
    echo -e "${YELLOW}🐳 Docker is available, testing Docker build...${NC}"
    
    # Create a simple Dockerfile for testing
    cat > Dockerfile.test << 'EOF'
FROM golang:1.21-alpine AS builder
WORKDIR /workspace
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -o manager cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /workspace/manager .
CMD ["./manager"]
EOF
    
    if docker build -f Dockerfile.test -t iam-operator-test:latest . > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Docker build - PASSED${NC}"
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # Clean up test image
        docker rmi iam-operator-test:latest > /dev/null 2>&1 || true
    else
        echo -e "${RED}❌ Docker build - FAILED${NC}"
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
    fi
    
    # Clean up test Dockerfile
    rm -f Dockerfile.test
else
    echo -e "${YELLOW}⚠️  Docker not available, skipping Docker build test${NC}"
fi

# Final Results
print_section "Test Results Summary"

echo -e "${BLUE}📊 Test Results:${NC}"
echo -e "   Total Tests: $TOTAL_TESTS"
echo -e "   Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "   Failed: ${RED}$((TOTAL_TESTS - PASSED_TESTS))${NC}"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo -e "\n${GREEN}🎉 All tests passed! The IAM Operator is ready for deployment.${NC}"
    echo -e "${GREEN}📋 Next steps:${NC}"
    echo -e "   1. Run './scripts/build-and-deploy.sh' to deploy to Kubernetes"
    echo -e "   2. Or manually deploy with 'kubectl apply -f deploy/simple-deploy.yaml'"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please fix the issues before deployment.${NC}"
    exit 1
fi
