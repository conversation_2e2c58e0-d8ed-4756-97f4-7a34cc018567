"""
IAM 服务发现客户端 - 通过 Kubernetes Service 发现 iam-manage
"""
import logging
import requests
import time
from typing import Dict, Any, Optional, Tuple
from kubernetes import client, config as k8s_config
from kubernetes.client.rest import ApiException
from ..utils.config import config
from ..utils.exceptions import IAMAPIError

logger = logging.getLogger(__name__)


class IAMServiceDiscoveryClient:
    """基于 Kubernetes Service 发现的 IAM 客户端"""
    
    def __init__(self, iam_namespace: str = "console", service_name: str = "iam-manage-xian"):
        """
        初始化服务发现客户端
        
        Args:
            iam_namespace: IAM 服务所在的命名空间
            service_name: IAM 服务名称
        """
        self.iam_namespace = iam_namespace
        self.service_name = service_name
        self.timeout = config.iam_api_timeout
        self.session = requests.Session()
        self._auth_token = None
        self._token_expires_at = 0
        self._cached_endpoint = None
        self._endpoint_cache_time = 0
        self._endpoint_cache_ttl = 300  # 5分钟缓存
        
        # 初始化 Kubernetes 客户端
        try:
            k8s_config.load_incluster_config()
            logger.info("Loaded in-cluster Kubernetes config")
        except k8s_config.ConfigException:
            try:
                k8s_config.load_kube_config()
                logger.info("Loaded local Kubernetes config")
            except Exception as e:
                logger.error(f"Failed to load Kubernetes config: {e}")
                raise
        
        self.k8s_client = client.CoreV1Api()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'IAM-Operator-ServiceDiscovery/1.0'
        })
        
        logger.info(f"IAM Service Discovery Client initialized for {iam_namespace}/{service_name}")
    
    def _discover_iam_endpoint(self) -> str:
        """
        通过 Kubernetes Service 发现 IAM 服务端点
        
        Returns:
            str: IAM 服务的完整端点 URL
            
        Raises:
            IAMAPIError: 当服务发现失败时
        """
        # 检查缓存
        current_time = time.time()
        if (self._cached_endpoint and 
            current_time - self._endpoint_cache_time < self._endpoint_cache_ttl):
            return self._cached_endpoint
        
        try:
            # 获取 Service 信息
            service = self.k8s_client.read_namespaced_service(
                name=self.service_name,
                namespace=self.iam_namespace
            )
            
            cluster_ip = service.spec.cluster_ip
            if not cluster_ip or cluster_ip == "None":
                raise IAMAPIError(f"Service {self.iam_namespace}/{self.service_name} has no ClusterIP")
            
            # 获取端口信息
            if not service.spec.ports:
                raise IAMAPIError(f"Service {self.iam_namespace}/{self.service_name} has no ports")
            
            # 查找合适的端口（优先使用名为 http 或第一个端口）
            target_port = None
            for port in service.spec.ports:
                if port.name and 'http' in port.name.lower():
                    target_port = port.port
                    break
            
            if target_port is None:
                target_port = service.spec.ports[0].port
            
            # 构建端点 URL
            endpoint = f"http://{cluster_ip}:{target_port}/v3"
            
            # 更新缓存
            self._cached_endpoint = endpoint
            self._endpoint_cache_time = current_time
            
            logger.info(f"Discovered IAM endpoint: {endpoint}")
            return endpoint
            
        except ApiException as e:
            if e.status == 404:
                raise IAMAPIError(f"Service {self.iam_namespace}/{self.service_name} not found")
            else:
                raise IAMAPIError(f"Failed to discover IAM service: {e}")
        except Exception as e:
            raise IAMAPIError(f"Unexpected error during service discovery: {e}")
    
    def _get_auth_token(self) -> str:
        """获取认证 Token"""
        # 检查 token 是否还有效
        if self._auth_token and time.time() < self._token_expires_at:
            return self._auth_token
        
        try:
            endpoint = self._discover_iam_endpoint()
            
            # 基于 iam-manager 的认证请求格式
            auth_request = {
                "auth": {
                    "identity": {
                        "methods": ["password"],
                        "password": {
                            "user": {
                                "name": config.get('iam_username', 'iam-operator'),
                                "password": config.get('iam_password', 'default-password'),
                                "domain": {"name": config.get('iam_domain', 'default')}
                            }
                        }
                    }
                }
            }
            
            start_time = time.time()
            response = self.session.post(
                f"{endpoint}/auth/tokens",
                json=auth_request,
                timeout=self.timeout
            )
            
            # 记录指标
            metrics.iam_api_request_duration.labels(
                method='POST',
                endpoint='/auth/tokens',
                status_code=response.status_code
            ).observe(time.time() - start_time)
            
            if response.status_code == 201:
                # 从响应头获取 token
                token = response.headers.get('X-Subject-Token')
                if token:
                    self._auth_token = token
                    # 设置 token 过期时间（默认1小时）
                    self._token_expires_at = time.time() + 3600
                    logger.info("Successfully obtained IAM auth token")
                    return token
                else:
                    raise IAMAPIError("No X-Subject-Token in auth response")
            else:
                raise IAMAPIError(f"Authentication failed: {response.status_code} - {response.text}")
                
        except requests.exceptions.RequestException as e:
            raise IAMAPIError(f"Network error during authentication: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during authentication: {e}")
            raise IAMAPIError(f"Authentication failed: {e}")
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            endpoint = self._discover_iam_endpoint()
            
            # 尝试访问健康检查端点
            response = self.session.get(
                f"{endpoint.replace('/v3', '')}/health",  # 移除 v3 路径用于健康检查
                timeout=5
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.warning(f"IAM health check failed: {e}")
            return False
    
    def test_service_discovery(self) -> Dict[str, Any]:
        """
        测试服务发现功能
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        result = {
            'service_discovery': False,
            'endpoint': None,
            'service_info': None,
            'health_check': False,
            'auth_test': False,
            'error': None
        }
        
        try:
            # 1. 测试服务发现
            endpoint = self._discover_iam_endpoint()
            result['service_discovery'] = True
            result['endpoint'] = endpoint
            
            # 2. 获取服务详细信息
            service = self.k8s_client.read_namespaced_service(
                name=self.service_name,
                namespace=self.iam_namespace
            )
            result['service_info'] = {
                'name': service.metadata.name,
                'namespace': service.metadata.namespace,
                'cluster_ip': service.spec.cluster_ip,
                'ports': [{'name': p.name, 'port': p.port, 'protocol': p.protocol} 
                         for p in service.spec.ports],
                'type': service.spec.type
            }
            
            # 3. 测试健康检查
            result['health_check'] = self.health_check()
            
            # 4. 测试认证（可选，谨慎使用）
            try:
                token = self._get_auth_token()
                result['auth_test'] = bool(token)
            except Exception as e:
                logger.warning(f"Auth test failed: {e}")
                result['auth_test'] = False
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Service discovery test failed: {e}")
        
        return result


# 全局服务发现客户端实例
service_discovery_client = IAMServiceDiscoveryClient()
