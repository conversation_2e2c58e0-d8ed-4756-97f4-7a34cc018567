"""
配置管理模块
"""
import os
from typing import Dict, Any


class Config:
    """配置管理类"""
    
    def __init__(self):
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        return {
            # IAM API 配置
            'iam_api_url': os.getenv(
                'IAM_API_URL',
                'http://iam-manage-xian.console.svc.cluster.local:8468'
            ),
            'iam_api_timeout': int(os.getenv('IAM_API_TIMEOUT', '30')),

            # IAM 服务发现配置
            'iam_service_discovery_enabled': os.getenv('IAM_SERVICE_DISCOVERY_ENABLED', 'false').lower() == 'true',
            'iam_service_namespace': os.getenv('IAM_SERVICE_NAMESPACE', 'console'),
            'iam_service_name': os.getenv('IAM_SERVICE_NAME', 'iam-manage-xian'),
            
            # 处理配置
            'processing_timeout': int(os.getenv('PROCESSING_TIMEOUT', '30')),
            'retry_interval': int(os.getenv('RETRY_INTERVAL', '60')),
            'max_retries': int(os.getenv('MAX_RETRIES', '3')),
            
            # 监控配置
            'metrics_port': int(os.getenv('METRICS_PORT', '8080')),
            'health_check_port': int(os.getenv('HEALTH_CHECK_PORT', '8081')),
            
            # 日志配置
            'log_level': os.getenv('LOG_LEVEL', 'INFO'),
            
            # 目标命名空间
            'target_namespaces': os.getenv(
                'TARGET_NAMESPACES', 
                'security,console,billing'
            ).split(','),
            
            # 状态存储
            'state_configmap_name': os.getenv('STATE_CONFIGMAP_NAME', 'iam-operator-state'),
            'state_configmap_namespace': os.getenv('STATE_CONFIGMAP_NAMESPACE', 'base'),
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self._config.get(key, default)
    
    @property
    def iam_api_url(self) -> str:
        return self._config['iam_api_url']
    
    @property
    def iam_api_timeout(self) -> int:
        return self._config['iam_api_timeout']
    
    @property
    def target_namespaces(self) -> list:
        return self._config['target_namespaces']
    
    @property
    def log_level(self) -> str:
        return self._config['log_level']
    
    @property
    def metrics_port(self) -> int:
        return self._config['metrics_port']


# 全局配置实例
config = Config()
