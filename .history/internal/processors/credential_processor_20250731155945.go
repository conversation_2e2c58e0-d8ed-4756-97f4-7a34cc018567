package processors

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"sigs.k8s.io/controller-runtime/pkg/log"

	"github.com/abc-stack/iam-operator/internal/clients"
	"github.com/abc-stack/iam-operator/internal/config"
)

// CredentialProcessor handles the processing of credentials found in ConfigMaps
type CredentialProcessor struct {
	iamClient *clients.IAMClient
	config    *config.Config
}

// NewCredentialProcessor creates a new credential processor
func NewCredentialProcessor(iamClient *clients.IAMClient, config *config.Config) *CredentialProcessor {
	return &CredentialProcessor{
		iamClient: iamClient,
		config:    config,
	}
}

// ProcessCredentials processes credentials from a ConfigMap
func (cp *CredentialProcessor) ProcessCredentials(ctx context.Context, sourceType, sourceName string, data map[string]string) error {
	logger := log.FromContext(ctx)

	logger.Info("Processing credentials",
		"source_type", sourceType,
		"source_name", sourceName)

	// Extract credentials from ConfigMap data
	credentials := cp.extractCredentials(data)

	if len(credentials) == 0 {
		logger.Info("No credentials found in ConfigMap", "source", sourceName)
		return nil
	}

	logger.Info("Found credentials",
		"source", sourceName,
		"count", len(credentials))

	// Process each credential
	for productName, cred := range credentials {
		if err := cp.processCredential(ctx, sourceName, productName, cred); err != nil {
			logger.Error(err, "Failed to process credential",
				"source", sourceName,
				"product", productName)
			// Continue processing other credentials even if one fails
		}
	}

	return nil
}

// extractCredentials extracts AKSK credentials from ConfigMap data
// Supports multiple formats:
// 1. Direct key-value pairs: access_key, secret_key, access-key, secret-key
// 2. PHP config files: $bss_ak, $bss_sk
// 3. YAML/JSON nested configs: iam.bss.ak, iam.bss.sk
// 4. Config file format: bss_ak=xxx, bss_sk=yyy
func (cp *CredentialProcessor) extractCredentials(data map[string]string) map[string]*Credential {
	credentials := make(map[string]*Credential)

	// Method 1: Extract direct key-value pairs (highest priority)
	directCreds := cp.extractDirectCredentials(data)
	for product, cred := range directCreds {
		credentials[product] = cred
	}

	// Method 2: Extract from file contents
	for fileName, content := range data {
		// Extract PHP-style credentials ($bss_ak, $bss_sk)
		phpCreds := cp.extractPHPCredentials(content)
		for product, cred := range phpCreds {
			credentials[fmt.Sprintf("%s_%s", product, fileName)] = cred
		}

		// Extract YAML/JSON-style credentials
		yamlCreds := cp.extractYAMLCredentials(content)
		for product, cred := range yamlCreds {
			credentials[fmt.Sprintf("%s_%s", product, fileName)] = cred
		}

		// Extract config file format credentials (key=value)
		configCreds := cp.extractConfigFileCredentials(content)
		for product, cred := range configCreds {
			credentials[fmt.Sprintf("%s_%s", product, fileName)] = cred
		}
	}

	return credentials
}

// extractPHPCredentials extracts credentials from PHP-style configuration
func (cp *CredentialProcessor) extractPHPCredentials(content string) map[string]*Credential {
	credentials := make(map[string]*Credential)

	// Pattern to match PHP variables like $bss_ak, $bss_sk
	akPattern := regexp.MustCompile(`\$([a-zA-Z0-9_]+)_ak\s*=\s*['"]([^'"]+)['"]`)
	skPattern := regexp.MustCompile(`\$([a-zA-Z0-9_]+)_sk\s*=\s*['"]([^'"]+)['"]`)

	akMatches := akPattern.FindAllStringSubmatch(content, -1)
	skMatches := skPattern.FindAllStringSubmatch(content, -1)

	// Create a map of products to access keys
	akMap := make(map[string]string)
	for _, match := range akMatches {
		if len(match) >= 3 {
			product := match[1]
			accessKey := match[2]
			akMap[product] = accessKey
		}
	}

	// Match secret keys with access keys
	for _, match := range skMatches {
		if len(match) >= 3 {
			product := match[1]
			secretKey := match[2]
			if accessKey, exists := akMap[product]; exists {
				credentials[product] = &Credential{
					AccessKey: accessKey,
					SecretKey: secretKey,
					Product:   product,
				}
			}
		}
	}

	return credentials
}

// extractYAMLCredentials extracts credentials from YAML/JSON-style configuration
func (cp *CredentialProcessor) extractYAMLCredentials(content string) map[string]*Credential {
	credentials := make(map[string]*Credential)

	// Simple pattern matching for YAML-style credentials
	// This is a basic implementation - could be enhanced with proper YAML parsing
	lines := strings.Split(content, "\n")

	var currentProduct string
	var currentAK, currentSK string

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// Look for product sections
		if strings.Contains(line, "iam:") || strings.Contains(line, "\"iam\"") {
			// Extract product name from context
			if strings.Contains(line, ":") {
				parts := strings.Split(line, ":")
				if len(parts) > 0 {
					currentProduct = strings.Trim(parts[0], " \"'")
				}
			}
		}

		// Look for access key
		if strings.Contains(line, "ak:") || strings.Contains(line, "\"ak\"") {
			if strings.Contains(line, ":") {
				parts := strings.Split(line, ":")
				if len(parts) > 1 {
					currentAK = strings.Trim(parts[1], " \"',")
				}
			}
		}

		// Look for secret key
		if strings.Contains(line, "sk:") || strings.Contains(line, "\"sk\"") {
			if strings.Contains(line, ":") {
				parts := strings.Split(line, ":")
				if len(parts) > 1 {
					currentSK = strings.Trim(parts[1], " \"',")
				}
			}
		}

		// If we have both keys, create credential
		if currentProduct != "" && currentAK != "" && currentSK != "" {
			credentials[currentProduct] = &Credential{
				AccessKey: currentAK,
				SecretKey: currentSK,
				Product:   currentProduct,
			}
			// Reset for next credential
			currentProduct, currentAK, currentSK = "", "", ""
		}
	}

	return credentials
}

// processCredential processes a single credential
func (cp *CredentialProcessor) processCredential(ctx context.Context, source, product string, cred *Credential) error {
	logger := log.FromContext(ctx)

	logger.Info("Validating credential",
		"source", source,
		"product", product,
		"access_key", cred.AccessKey[:8]+"...")

	// Validate credential through IAM API
	if err := cp.iamClient.AuthenticateAKSK(ctx, cred.AccessKey, cred.SecretKey); err != nil {
		return fmt.Errorf("credential validation failed for %s/%s: %w", source, product, err)
	}

	logger.Info("Credential validation successful",
		"source", source,
		"product", product)

	return nil
}

// Credential represents an AKSK credential pair
type Credential struct {
	AccessKey string
	SecretKey string
	Product   string
}

// IsIAMConfigMap checks if a ConfigMap contains IAM credentials
// Supports multiple real-world formats:
// 1. Direct key-value pairs: access_key, secret_key
// 2. PHP config files: $bss_ak, $bss_sk
// 3. YAML/JSON nested configs: iam.bss.ak, iam.bss.sk
// 4. Multi-product configs: bss_ak, waf_ak, etc.
func IsIAMConfigMap(data map[string]string, namespace string, targetNamespaces []string) bool {
	// Check if namespace is in target list
	isTargetNamespace := false
	for _, ns := range targetNamespaces {
		if ns == namespace {
			isTargetNamespace = true
			break
		}
	}

	if !isTargetNamespace {
		return false
	}

	// Method 1: Check direct credential keys (test cases and simple configs)
	hasAccessKey := false
	hasSecretKey := false

	for key := range data {
		// Standard formats
		if key == "access_key" || key == "accessKey" || key == "ACCESS_KEY" ||
			key == "access-key" {
			hasAccessKey = true
		}
		if key == "secret_key" || key == "secretKey" || key == "SECRET_KEY" ||
			key == "secret-key" {
			hasSecretKey = true
		}

		// Product-specific keys (e.g., bss_access_key, iam_ak)
		if strings.Contains(key, "access_key") || strings.Contains(key, "_ak") {
			hasAccessKey = true
		}
		if strings.Contains(key, "secret_key") || strings.Contains(key, "_sk") {
			hasSecretKey = true
		}
	}

	if hasAccessKey && hasSecretKey {
		return true
	}

	// Method 2: Check content patterns (PHP, config files, multi-product)
	for _, content := range data {
		// PHP format: $bss_ak = 'xxx'; $bss_sk = 'yyy';
		if strings.Contains(content, "_ak") && strings.Contains(content, "_sk") {
			return true
		}

		// YAML/JSON format: iam: { bss: { ak: xxx, sk: yyy } }
		if strings.Contains(content, "iam:") && strings.Contains(content, "ak:") {
			return true
		}

		// Config file format: bss_ak=xxx\nbss_sk=yyy
		if (strings.Contains(content, "ak=") || strings.Contains(content, "ak:")) &&
			(strings.Contains(content, "sk=") || strings.Contains(content, "sk:")) {
			return true
		}

		// PHP variable format: $product_ak and $product_sk
		if strings.Contains(content, "$") &&
			(strings.Contains(content, "_ak") || strings.Contains(content, "_sk")) {
			return true
		}
	}

	return false
}
