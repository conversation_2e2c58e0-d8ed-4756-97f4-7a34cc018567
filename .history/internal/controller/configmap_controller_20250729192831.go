/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	"github.com/abc-stack/iam-operator/internal/config"
	"github.com/abc-stack/iam-operator/internal/discovery"
	"github.com/abc-stack/iam-operator/internal/processors"
)

// ConfigMapReconciler reconciles a ConfigMap object
type ConfigMapReconciler struct {
	client.Client
	Scheme *runtime.Scheme

	// Dependencies
	Config              *config.Config
	ServiceDiscovery    *discovery.ServiceDiscovery
	CredentialProcessor *processors.CredentialProcessor
	KubernetesClient    kubernetes.Interface
}

// +kubebuilder:rbac:groups=core,resources=configmaps,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=configmaps/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=core,resources=configmaps/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// It processes ConfigMaps that contain IAM credentials and validates them through the IAM API.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.21.0/pkg/reconcile
func (r *ConfigMapReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	logger.Info("Reconciling ConfigMap", "namespace", req.Namespace, "name", req.Name)

	// Fetch the ConfigMap
	var configMap corev1.ConfigMap
	if err := r.Get(ctx, req.NamespacedName, &configMap); err != nil {
		if errors.IsNotFound(err) {
			// ConfigMap was deleted, nothing to do
			logger.Info("ConfigMap not found, likely deleted", "namespace", req.Namespace, "name", req.Name)
			return ctrl.Result{}, nil
		}
		logger.Error(err, "Failed to get ConfigMap")
		return ctrl.Result{}, err
	}

	// Check if this ConfigMap contains IAM credentials
	if !processors.IsIAMConfigMap(configMap.Data, configMap.Namespace, r.Config.TargetNamespaces) {
		logger.V(1).Info("ConfigMap does not contain IAM credentials, skipping",
			"namespace", req.Namespace, "name", req.Name)
		return ctrl.Result{}, nil
	}

	logger.Info("Processing IAM ConfigMap", "namespace", req.Namespace, "name", req.Name)

	// Process the credentials
	sourceName := fmt.Sprintf("%s/%s", configMap.Namespace, configMap.Name)
	if err := r.CredentialProcessor.ProcessCredentials(ctx, "configmap", sourceName, configMap.Data); err != nil {
		logger.Error(err, "Failed to process credentials", "source", sourceName)
		// Return error to trigger retry
		return ctrl.Result{RequeueAfter: r.Config.RetryInterval}, err
	}

	logger.Info("Successfully processed ConfigMap", "namespace", req.Namespace, "name", req.Name)
	return ctrl.Result{}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *ConfigMapReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&corev1.ConfigMap{}).
		Named("configmap").
		Complete(r)
}
