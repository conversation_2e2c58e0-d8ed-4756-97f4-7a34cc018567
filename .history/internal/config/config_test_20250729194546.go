package config

import (
	"os"
	"testing"
	"time"
)

func TestLoadConfig(t *testing.T) {
	// Save original environment
	originalEnv := make(map[string]string)
	envVars := []string{
		"IAM_SERVICE_DISCOVERY_ENABLED",
		"IAM_SERVICE_NAMESPACE",
		"IAM_SERVICE_NAME",
		"IAM_API_URL_FALLBACK",
		"IAM_API_TIMEOUT",
		"IAM_DOMAIN",
		"IAM_USERNAME",
		"IAM_PASSWORD",
		"TARGET_NAMESPACES",
		"RETRY_INTERVAL",
	}

	for _, env := range envVars {
		originalEnv[env] = os.Getenv(env)
	}

	// Clean environment
	defer func() {
		for _, env := range envVars {
			if val, exists := originalEnv[env]; exists {
				os.Setenv(env, val)
			} else {
				os.Unsetenv(env)
			}
		}
	}()

	// Clear all environment variables
	for _, env := range envVars {
		os.Unsetenv(env)
	}

	t.Run("Default configuration", func(t *testing.T) {
		config := LoadConfig()

		// Test defaults
		if config.IAMServiceDiscoveryEnabled != true {
			t.Errorf("Expected IAMServiceDiscoveryEnabled to be true, got %v", config.IAMServiceDiscoveryEnabled)
		}

		if config.IAMServiceNamespace != "console" {
			t.Errorf("Expected IAMServiceNamespace to be 'console', got %s", config.IAMServiceNamespace)
		}

		if config.IAMServiceName != "iam-manage-xian" {
			t.Errorf("Expected IAMServiceName to be 'iam-manage-xian', got %s", config.IAMServiceName)
		}

		if config.IAMAPITimeout != 30*time.Second {
			t.Errorf("Expected IAMAPITimeout to be 30s, got %v", config.IAMAPITimeout)
		}

		if len(config.TargetNamespaces) != 1 || config.TargetNamespaces[0] != "default" {
			t.Errorf("Expected TargetNamespaces to be ['default'], got %v", config.TargetNamespaces)
		}
	})

	t.Run("Custom configuration", func(t *testing.T) {
		// Set custom environment variables
		os.Setenv("IAM_SERVICE_DISCOVERY_ENABLED", "false")
		os.Setenv("IAM_SERVICE_NAMESPACE", "iam-system")
		os.Setenv("IAM_SERVICE_NAME", "iam-api")
		os.Setenv("IAM_API_URL_FALLBACK", "http://custom-iam:8080")
		os.Setenv("IAM_API_TIMEOUT", "60s")
		os.Setenv("IAM_DOMAIN", "custom")
		os.Setenv("IAM_USERNAME", "testuser")
		os.Setenv("IAM_PASSWORD", "testpass")
		os.Setenv("TARGET_NAMESPACES", "ns1,ns2,ns3")
		os.Setenv("RETRY_INTERVAL", "10m")

		config := LoadConfig()

		if config.IAMServiceDiscoveryEnabled != false {
			t.Errorf("Expected IAMServiceDiscoveryEnabled to be false, got %v", config.IAMServiceDiscoveryEnabled)
		}

		if config.IAMServiceNamespace != "iam-system" {
			t.Errorf("Expected IAMServiceNamespace to be 'iam-system', got %s", config.IAMServiceNamespace)
		}

		if config.IAMServiceName != "iam-api" {
			t.Errorf("Expected IAMServiceName to be 'iam-api', got %s", config.IAMServiceName)
		}

		if config.IAMAPIURLFallback != "http://custom-iam:8080" {
			t.Errorf("Expected IAMAPIURLFallback to be 'http://custom-iam:8080', got %s", config.IAMAPIURLFallback)
		}

		if config.IAMAPITimeout != 60*time.Second {
			t.Errorf("Expected IAMAPITimeout to be 60s, got %v", config.IAMAPITimeout)
		}

		if config.IAMDomain != "custom" {
			t.Errorf("Expected IAMDomain to be 'custom', got %s", config.IAMDomain)
		}

		if config.IAMUsername != "testuser" {
			t.Errorf("Expected IAMUsername to be 'testuser', got %s", config.IAMUsername)
		}

		if config.IAMPassword != "testpass" {
			t.Errorf("Expected IAMPassword to be 'testpass', got %s", config.IAMPassword)
		}

		expectedNamespaces := []string{"ns1", "ns2", "ns3"}
		if len(config.TargetNamespaces) != len(expectedNamespaces) {
			t.Errorf("Expected %d target namespaces, got %d", len(expectedNamespaces), len(config.TargetNamespaces))
		}

		for i, expected := range expectedNamespaces {
			if i >= len(config.TargetNamespaces) || config.TargetNamespaces[i] != expected {
				t.Errorf("Expected TargetNamespaces[%d] to be '%s', got '%s'", i, expected, config.TargetNamespaces[i])
			}
		}

		if config.RetryInterval != 10*time.Minute {
			t.Errorf("Expected RetryInterval to be 10m, got %v", config.RetryInterval)
		}
	})
}

func TestIsTargetNamespace(t *testing.T) {
	config := &Config{
		TargetNamespaces: []string{"default", "kube-system", "abc-stack"},
	}

	testCases := []struct {
		namespace string
		expected  bool
	}{
		{"default", true},
		{"kube-system", true},
		{"abc-stack", true},
		{"other", false},
		{"", false},
	}

	for _, tc := range testCases {
		t.Run(tc.namespace, func(t *testing.T) {
			result := config.IsTargetNamespace(tc.namespace)
			if result != tc.expected {
				t.Errorf("Expected IsTargetNamespace('%s') to be %v, got %v", tc.namespace, tc.expected, result)
			}
		})
	}
}

func TestValidate(t *testing.T) {
	t.Run("Valid configuration", func(t *testing.T) {
		config := &Config{
			IAMAPIURLFallback: "http://iam.example.com",
			IAMDomain:         "default",
			IAMUsername:       "user",
			IAMPassword:       "pass",
			TargetNamespaces:  []string{"default"},
		}

		err := config.Validate()
		if err != nil {
			t.Errorf("Expected valid configuration, got error: %v", err)
		}
	})

	t.Run("Missing IAM API URL", func(t *testing.T) {
		config := &Config{
			IAMDomain:        "default",
			IAMUsername:      "user",
			IAMPassword:      "pass",
			TargetNamespaces: []string{"default"},
		}

		err := config.Validate()
		if err == nil {
			t.Error("Expected error for missing IAM API URL")
		}
	})

	t.Run("Missing IAM credentials", func(t *testing.T) {
		config := &Config{
			IAMAPIURLFallback: "http://iam.example.com",
			IAMDomain:         "default",
			TargetNamespaces:  []string{"default"},
		}

		err := config.Validate()
		if err == nil {
			t.Error("Expected error for missing IAM credentials")
		}
	})

	t.Run("Empty target namespaces", func(t *testing.T) {
		config := &Config{
			IAMAPIURLFallback: "http://iam.example.com",
			IAMDomain:         "default",
			IAMUsername:       "user",
			IAMPassword:       "pass",
			TargetNamespaces:  []string{},
		}

		err := config.Validate()
		if err == nil {
			t.Error("Expected error for empty target namespaces")
		}
	})
}
