apiVersion: v1
kind: Namespace
metadata:
  name: iam-operator-system
  labels:
    control-plane: controller-manager
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: iam-operator-controller-manager
  namespace: iam-operator-system
  labels:
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: iam-operator-manager-role
  labels:
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
rules:
# ConfigMap permissions for credential processing (read-only)
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
# Service permissions for service discovery
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch"]
# Event permissions for logging
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
# Leader election permissions
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: iam-operator-manager-rolebinding
  labels:
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: iam-operator-manager-role
subjects:
- kind: ServiceAccount
  name: iam-operator-controller-manager
  namespace: iam-operator-system
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: iam-operator-config
  namespace: iam-operator-system
data:
  # IAM Service Discovery Configuration
  IAM_SERVICE_DISCOVERY_ENABLED: "true"
  IAM_SERVICE_NAMESPACE: "console"
  IAM_SERVICE_NAME: "iam-manage-xian"
  
  # IAM API Configuration
  IAM_API_URL_FALLBACK: "http://iam.xian.dev7.abcstackint.com:35357"
  IAM_API_TIMEOUT: "30s"
  IAM_DOMAIN: "default"
  IAM_USERNAME: "proxy"
  IAM_PASSWORD: "proxy"
  
  # Target Namespaces (comma-separated)
  TARGET_NAMESPACES: "default,console,kube-system"
  
  # Retry Configuration
  RETRY_INTERVAL: "5m"
  
  # Log Level
  LOG_LEVEL: "info"
---
apiVersion: v1
kind: Secret
metadata:
  name: iam-operator-secret
  namespace: iam-operator-system
type: Opaque
data:
  # Base64 encoded credentials
  # echo -n "proxy" | base64
  IAM_USERNAME: cHJveHk=
  IAM_PASSWORD: cHJveHk=
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: iam-operator-controller-manager
  namespace: iam-operator-system
  labels:
    control-plane: controller-manager
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
spec:
  selector:
    matchLabels:
      control-plane: controller-manager
      app.kubernetes.io/name: iam-operator-go
  replicas: 1
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
        app.kubernetes.io/name: iam-operator-go
    spec:
      nodeSelector:
        kubernetes.io/hostname: iaas5-kangding.dev7.abcstackint.com
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      containers:
      - command: ["/usr/local/bin/iam-operator"]
        args:
        - --leader-elect
        - --health-probe-bind-address=:8081
        image: iam-operator:local
        imagePullPolicy: Never
        name: manager
        ports: []
        env:
        - name: IAM_SERVICE_DISCOVERY_ENABLED
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: IAM_SERVICE_DISCOVERY_ENABLED
        - name: IAM_SERVICE_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: IAM_SERVICE_NAMESPACE
        - name: IAM_SERVICE_NAME
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: IAM_SERVICE_NAME
        - name: IAM_API_URL_FALLBACK
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: IAM_API_URL_FALLBACK
        - name: IAM_API_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: IAM_API_TIMEOUT
        - name: IAM_DOMAIN
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: IAM_DOMAIN
        - name: IAM_USERNAME
          valueFrom:
            secretKeyRef:
              name: iam-operator-secret
              key: IAM_USERNAME
        - name: IAM_PASSWORD
          valueFrom:
            secretKeyRef:
              name: iam-operator-secret
              key: IAM_PASSWORD
        - name: TARGET_NAMESPACES
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: TARGET_NAMESPACES
        - name: RETRY_INTERVAL
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: RETRY_INTERVAL
        securityContext:
          readOnlyRootFilesystem: true
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - "ALL"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
      serviceAccountName: iam-operator-controller-manager
      terminationGracePeriodSeconds: 10
