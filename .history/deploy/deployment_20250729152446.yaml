apiVersion: apps/v1
kind: Deployment
metadata:
  name: iam-operator
  namespace: base
  labels:
    app: iam-operator
    version: v1.0.0
spec:
  replicas: 1
  selector:
    matchLabels:
      app: iam-operator
  template:
    metadata:
      labels:
        app: iam-operator
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: iam-operator
      containers:
      - name: iam-operator
        image: registry.dev7.abcstackint.com:5000/abc-stack/hawk-python:20250521_1747797770085
        imagePullPolicy: IfNotPresent
        command: ["/bin/bash"]
        args:
          - -c
          - |
            echo "🚀 启动IAM Operator..."

            # 安装Python 3和依赖
            yum install -y epel-release python36 python36-pip
            ln -sf /usr/bin/python3.6 /usr/bin/python3
            ln -sf /usr/bin/pip3.6 /usr/bin/pip3

            # 安装依赖
            cd /app
            pip3 install -r requirements.txt

            # 启动operator
            python3 -m src.main
        env:
        # 从 ConfigMap 读取配置 - 服务发现模式
        - name: IAM_SERVICE_DISCOVERY_ENABLED
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: iam-service-discovery-enabled
        - name: IAM_SERVICE_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: iam-service-namespace
        - name: IAM_SERVICE_NAME
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: iam-service-name
        - name: IAM_API_URL_FALLBACK
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: iam-api-url-fallback
        - name: IAM_API_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: iam-api-timeout
        - name: PROCESSING_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: processing-timeout
        - name: RETRY_INTERVAL
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: retry-interval
        - name: MAX_RETRIES
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: max-retries
        - name: METRICS_PORT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: metrics-port
        - name: HEALTH_CHECK_PORT
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: health-check-port
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: log-level
        - name: TARGET_NAMESPACES
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: target-namespaces
        - name: STATE_CONFIGMAP_NAME
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: state-configmap-name
        - name: STATE_CONFIGMAP_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: iam-operator-config
              key: state-configmap-namespace
        ports:
        - name: metrics
          containerPort: 8080
          protocol: TCP
        - name: health
          containerPort: 8081
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /healthz
            port: health
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: health
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
      securityContext:
        fsGroup: 1000
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
