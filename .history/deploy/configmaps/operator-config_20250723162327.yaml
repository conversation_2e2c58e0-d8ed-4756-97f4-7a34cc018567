apiVersion: v1
kind: ConfigMap
metadata:
  name: iam-operator-config
  namespace: base
  labels:
    app: iam-operator
data:
  # IAM API 配置
  iam-api-url: "http://iam-manage-xian.console.svc.cluster.local:8468"
  iam-api-timeout: "30"
  
  # 处理配置
  processing-timeout: "30"
  retry-interval: "60"
  max-retries: "3"
  
  # 监控配置
  metrics-port: "8080"
  health-check-port: "8081"
  
  # 日志配置
  log-level: "INFO"
  
  # 目标命名空间（逗号分隔）
  target-namespaces: "security,console,billing"
  
  # 状态存储配置
  state-configmap-name: "iam-operator-state"
  state-configmap-namespace: "base"
