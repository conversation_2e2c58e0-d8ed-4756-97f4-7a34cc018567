FROM iregistry.baidu-int.com/abc-stack/lightxdb-centos7-base:v2.6-autofix

LABEL maintainer="lixianzhu" name="xdb-mysql"

ENV XDB_DEPLOY_ARCH centralized
ENV DEPLOY_PATH /home/<USER>/
ENV PACKAGE_PATH  /home/<USER>/lightxdb
ENV MIDDLEWARE_OUTPUT baidu/cloudbed/xdb-operator/output/images/


ENV PATH=/opt/compiler/gcc-8.2/bin:/opt/compiler/gcc-4.8.2/bin:$PATH


RUN mkdir -p /home/<USER>/xagent/conf  && \
    mkdir -p ${PACKAGE_PATH}/xdbcomponents-init  && \
    mkdir -p ${PACKAGE_PATH}/deploy  && \
    chown -R work:work /home/<USER>

COPY ${MIDDLEWARE_OUTPUT}/xdb-proxy/software/xagent.tar.gz  ${PACKAGE_PATH}/xdbcomponents-init/xagent.tar.gz
COPY ${MIDDLEWARE_OUTPUT}/xdb-mysql/software/mysql5744.tar.gz  ${PACKAGE_PATH}/xdbcomponents-init/mysql5744.tar.gz
COPY ${MIDDLEWARE_OUTPUT}/xdb-mysql/control.sh ${PACKAGE_PATH}/deploy/control.sh


# # USER work
WORKDIR /home/<USER>/

CMD ["/bin/bash", "-c", "/home/<USER>/lightxdb/deploy/control.sh start && sleep infinity"]
