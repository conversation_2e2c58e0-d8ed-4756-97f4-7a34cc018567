#!/bin/bash

#set -x

WORK_DIR="/home/<USER>"
ROOT_DIR="$(cd $(dirname $0); pwd)"
XAGENT_DIR="$WORK_DIR/xagent"
PACKAGE_NAME="$(basename $(dirname $ROOT_DIR))"
DATA_DIR=/home/<USER>
LIGHTXDB_DIR=${DATA_DIR}/lightxdb
MYSQL_DIR=${DATA_DIR}/mysql_3203
XAGENT_DIR=${DATA_DIR}/xagent
XAGENT_CONFIG_FILE=${XAGENT_DIR}/conf/xagent.conf
RECOVER_DIR=${DATA_DIR}/_recover_
RECOVER_CONF=${RECOVER_DIR}/snapshot.conf
# 获取当前用户
current_user=$(whoami)


apply_permissions() {
    chown -R work:work "${MYSQL_DIR}"
    chown -R work:work "${XAGENT_DIR}"

}

common_operation() {
    mkdir -p "${MYSQL_DIR}"
    mkdir -p "${XAGENT_DIR}"
}

set_xagent_conf() {
    if [[ -f "${XAGENT_CONFIG_FILE}" ]]; then
        timestamp=$(date +"%Y%m%d_%H%M%S")
        # 备份现有的xagent.conf文件
        cp "${XAGENT_CONFIG_FILE}" "${XAGENT_CONFIG_FILE}.${timestamp}.bak"
        echo "备份现有配置文件为 ${XAGENT_CONFIG_FILE}.${timestamp}.bak"
    fi

    # 生成xagent.conf文件

    cat <<EOF > "${XAGENT_CONFIG_FILE}"
[XAGENT_XAGENT]
port                 = 8500
ub_server_conf       = ub_server.conf
region               = bj
zookeeper_host       = ${ZOOKEEPER_HOST}


[XAGENT_MYSQL_3203]
port                 = 3203
basedir              = /home/<USER>/mysql_3203
app_id               = ${APP_ID}
cluster_id           = ${CLUSTER_ID}
zookeeper_host       = ${ZOOKEEPER_HOST}


[XAGENT_LOG_ROTATE]
log_switch = 3 
flush_dirty_page_open = 1 
max_log_size = 20G 
log_rotate_time = 05 00 * * * 
general_log_expired_time = 2592000
slow_log_expired_time   = 2592000
error_log_expired_time  = 2592000
relay_log_expired_time  = 86400
binlog_expired_time = 604800
dbproxy_log_expired_time = 604800
reserved_repl_logs_size = 100G    
reserved_common_logs_size = 100G
reserved_dbproxy_logs_size = ${DBPROXY_LOGS_SIZE}
backup_log_format = YYYYmmDDHH
max_disk_usage = 80
EOF
}

recovery() {
    # 创建日志文件
    
    # 创建恢复目录
    mkdir -p "${RECOVER_DIR}"

    # 检查并备份配置文件
    if [[ -f "${RECOVER_CONF}" ]]; then
        timestamp=$(date +"%Y%m%d_%H%M%S")
        cp "${RECOVER_CONF}" "${RECOVER_CONF}.${timestamp}.bak"
        echo "$(date +"%Y-%m-%d %H:%M:%S") - 备份现有配置文件为 ${RECOVER_CONF}.${timestamp}.bak" 
    else
        echo "$(date +"%Y-%m-%d %H:%M:%S") - 未找到配置文件 ${RECOVER_CONF}，跳过备份." 
    fi

    # 生成 xagent.conf 文件
    cat <<EOF > "${RECOVER_CONF}"
g_app_id=${RECOVER_XDB_APPID}
g_slice_id=${RECOVER_XDB_CLUSTER_ID}
g_mysql_base_dir=/home/<USER>/mysql_3203/
g_storage_type=fs
g_fs_base_dir=${RECOVER_DIR}
g_expire_duration=96h
EOF

    recover_path="${BackupFilePath/_backups_/_recover_}"

    # 去除最后一层（文件名）
    recover_path_dir="${recover_path%/*}"

    mkdir -p "${recover_path_dir}"

    # 创建软链接
    ln -s "${BackupFilePath}" "${recover_path}"
    echo "$(date +"%Y-%m-%d %H:%M:%S") - 创建软链接: ${BackupFilePath} -> ${recover_path}" 

    # 检查恢复条件
    if [[ -f "${BackupFilePath}" ]]; then
        echo "$(date +"%Y-%m-%d %H:%M:%S") - 开始恢复..." 
        su - work -c "bash ${XAGENT_DIR}/script/backup_lightxdb/backup_lightxdb_snapshot.sh ${RECOVER_CONF} recover"
        if [[ $? -eq 0 ]]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S") - 恢复成功." 
        else
            echo "$(date +"%Y-%m-%d %H:%M:%S") - 恢复失败."
            exit 1
        fi
    else
        echo "$(date +"%Y-%m-%d %H:%M:%S") - 备份文件 ${BackupFilePath} 不存在，跳过恢复." 
    fi
}

mysql_setup() {
    # 检查 MySQL 目录的 var 子目录是否存在
    if [[ ! -d "${MYSQL_DIR}/var" ]]; then
        echo "${MYSQL_DIR}/var 目录不存在，正在解压 MySQL..."
        tar -zxf ${LIGHTXDB_DIR}/xdbcomponents-init/mysql5744.tar.gz -C ${DATA_DIR}
        
        if [[ $? -ne 0 ]]; then
            echo "解压 MySQL 安装包失败"
        fi
    else
        echo "${MYSQL_DIR}/var 目录已存在，跳过解压。"
    fi
}



xagent_setup() {
    if [[ ! -f "${XAGENT_DIR}/load.sh" ]]; then
        echo "${XAGENT_DIR} 为空,正在解压xagent..."
        tar -zxf ${LIGHTXDB_DIR}/xdbcomponents-init/xagent.tar.gz -C ${DATA_DIR}
        if [[ $? -ne 0 ]]; then
            echo "解压xagent失败"
        fi
    fi
    if [[ ! -d "${XAGENT_DIR}/software/xtrabackup_baidu_gcc82" ]]; then
        echo "${XAGENT_DIR}/software/xtrabackup_baidu_gcc82 为空,正在解压xtrabackup ..."
        tar -zxf ${XAGENT_DIR}/software/xtrabackup_baidu_gcc82.tar.gz -C ${XAGENT_DIR}/software
        if [[ $? -ne 0 ]]; then
            echo "解压xagent失败"
        fi
    fi

}

start_xagent() {
    if [[ -f "${XAGENT_DIR}/load.sh" ]]; then
        echo "Checking if xagent is running..."
        if ! nc -z localhost 8500; then
            echo "xagent port is not active, starting xagent..."
            su - work -c "bash ${XAGENT_DIR}/load.sh start"
            if [[ $? -ne 0 ]]; then
                echo "Failed to start xagent..."
            fi
        else
            echo "xagent is already running."
        fi
    fi
}



uninstall() {
    :
}



start() {
    if ! nc -z localhost 3203; then 
        echo "mysql_3203 port is not active, starting mysql_3203..."
        su - work -c "bash ${MYSQL_DIR}/bin/mysql.server start"
        if [[ $? -ne 0 ]]; then
            echo "Failed to start mysql_3203..."
            exit 1
        fi
    else
        echo "mysql_3203 is already running."
    fi

    if [[ -f "${XAGENT_DIR}/load.sh" ]]; then
        if ! nc -z localhost 8500; then
            echo "xagent port is not active, starting xagent..."
            su - work -c "bash ${XAGENT_DIR}/load.sh start"
            if [[ $? -ne 0 ]]; then
                echo "Failed to start xagent..."
                exit 1
            fi
        else
            echo "xagent is already running."
        fi
    fi


}




stop() {

    if [[ -f "${XAGENT_DIR}/load.sh" ]]; then
        echo "stop xagent"
        su - work -c "bash ${XAGENT_DIR}/load.sh stop"
    fi

    if [[ -f "${MYSQL_DIR}/bin/mysql.server" ]]; then
        echo "stop mysql"
        su - work -c "bash ${MYSQL_DIR}/bin/mysql.server stop"
    fi

}

status() {

   success=1
      # check xagent service
    xagent_num=$(ps aux | grep "${XAGENT_DIR}/bin/xagent" | grep -v grep | wc -l)
    if [ ${xagent_num} -eq 2 ]; then
        echo "xagent service is running properly."
    else
        echo "xagent service is not running properly."
        success=0
    fi


       # check mysql service
    mysql_num=$(ps aux | grep -v grep | grep "${MYSQL_DIR}/libexec/mysqld" | wc -l)
    if [ ${mysql_num} -eq 1 ]; then
        echo "mysql service is running properly."
    else
        echo "mysql service is not running properly."
        success=0
    fi

}

main_function() {
    common_operation
    xagent_setup
    mysql_setup
    set_xagent_conf
    apply_permissions
}



case "$1" in
    "uninstall")
        uninstall
    ;;
    "install")
        install
    ;;
    "start")
        main_function
        start
    ;;
    "recover")
        main_function
        start
        recovery
    ;;
    "stop")
        stop
    ;;
    "status")
        status
    ;;
    *)
        exit -1
    ;;
esac




exit 0