#!/bin/bash

#set -x

WORK_DIR="/home/<USER>"
ROOT_DIR="$(cd $(dirname $0); pwd)"
XAGENT_DIR="$WORK_DIR/xagent"
PACKAGE_NAME="$(basename $(dirname $ROOT_DIR))"
DATA_DIR=/home/<USER>
LIGHTXDB_DIR=${DATA_DIR}/lightxdb
DBPROXY_DIR=${DATA_DIR}/dbproxy_6203
XAGENT_DIR=${DATA_DIR}/xagent
XAGENT_CONFIG_FILE=${XAGENT_DIR}/conf/xagent.conf
# 获取当前用户
current_user=$(whoami)

# 日志函数
log_info() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $*"; }
log_warn() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $*"; }
log_error() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $*"; }

# 信号处理函数
cleanup_and_exit() {
    log_info "Caught signal, stopping all services..."
    if [[ -f "/home/<USER>/xagent/load.sh" ]]; then
        log_info "Stopping xagent..."
        echo "$(date '+%Y-%m-%d %H:%M:%S') [SIGNAL] stop xagent..." >> /home/<USER>/xagent/log/stop.log
        bash /home/<USER>/xagent/load.sh stop >> /home/<USER>/xagent/log/stop.log 2>&1
    fi
    if [[ -f "/home/<USER>/dbproxy_6203/load.sh" ]]; then
        log_info "Stopping dbproxy_6203..."
        echo "$(date '+%Y-%m-%d %H:%M:%S') [SIGNAL] stop dbproxy_6203..." >> /home/<USER>/dbproxy_6203/log/stop.log
        bash /home/<USER>/dbproxy_6203/load.sh stop >> /home/<USER>/dbproxy_6203/log/stop.log 2>&1
    fi
    log_info "All services stopped successfully"
    exit 0
}


apply_permissions() {
    chown -R work:work /home/<USER>
}

common_operation() {
    mkdir -p "${DBPROXY_DIR}"
    mkdir -p "${XAGENT_DIR}"
}

set_xagent_conf() {
    if [[ -f "${XAGENT_CONFIG_FILE}" ]]; then
        timestamp=$(date +"%Y%m%d_%H%M%S")
        # 备份现有的xagent.conf文件
        cp "${XAGENT_CONFIG_FILE}" "${XAGENT_CONFIG_FILE}.${timestamp}.bak"
        echo "备份现有配置文件为 ${XAGENT_CONFIG_FILE}.${timestamp}.bak"
    fi
    # 生成xagent.conf文件
    cat <<EOF > "${XAGENT_CONFIG_FILE}"
[XAGENT_XAGENT]
port                 = 8500
ub_server_conf       = ub_server.conf
region               = bj
zookeeper_host       = ${ZOOKEEPER_HOST}


[XAGENT_DBPROXY_6203]
port                 = 6203
basedir              = ${DBPROXY_DIR}
zookeeper_host       = ${ZOOKEEPER_HOST}

[XAGENT_LOG_ROTATE]
log_switch = 3 
flush_dirty_page_open = 1 
max_log_size = 20G 
log_rotate_time = 05 00 * * * 
general_log_expired_time = 2592000
slow_log_expired_time   = 2592000
error_log_expired_time  = 2592000
relay_log_expired_time  = 86400
binlog_expired_time = 604800
dbproxy_log_expired_time = 604800
reserved_repl_logs_size = 100G    
reserved_common_logs_size = 100G
reserved_dbproxy_logs_size = ${DBPROXY_LOGS_SIZE}
backup_log_format = YYYYmmDDHH
max_disk_usage = 80
EOF
}

dbproxy_setup() {
    if [[ ! -f "${DBPROXY_DIR}/load.sh" ]]; then  # 修正判断条件
        echo "${DBPROXY_DIR} 为空,正在解压dbproxy..."
        tar -zxf ${LIGHTXDB_DIR}/xdbcomponents-init/dbproxy_1.4.37.7.tar.gz -C ${DATA_DIR}
        if [[ $? -ne 0 ]]; then
            echo "解压dbproxy失败"
        fi

    fi
}

xagent_setup() {
    if [[ ! -f "${XAGENT_DIR}/load.sh" ]]; then
        echo "${XAGENT_DIR} 为空,正在解压xagent..."
        tar -zxf ${LIGHTXDB_DIR}/xdbcomponents-init/xagent.tar.gz -C ${DATA_DIR}
        if [[ $? -ne 0 ]]; then
            echo "解压xagent失败"
        fi
    fi
}

start_xagent() {
    if [[ -f "${XAGENT_DIR}/load.sh" ]]; then
        echo "Checking if xagent is running..."
        if ! nc -z localhost 8500; then
            echo "xagent port is not active, starting xagent..."
            su - work -c "bash ${XAGENT_DIR}/load.sh start"
            if [[ $? -ne 0 ]]; then
                echo "Failed to start xagent..."
            fi
        else
            echo "xagent is already running."
        fi
    fi
}



uninstall() {
    :
}



start() {
    # 设置信号处理
    trap cleanup_and_exit SIGTERM SIGINT
    if [[ -f "/home/<USER>/dbproxy_6203/load.sh" ]]; then
            echo "Checking if dbproxy_6203 is running..."
            if ! nc -z localhost 6203; then
                echo "dbproxy_6203 port is not active, starting dbproxy_6203..."
                bash /home/<USER>/dbproxy_6203/load.sh start
                if [[ $? -ne 0 ]]; then
                    echo "Failed to start dbproxy_6203..."
                    exit 1
                fi
            else
                echo "dbproxy_6203 is already running."
            fi
    fi
    if [[ -f "/home/<USER>/xagent/load.sh" ]]; then
        echo "Checking if xagent is running..."
        if ! nc -z localhost 8500; then
            echo "xagent port is not active, starting xagent..."
            bash /home/<USER>/xagent/load.sh start
            if [[ $? -ne 0 ]]; then
                echo "Failed to start xagent..."
                exit 1
            fi
        else
            echo "xagent is already running."
        fi
    fi

    # 启动后 tail 日志并 wait
    log_file="/home/<USER>/dbproxy_6203/log/dbproxy.log.wf"
    if [ ! -f "$log_file" ]; then
        mkdir -p "$(dirname "$log_file")"
        touch "$log_file"
        chmod 644 "$log_file"
    fi
    log_info "Redirecting dbproxy log to stdout..."
    tail -f "$log_file" 1>&1 &
    log_info "All services started successfully, waiting for signals..."
    wait
}

stop() {
    if [[ -f "/home/<USER>/xagent/load.sh" ]]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') stop xagent..." >> /home/<USER>/xagent/log/stop.log
        bash /home/<USER>/xagent/load.sh stop >> /home/<USER>/xagent/log/stop.log 2>&1
    fi
    if [[ -f "/home/<USER>/dbproxy_6203/load.sh" ]]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') stop dbproxy_6203..." >> /home/<USER>/dbproxy_6203/log/stop.log
        bash /home/<USER>/dbproxy_6203/load.sh stop >> /home/<USER>/dbproxy_6203/log/stop.log 2>&1
    fi
}

status() {

   success=1
      # check xagent service
    xagent_num=$(ps aux | grep '/home/<USER>/xagent/bin/xagent' | grep -v grep | wc -l)
    if [ ${xagent_num} -eq 2 ]; then
        echo "xagent service is running properly."
    else
        echo "xagent service is not running properly."
        success=0
    fi


    # check dbproxy service
    dbproxy_num=$(ps aux | grep -v grep | grep "bin/dbproxy_6203" | wc -l)
    if [ ${dbproxy_num} -eq 5 ]; then
        echo "dbproxy service is running properly."
    else
        echo "dbproxy service is not running properly."
        success=0
    fi

    if [ ${success} -eq 1 ]; then
        exit 0
    else
        exit 1
    fi

}

main_function() {
    common_operation
    xagent_setup
    dbproxy_setup
    set_xagent_conf
    apply_permissions
}

init() {
    main_function
}

case "$1" in
    "uninstall")
        uninstall
    ;;
    "install")
        install
    ;;
    "init")
        init
    ;;
    "start")
        start
    ;;
    "stop")
        stop
    ;;
    "status")
        status
    ;;
    *)
        exit -1
    ;;
esac




exit 0