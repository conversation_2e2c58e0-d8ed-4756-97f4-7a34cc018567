# FROM iregistry.baidu-int.com/abc-stack/lightxdb-centos7-base:v2.6-autofix
# 集成了gcc4.8.2 8.2
FROM iregistry.baidu-int.com/abc-stack/lightxdb-centos7-base:v4.0     


LABEL maintainer="lix<PERSON><PERSON><PERSON>" name="xdb-proxy"

ENV XDB_DEPLOY_ARCH centralized
ENV DEPLOY_PATH /home/<USER>/
ENV PACKAGE_PATH  /home/<USER>/lightxdb
ENV OPERATOR_OUTPUT baidu/cloudbed/xdb-operator/output/images/xdb-proxy/
ENV MIDDLEWARE_OUTPUT baidu/cloudbed/middleware/output/

ENV PATH=/opt/compiler/gcc-8.2/bin:/opt/compiler/gcc-4.8.2/bin:$PATH

COPY ${MIDDLEWARE_OUTPUT}xdb-init/tools/dumb-init_1.2.5_x86_64 /usr/local/bin/dumb-init


RUN mkdir -p /home/<USER>/xagent/conf  && \
    mkdir -p ${PACKAGE_PATH}/xdbcomponents-init  && \
    mkdir -p ${PACKAGE_PATH}/deploy  && \
    chmod +x /usr/local/bin/dumb-init  && \
    chown -R work:work /home/<USER>

COPY ${OPERATOR_OUTPUT}/software/xagent.tar.gz  ${PACKAGE_PATH}/xdbcomponents-init/xagent.tar.gz
COPY ${OPERATOR_OUTPUT}/software/dbproxy_1.4.37.7.tar.gz  ${PACKAGE_PATH}/xdbcomponents-init/dbproxy_1.4.37.7.tar.gz
COPY ${OPERATOR_OUTPUT}/control.sh ${PACKAGE_PATH}/deploy/control.sh


# # USER work
WORKDIR /home/<USER>/

ENTRYPOINT ["/usr/local/bin/dumb-init", "--"]
CMD ["/home/<USER>/lightxdb/deploy/control.sh", "start"]
