# Build container
FROM iregistry.baidu-int.com/abc-stack/python:3.10-slim-base

ENV TZ=Asia/Shanghai
ENV DEPLOY_PATH  /xdb
ENV AGILE_OUTPUT baidu/cloudbed/xdb-operator/output/
ENV PYTHONPATH=/xdb:$PYTHONPATH

RUN mkdir -pv /xdb/xdboperator

WORKDIR /xdb

COPY ${AGILE_OUTPUT}/xdboperator /xdb/xdboperator

CMD ["kopf", "run"," /xdb/xdboperator/controller/operator.py", "-A", "--verbose","--liveness=http://0.0.0.0:8080/healthz"]
