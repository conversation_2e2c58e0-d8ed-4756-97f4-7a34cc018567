FROM iregistry.baidu-int.com/abc-stack/lightxdb-centos7-base:v2.6-autofix

# 全部组件融合在一起的镜像,对于chart流水线是xdb 一个总的流水线
LABEL maintainer="lixian<PERSON>hu" name="xdb"

ENV XDB_DEPLOY_ARCH centralized
ENV DEPLOY_PATH /home/<USER>/
ENV PACKAGE_PATH  /home/<USER>/lightxdb
ENV AGILE_OUTPUT baidu/paas-xcloud/lightxdb/output/
ENV PATH=/opt/compiler/gcc-8.2/bin:/opt/compiler/gcc-4.8.2/bin:/home/<USER>/mysql_3203/bin:/home/<USER>/xagent/bin:/home/<USER>/zone-master/bin:$PATH

COPY ${AGILE_OUTPUT}/xdbcomponents-init/xagent.tar.gz   ${PACKAGE_PATH}
# copy s6 etc
# COPY --chmod=755  ${AGILE_OUTPUT}/s6_etc/xdb/etc/s6-overlay /etc/s6-overlay


RUN mkdir -p /home/<USER>/dbporxy  && \
    mkdir -p /home/<USER>/xagent/conf  && \
    chown -R work:work /home/<USER>

# USER work
WORKDIR /home/<USER>/
ENTRYPOINT ["/init"]
CMD ["/bin/bash", "-c", "/home/<USER>/lightxdb/deploy/control.sh init-start-cluster && sleep infinity"]
