{"name": "Kubebuilder <PERSON>C<PERSON>r", "image": "golang:1.24", "features": {"ghcr.io/devcontainers/features/docker-in-docker:2": {}, "ghcr.io/devcontainers/features/git:1": {}}, "runArgs": ["--network=host"], "customizations": {"vscode": {"settings": {"terminal.integrated.shell.linux": "/bin/bash"}, "extensions": ["ms-kubernetes-tools.vscode-kubernetes-tools", "ms-azuretools.vscode-docker"]}}, "onCreateCommand": "bash .devcontainer/post-install.sh"}