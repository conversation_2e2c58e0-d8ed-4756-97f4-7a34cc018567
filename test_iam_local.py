#!/usr/bin/env python3
"""
本地测试 IAM API 的脚本
用于开发阶段测试 IAM 服务连接性和 API 调用
"""

import requests
import json
import os
import sys
from typing import Dict, Any

class IAMTester:
    def __init__(self):
        # IAM API 配置 - 基于项目中的配置
        self.base_urls = [
            "http://iam-openapi.console.svc.cluster.local:8480",
            "http://nmg02-bce-test6.nmg02.baidu.com:8468"
        ]
        self.timeout = 10
        
        # 认证信息 - 请根据实际情况修改
        self.auth_config = {
            "username": os.getenv("IAM_USERNAME", "test-user"),
            "password": os.getenv("IAM_PASSWORD", "test-password"),
            "domain": os.getenv("IAM_DOMAIN", "default")
        }
    
    def test_connectivity(self):
        """测试 IAM 服务连通性"""
        print("=== 测试 IAM 服务连通性 ===")
        
        for base_url in self.base_urls:
            print(f"\n测试服务: {base_url}")
            
            # 测试健康检查端点
            health_endpoints = ["/health", "/v3", "/"]
            
            for endpoint in health_endpoints:
                try:
                    url = f"{base_url}{endpoint}"
                    print(f"  测试端点: {url}")
                    
                    response = requests.get(url, timeout=self.timeout)
                    print(f"    状态码: {response.status_code}")
                    
                    if response.status_code < 400:
                        print(f"    ✅ 端点可访问")
                        if response.text:
                            print(f"    响应: {response.text[:100]}...")
                    else:
                        print(f"    ⚠️ 端点返回错误: {response.text[:100]}")
                        
                except requests.exceptions.ConnectTimeout:
                    print(f"    ❌ 连接超时")
                except requests.exceptions.ConnectionError:
                    print(f"    ❌ 连接错误")
                except Exception as e:
                    print(f"    ❌ 其他错误: {e}")
    
    def test_authentication(self, base_url: str):
        """测试 IAM 认证"""
        print(f"\n=== 测试 IAM 认证: {base_url} ===")
        
        auth_request = {
            "auth": {
                "identity": {
                    "methods": ["password"],
                    "password": {
                        "user": {
                            "name": self.auth_config["username"],
                            "password": self.auth_config["password"],
                            "domain": {"name": self.auth_config["domain"]}
                        }
                    }
                }
            }
        }
        
        try:
            url = f"{base_url}/v3/auth/tokens"
            print(f"认证端点: {url}")
            print(f"认证用户: {self.auth_config['username']}")
            
            response = requests.post(
                url,
                json=auth_request,
                headers={"Content-Type": "application/json"},
                timeout=self.timeout
            )
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 201:
                token = response.headers.get('X-Subject-Token')
                if token:
                    print(f"✅ 认证成功!")
                    print(f"Token: {token[:20]}...")
                    return token
                else:
                    print("⚠️ 认证响应中没有找到 X-Subject-Token")
            else:
                print(f"❌ 认证失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 认证请求失败: {e}")
        
        return None
    
    def test_api_calls(self, base_url: str, token: str):
        """测试其他 API 调用"""
        print(f"\n=== 测试 API 调用: {base_url} ===")
        
        headers = {
            "X-Auth-Token": token,
            "Content-Type": "application/json"
        }
        
        # 测试用户列表
        try:
            url = f"{base_url}/v3/users"
            print(f"测试用户列表: {url}")
            
            response = requests.get(url, headers=headers, timeout=self.timeout)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 用户列表获取成功")
                data = response.json()
                if 'users' in data:
                    print(f"用户数量: {len(data['users'])}")
            else:
                print(f"⚠️ 用户列表获取失败: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ 用户列表请求失败: {e}")
        
        # 测试域列表
        try:
            url = f"{base_url}/v3/domains"
            print(f"测试域列表: {url}")
            
            response = requests.get(url, headers=headers, timeout=self.timeout)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 域列表获取成功")
                data = response.json()
                if 'domains' in data:
                    print(f"域数量: {len(data['domains'])}")
            else:
                print(f"⚠️ 域列表获取失败: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ 域列表请求失败: {e}")
    
    def run_full_test(self):
        """运行完整测试"""
        print("IAM API 开发测试工具")
        print("=" * 50)
        
        # 1. 测试连通性
        self.test_connectivity()
        
        # 2. 对每个可用的服务测试认证和 API 调用
        for base_url in self.base_urls:
            token = self.test_authentication(base_url)
            if token:
                self.test_api_calls(base_url, token)
        
        print("\n" + "=" * 50)
        print("测试完成!")
        print("\n使用说明:")
        print("1. 如果连通性测试失败，请检查网络连接")
        print("2. 如果认证失败，请检查用户名密码配置")
        print("3. 可以通过环境变量设置认证信息:")
        print("   export IAM_USERNAME=your-username")
        print("   export IAM_PASSWORD=your-password")
        print("   export IAM_DOMAIN=your-domain")

def main():
    """主函数"""
    tester = IAMTester()
    tester.run_full_test()

if __name__ == "__main__":
    main()
