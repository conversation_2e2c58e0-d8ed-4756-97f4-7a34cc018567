appspace:
  charts:
    provides:
      iamPipeline:
        interface: iam-pipeline
        domain: iam-pipeline.agilecloud.com
        port: 8298
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      iam:
        interface: iam-nginx
        optional: false
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        console_iam:
          ak: '{{ console_iam_ak }}'
          sk: '{{ console_iam_sk }}'
          userId: '{{ console_iam_user_id }}'
          password: '{{ console_iam_password }}'
    configVals:
      proxyTopic: 0cd901398654410caeb745dc8d0c9957__bce_iam_core
      memoryChannel_capacity: 40000
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
    securityContext: {}
    security:
      enable: false
    middleware:
      kafka:
        topic:
          clusterName: '{{ kafka_cluster_name }}'
          domain: '{{ kafka_domain }}'
          topicName: 0cd901398654410caeb745dc8d0c9957__bce_iam_core
          replication: 3
          partitions: 30
          port: 8959
    replica: 3
    namespace: console
    images:
      iam-pipeline:
        repository: abc-stack/iam-pipeline
        imageTag: ${iam-pipeline.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-pipeline
      command: []
      args: []
      log:
        - dirPaths:
            - /home/<USER>/pipeline/log/
          logType: local
          logVolume: iam-pipeline-log
          rule:
            duration: 7
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 4000m
        limitMemory: 6000Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-pipeline
        mountPath: /home/<USER>/pipeline/conf/flume-conf.properties.pipeline
        subPath: flume-conf.properties.pipeline
        name: iam-pipeline
      - type: CONFIG_MAP
        configName: iam-pipeline
        mountPath: /home/<USER>/pipeline/conf/iamConf.properties
        subPath: iamConf.properties
        name: iam-pipeline
      - type: CONFIG_MAP
        configName: iam-pipeline
        mountPath: /home/<USER>/pipeline/conf/log4j2.xml
        subPath: log4j2.xml
        name: iam-pipeline
      - type: CONFIG_MAP
        configName: iam-pipeline
        mountPath: /home/<USER>/pipeline/bin/pipeline_control
        subPath: pipeline_control
        name: iam-pipeline
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: false
        readinessProbe:
          enable: true
          type: EXEC
          cmd: [ "/bin/bash", "-c", "ps -ef | grep flume | grep pipeline | grep -v grep" ]
          initialDelaySeconds: 5
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
          timeoutSeconds: 5
        startupProbe:
          enable: false
      volume:
        - name: iam-pipeline-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/pipeline/log/
          storageType: local-path
          size: 30Gi
      ports:
      - name: 8298-tcp
        containerPort: 8298
        targetPort: 8298
        servicePort: 8298
        protocol: TCP
        nodePort: ''
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
