agent.sources = iamCoreKafkaSource
agent.channels = loadBalanceChannel1 loadBalanceChannel2 loadBalanceChannel3
agent.sinks = iamCoreEsSink1 iamCoreEsSink2 iamCoreEsSink3
agent.sources.iamCoreKafkaSource.selector.type = com.baidu.bce.iam.selector.MyLoadBalancingChannelSelector
# For each one of the sources, the type is defined
agent.sources.iamCoreKafkaSource.type = org.apache.flume.source.kafka.KafkaSource
agent.sources.iamCoreKafkaSource.kafka.bootstrap.servers = {{.Values.appspace.charts.middleware.kafka.topic.domain}}:{{.Values.appspace.charts.middleware.kafka.topic.port}}
agent.sources.iamCoreKafkaSource.kafka.topics = {{.Values.appspace.charts.configVals.proxyTopic}}
agent.sources.iamCoreKafkaSource.kafka.consumer.group.id = iamcore-consumer-online4
agent.sources.iamCoreKafkaSource.kafka.consumer.batchSize = 1000
agent.sources.iamCoreKafkaSource.kafka.consumer.maxBatchSize = 1000
agent.sources.iamCoreKafkaSource.kafka.consumer.session.timeout.ms = 30000
agent.sources.iamCoreKafkaSource.kafka.consumer.heartbeat.interval.ms = 10000
agent.sources.iamCoreKafkaSource.kafka.consumer.max.poll.records = 300
agent.sources.iamCoreKafkaSource.kafka.consumer.max.poll.interval.ms = 5000000
agent.sources.iamCoreKafkaSource.flumeBatchSize = 1000
# The channel can be defined as follows.
agent.sources.iamCoreKafkaSource.channels = loadBalanceChannel1 loadBalanceChannel2 loadBalanceChannel3
# Each sink's type must be defined
agent.sinks.Logger.type = logger
agent.sinks.iamCoreEsSink1.type = com.baidu.bce.iam.sink.IamElasticSearchSink
agent.sinks.iamCoreEsSink1.hosts = http://testagilecloud.com:40004
agent.sinks.iamCoreEsSink1.readTimeOut = 10000
agent.sinks.iamCoreEsSink1.username = superuser
agent.sinks.iamCoreEsSink1.passwd = IAM_iamcore
agent.sinks.iamCoreEsSink1.batchSize = 1000
agent.sinks.iamCoreEsSink2.type = com.baidu.bce.iam.sink.IamElasticSearchSink
agent.sinks.iamCoreEsSink2.hosts = http://testagilecloud.com:40004
agent.sinks.iamCoreEsSink2.readTimeOut = 10000
agent.sinks.iamCoreEsSink2.username = superuser
agent.sinks.iamCoreEsSink2.passwd = IAM_iamcore
agent.sinks.iamCoreEsSink2.batchSize = 1000
agent.sinks.iamCoreEsSink3.type = com.baidu.bce.iam.sink.IamElasticSearchSink
agent.sinks.iamCoreEsSink3.hosts = http://testagilecloud.com:40004
agent.sinks.iamCoreEsSink3.readTimeOut = 10000
agent.sinks.iamCoreEsSink3.username = superuser
agent.sinks.iamCoreEsSink3.passwd = IAM_iamcore
agent.sinks.iamCoreEsSink3.batchSize = 1000
#Specify the channel the sink should use
agent.sinks.iamCoreEsSink1.channel = loadBalanceChannel1
agent.sinks.iamCoreEsSink2.channel = loadBalanceChannel2
agent.sinks.iamCoreEsSink3.channel = loadBalanceChannel3
# Each channel's type is defined.
agent.channels.loadBalanceChannel1.type = memory
agent.channels.loadBalanceChannel2.type = memory
agent.channels.loadBalanceChannel3.type = memory
# Other config values specific to each type of channel(sink or source)
# can be defined as well
# In this case, it specifies the capacity of the memory channel
agent.channels.loadBalanceChannel1.capacity = 40000
agent.channels.loadBalanceChannel1.transactionCapacity = 20000
agent.channels.loadBalanceChannel2.capacity = 40000
agent.channels.loadBalanceChannel2.transactionCapacity = 20000
agent.channels.loadBalanceChannel3.capacity = 40000
agent.channels.loadBalanceChannel3.transactionCapacity = 20000