{{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
endpoint=http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3
username=console_iam
password={{.Values.appspace.charts.requires.iam.console_iam.password}}
maximumCacheSize=20000
accessKeyUpdateScheduleInterval=15
accessKeyUpdateScheduleInitialDelay=1
accessKeyUpdateRate=25