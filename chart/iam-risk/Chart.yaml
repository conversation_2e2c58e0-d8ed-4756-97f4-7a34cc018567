name: iam-risk
apiVersion: v2
version: 0.1.0
description: 认证鉴权组件,不止是对内部服务间互访会走IAM认证鉴权, 外部的请求比如说我们的SDK、openapi过来的请求也会走认证鉴权！
maintainers:
- name: jinshuai01
  email: <EMAIL>
annotations:
  arch: x86_64
  level: global
  grade: 关键组件
  deployStage: 公共服务阶段
  impactRange: 影响用户登录、整个云平台鉴权操作,包括内部的服务互访也会有问题!
  minReplica: 1
  maxReplica: 99999
  stepReplica: 1
  isStatus: false
  statefulProtocol: '-'
  subsystemEn: IAM
  subsystem: 账户管理IAM
  os: '-'
  category: 运营
  dependencies: |
    - name: iam-nginx
    - name: iam-sts
    - name: xdb-schema-init-bce-iam
    - name: redis
  buildInfo: ''
