appspace:
  charts:
    provides:
      iamRisk:
        interface: iam-risk
        domain: iam-risk.agilecloud.com
        port: 8198
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      hacluster:
        interface: hacluster
        optional: false
        domain: '{{ hacluster_domain }}'
        port: '{{ hacluster_port }}'
      iamSts:
        interface: iam-sts
        optional: false
        domain: '{{ iam_sts_domain }}'
        port: '{{ iam_sts_port }}'
      iam:
        interface: iam-nginx
        optional: false
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        proxy:
          ak: '{{ proxy_ak }}'
          sk: '{{ proxy_sk }}'
          userId: '{{ proxy_user_id }}'
          password: '{{ proxy_password }}'
      privateSkywalkingGrpc:
        interface: private-skywalking-grpc
        optional: true
        domain: '{{ private_skywalking_grpc_domain }}'
        port: '{{ private_skywalking_grpc_port }}'
    configVals:
      riskKieRulePath: ../conf/rule.drl
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
    securityContext: {}
    security:
      enable: false
    middleware:
      redis:
        clusterName: '{{ redis_cluster_name }}'
        domain: '{{ redis_domain }}'
        password: '{{ redis_password }}'
        version: 5
        port: 6379
      xdb:
        bce_iam:
          clusterName: '{{ bce_iam_cluster_name }}'
          user: '{{ bce_iam_user }}'
          password: '{{ bce_iam_password }}'
          domain: '{{ bce_iam_domain }}'
          port: 6203
          permission: write
    replica: 3
    namespace: console
    images:
      iam-risk:
        repository: abc-stack/iam-risk
        imageTag: ${iam-risk.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-risk
      command: []
      args: []
      log:
        - dirPaths:
            - "/home/<USER>/risk/log/access"
            - "/home/<USER>/risk/log/debug"
            - "/home/<USER>/risk/log/error"
            - "/home/<USER>/risk/log/info"
          logType: local
          logVolume: iam-risk-log
          rule:
            duration: 3
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 4000m
        limitMemory: 4096Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-risk
        mountPath: /home/<USER>/risk/conf/endpoint.json
        subPath: endpoint.json
        name: iam-risk
      - type: CONFIG_MAP
        configName: iam-risk
        mountPath: /home/<USER>/risk/conf/application.properties
        subPath: application.properties
        name: iam-risk
      - type: CONFIG_MAP
        configName: iam-risk
        mountPath: /home/<USER>/risk/conf/iamConf.properties
        subPath: iamConf.properties
        name: iam-risk
      - type: CONFIG_MAP
        configName: iam-risk
        mountPath: /home/<USER>/risk/bin/skywalking-agent/config/agent.config
        subPath: agent.config
        name: iam-risk
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8198
          initialDelaySeconds: 15
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-risk-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/risk/log/
          storageType: local-path
          size: 30Gi
      ports:
      - name: 8198-tcp
        containerPort: 8198
        targetPort: 8198
        servicePort: 8198
        protocol: TCP
        nodePort: ''
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
