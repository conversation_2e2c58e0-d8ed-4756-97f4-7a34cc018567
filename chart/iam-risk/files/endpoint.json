{
    "regions": [
    {
        "region": "bj",
            "services": [
            {
                "service": "PASSPORT",
                "endpoint": "http://10.94.38.41:7801"
            },
            {
                "service": "UC",
                "endpoint": "mycas.baidu.com:8880"
            },
            {
                "service": "STS",
                {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
                "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iamSts.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iamSts.port }}/v1"
            },
            {
                "service": "IAM",
                {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
                "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
            }
        ]
    },
    {{- range $i, $region := $.Values.appspace.charts.global.regions }}
    {{- if $i -}}
    ,
    {{- end }}
    {{- $curRegion :=$region.region }}
    {
        "region": "{{ $region.region}}",
        "services": [
            {
                "service": "IAM",
                "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
            },
            {
                "service": "STS",
                "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iamSts.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iamSts.port }}/v1"
            }
        ]
    }
    {{- end }}


    ]
}
