package bcepass_rule

import com.baidu.bce.iam.facade.constant.risk.RiskLevel
import com.baidu.bce.iam.facade.constant.risk.RiskLevel
import com.baidu.bce.iam.facade.constant.bcepass.UserOperate
import com.baidu.bce.iam.risk.fact.impl.BcePassFact

// salience高的rule先执行

// 环境发生改变，比如IP和登录时的不一致
rule "context_modified"
salience 10100
    when
        $context: BcePassFact(isIpModified());
    then
        $context.setMessage("context_modified");
        $context.setRiskLevel(RiskLevel.RISK_LEVEL_HIGH);
end

// 长时间未登录，超过7天
rule "long_time_no_login"
salience 10099
    when
        $context: BcePassFact(System.currentTimeMillis()-lastLoginTimestamp>7*24*60*60*1000);
    then
        $context.setMessage("long_time_no_login");
        $context.setRiskLevel(RiskLevel.RISK_LEVEL_HIGH);
end

// 命中黑名单
rule "black_list"
salience 10098
    when
        $context: BcePassFact(isIpInBlackList());
    then
        $context.setMessage("black_list");
        $context.setRiskLevel(RiskLevel.RISK_LEVEL_HIGH);
end

// ip登陆失败次数超过5次(24小时内)
rule "ip_login_failed_limit"
salience 10097
    when
        $context: BcePassFact(getIpLoginFailedCount()>5);
    then
        $context.setMessage("ip_login_failed_limit");
        $context.setRiskLevel(RiskLevel.RISK_LEVEL_HIGH);
end

// 用户失败次数超过5次(24小时内)
rule "user_login_failed_limit"
salience 10097
    when
        $context: BcePassFact(getUserLoginFailedCount()>5);
    then
    $context.setMessage("user_login_failed_limit");
        $context.setRiskLevel(RiskLevel.RISK_LEVEL_VERY_HIGH);
end

// pass验证过手机或者邮箱
rule "pass_verified"
salience 3
    when
        $context: BcePassFact(isVerifiedMobile() || isVerifiedEmail());
    then
        $context.setMessage("pass_verified");
        $context.setRiskLevel(RiskLevel.RISK_LEVEL_NONE);
end

// bce mfa验证过
rule "mfa_verified"
salience 2
    when
        $context: BcePassFact(isSafeEnviroment());
    then
        $context.setMessage("mfa_verified");
        $context.setRiskLevel(RiskLevel.RISK_LEVEL_NONE);
    end

// 开启登录MFA && 关闭登录保持, 无论有没有mfaVerify记录, riskLevel=High
// 开启登录MFA && 开启登录保持, 无mfaVerify记录, riskLevel=High
// 开启登录MFA && 开启登录保持, 有mfaVerify记录, 继续过其他的规则
// 未开启登录MFA, 继续过其他的规则
rule "login_protection"
salience 1
    when
        $context: BcePassFact(
            (getUserOperate() ==UserOperate.LOGIN && isLoginMfaEnable() && !isLoginKeepEnable())
            || (getUserOperate() ==UserOperate.LOGIN && isLoginMfaEnable() && isLoginKeepEnable() && !isInLoginKeepTimeWindow())
        );
    then
        $context.setMessage("login_protection");
        $context.setRiskLevel(RiskLevel.RISK_LEVEL_HIGH);
    end

// 命中白名单
rule "white_list"
salience 0
    when
        $context: BcePassFact(isIpInWhiteList());
    then
        $context.setMessage("white_list");
        $context.setRiskLevel(RiskLevel.RISK_LEVEL_NONE);
end
