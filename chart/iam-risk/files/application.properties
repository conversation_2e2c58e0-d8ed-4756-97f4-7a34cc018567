spring.application.name=iam-risk
bce_plat_web_framework.sc.enabled=false
server.port={{.Values.appspace.charts.provides.iamRisk.port}}

# 日志级别调整
logging.level.org.springframework.web=DEBUG
logging.has_access_log=false
logging.has_request-id=false

# 关闭spring-cloud的认证校验
management.security.enabled=false
security.basic.enabled=false

# iam-risk 配置
iam.service.id=mock_service_id
iam.client.conf=../conf/iamConf.properties
kafka.config=file:../conf/kafka.properties
endpoint.default.regionName:{{.Values.appspace.charts.platformConfigVals.region}}
endpoint.config=file:../conf/endpoint.json
risk.kie.rulePath={{.Values.appspace.charts.configVals.riskKieRulePath}}
# risk.es.server.read=http://cq02-bce-iam-data00.cq02:8200
# risk.es.username.read=root
# risk.es.password.read=bLg27uVKJr3tuTXg
# risk.es.server.write=http://cq02-bce-iam-data01.cq02:8200
# risk.es.username.write=root
# risk.es.password.write=bLg27uVKJr3tuTXg
# risk.es.read.readTimeOut=200
# risk.es.write.readTimeOut=30000
mfa.ttl.seconds:604800
 
iam.filter.authenticate=true
logging.access_dir=/home/<USER>/risk/log/access
passport.app.id=1240
# uc login resolver
uc.app.id=285
cookie.domain=.bcetest.baidu.com
# tomcat配置
server.max-http-header-size=1024000



# 数据库访问配置
# 主数据源，默认的
spring.datasource.master.url=jdbc:mysql://{{.Values.appspace.charts.middleware.xdb.bce_iam.domain}}:{{.Values.appspace.charts.middleware.xdb.bce_iam.port}}/bce_iam
spring.datasource.master.username={{.Values.appspace.charts.middleware.xdb.bce_iam.user}}
spring.datasource.master.password={{.Values.appspace.charts.middleware.xdb.bce_iam.password}}
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
# 数据库访问配置
# # 主数据源，默认的
# spring.datasource.master.url=***************************************
# spring.datasource.master.username=bce_iam_w
# spring.datasource.master.password=bLg27uVKJr3tuTXg
# spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

#指定bean所在包
mybatis.type-aliases-package=com.baidu.bce.iam.keystone
#指定映射文件
mybatis.mapperLocations=classpath:mapper/*.xml
# 下面为连接池的补充设置，应用到上面所有数据源中
# 初始化大小，最小，最大
spring.datasource.initialSize=20
spring.datasource.minIdle=20
spring.datasource.maxActive=100
spring.datasource.maxWait=60000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.timeBetweenEvictionRunsMillis=60000
# 配置一个连接在池中最小生存的时间，单位是毫秒
spring.datasource.minEvictableIdleTimeMillis=300000
# 打开PSCache，并且指定每个连接上PSCache的大小
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
# 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
spring.datasource.filters=stat,wall
# 通过connectProperties属性来打开mergeSql功能；慢SQL记录
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.validation-query=SELECT 1
spring.datasource.validation-interval=30000
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false
spring.datasource.log-abandoned=true
spring.datasource.remove-abandoned=true
spring.datasource.remove-abandoned-timeout=60
spring.datasource.queryTimeout=5000
spring.datasource.loginTimeout=5000
spring.datasource.socketTimeout=2000
 
# 缓存相关配置
spring.cache.type=redis
spring.cache.cache-names=iamRisk
# spring.redis.host=*************
# spring.redis.port=9010
# spring.redis.password=CeGg631QVax-yvAfub5rsShcd8pDtqJk
# spring.redis.jedis.pool.max-wait=1000ms
spring.redis.host={{.Values.appspace.charts.middleware.redis.domain}}
spring.redis.port={{.Values.appspace.charts.middleware.redis.port}}
spring.redis.password={{.Values.appspace.charts.middleware.redis.password}}
spring.redis.pool.max-wait=1000

mfa.trails.enabled=false
iam.trails.enabled=false
quota.trails.enabled=false
risk.trails.enabled=false
risk.es.server.enable=false

osp.service.list=yunying,admin,osp-product

sc.webframework.ScLoggerConfiguration.enable=false
webframework.register.center.env.vip.enabled=false
webframework.register.center.tianlu.enabled=false
management.health.formula-discovery.enabled=false
spring.cloud.discovery.client.health-indicator.enabled=false