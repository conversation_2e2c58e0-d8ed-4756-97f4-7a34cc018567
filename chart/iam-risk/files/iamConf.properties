iam.region={{.Values.appspace.charts.platformConfigVals.region}}
{{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
host={{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}
port={{ $.Values.appspace.charts.requires.iam.port }}
protocol=http
username=proxy
password={{.Values.appspace.charts.requires.iam.proxy.password}}
default_domain=default
max_cache_size=10000
cache_time_to_live=300
cache_token_discard_ttl=30
sub_user_support=false
