<?xml version="1.0"?>
<!--
  ~ Copyright (C) 2020 Baidu, Inc. All Rights Reserved.
  -->
<EntityDescriptor xmlns="urn:oasis:names:tc:SAML:2.0:metadata" xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
                  xmlns:ds="http://www.w3.org/2000/09/xmldsig#" entityID="urn:bce:baidu:webservices">
    <SPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol" WantAssertionsSigned="true">
        <KeyDescriptor use="signing">
            <ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#">
                <ds:X509Data>
                    <ds:X509Certificate>MIIDUjCCAjqgAwIBAgIEUOLIQTANBgkqhkiG9w0BAQUFADBrMQswCQYDVQQGEwJGSTEQMA4GA1UE CBMHVXVzaW1hYTERMA8GA1UEBxMISGVsc2lua2kxGDAWBgNVBAoTD1JNNSBTb2Z0d2FyZSBPeTEM MAoGA1UECwwDUiZEMQ8wDQYDVQQDEwZhcG9sbG8wHhcNMTMwMTAxMTEyODAxWhcNMjIxMjMwMTEy ODAxWjBrMQswCQYDVQQGEwJGSTEQMA4GA1UECBMHVXVzaW1hYTERMA8GA1UEBxMISGVsc2lua2kx GDAWBgNVBAoTD1JNNSBTb2Z0d2FyZSBPeTEMMAoGA1UECwwDUiZEMQ8wDQYDVQQDEwZhcG9sbG8w ggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCXqP0wqL2Ai1haeTj0alwsLafhrDtUt00E 5xc7kdD7PISRA270ZmpYMB4W24Uk2QkuwaBp6dI/yRdUvPfOT45YZrqIxMe2451PAQWtEKWF5Z13 F0J4/lB71TtrzyH94RnqSHXFfvRN8EY/rzuEzrpZrHdtNs9LRyLqcRTXMMO4z7QghBuxh3K5gu7K qxpHx6No83WNZj4B3gvWLRWv05nbXh/F9YMeQClTX1iBNAhLQxWhwXMKB4u1iPQ/KSaal3R26pON UUmu1qVtU1quQozSTPD8HvsDqGG19v2+/N3uf5dRYtvEPfwXN3wIY+/R93vBA6lnl5nTctZIRsyg 0Gv5AgMBAAEwDQYJKoZIhvcNAQEFBQADggEBAFQwAAYUjso1VwjDc2kypK/RRcB8bMAUUIG0hLGL 82IvnKouGixGqAcULwQKIvTs6uGmlgbSG6Gn5ROb2mlBztXqQ49zRvi5qWNRttir6eyqwRFGOM6A 8rxj3Jhxi2Vb/MJn7XzeVHHLzA1sV5hwl/2PLnaL2h9WyG9QwBbwtmkMEqUt/dgixKb1Rvby/tBu RogWgPONNSACiW+Z5o8UdAOqNMZQozD/i1gOjBXoF0F5OksjQN7xoQZLj9xXefxCFQ69FPcFDeEW bHwSoBy5hLPNALaEUoa5zPDwlixwRjFQTc5XXaRpgIjy/2gsL8+Y5QRhyXnLqgO67BlLYW/GuHE=</ds:X509Certificate>
                </ds:X509Data>
            </ds:KeyInfo>
        </KeyDescriptor>
        <NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:transient</NameIDFormat>
        <NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:persistent</NameIDFormat>
        <NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress</NameIDFormat>
        <AssertionConsumerService index="1" isDefault="true" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="{{.Values.appspace.charts.configVals.login_protocol}}://{{.Values.appspace.charts.requires.nginxLogin.domain}}/saml" />
        <AttributeConsumingService index="1">
            <ServiceName xml:lang="en">BCE Management Console Single Sign-On</ServiceName>
            <RequestedAttribute isRequired="true" Name="https://bce.baidu.com/SAML/Attributes/Role" FriendlyName="RoleEntitlement" />
            <RequestedAttribute isRequired="true" Name="https://bce.baidu.com/SAML/Attributes/RoleSessionName" FriendlyName="RoleSessionName" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduPersonAffiliation" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduPersonNickname" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduPersonOrgDN" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduPersonOrgUnitDN" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduPersonPrimaryAffiliation" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduPersonPrincipalName" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduPersonEntitlement" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduPersonPrimaryOrgUnitDN" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduPersonScopedAffiliation" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******0" FriendlyName="eduPersonTargetedID" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******1" FriendlyName="eduPersonAssurance" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduOrgHomePageURI" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduOrgIdentityAuthNPolicyURI" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduOrgLegalName" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduOrgSuperiorURI" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******.4.1.5923.*******" FriendlyName="eduOrgWhitePagesURI" />
            <RequestedAttribute isRequired="false" Name="urn:oid:*******" FriendlyName="cn" />
        </AttributeConsumingService>
    </SPSSODescriptor>
    <Organization>
        <OrganizationName xml:lang="en">Baidu Cloud Engine</OrganizationName>
        <OrganizationDisplayName xml:lang="en">BCE</OrganizationDisplayName>
        <OrganizationURL xml:lang="en">https://bce.baidu.com</OrganizationURL>
    </Organization>
</EntityDescriptor>