server.port={{.Values.appspace.charts.provides.iamSts.port}}
spring.application.name=sts
iam.client.feign.enable=false
sts.client.feign.enable=false

iam.console.username=proxy
iam.console.password={{.Values.appspace.charts.requires.iam.proxy.password}}
bce_plat_web_framework.sc.enabled=false
bce_plat_web_framework.security.csrf.disabled=true

sc.webframework.ScLoggerConfiguration.enable=false
webframework.register.center.env.vip.enabled=false
webframework.register.center.tianlu.enabled=false
management.health.formula-discovery.enabled=false
spring.cloud.discovery.client.health-indicator.enabled=false