logging.has_access_log=false
logging.has_request-id=false

iam.client.conf=../conf/iamConf.properties
kafka.config=file:../conf/kafka.properties

iam.autoRole.conf=../conf/autoRole.properties
sts.region={{.Values.appspace.charts.platformConfigVals.region}}
sts.accessKey.id={{.Values.appspace.charts.requires.iam.proxy.ak}}
sts.accessKey.secret={{.Values.appspace.charts.requires.iam.proxy.sk}}
sts.filter.intranet.force=true

sts.account.assume_role.whitelist:2e2638e662c840cbabadec20cb3ff384;\
6ce1d88dd9114d9d9f5c61a272fe47e1;\
3fdfa4ac10e64561829f42add967fe11;\
34d36b53479e487f9f527f9500b9309f

#=================== HTTP / HTTPS ===================#
{{ if $.Values.appspace.charts.global.AbcstackGlobalUseHttps }}
login.protocol:https
{{ else }}
login.protocol:{{.Values.appspace.charts.configVals.login_protocol}}
{{- end }}

assume_role.saml.mockIdp=false
assume_role.saml.verify.permission=true
assume_role.saml.sp-metadata=../conf/sp-saml-metadata.xml
assume_role.user.sp.entity-pattern:${login.protocol}://{{.Values.appspace.charts.requires.nginxLogin.domain}}/[0-9a-zA-Z]+/saml

logging.level.org.springframework.web=DEBUG
iam.trails.enabled=false

iam.endpoint=http://{{.Values.appspace.charts.requires.iam.domain}}:{{.Values.appspace.charts.requires.iam.port}}/v3
iam.username=proxy
iam.password={{.Values.appspace.charts.requires.iam.proxy.password}}
