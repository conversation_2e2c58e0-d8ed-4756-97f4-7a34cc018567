appspace:
  charts:
    provides:
      iamSts:
        interface: iam-sts
        domain: sts.agilecloud.com
        port: 8586
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      iam:
        interface: iam-nginx
        optional: false
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        proxy:
          ak: '{{ proxy_ak }}'
          sk: '{{ proxy_sk }}'
          userId: '{{ proxy_user_id }}'
          password: '{{ proxy_password }}'
      nginxLogin:
        interface: nginx-login
        optional: true
        domain: '{{ nginx_login_domain }}'
        port: '{{ nginx_login_port }}'
      nginxConsole:
        interface: nginx-console
        optional: true
        domain: '{{ nginx_console_domain }}'
        port: '{{ nginx_console_port }}'
      privateSkywalkingGrpc:
        interface: private-skywalking-grpc
        optional: true
        domain: '{{ private_skywalking_grpc_domain }}'
        port: '{{ private_skywalking_grpc_port }}'
    configVals:
      login_protocol: http
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      apptree:
        enable: true
      hostNetwork:
        enable: false
    monitor:
      noaheePro:
        enable: true
    securityContext: {}
    security:
      enable: false
    middleware: {}
    replica: 3
    namespace: console
    images:
      iam-sts:
        repository: abc-stack/iam-sts
        imageTag: ${iam-sts.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-sts
      command: []
      args: []
      log:
        - dirPaths:
            - "/home/<USER>/sts/log/access"
            - "/home/<USER>/sts/log/debug"
            - "/home/<USER>/sts/log/error"
          logType: local
          logVolume: iam-sts-log
          rule:
            duration: 3
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 10000m
        limitMemory: 10240Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-sts
        mountPath: /home/<USER>/sts/conf/autoRole.properties
        subPath: autoRole.properties
        name: iam-sts
      - type: CONFIG_MAP
        configName: iam-sts
        mountPath: /home/<USER>/sts/conf/application.properties
        subPath: application.properties
        name: iam-sts
      - type: CONFIG_MAP
        configName: iam-sts
        mountPath: /home/<USER>/sts/conf/iamConf.properties
        subPath: iamConf.properties
        name: iam-sts
      - type: CONFIG_MAP
        configName: iam-sts
        mountPath: /home/<USER>/sts/conf/bootstrap.properties
        subPath: bootstrap.properties
        name: iam-sts
      - type: CONFIG_MAP
        configName: iam-sts
        mountPath: /home/<USER>/sts/conf/sp-saml-metadata.xml
        subPath: sp-saml-metadata.xml
        name: iam-sts
      - type: CONFIG_MAP
        configName: iam-sts
        mountPath: /home/<USER>/sts/bin/skywalking-agent/config/agent.config
        subPath: agent.config
        name: iam-sts
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8586
          initialDelaySeconds: 50
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-sts-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/sts/log/
          storageType: local-path
          size: 30Gi
      ports:
      - name: 8586-tcp
        containerPort: 8586
        protocol: TCP
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
