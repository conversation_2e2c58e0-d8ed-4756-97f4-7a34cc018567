appspace:
  charts:
    provides:
      iamBcelogin:
        interface: iam-bcelogin
        domain: consolelogin.agilecloud.com
        port: 8098
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
      bceLogstash:
        interface: bce-logstash
        domain: bce-logstash.agilecloud.com
        port: 9600
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      iamSts:
        interface: iam-sts
        optional: false
        domain: '{{ iam_sts_domain }}'
        port: '{{ iam_sts_port }}'
      iamRisk:
        interface: iam-risk
        optional: false
        domain: '{{ iam_risk_domain }}'
        port: '{{ iam_risk_port }}'
      iam:
        interface: iam-nginx
        optional: false
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        console:
          ak: '{{ console_ak }}'
          sk: '{{ console_sk }}'
          userId: '{{ console_user_id }}'
          password: '{{ console_password }}'
      iamBcepass:
        interface: iam-bcepass
        optional: false
        domain: '{{ iam_bcepass_domain }}'
        port: '{{ iam_bcepass_port }}'
      nginxLogin:
        interface: nginx-login
        optional: true
        domain: '{{ nginx_login_domain }}'
        port: '{{ nginx_login_port }}'
      nginxConsole:
        interface: nginx-console
        optional: true
        domain: '{{ nginx_console_domain }}'
        port: '{{ nginx_console_port }}'
      nginxConsoleVcp:
        interface: nginx-console-vcp
        optional: true
        domain: '{{ nginx_console_vcp_domain }}'
        port: '{{ nginx_console_vcp_port }}'
      platUserConfig:
        interface: plat-user-config
        optional: false
        domain: '{{ plat_user_config_domain }}'
        port: '{{ plat_user_config_port }}'
      privateSkywalkingGrpc:
        interface: private-skywalking-grpc
        optional: true
        domain: '{{ private_skywalking_grpc_domain }}'
        port: '{{ private_skywalking_grpc_port }}'
    configVals:
      cookie_domain: .agilecloud.com
      login_urls_no_need_auth: /;/headers/**;/favicon.ico;/error
      login_urls_need_auth: /api-test;/postlogin;/collaborator/postlogin;/logout;/swagger/**;/actuator;/actuator/**
      login_protocol: http
      enable_captcha_check: true
      vs_login_enabled: true
      vs_cookie_domain_prefix: .vcp
      vs_login_domain_prefix: console.vcp
      vs_login_count_ip_limit: 200
      vs_login_count_ip_period: 60
      frame_options_allow_domains: consoletest.x1.dxmyun.com
      usb_key_provider: ANHENG
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
    securityContext: {}
    security:
      enable: false
    middleware:
      kafka:
        topic:
          clusterName: '{{ kafka_cluster_name }}'
          domain: '{{ kafka_domain }}'
          topicName: agilecloud-access-debug
          replication: 3
          partitions: 3
          port: 8959
    replica: 3
    namespace: console
    images:
      iam-bce-login:
        repository: abc-stack/iam-bce-login
        imageTag: ${iam-bce-login.image.tag}
      bce-logstash:
        repository: abc-stack/bce-logstash
        imageTag: ${bce-logstash.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-bce-login
      command: []
      args: []
      log:
        - dirPaths:
            - "/home/<USER>/bce-login/log/debug"
            - "/home/<USER>/bce-login/log/warn"
            - "/home/<USER>/bce-login/log/access"
            - "/home/<USER>/bce-login/log/error"
            - "/home/<USER>/bce-login/log/access_debug"
            - "/home/<USER>/bce-login/log/info"
          logType: local
          logVolume: iam-bce-login-log
          rule:
            duration: 3
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 4000m
        limitMemory: 2800Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-bce-login
        mountPath: /home/<USER>/bce-login/conf/endpoint.json
        subPath: endpoint.json
        name: iam-bce-login
      - type: CONFIG_MAP
        configName: iam-bce-login
        mountPath: /home/<USER>/bce-login/conf/application.properties
        subPath: application.properties
        name: iam-bce-login
      - type: CONFIG_MAP
        configName: iam-bce-login
        mountPath: /home/<USER>/bce-login/bin/skywalking-agent/config/agent.config
        subPath: agent.config
        name: iam-bce-login
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8098
          initialDelaySeconds: 225
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-bce-login-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/bce-login/log/
          storageType: local-path
          size: 30Gi
      ports:
      - name: 8098-tcp
        containerPort: 8098
        targetPort: 8098
        servicePort: 8098
        protocol: TCP
        nodePort: ''
    - name: bce-logstash
      command: []
      args: []
      log:
        - dirPaths:
            - /home/<USER>/logstash/logs
          logType: local
          logVolume: bce-logstash-log
          rule:
            duration: 7
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 2Gi
        ephemeralStorage: 1024Mi
        limitCPU: 2000m
        limitMemory: 4Gi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
        - type: CONFIG_MAP
          configName: iam-bce-login
          mountPath: /home/<USER>/logstash/config/logstash.conf.kafka
          subPath: logstash.conf.kafka
          name: iam-bce-login
        - type: CONFIG_MAP
          configName: iam-bce-login
          mountPath: /home/<USER>/logstash/config/logstash.yml
          subPath: logstash.yml
          name: iam-bce-login
        - type: CONFIG_MAP
          configName: iam-bce-login
          mountPath: /home/<USER>/logstash/config/profile_api.conf
          subPath: profile_api.conf
          name: iam-bce-login
        - type: CONFIG_MAP
          configName: iam-bce-login
          mountPath: /home/<USER>/logstash/bin/logstash_control.sh
          subPath: logstash_control.sh
          name: iam-bce-login
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 9600
          initialDelaySeconds: 5      # 表示容器启动后，等待多少时间之后再启动各类探针,默认10s
          periodSeconds: 30           # 表示探针执行检测的间隔时间，默认10s
          failureThreshold: 5         # 表示探针执行检测时，连续失败多少次，容器状态就会被确认不健康，默认3次
          successThreshold: 1         # 表示探针执行检测失败之后，如果容器状态想再次被标记为健康，至少需要经过多少次连续成功检测，默认1次
          timeoutSeconds: 5           # 表示探针执行检测的超时后的等待时间，1s
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: bce-logstash-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/logstash/logs/
          storageType: local-path
          size: 30Gi
        - name: iam-bce-login-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/bce-login/log/
          storageType: local-path
          size: 30Gi
      ports: []
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
