spring.application.name=bce-console-login
server-host:{{.Values.appspace.charts.requires.nginxConsole.domain}}
server.port:{{.Values.appspace.charts.provides.iamBcelogin.port}}
bce_plat_web_framework.sc.enabled=false

bcia.login.enabled:true
swagger.start:false
swagger.app.docs: http://{{.Values.appspace.charts.requires.nginxConsole.domain}}:{{.Values.appspace.charts.provides.iamBcelogin.port}}

#=================== Captcha config ===================#
user.login.captcha.check:{{.Values.appspace.charts.configVals.enable_captcha_check}}
#=================== HTTP / HTTPS ===================#

{{if .Values.appspace.charts.global.AbcstackGlobalUseHttps }}
login.protocol:https
{{ else }}
login.protocol:{{.Values.appspace.charts.configVals.login_protocol}}
{{- end }}

#=================== access ===================#
portal.url:${login.protocol}://{{.Values.appspace.charts.requires.nginxConsole.domain}}
bce.user-authentication:true
login.iam.account.login.enabled:true
# enable bcepass user login
login.userlogin.enable:true
login.domain.address.redirect.to.userlogin:true
login.url:${login.protocol}://{{.Values.appspace.charts.requires.nginxLogin.domain}}
default.login.success.callback.url:${login.protocol}://{{.Values.appspace.charts.requires.nginxConsole.domain}}

login.url.simple:${login.protocol}://{{.Values.appspace.charts.requires.nginxLogin.domain}}

cookie.domain:{{.Values.appspace.charts.global.domain}}
console.endpoint: ${login.protocol}://{{.Values.appspace.charts.requires.nginxConsole.domain}}
login.cookie.md5.key:bcetest
login.cookie.effective.create.time:60
login.cookie.expires.time:30
login.cookie.is.need.check.valid:true

login.api.js.path:js/offline/rd-api.js
login.urls.not.need.auth:{{.Values.appspace.charts.configVals.login_urls_no_need_auth}}
login.urls.need.auth:{{.Values.appspace.charts.configVals.login_urls_need_auth}}

passport.appid=1240
passport.session.endpoint:http://nouse:nouse
passport.reg.url:https://nouse
passport.auth.url:https://nouse
passport.auth.stoken.web.enable:true
passport.auth.stoken.android.enable:true
passport.auth.stoken.ios.enable:true

uc.app.id:285
uc.server:nouse:nouse
uc.server.jump.url:https://nouse:nouse/?action=check&appid=
logout.uc.path:http://nouse:nouse/?action=logout&u=

logout.passport.path:http://nouse:nouse/?logout&u=

login.uuaplogin.url:http://nouse:nouse/login

bce.login.accesskey={{.Values.appspace.charts.requires.iam.console.ak}}
bce.login.secretkey={{.Values.appspace.charts.requires.iam.console.sk}}

#================ uc & passport user info ================#
passport.passgateEndpoint:http://nouse:nouse/passgate
passport.app.username:bceplat
passport.app.password:bceplat
uc.secure.host:http://nouse:nouse/usersecureinfo

iam.console.username=console
iam.console.password={{.Values.appspace.charts.requires.iam.console.password}}

#======================================================#

#=================== logging config ===================#
logging.requestId_urlPattern:/*
logging.has_console_appender: true
logging.has_web_debug_appender: false
logging.web_debug_path: /debug
logging.web_debug.level: INFO
### logback rolling log, uncomment to open appender ###
logging.info_log_file_path:  ../log/info/bce-login.info.log
logging.error_log_file_path: ../log/error/bce-login.error.log
logging.warn_log_file_path:  ../log/warn/bce-login.warn.log
logging.debug_log_file_path: ../log/debug/bce-login.debug.log

### access log, uncomment to open appender ###
logging.access.maxLength:16384
logging.access_debug_uri_prefix:/api,/postlogin,/login,/logout,/collaborator,/switchlogin,/saml
logging.access_log_file_path: ../log/access/bce-login.access.log
logging.access_debug_log_file_path: ../log/access_debug/bce-login.access_debug.log
logging.access.desensitize_expression: `"(mobilePhone)"\\\s*:\\\s*"(\\\d{3})\\\d{6}(\\\d{2})"`:`"$1":"$2******$3"`;\

                                       `(mobilePhone)='(\\\d{3})\\\d{6}(\\\d{2})'`:`$1='$2******$3'`;\

                                       `"(email)"\\\s*:\\\s*"(.*)...@..(.*)"`:`"$1":"$2***@**$3"`;\

                                       `(email)='(.*)...@..(.*)'`:`$1='$2***@**$3'`;\

                                       `(password|original_password)=[^&]*`:`$1=*****`;\

                                       `"(password|original_password)"\\\s*:\\\s*"[^"]*?"`:`"$1":"*****"`;\

                                       `"(secret)"\\\s*:\\\s*"(..)[^"]*?(..)"`:`"$1":"$2*****$3"`;

logging.log.desensitize_expression: `"(mobilePhone)"\\\s*:\\\s*"(\\\d{3})\\\d{6}(\\\d{2})"`:`"$1":"$2******$3"`;\

                                    `(mobilePhone)='(\\\d{3})\\\d{6}(\\\d{2})'`:`$1='$2******$3'`;\

                                    `"(email)"\\\s*:\\\s*"(.*)...@..(.*)"`:`"$1":"$2***@**$3"`;\

                                    `(email)='(.*)...@..(.*)'`:`$1='$2***@**$3'`;\

                                    `"(password|original_password)"\\\s*:\\\s*"[^"]*?"`:`"$1":"*****"`;\

                                    `"(secret)"\\\s*:\\\s*"(..)[^"]*?(..)"`:`"$1":"$2*****$3"`;
#======================================================#
debug.cookie.dump: true

https.auto_redirect: false
https.hosts: qasandbox.bcetest.baidu.com

login.console.token.renew.advance=1800

saml.login.url.simple=${login.protocol}://{{.Values.appspace.charts.requires.nginxLogin.domain}}/saml
saml.login.issuer=${login.protocol}://{{.Values.appspace.charts.requires.nginxLogin.domain}}/{ACCOUNTID}/saml

# close spring cloud and compass
compass.use.close=true
sc.webframework.ScLoggerConfiguration.enable=false
webframework.register.center.env.vip.enabled=false
webframework.register.center.tianlu.enabled=false
management.health.formula-discovery.enabled=false
spring.cloud.discovery.client.health-indicator.enabled=false
spring.application.name=console-login
bce_plat_web_framework.sc.enabled=false
webframework.SecurityAutoConfiguration.disable.flag=true

# vsAccount config
vs.login.enabled={{.Values.appspace.charts.configVals.vs_login_enabled}}
vs.cookie.domain={{.Values.appspace.charts.configVals.vs_cookie_domain_prefix}}{{.Values.appspace.charts.global.domain}}
vs.login.domain={{.Values.appspace.charts.requires.nginxConsoleVcp.domain}}
vs.login.count.ip.limit={{.Values.appspace.charts.configVals.vs_login_count_ip_limit}}
vs.login.count.ip.period={{.Values.appspace.charts.configVals.vs_login_count_ip_period}}
frame.options.allow.domains={{.Values.appspace.charts.configVals.frame_options_allow_domains}}

# security assessment
usbKeyProvider={{.Values.appspace.charts.configVals.usb_key_provider}}
