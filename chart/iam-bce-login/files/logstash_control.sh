#! /bin/sh
source /etc/profile
ulimit -s 20480
ulimit -c unlimited

host_dir=$(pwd)
proc_name="logstash-core"
file_name="logstash-plain.log"
exec_bin=$host_dir/bin/logstash
xmq_temp=$host_dir/config/logstash.conf.kafka
pid=0

proc_num() {
  num=$(ps -ef | grep $proc_name | grep -v grep | wc -l)
  echo $num
  return $num
}

USAGE() {
  echo "usage: $0 start|stop|restart|offline|online"
}

if [ $# -lt 1 ]; then
  USAGE
  exit -1
fi

CMD="$1"
shift

OPTS=$*

proc_id() {
  pid=$(ps -ef | grep $proc_name | grep -v grep | awk '{print $2}')
}

start() {
  num=$(proc_num)
  if [[ $num > 0 ]]; then
    echo "${proc_name} already started"
    return 0
  else
    echo "${proc_name} not running,start now..."
  fi
  echo ${host_dir}
  nohup $exec_bin -f ${xmq_temp} >>$host_dir/logs/$file_name 2>&1 &
  sleep 3
  proc_id
  echo $pid, $(date) >>$host_dir/logs/$file_name
}

stop_start() {
  stop
  echo "${proc_name} stopped."
  sleep 2
  start
  sleep 3
  proc_id
  echo $pid, $(date) >>$host_dir/logs/$file_name
}

stop() {
  num=$(proc_num)
  if [[ $num == 0 ]]; then
    echo "${proc_name} already stop"
    return 0
  else
    echo "${proc_name} running,stop now..."
  fi
  echo -n "waiting 60s(max)"
  for pid in $(ps -ef | grep $proc_name | grep -v grep | awk '{print $2}'); do
    echo "stop ${proc_name} : ${pid}"
    kill $pid
  done
  echo -n "waiting 60s(max)"
  waitRemain=60
  while [ ${waitRemain} -gt 0 -a $(proc_num) -ne 0 ]; do
    sleep 1
    echo -n "."
    waitRemain=$(expr ${waitRemain} - 1)
  done
  echo ""
  if [[ $(proc_num) -ne 0 ]]; then
    for pid in $(ps -ef | grep -v grep | grep -i "${proc_name}" | awk '{print $2}'); do
      echo "stop fail,force stop ${proc_name} : ${pid}"
      kill -9 $pid
    done
  fi
  echo "${proc_name} stopped."
  exit 0
}

case "$CMD" in
stop) stop ;;
start) start ;;
restart)
  stop
  sleep 3
  start
  ;;
proc_num) proc_num ;;
help) USAGE ;;
*) USAGE ;;
esac