POST /api/iam/user/policy/add
POST /api/iam/user/update
POST /api/iam/policy/create
POST /api/iam/policy/delete
POST /api/iam/user/create
POST /api/iam/user/accesskey/create
POST /api/service/accesskey/create
POST /api/iam/user/policy/delete
POST /api/iam/policy/update
POST /api/service/accesskey/delete
POST /api/iam/organization/scp/create
POST /api/iam/organization/scp/attachment/update
POST /api/bcc/order/v2/confirm
POST /api/iam/user/delete
POST /api/cds/order/v2/confirm
POST /api/bcc/asp/update
POST /api/iam/certificate/create
POST /api/bcc/cds/delete
POST /api/iam/organization/invite
POST /api/iam/organization/invitation/process
POST /api/iam/organization/target/attachment/update
POST /api/iam/organization/register
POST /api/kafka/v2/topic/delete
POST /api/bos/object/delete
POST /api/blb/listener/create
POST /api/kafka/v2/topic/create
POST /api/bos/bucket/delete
POST /api/kafka/v2/certificate/create
POST /api/blb/order/v2/confirm
POST /api/blb/rs/create
POST /api/bos/bucket/create
POST /api/blb/listener/delete
POST /api/blb/rs/update
POST /api/blb/rs/delete
POST /api/iam/user/accesskey/delete
POST /api/blb/listener/update
POST /api/service/accesskey/update
POST /api/blb/instance/delete
POST /api/iam/organization/delete
POST /api/blb/rs/batch_update
POST /api/kafka/v2/authorization/certificates/modify
POST /api/kafka/v2/certificate/delete
POST /api/kafka/v2/certificate/regenerate
POST /api/kafka/v2/topic/partition/add
POST /api/iam/organization/unit/create
POST /api/iam/organization/create
POST /api/iam/organization/unit/update
POST /api/iam/organization/assignment/update
POST /api/iam/organization/leave
POST /api/iam/certificate/delete
POST /api/iam/organization/allowadmin
POST /api/iam/organization/scp/delete
POST /api/eip/order/v2/confirm
POST /api/bcc/snapshot/create
POST /api/bcc/instance/delete
POST /api/bcc/image/delete
GET /postlogin
GET /logout
POST /api/bcc/asp/attach
POST /api/bos/object/create
POST /api/kafka/v2/authorization/add
POST /api/kafka/v2/authorization/topics/modify
POST /api/bos/image/protection
POST /api/bcc/cds/snapshot/create
POST /api/bos/bucket/replication/put
POST /api/bcc/instance/stop
POST /api/bcc/instance/create_image
POST /api/bcc/asp/create
POST /api/bcc/image/share
POST /api/bcc/asp/delete
POST /api/bcc/image/unshare
POST /api/bcc/instance/reboot
POST /api/bos/bucket/replication/delete
POST /api/bcc/snapshot/rollback
POST /api/bcc/snapshot/batch_delete
POST /api/bcc/keypair/attach
POST /api/bcc/keypair/detach
POST /api/bcc/instance/changepwd
POST /api/bos/cors/put
POST /api/bcc/cds/rename
POST /api/bcc/cds/detach
POST /api/bcc/cds/attach
POST /api/bcc/instance/update_desc
POST /api/bos/lifecycle/put
POST /api/bos/lifecycle/delete
POST /api/bos/bucket/encryption/set
POST /api/bos/bucket/static/page/put
POST /api/bos/bucket/static/page/delete
POST /api/bos/cors/delete
POST /api/bos/bucket/logging/set
POST /api/bmr/order/v2/confirm
DELETE /v2/instance/{instanceId}
POST /api/rds/instance/update_name
POST /api/rds/order/v2/confirm
POST /api/rds/account/create
POST /api/rds/database/create
POST /api/rds/instance/delete
POST /api/rds/account/delete
POST /api/rds/argument/modify
POST /api/bcc/dcc/order/v2/confirm
POST /api/bcc/dcc/instance/create
POST /api/bcc/dcc/host/rename
POST /api/bcc/dcc/host/redescription
POST /api/bcc/dcc/host/vcpu
POST /api/bls/task/create
POST /api/bls/task/update
POST /api/bls/task/pause
POST /api/bls/task/start
POST /api/bls/task/remove
POST /api/rds/instance/update_internet_access
POST /api/rds/instance/update_replicationType
POST /api/rds/database/update_desc
POST /api/rds/instance/update_domain
POST /api/rds/whitelist/set
POST /api/rds/dbfirewall/update_state
POST /api/rds/backup/setting
POST /api/rds/backup/rebase
POST /api/rds/database/delete
POST /api/rds/account/update_password
POST /api/rds/account/update_desc
POST /api/eip/rename
POST /api/network/v1/subnet/delete
POST /api/network/v1/subnet/create
POST /api/network/v1/vpc/create
POST /api/network/v1/aclrule/create
POST /api/network/v1/vpcs/delete
POST /api/network/v1/rule/create
POST /api/network/v1/security/copy
POST /api/network/v1/security/batch_delete
POST /api/network/v1/security/create
POST /api/network/v1/rule/delete
POST /api/network/v1/nat/order/confirm/new
POST /api/network/v1/security/update_field
POST /api/network/v1/security/update
POST /api/network/v1/vpn/order/confirm/new
POST /api/network/v1/peerconn/order/confirm/new
POST /api/network/v1/security/join_security_groups
POST /api/eip/bind
POST /api/eip/release
POST /api/eipgroup/order/confirm
POST /api/scs/delete
PUT /v1/blb/{blbId}/listener
PUT /v1/eip/{eip}
POST /v1/order/confirm
POST /v1/instance
DELETE /v1/vpc/{vpcId}
POST /v2/instance
POST /api/eipbp/order/confirm
PUT /v1/instance/{instanceId}
DELETE /v1/instance/{instanceId}
POST /api/tag/create
POST /api/tag/delete
POST /v2/image
PUT /v2/volume/{volumeId}
DELETE /v1/eip/{eip}
POST /v1/blb/{blbId}/TCPlistener
POST /v1/blb/{blbId}/backendserver
POST /v1/blb/{blbId}/HTTPlistener
POST /v2/volume
POST /v1/vpc
POST /v1/eip
POST /api/scs/order/v3/confirm
POST /v2/snapshot
DELETE /v2/snapshot/{snapshotId}
PUT /v1/vpc/{vpcId}
PUT /v2/securityGroup/{securityGroupId}
POST /v1/subnet
DELETE /v1/subnet/{subnetId}
POST /v2/securityGroup
DELETE /v2/securityGroup/{securityGroupId}
PUT /v1/subnet/{subnetId}
PUT /v1/blb/{blbId}/backendserver
POST /api/bes/order/v2/bcc/confirm
POST /api/bes/deploy/delete
POST /api/bes/deploy/password/reset
POST /api/bes/deploy/stop
POST /api/eipbp/update
POST /api/eipbp/autoRelease
POST /api/eipbp/delete
POST /api/scs/clear
POST /api/drds/cluster/delete
POST /api/drds/order/v2/confirm
POST /api/drds/account/create
POST /api/drds/database/create
POST /api/drds/database/update_privileges
POST /api/drds/ddltask/create
POST /api/drds/database/delete
POST /api/drds/account/update_password
POST /api/drds/account/update_privileges
POST /api/drds/account/delete
POST /api/drds/whitelist/set
POST /api/drds/cluster/update_name
POST /api/drds/cluster/update_full_scan
POST /api/drds/cluster/update_internet_access
POST /api/drds/cluster/update_domain
DELETE /v1/cluster/{clusterId}
POST /api/waf/v1/cdnwaf/config/save
POST /api/waf/v1/rule/save
POST /api/waf/v1/rename
POST /api/waf/v1/order/confirm
POST /login
POST /api/bct/trail/create
POST /api/bct/trail/update
POST /api/bct/trail/delete
POST /v1/policy
POST /v1/user
PUT /v1/user/{userName}/loginProfile
POST /v1/group
PUT /v1/group/{groupName}
POST /v1/user/{userName}/accesskey
PUT /v1/user/{userName}
DELETE /v1/group/{groupName}/policy/{policyName}
DELETE /v1/group/{groupName}
DELETE /v1/group/{groupName}/user/{userName}
DELETE /v1/policy/{policyName}
PUT /v1/group/{groupName}/policy/{policyName}
DELETE /user/{userName}/accesskey/{accessKeyId}
DELETE /v1/user/{userName}/loginProfile
DELETE /v1/user/{userName}
DELETE /v1/user/{userName}/policy/{policyName}
PUT /v1/group/{groupName}/user/{userName}
PUT /v1/user/{userName}/policy/{policyName}
POST /api/rds/instance/reboot
POST /api/rds/backup/do
POST /api/iam/user/mfa/bind
POST /api/iam/user/mfa/devices/create
POST /api/bss/v1/ids/update_auth_eip
POST /api/bss/v1/ids/update_postpay
POST /api/bss/v1/ids/update_log
GET /collaborator/uuappostlogin
POST /api/enic
GET /collaborator/postlogin
POST /api/iam/account/activate
POST /api/snic/endpoint/update
POST /api/iam/account/cancel
POST /api/iam/account/cancel/precheck
POST /api/iam/locale/configs/update
POST /api/iam/sts/role/bcepolicies/grant
POST /api/iam/sts/role/create
POST /api/iam/sts/role/bcepolicies/delete
POST /switchlogin
POST /api/iam/sts/role/delete
POST /saml
POST /api/iam/user/accesskey/update
POST /api/blb/appblb/sg/create
POST /api/blb/appblb/port/create
POST /api/blb/appblb/listener/create
POST /api/blb/appblb/listener/update
POST /api/blb/appblb/listener/batchdelete
POST /api/blb/appblb/instance/rs/batchcreate
POST /api/blb/appblb/instance/rs/batchupdate
POST /api/network/v1/dc/channel/create
POST /api/network/v1/dc/channel/update
POST /api/network/v1/dc/phy/update
POST /api/network/v1/dc/phy/create
POST /api/network/v1/dc/gw/update
POST /api/network/v1/dc/gw/unbind
POST /api/network/v1/dc/gw/bind
POST /api/snic/endpoint/delete
POST /api/network/v1/dc/channel/updateusers
POST /api/network/v1/dc/channel/recreate
POST /api/ipv6gw/order/confirm/new
POST /api/ipv6gw/order/confirm/resize
POST /api/ipv6gw/segment/create
POST /api/ipv6gw/qos/update
POST /api/ipv6gw/qos/create
POST /api/ipv6gw/delete
POST /api/blb/instance/update
POST /api/ld/create
POST /api/ld/associations
POST /api/ld/batchDelete
POST /api/network/v1/dc/gw/delete
POST /api/blb/appblb/port/update
POST /api/blb/appblb/instance/rs/delete
POST /v1/blb
POST /v1/appblb
PUT /v1/blb/{blbId}
PUT /v1/appblb/{blbId}
POST /v1/appblb/{blbId}/blbrs
POST /v1/appblb/{blbId}/appservergroupport
DELETE /v1/appblb/{blbId}
POST /v1/appblb/{blbId}/appservergroup
PUT /v1/appblb/{blbId}/blbrs
PUT /v1/appblb/{blbId}/appservergroupport
PUT /v1/appblb/{blbId}/appservergroup
POST /v1/appblb/{blbId}/SSLlistener
PUT /v1/appblb/{blbId}/SSLlistener
POST /v1/appblb/{blbId}/UDPlistener
PUT /v1/appblb/{blbId}/UDPlistener
PUT /v1/appblb/{blbId}/HTTPlistener
POST /v1/appblb/{blbId}/HTTPlistener
POST /v1/appblb/{blbId}/policys
PUT /v1/appblb/{blbId}/HTTPSlistener
POST /v1/appblb/{blbId}/TCPlistener
POST /v1/appblb/{blbId}/HTTPSlistener
POST /v1/blb/{blbId}/SSLlistener
PUT /v1/blb/{blbId}/SSLlistener
POST /v1/blb/{blbId}/UDPlistener
PUT /v1/blb/{blbId}/UDPlistener
PUT /v1/appblb/{blbId}/policys
PUT /v1/blb/{blbId}/HTTPSlistener
POST /v1/blb/{blbId}/HTTPSlistener
PUT /v1/blb/{blbId}/HTTPlistener
PUT /v1/blb/{blbId}/TCPlistener
POST /api/ipv6gw/order/confirm/to_postpay
POST /api/ipv6gw/order/confirm/cancel_to_postpay
POST /api/ipv6gw/update
POST /api/network/v1/dc/channel/delete
POST /api/ipv6gw/segment/delete
POST /api/ipv6gw/qos/batch_delete
DELETE /api/enic/{eniUuid}
POST /api/enic/{eniUuid}/privateIp
DELETE /api/enic/{eniUuid}/privateIp/{ip}
POST /api/ld/record/update
POST /api/ld/record/batchDelete
POST /api/network/v1/dc/phy/recreate
POST /v1/etGateway
PUT /v1/etGateway/{etGatewayId}
DELETE /v1/etGateway/{etGatewayId}
POST /v1/etGateway/{etGatewayId}/healthCheck
DELETE /v1/blb/{blbId}
POST /api/blb/appblb/sg/update
POST /api/blb/appblb/sg/delete
POST /api/blb/appblb/port/batchdelete
PUT /v1/appblb/{blbId}/TCPlistener
PUT /v1/appblb/{blbId}/listener
POST /api/kms
GET /api/kms/downloadKeyMaterial
POST /api/bos/access/set
POST /v1/api/logical/network/eni/ipv6/privateIp
POST /v1/api/logical/network/eni/privateIp/batchAdd
DELETE /v1/api/logical/network/eni/ipv6/{eniUuid}/privateIp/{ip}
POST /v1/api/logical/network/eni/privateIp/batchDel
POST /api/bcc/eip/unbindV2
POST /api/bcc/eip/bindV2
POST /api/eip/unbind
PUT /api/network/v1/security/rule/v2/update
PUT /api/network/v1/security/rule/batchDelete
PUT /api/network/v1/security/rule/add
DELETE /v2/securityGroup/rule/{sgRuleId}
POST /api/iam/user/policy/update
POST 
POST /api/edap/v1/database
POST /api/edap/v1/topic
POST /api/iam/group/user/batch/add
PUT /api/edap/v1/project/{projectName}
DELETE /api/edap/v1/project/{projectName}
PUT /api/edap/v1/project/{projectName}/workbench/jobgroup/{jobGroupName}
PUT /api/edap/v1/topic/{topicName}
DELETE /api/edap/v1/topic/{topicName}
DELETE /api/edap/v1/database/{databaseName}
POST /api/edap/v1/database/{databaseName}/table
PUT /api/edap/v1/database/{databaseName}
PUT /api/edap/v1/database/{databaseName}/table/{tableName}
DELETE /api/edap/v1/database/{databaseName}/table/{tableName}
POST /api/edap/v1/metasync/{syncName}
PUT /api/edap/v1/metasync/{syncName}
POST /api/edap/v1/metasync
POST /api/edap/v1/location/{locationName}
PUT /api/edap/v1/location/{locationName}
POST /api/edap/v1/connection/{connectionName}
PUT /api/edap/v1/connection/{connectionName}
POST /api/edap/v1/profile/{profileName}
DELETE /api/edap/v1/profile/{profileName}
PUT /api/edap/v1/project/{projectName}/workbench/pipeline/{pipelineName}
DELETE /api/edap/v1/project/{projectName}/workbench/pipeline/{pipelineName}
DELETE /api/edap/v1/project/{projectName}/workbench/jobgroup/{jobGroupName}
PUT /api/edap/v1/project/{projectName}/note/{noteName}
DELETE /api/edap/v1/project/{projectName}/note/{noteName}
DELETE /api/edap/v1/connection/{connectionName}
DELETE /api/edap/v1/location/{locationName}
GET /api/edap/v1/project/{projectName}/workbench/script/{scriptName}
PUT /api/edap/v1/project/{projectName}/workbench/script/{scriptName}
DELETE /api/edap/v1/project/{projectName}/workbench/script/{scriptName}
GET /api/edap/v1/codetable/{codeTableName}
GET /api/edap/v1/standard/{standardCode}
GET /api/edap/v1/location/{locationName}
GET /api/edap/v1/metasync/{syncName}
GET /api/edap/v1/project/{projectName}/workbench/pipeline/{pipelineName}
GET /api/edap/v1/connection/{connectionName}
GET /api/edap/v1/project/{projectName}/workbench/jobgroup/{jobGroupName}
GET /api/edap/v1/topic/{topicName}/database
GET /api/edap/v1/project/{projectName}/note/{noteName}
PUT /api/edap/v1/profile/{profileName}
GET /api/edap/v1/topic/{topicName}
GET /api/edap/v1/database/{databaseName}/table
GET /api/edap/v1/profile/{profileName}
POST /api/edap/v1/quality/task
GET /api/edap/v1/project/{projectName}/workbench/realtime/{realtimeName}
GET /api/edap/v1/project/{projectName}/workbench/flink/{flinkName}
GET /api/edap/v1/authorization
POST /api/edap/v1/authorization
POST /api/iam/group/user/remove
GET /api/edap/v1/quality
PUT /api/edap/v1/project/{projectName}/workbench/realtime/{realtimeName}
DELETE /api/edap/v1/project/{projectName}/workbench/realtime/{realtimeName}
PUT /api/edap/v1/project/{projectName}/workbench/flink/{flinkName}
DELETE /api/edap/v1/project/{projectName}/workbench/flink/{flinkName}
POST /api/edap/v1/quality/rule
PUT /api/edap/v1/codetable/{codeTableName}
POST /api/edap/v1/codetable
PUT /api/edap/v1/quality/rule/{ruleCode}
DELETE /api/edap/v1/codetable/{codeTableName}
GET /api/edap/v1/quality/rule/{ruleCode}
POST /api/edap/v1/commontable
GET /api/edap/v1/commontable/{commonTableName}
PUT /api/edap/v1/commontable/{commonTableName}
DELETE /api/edap/v1/commontable/{commonTableName}
POST /api/edap/v1/standard
PUT /api/edap/v1/standard/{standardCode}
DELETE /api/edap/v1/quality/rule/{ruleCode}
DELETE /api/edap/v1/standard/{standardCode}
GET /api/edap/v1/quality/task/{taskCode}
PUT /api/edap/v1/quality/task/{taskCode}
DELETE /api/edap/v1/quality/task/{taskCode}
POST /api/edap/v1/project/{projectName}
POST /api/edap/v1/project/{projectName}/workbench/script/{scriptName}
POST /api/edap/v1/project/{projectName}/workbench/realtime/{realtimeName}
POST /api/edap/v1/project/{projectName}/workbench/flink/{flinkName}
POST /api/edap/v1/project/{projectName}/tablequery
POST /api/edap/v1/project/{projectName}/workbench/jobgroup/{jobGroupName}
POST /api/edap/v1/project/{projectName}/note/{noteName}
POST /api/edap/v1/project/{projectName}/workbench/pipeline/{pipelineName}
POST /v1/acl/rule
DELETE /v1/acl/rule/{ruleId}
DELETE /v1/eni/{eniId}/privateIp/{privateIp}
POST /api/network/v1/rule/update
POST /api/network/v1/aclrule/delete
POST /api/network/v1/aclrule/update
POST /v1/eni/{eniId}/privateIp
DELETE /v1/route/rule/{ruleId}
POST /v1/route/rule
PUT /v1/acl/rule/{ruleId}
POST /api/bcc/dcc/instance/delete
PUT /v2/instance/{instanceId}
PUT /v2/instance/{instanceId}/tag
POST /v2/instanceBySpec
POST /v2/instance/batchDelete
POST /v2/instance/{instanceId}
POST /v2/instance/delete
PUT /v2/instanceBySpec/{instanceId}
PUT /v2/instanceReturnOrderId/{instanceId}
PUT /v2/subnet/changeSubnet
PUT /v2/vpc/changeVpc
DELETE /v2/recycle/instance/{instanceId}
PUT /v2/instance/{instanceId}/deletionProtection
PUT /v2/keypair
PUT /v2/keypair/{keypairId}
DELETE /v2/keypair/{keypairId}
POST /api/bbc/order/v2/confirm
POST /v1/instance
POST /api/bbc/instance/delete
DELETE /v2/instance/{instanceId}
POST /api/bbc/instance/create_image
POST /api/bbc/image/delete
POST /api/bbc/vpc/security/bindSecurityGroups
POST /api/network/v1/security/batch/unbind
POST /api/bbc/instance/rebuild
POST /v1/instance/delete
DELETE /v1/image/{imageId}
POST /v1/bbc/deletePrepayInstance
POST /v1/image
POST /v1/instance/securitygroup
PUT /v1/instance/{instanceId}
POST /api/evs/device
POST /v1/device
PUT /v1/device/{deviceId}
POST /api/evs/space
DELETE /api/evs/space/{spaceId}
PUT /api/evs/space/{spaceId}
POST /v1/space
PUT /v1/space/{spaceId}
DELETE /v1/space/{spaceId}
DELETE /api/evs/device/{deviceId}
DELETE /v1/device/{deviceId}
PUT /api/evs/device/{deviceId}
POST /api/bcm/v1/csm/services/alarm/config/delete
POST /api/bcm/v1/csm/services/alarm/config/create
POST /api/bcm/v1/csm/instance-group/create
POST /api/bcm/v1/csm/instance-group/delete
POST /csm/api/v1/services/alarm/config/create
POST /csm/api/v1/services/alarm/config/delete
POST /csm/api/v1/userId/{userId}/instance-group
DELETE /csm/api/v1/userId/{userId}/instance-group/{groupId}
POST /api/as/group
POST /api/as/group/delete
PUT /v2/instance/rebuild
POST /v3/instance
POST /v2/instanceReturnOrderId
PUT /v2/instance/batchAddIp
PUT /v2/instance/batchDelIp
POST /v2/volume/{volumeId}
POST /v3/volume
POST /v2/instance/role
POST /v2/instance/addIpv6
POST /v2/instance/delIpv6
POST /v2/instance/recovery
DELETE /v2/volume/{volumeId}
POST /v2/volumeReturnOrderId
PUT /v2/volumeReturnOrderId/{volumeId}
DELETE /v2/image/{imageId}
POST /v2/image/{imageId}
PUT /v2/image/{imageId}/tag
POST /v2/asp
DELETE /v2/asp/{aspId}
PUT /v2/asp/update
PUT /v2/asp/{aspId}
POST /v2/instance/bid
POST /v2/instance/deployset/create
POST /v2/instance/deployset/updateRelation
POST /v2/instance/deployset/delRelation
POST /v2/image/os
POST /api/bcc/instance/stopNoCharge
POST /api/bcc/instance/start
POST /api/bcc/tag/assign
POST /api/bcc/instance/rename
POST /api/bcc/instance/rebuild
POST /api/bcc/instance/changeHostname
POST /api/bcc/instance/expired/release
POST /api/bcc/instance/config/deletion_protection
POST /api/bcc/instance/changeSubnet
POST /api/bcc/instance/updatePrivateIP
POST /api/bcc/deployset/updateInstanceDeploy
POST /api/bcc/instance/role
POST /api/sdwan/edge/route/static
POST /api/sdwan/edge/config/link
POST /api/sdwan/create
DELETE /api/sdwan/delete/{sdwanId}
PUT /api/sdwan/update
POST /api/sdwan/edge/config/cidr
POST /api/sdwan/edge/config/dualArm
PUT /api/sdwan/edge/route/static
POST /api/network/v1/dc/gw/create
POST /api/network/v1/dc/gw/hc
PUT /api/network/v1/dc/gw/hc/{healthCheckId}
POST /api/network/v1/dc/phy/order/confirm/new
POST /api/network/v1/dc/phy/delete
POST /v1/et/init
POST /v1/et/config
PUT /v1/et/{dcphyId}
POST /v1/et/{dcphyId}/channel
PUT /v1/et/{dcphyId}/channel/{channelId}
DELETE /v1/et/{dcphyId}/channel/{channelId}
POST /api/network/v1/dc/channel/route
PUT /api/network/v1/dc/channel/route/{dcChannelId}
DELETE /api/network/v1/dc/channel/route/{dcChannelId}
POST /api/csn
POST /api/csn/instance
POST /api/csn/propagation/delete
POST /api/csn/csnRtAssociation
POST /api/csn/propagation
POST /api/csn/bp/order/confirm
POST /api/csn/route
POST /api/csn/bp/limit
PUT /api/csn/csnRtAssociation
POST /api/csn/bp/limit/delete
POST /api/csn/grantRule
PUT /api/csn/grantRule/revoke
DELETE /api/csn/bp/{csnBpId}
POST /api/csn/bp/order/confirm/resize
PUT /api/csn/{csnId}
DELETE /api/csn/{csnId}
POST /api/csn/grantRule/delete
DELETE /api/csn/instance/{csnMemberId}
DELETE /api/csn/csnRtAssociation/{attachId}
PUT /api/csn/propagation
POST /api/csn/route/delete
PUT /api/csn/{csnId}/bp/{csnBpId}
POST /api/csn/routeTable
PUT /api/csn/route/{csnRouteTableId}
DELETE /api/csn/route/{csnRouteTableId}
PUT /api/csn/bp/limit
PUT /api/csn/grantRule
PUT /api/csn/bp/{csnBpId}
PUT /api/kafka/v3/clusters/{clusterId}
PUT /v1/dedicatedHost/{dccId}/tag
PUT /v1/dedicatedHost/instance/{instanceId}/tag
POST /api/bbc/instance/changeHostname
POST /api/bbc/instance/changepwd
POST /api/bbc/instance/reboot
POST /api/bbc/instance/stop
POST /api/bbc/instance/start
POST /api/bbc/instance/changeSubnet
PUT /v1/subnet/changeSubnet
POST /api/kafka/v3/clusters
DELETE /api/kafka/v3/clusters/{clusterId}
PUT /api/kafka/v3/clusters/{clusterId}/nodes/{nodeId}/restart-broker
POST /api/kafka/v3/clusters/{clusterId}/acls
POST /v2/clusters/{clusterId}/acls
DELETE /api/kafka/v3/clusters/{clusterId}/acls
PUT /api/kafka/v3/clusters/{clusterId}/operations/{operationId}/start
PUT /api/kafka/v3/clusters/{clusterId}/operations/{operationId}/cancel
PUT /api/kafka/v3/clusters/{clusterId}/operations/{operationId}/suspend
PUT /api/kafka/v3/clusters/{clusterId}/operations/{operationId}/resume
POST /api/kafka/v3/clusters/{clusterId}/topics
POST /v2/clusters/{clusterId}/topics
DELETE /api/kafka/v3/clusters/{clusterId}/topics/{topicName}
PUT /api/kafka/v3/clusters/{clusterId}/topics/{topicName}
POST /api/kafka/v3/clusters/{clusterId}/users
DELETE /api/kafka/v3/clusters/{clusterId}/users/{username}
PUT /api/kafka/v3/clusters/{clusterId}/users/{username}
PUT /v2/clusters/{clusterId}/users/{userName}
POST /api/kafka/v3/clusters/{clusterId}/consumer-groups/{consumerGroupName}/offsets
DELETE /api/kafka/v3/clusters/{clusterId}/consumer-groups/{consumerGroupName}
DELETE /v2/clusters/{clusterId}/topics/{topicName}
PUT /api/network/v1/dc/channel/associate/{dcChannelId}
POST /api/network/v1/security/quit_security_group
POST /api/network/v1/security/eni_join_security_groups
POST /api/network/v1/security/eni_quit_security_group
POST /api/bes/resize/confirm
POST /api/bes/deploy/instance/delete
POST /api/bes/deploy/restart
POST /api/bes/deploy/migrate_AZ
POST /api/bes/upgrade/confirm
POST /api/bes/deploy/config/update
POST /api/bes/deploy/es_log_view/_settings
POST /api/bes/schedule/create
POST /api/bes/schedule/delete
POST /api/bes/schedule/update
POST /api/bes/deploy/plugin/upload/{BesCluster}/{moduleType}
POST /api/bes/deploy/plugin/install
POST /api/bes/deploy/plugin/uninstall
POST /api/ld/update
POST /api/ld/record/create
POST /api/ld/record/uploader
POST /v1/privatezone
DELETE /v1/privatezone/{zoneId}
PUT /v1/privatezone/{zoneId}
POST /v1/privatezone/{zoneId}/record
PUT /v1/privatezone/record/{recordId}
DELETE /v1/privatezone/record/{recordId}
POST /v1/publicservice/record
DELETE /v1/publicservice/record/{recordId}
PUT /v1/publicservice/record/{recordId}
POST /api/ld/resolver/create
PUT /api/ld/resolver/{resolverId}
DELETE /api/ld/resolver/{resolverId}
POST /api/ld/resolver/rule
PUT /api/ld/resolver/rule/{ruleId}
DELETE /api/ld/resolver/rule/{ruleId}
PUT /api/ld/resolver/rule/bind
POST /api/ld/log/server/create
POST /api/ld/log/server/update
POST /api/ld/log/server/enable
POST /api/ld/log/server/delete
POST /api/dns/zone/add
POST /api/dns/zone/delete_old
POST /api/dns/zone/delete
POST /api/dns/order/v2/confirm
POST /api/dns/domain/add
POST /api/dns/domain/batch_add
POST /api/dns/domain/single_operation
POST /api/dns/domain/single_edit
POST /api/dns/domain/single_delete
POST /v1/dns/zone
DELETE /v1/dns/zone/{zoneName}
POST /v1/dns/zone/order
PUT /v1/dns/zone/order
PUT /v1/dns/zone/order/{zoneName}
POST /v1/dns/zone/{zoneName}/record
PUT /v1/dns/zone/{zoneName}/record/{recordId}
DELETE /v1/dns/zone/{zoneName}/record/{recordId}
POST /api/network/v1/acl/upload
POST /api/network/v1/vpc/update
POST /api/network/v1/vpc/openRelay
POST /api/network/v1/vpc/shutDownRelay
POST /api/network/v2/flowlog
POST /api/network/gateway/limitrule
PUT /api/network/gateway/limitrule
PUT /api/network/gateway/limitrule/delete
POST /api/network/v2/flowlog/update
POST /api/network/v2/flowlog/delete
POST /api/network/v2/flowlog/enable
DELETE /v1/IPv6Gateway/{gatewayId}
PUT /v1/IPv6Gateway/{gatewayId}
POST /v1/IPv6Gateway/{gatewayId}/rateLimitRule
PUT /v1/IPv6Gateway/{gatewayId}/rateLimitRule/{rateLimitRuleId}
DELETE /v1/IPv6Gateway/{gatewayId}/rateLimitRule/{rateLimitRuleId}
POST /v1/IPv6Gateway/{gatewayId}/egressOnlyRule
DELETE /v1/IPv6Gateway/{gatewayId}/egressOnlyRule/{egressOnlyRuleId}
POST /api/vpn/vpn/order/confirm/new
POST /api/vpn/vpn/update
POST /api/vpn/vpn/release
POST /api/vpn/vpn/order/confirm/resize
POST /api/vpn/vpn/order/confirm/toPrePay
POST /api/vpn/vpn/order/confirm/toPostPay
POST /api/vpn/vpn/order/cancelChangingBilling
POST /api/vpn/vpn/bindEip
POST /api/vpn/vpn/unbindEip
POST /api/vpn/vpnConn/create
POST /api/vpn/vpnConn/delete
POST /api/vpn/vpnConn/update
POST /api/vpn/sslvpn/user
DELETE /api/vpn/{vpnId}/sslvpn/user/{userId}
PUT /api/vpn/{vpnId}/vpnConn/{vpnConnId}
POST /api/vpn/nat/rule/create
POST /api/vpn/nat/rule/batchdelete
POST /api/vpn/nat/rule/update
POST /v1/vpn
PUT /v1/vpn/{vpnId}
DELETE /v1/vpn/{vpnId}
POST /v1/vpn/{vpnId}/vpnconn
DELETE /v1/vpn/vpnconn/{vpnConnId}
PUT /v1/vpn/vpnconn/{vpnConnId}
POST /api/network/v1/subnet/ipreserve
DELETE /api/network/v1/subnet/ipreserve/{ipReserveId}
POST /api/network/v1/subnet/reserveportpool
PUT /api/network/v1/subnet/{subnetUuid}/reserveportpool/{reservePortPoolUuid}
DELETE /api/network/v1/subnet/{subnetUuid}/reserveportpool/{reservePortPoolUuid}
POST /v1/subnet/ipreserve
DELETE /v1/subnet/ipreserve/{ipReserveId}
POST /api/eip/tp/order/confirm
POST /v1/eiptp
POST /api/hceg/order/confirm
PUT /api/hceg/rename
POST /api/hceg/release
PUT /api/hceg/bind
PUT /api/hceg/unbind
POST /api/eip/tag/assign
POST /api/eip/recycle/release
POST /api/eip/recycle/restore
POST /api/eip/ipv6
DELETE /v1/eip/recycle/{eip}
PUT /v1/eip/recycle/{eip}
PUT /api/enic/{eniUuid}
POST /v1/eni/{eniId}/privateIp/batchAdd
POST /v1/eni/{eniId}/privateIp/batchDel
PUT /v1/eni/{eniId}
POST /api/network/probe
DELETE /api/network/probe/{probeId}
PUT /api/network/probe/{probeId}
POST /api/network/v1/qos/
PUT /api/network/v1/qos/{qosId}
DELETE /api/network/v1/qos/{qosId}
POST /api/cfw
PUT /api/cfw/{cfwId}
DELETE /api/cfw/{cfwId}
POST /api/cfw/rule
POST /api/cfw/rule/delete
PUT /api/cfw/rule/{cfwRuleId}
POST /api/cfw/bind
POST /api/cfw/unbind
PUT /api/cfw/{cfwId}/instance/{instanceId}
POST /api/peerconn/peerconn/accept
POST /api/peerconn/peerconn/reject
POST /api/peerconn/peerconn/update
POST /api/peerconn/peerconn/dnsSync
POST /api/peerconn/peerconn/alter_bandwidth
POST /api/peerconn/peerconn/delete
POST /api/peerconn/crossBorder/audit
PUT /api/peerconn/crossBorder/audit/{accountId}
POST /api/peerconn/crossBorder/order/confirm/new
PUT /api/peerconn/crossBorder/{peerconnId}
POST /api/peerconn/crossBorder/order/confirm/renew
POST /api/peerconn/crossBorder/order/resize
POST /v1/peerconn
PUT /v1/peerconn/{peerConnId}
DELETE /v1/peerconn/{peerConnId}
PUT /api/vpn/{vpnId}/sslvpn/user/{userId}
PUT /api/eip/direct/{eip}
POST /v1/IPv6Gateway
POST /v1/vpn/{vpnId}/sslVpnServer
DELETE /v1/vpn/{vpnId}/sslVpnServer/{sslVpnServerId}
PUT /v1/vpn/{vpnId}/sslVpnServer/{sslVpnServerId}
POST /v1/vpn/{vpnId}/sslVpnUser
DELETE /v1/vpn/{vpnId}/sslVpnUser/{vpnUserId}
PUT /v1/vpn/{vpnId}/sslVpnUser/{vpnUserId}
PUT /api/network/pathanalise/{id}
DELETE /api/network/pathanalise/{id}
DELETE /api/network/pathanalise/analise/{id}
POST /api/network/pathanalise
POST /api/network/pathanalise/{id}
POST /api/mirror/session
PUT /api/mirror/session/{id}
POST /api/mirror/ruleGroup
DELETE /api/mirror/ruleGroup/{id}
PUT /api/mirror/ruleGroup/rule/{id}
PUT /api/mirror/ruleGroup/{id}
DELETE /api/mirror/ruleGroup/rule/{id}
POST /v1/eipbp
DELETE /v1/eipbp/{id}
PUT /v1/eipbp/{id}
POST /api/eipgroup/bandwidthLimitEipList/{id}
DELETE /api/eipgroup/bandwidthLimitEipList/{id}/{ip}
POST /api/eipgroup/rename
POST /api/eipgroup/release
POST /v1/eipgroup
PUT /v1/eipgroup/{id}
PUT /api/snic/endpoint/delete/{endpointId}
PUT /api/snic/endpoint/{endpointId}
PUT /v1/endpoint/{endpointId}
DELETE /v1/endpoint/{endpointId}
POST /api/snic/order/confirm/new
POST /api/network/v1/enterprise/security/create
PUT /api/network/v1/enterprise/security/update_field
POST /api/network/v1/enterprise/security/copy
POST /api/network/v1/enterprise/security/batch_delete
POST /api/network/v1/enterprise/security/rule/add
PUT /api/network/v1/enterprise/security/rule/update
POST /api/network/v1/enterprise/security/rule/batch_delete
PUT /api/network/v1/enterprise/security/append/bindInstance
PUT /api/network/v1/enterprise/security/replace/bindInstance
POST /v1/enterprise/security
DELETE /v1/enterprise/security/{esg_id}
PUT /v1/enterprise/security/{esg_id}
DELETE /v1/enterprise/security/rule/{esgRuleId}
PUT /v1/enterprise/security/rule/{esgRuleId}
POST /api/network/v1/haVip/create
POST /api/network/v1/haVip/batch_delete
POST /api/network/v1/haVip/bindEip
POST /api/network/v1/haVip/unbindEip
PUT /api/network/v1/haVip/update_field
POST /api/network/v1/haVip/bindInstance
POST /api/network/v1/haVip/unbindInstance
POST /v1/havip
DELETE /v1/havip/{havip_id}
PUT /v1/havip/{havip_id}
POST /api/network/v1/ip/set/create
PUT /api/network/v1/ip/set/update_field
POST /api/network/v1/ip/set/batch_delete
POST /api/network/v1/ip/set/addIp
PUT /api/network/v1/ip/set/updateIp
POST /api/network/v1/ip/set/batch_delete_ip
POST /api/network/v1/ip/group/create
PUT /api/network/v1/ip/group/update_field
POST /api/network/v1/ip/group/batch_delete
POST /api/network/v1/ip/group/bindSet
POST /api/network/v1/ip/group/unbindSet
POST /api/nat/order/confirm/new
POST /api/nat/update
POST /api/nat/release
POST /api/nat/order/confirm/toprepay
POST /api/nat/order/confirm/topostpay
POST /api/nat/cancelChangingCharging
POST /api/nat/order/confirm/resize
POST /api/nat/eip/bind
POST /api/nat/eip/unbind
POST /api/nat/{natId}/snatRule
PUT /api/nat/{natId}/snatRule/{ruleId}
DELETE /api/nat/{natId}/snatRule
POST /api/nat/dnatRule/create
POST /api/nat/dnatRule/update
POST /api/nat/dnatRule/delete
POST /api/nat/limitRule/create
PUT /api/nat/limitRule/update
PUT /api/nat/limitRule/delete
POST /v1/nat
PUT /v1/nat/{natId}
DELETE /v1/nat/{natId}
POST /v1/nat/{natId}/snatRule
POST /v1/nat/snatRule/batchCreate
PUT /v1/nat/{natId}/snatRule/{ruleId}
DELETE /v1/nat/{natId}/snatRule/{ruleId}
POST /v1/nat/{natId}/dnatRule
POST /v1/nat/{natId}/dnatRule/batchCreate
PUT /v1/nat/{natId}/dnatRule/{ruleId}
DELETE /v1/nat/{natId}/dnatRule/{ruleId}
POST /v1/nat/{natId}/limitRule
PUT /v1/nat/{natId}/limitRule/{ruleId}
DELETE /v1/nat/{natId}/limitRule/{ruleId}
PUT /v1/nat/{natId}/limitRule/batchDelete
PUT /v1/nat/{natId}/ip
DELETE /api/mirror/session
POST /v1/endpoint
POST /v1/trailTask/create
POST /v1/trailTask/update
POST /v1/trailTask/delete
POST /v1/cfw
PUT /v1/cfw/{cfwId}
DELETE /v1/cfw/{cfwId}
POST /v1/cfw/{cfwId}/rule
PUT /v1/cfw/{cfwId}/delete/rule
PUT /v1/cfw/{cfwId}/rule/{cfwRuleId}
POST /v1/csn
DELETE /v1/csn/{csnId}
PUT /v1/csn/{csnId}
POST /v1/csn/routeTable/{csnRtId}/rule
DELETE /v1/csn/routeTable/{csnRtId}/rule/{csnRtRuleUuid}
POST /v1/csn/routeTable/{csnRtId}/propagation
DELETE /v1/csn/routeTable/{csnRtId}/propagation/{attachId}
POST /v1/csn/routeTable/{csnRtId}/association
DELETE /v1/csn/routeTable/{csnRtId}/association/{attachId}
POST /v1/csn/bp
PUT /v1/csn/bp/{csnBpId}
DELETE /v1/csn/bp/{csnBpId}
POST /v1/csn/bp/{csnBpId}/limit
PUT /v1/csn/bp/{csnBpId}/limit
POST /v1/csn/bp/{csnBpId}/limit/delete
PUT /v1/csn/{csnId}/tgw/{tgwId}
DELETE /v1/eni/{eniId}
POST /v1/eni
PUT /v2/securityGroup/rule/update
PUT /v1/route/rule/{routeRuleId}
POST /api/bos/symlink/put
POST /api/bos/bucket/trash_object_delete
POST /api/bos/object/rename
POST /api/bos/object/copy
POST /api/bos/bucket/trash_object_recover
POST /api/bos/object/delete_multi_v2
POST /api/bos/object/acl/set
POST /api/bos/bucket/deleteBucketObjectLock
POST /api/bos/bucket/completeBucketObjectLock
POST /api/bos/bucket/extendObjectLock
POST /api/bos/bucket/cache/config/delete
POST /api/bos/bucket/cache/config/save
POST /api/bos/bucket/initBucketObjectLock
POST /api/bos/image/style/delete
POST /api/bos/image/style/put
POST /api/bos/eventnotification/deletenotification
POST /api/bos/eventnotification/updatenotification
POST /api/bos/tag/assign
POST /api/bos/bucket/trash_delete
POST /api/bos/bucket/quota/delete
POST /api/bos/eventnotification/putnotification
POST /api/bos/bucket/putrequestPayment
POST /api/bos/bucket/quota/put
POST /api/bos/mirroring/put
PUT /api/bos/bucket/inventory/put
POST /api/bos/bucket/inventory/delete
POST /api/bos/mirroring/delete
POST /api/bos/bucket/trash_open
POST /api/bos/image/style/import
POST /api/bbc/deployset/create
POST /api/bbc/tag/assign
POST /api/bbc/eip/bind
POST /api/bbc/eip/unbind
PUT /api/bbc/instance/vpc/change
POST /api/bbc/vpc/security/bindEnterpriseSecurityGroups
POST /api/bbc/vpc/eni/batchDelIp
POST /api/bbc/vpc/eni/batchAddIp
POST /api/bbc/instance/update_desc
POST /api/bbc/instance/updatePrivateIP
POST /api/bbc/instance/recovery
POST /api/bbc/instance/rename
POST /v1/instance/enterpriseSecurityGroups
POST /v1/instance/recovery
PUT /v1/instance/batchDelIp
PUT /v1/instance/batchAddIp
POST /v1/instance/batchCreateAutoRenewRules
PUT /v1/vpc/changeVpc
POST /v1/deployset
POST /v1/instance/toPostpay
DELETE /v1/deployset/{deploysetId}
PUT /v1/instance/{instanceId}/tag
POST /v1/instance/toPrepay
POST /api/bcc/instance/transfer/createTransferTask
POST /api/bcc/tag/dbcc/assign
POST /api/bcc/instance/transfer/createTaskBySubnet
POST /api/bcc/tag/dcc/assign
POST /api/bcc/instance/transfer/cancelTransferTask
POST /api/bcc/dcc/instance/updateConfig/create
POST /api/bcc/dcc/host/delete
POST /api/bcc/reserved/create
POST /api/bcc/reserved/autoRenew
PUT /api/bcc/reserved/modify
POST /api/bcc/vpc/eni/delIp
POST /api/bcc/vpc/security/bindSecurityGroups
POST /api/bcc/deployset/delete
POST /api/bcc/instance/quickTemplate/create
POST /api/bcc/instance/quickTemplate/delete
DELETE /v2/instance/deployset/{deployId}
POST /api/bcc/deployset/delDeployInstance
POST /api/bcc/deployset/attribute
POST /api/bcc/deployset
POST /api/bcc/keypair/update_desc
POST /api/bcc/keypair/rename
POST /api/bcc/keypair/import
GET /api/bcc/keypair/create
POST /api/bcc/keypair/create
POST /api/bcc/keypair/delete
POST /api/bcc/instance/recoveryBilling
POST /api/bcc/instance/deleteAutoRenewRule
POST /v2/instance/batchCreateAutoRenewRules
PUT /v2/instanceBatchBySpec
POST /api/bcc/instance/createAutoRenewRule
POST /api/bcc/order/v2/batch/confirm
PUT /v2/instance/batch/changeZoneAndSubnet
PUT /v2/instance/changeZoneAndSubnet
POST /api/bcc/instance/batch/zoneSubnet/change
POST /api/bcc/instance/batchChangeNameAndHostname
POST /api/bcc/instance/changeZoneAndSubnet
PUT /api/bcc/instance/changeVpc
POST /api/bcc/instance/batchChangeSubnet
POST /api/bcc/instance/delIpv6
POST /api/bcc/eip/unbind
POST /api/bcc/eip/bind
POST /api/bcc/instance/addIpv6
PUT /v2/instance/reserved/modify
POST /v2/instance/reserved/renew
POST /v2/instance/reserved/autoRenew
POST /v2/instance/reserved/create
PUT /v2/instance/deployset/{deploySetId}
POST /v2/instance/reserved/cancelAutoRenew
POST /v2/keypair
POST /v2/image/import
POST /api/qianfan/prompt/api/v1/template/delete
POST /api/qianfan/prompt/api/v1/template/create
POST /api/qianfan/carrier/api
POST /api/qianfan/prompt/api/v1/template/update
POST /api/qianfan/canghai/api
POST /api/qianfan/prompt/api/v1/batchOptimize/delete
POST /api/qianfan/prompt/api/v1/singleOptimize/delete
POST /api/qianfan/prompt/api/v1/evaluate/create
POST /api/qianfan/prompt/api/v1/singleOptimize/create
POST /api/qianfan/console/order/v2/confirm
POST /api/qianfan/console/order/destroy
POST /api/qianfan/console/app/save
POST /api/qianfan/console/app/delete
POST /api/qianfan/console/resource/privatePoolClose
POST /api/bls/v2/log/alarm/policy/disable
POST /api/bls/v2/log/alarm/policy/enable
POST /api/bls/v2/log/alarm/policy/delete
POST /api/bls/v3/log/logshipper/update
POST /api/bls/v2/log/logshipper/create
POST /api/bls/v2/log/alarm/policy/update
POST /api/bls/v2/log/alarm/policy/create
POST /api/bls/v2/log/alarm/create
POST /api/bls/v2/log/fastquery/delete
POST /api/bls/v2/log/logstore/index/delete
POST /api/bls/v2/log/alarm/update
POST /api/bls/v2/log/alarm/delete
POST /api/bls/v2/host/delete
POST /api/bls/v2/log/logstore/update
POST /api/bls/v2/log/fastquery/create
POST /api/bls/v2/instanceGroup/host/delete
POST /api/bls/v2/instanceGroup/delete
POST /api/bls/v2/log/logshipper/delete
POST /api/bls/v2/task/pause
POST /api/bls/v3/log/logshipper/create
POST /api/bls/v2/log/logstore/delete
POST /api/bls/v2/log/logshipper/status
POST /api/bls/v2/task/host/add
POST /api/bls/v2/log/logstore/index/update
POST /api/bls/v2/task/update
POST /api/bls/v2/instanceGroup/create
POST /api/bls/v2/instanceGroup/add
POST /api/bls/v2/log/logstore/create
POST /api/bls/v2/task/remove
POST /api/bls/v2/token/create
POST /api/bls/v2/token/remove
POST /api/bls/v2/host/task/remove
GET /login/securitytoken
POST /v2/promptTemplates
POST /v2/model
POST /v2/charge
POST /v2/service
POST /v2/batchinference
POST /v2/finetuning
POST /csm/api/v1/custom/alarm/configs/block
POST /csm/api/v1/custom/alarm/configs/unblock
POST /csm/api/v1/userId/{userId}/site/alarm/config/unblock
DELETE /csm/api/v1/userId/{userId}/application
POST /csm/api/v1/services/alarm/config/unblock
PUT /csm/api/v1/userId/{userId}/site/alarm/config/update
POST /csm/api/v1/userId/{userId}/site/alarm/config/block
POST /csm/api/v1/services/alarm/config/block
PUT /csm/api/v1/custom/alarm/configs/update
PUT /csm/api/v1/userId/{userId}/application/alarm/config/update
DELETE /csm/api/v1/userId/{userId}/site/delete
POST /csm/api/v1/site/once/taskDelete
POST /csm/api/v1/site/once/{protocolType}/taskCreate
PUT /csm/api/v2/userId/{userId}/services/{services}/alarm/config/update
POST /api/bcm/v1/csm/site/{type}/create
POST /csm/api/v1/userId/{userId}/custom/namespaces/delete
POST /csm/api/v1/userId/{userId}/custom/namespaces/create
PUT /csm/api/v1/userId/{userId}/application/task/update
DELETE /csm/api/v1/userId/{userId}/application/task/delete
POST /csm/api/v1/userId/{userId}/application/task/create
DELETE /csm/api/v1/userId/{userId}/application/instance
POST /api/bcm/v1/csm/custom/alarm/configs/stop
POST /csm/api/v1/userId/{userId}/application/instance/bind
PUT /csm/api/v1/userId/{userId}/application
POST /api/bcm/v1/csm/userId/application/create
POST /csm/api/v1/userId/{userId}/application
POST /api/bcm/v1/csm/site/ip/taskDelete
POST /api/bcm/v1/csm/custom/metrics/delete
POST /api/bcm/v1/csm/userId/application/update
POST /api/bcm/v1/csm/custom/namespaces/update
POST /api/bcm/v1/csm/custom/alarm/configs/block
POST /api/bcm/v1/csm/site/alarm/configs/stop
POST /api/bcm/v1/csm/site/ip/taskCreate
POST /api/bcm/v1/csm/site/alarm/configs/start
POST /api/bcm/v1/csm/site/once/taskDelete
POST /api/bcm/v1/csm/site/once/taskCreate
POST /api/bcm/v1/csm/custom/namespaces/delete
POST /api/bcm/v1/csm/custom/metrics/create
POST /api/bcm/v1/csm/userId/application/alarm/config/start
POST /api/bcm/v1/csm/userId/application/alarm/config/stop
POST /api/bcm/v1/csm/custom/alarm/configs/update
POST /api/bcm/v1/csm/site/alarm/configs/delete
POST /api/bcm/v1/csm/site/alarm/configs/block
POST /api/bcm/v1/csm/custom/namespaces/create
POST /api/bcm/v1/csm/site/alarm/config/update
POST /api/bcm/v1/csm/custom/event/alarm/configs/create
POST /api/bcm/v1/csm/site/delete
POST /api/bcm/v1/csm/site/alarm/config/create
POST /api/bcm/v1/csm/userId/application/delete
POST /api/bcm/v1/csm/dashboard/create
POST /api/bcm/v1/csm/dashboard/update
POST /api/bcm/v1/csm/dashboard/delete
POST /api/bcm/v1/csm/userId/application/instance/bind
POST /api/bcm/v1/csm/userId/application/task/delete
POST /api/bcm/v1/csm/userId/application/instance/delete
POST /api/bcm/v1/csm/services/alarm/config/start
POST /api/bcm/v1/csm/userId/application/alarm/delete
POST /api/bcm/v1/csm/services/alarm/config/update
POST /api/bcm/v1/event/alarm/policies/create
POST /api/bcm/v1/csm/site/stop
POST /api/bcm/v1/csm/custom/alarm/configs/create
POST /api/bcm/v1/csm/services/alarm/config/unblock
POST /api/bcm/v1/csm/services/alarm/config/stop
POST /api/bcm/v1/csm/userId/application/task/update
POST /api/bcm/v1/csm/userId/application/alarm/config/update
POST /api/bcm/v1/csm/userId/application/task/create
POST /api/bcm/v1/csm/services/alarm/config/block
POST /api/bcm/v1/csm/custom/metrics/update
POST /csm/api/v1/services/alarm/config/create
POST /api/as/group/start
POST /api/as/template
POST /api/as/group/pause
POST /api/as/rule/{computeScalingGroupRuleName}
PUT /api/as/group/{groupId}
PUT /api/as/group
POST /api/as/rule
POST /api/as/group/{computeScalingGroupId}
POST /api/as/group/{groupId}/relation
POST /api/as/group/{groupId}
POST /api/as/group/{groupId}/blb
PUT /api/as/node/{groupId}
PUT /v1/group
PUT /v1/template
POST /v1/group/{groupId}/relation
PUT /v1/node/{groupId}
PUT /v1/rule/{ruleId}
DELETE /v1/rule/{ruleId}
POST /v1/group/{groupId}
POST /v1/rule
POST /v1/group/delete
POST /v1/group
POST /v1/group/pause
POST /v1/group/{groupId}/blb
POST /v1/group/start
PUT /v1/group/{groupId}
POST /v2/instance/reserved/transfer/revoke
POST /v2/instance/reserved/transfer/refuse
POST /v2/instance/ehc/cluster/create
POST /v2/instance/ehc/cluster/modify
POST /v2/instance/ehc/cluster/delete
POST /api/bcccheck/app/execution/delete
POST /v2/instance/reserved/transfer/accept
POST /v2/instance/reserved/transfer/create
POST /api/bcc/ehcCluster/deleted
POST /api/bcc/ehcCluster/update
POST /api/bcc/reserved/transfer/accept
POST /api/bcc/reserved/transfer/revoke
POST /api/bcc/reserved/transfer/create
POST /api/bcccheck/app/execution/feed
POST /api/bcc/reserved/transfer/refuse
POST /v2/dataset
PUT /api/cce/service/v2/cluster/{clusterId}/clusterName
PUT /api/cce/service/v2/cluster/{clusterId}/forbiddelete
POST /api/cce/service/v2/cluster
GET /api/cce/service/v2/kubeconfig/{clusterId}/public
DELETE /api/cce/service/v1/cluster/{clusterId}
PUT /api/cce/service/v2/cluster/{clusterId}/add_eni_subnets
GET /api/cce/service/v2/kubeconfig/{clusterId}/vpc
PUT /api/cce/service/v2/cluster/{clusterId}/instancegroup/{instanceGroupId}/replicas
PUT /api/cce/service/v2/cluster/{clusterId}/crd
POST /api/cce/service/v2/cluster/{clusterId}/instancegroup
DELETE /api/cce/service/v2/cluster/{clusterId}/instancegroup/{instanceGroupId}
PUT /api/cce/service/v2/cluster/{clusterId}/instancegroup/{instanceGroupId}/attachInstances
PUT /api/cce/service/v2/cluster/{clusterId}/instancegroup/{instanceGroupId}/autoscaler
PUT /api/cce/service/v2/cluster/{clusterId}/instancegroup/{instanceGroupId}/configure
POST /api/cce/service/v2/cluster
PUT /api/cce/service/v2/cluster/{clusterId}/forbiddelete
PUT /api/cce/service/v2/cluster/{clusterId}/clusterName
PUT /api/cce/service/v2/cluster/{clusterId}/description
DELETE /api/cce/service/v2/cluster/{clusterId}
PUT /api/cce/service/v2/cluster/{clusterId}/instancegroup/{instanceGroupId}/instancetemplate
POST /api/iam/sts/role/deleteV2
POST /api/iam/sts/role/activateV2
POST /api/iam/sts/role/bcepolicies/update
PUT /v1/role/{roleName}
DELETE /v1/role/{roleName}/policy/{policyName}
POST /v1/role
DELETE /v1/role/{roleName}
PUT /v1/role/{roleName}/policy/{policyName}
PUT /api/cce/monitor/event/open
PUT /api/cce/monitor/event/close
POST /api/cce/service/v2/cluster/{clusterId}/logconfigs
DELETE /api/cce/service/v2/cluster/{clusterId}/logconfigs/default/kube-apiserver-log
POST /api/cce/service/v2/cluster/{clusterId}/addon
POST /api/cce/service/v2/cluster/{clusterId}/bcm
PUT /api/cce/service/v2/cluster/{clusterId}/logconfigs/default/kube-apiserver-log
POST /api/cce/monitor/audit/deploy
POST /api/cce/service/v2/cluster/{clusterId}/addon/enablelog
POST /api/cce/monitor/audit
POST /api/cce/monitor/audit/update
POST /api/cce/service/v2/cluster/{clusterId}/workflow
POST /api/bmr/v2/cluster/terminate
POST /api/bmr/v2/service_manager/component/doaction
POST /api/bmr/v2/service_manager/service/doaction
POST /api/bmr/v2/service_manager/serviceconfig/modify
POST /api/bmr/v2/instance/start
POST /api/waf/v1/config/base_update
POST /api/waf/v1/resource/destroy
POST /api/waf/v1/config/domain_update
POST /api/waf/v2/saaswaf/distribution/deliver_delete
POST /api/waf/v2/saaswaf/custom_page
POST /api/waf/v2/saaswaf/distribution/deliver_create
POST /api/waf/v1/config/domain_delete
POST /api/waf/v2/cache/purge_urls
POST /api/waf/v2/cache_rule
POST /api/waf/v2/cache_rule/batch_switch
POST /api/waf/v2/cache/purge_domain
POST /api/waf/v2/cert_add
POST /api/waf/v2/saaswaf/hc_batch_switch
POST /api/waf/v2/saaswaf/health_check_save
POST /api/waf/v2/bot/rules
POST /api/waf/v2/saaswaf/config/domain_update
POST /api/waf/v2/saaswaf/config/base_update
POST /api/waf/v2/order/confirm_saaswaf
PUT /api/waf/v2/waf_cc/template/update
POST /api/waf/v2/saaswaf/config/domain_delete
POST /api/waf/v2/waf_cc/template/create
POST /api/waf/v2/saaswaf/traffic_packet_bind
POST /api/waf/v2/order/confirm_traffic_packet
DELETE /api/waf/v2/waf_cc/template/delete
POST /api/waf/v2/sasswaf/traffic_packet_rename
POST /api/waf/v2/waf/notify_to_user
POST /v1/probe
PUT /v1/probe/{probeId}
PUT /api/bsap/v1/assets
POST /api/bsap/v1/micro-segmentation/rule/add
DELETE /api/bsap/v1/micro-segmentation/rule/del
PUT /api/bsap/v1/micro-segmentation/rule/edit
POST /api/bsap/v1/micro-segmentation/rule/relate-assets
POST /api/bsap/v1/vulnerability-scan/task/add
DELETE /api/bsap/v1/vulnerability-scan/task/del
PUT /api/bsap/v1/vulnerability-scan/task/edit
POST /api/bsap/v1/vulnerability-scan/white/add
DELETE /api/bsap/v1/vulnerability-scan/white/del
POST /api/bsap/v1/baseline-examination/task/add
DELETE /api/bsap/v1/baseline-examination/task/del
PUT /api/bsap/v1/baseline-examination/task/edit
PUT /api/bsap/v1/waf/rule-status
PUT /api/bsap/v1/waf/custom-rule/edit
POST /api/bsap/v1/waf/instance-custom-rule/add
PUT /api/bsap/v1/waf/instance-custom-rule/edit
POST /api/bsap/v1/waf/instance-custom-rule/del
POST /api/bsap/v1/waf/ip-black/form-add
POST /api/bsap/v1/waf/ip-black/del
POST /api/bsap/v1/waf/ip-black/txt-add
POST /api/bsap/v1/waf/ip-white/form-add
POST /api/bsap/v1/waf/ip-white/del
POST /api/bsap/v1/waf/ip-white/txt-add
POST /api/bsap/v1/report/subscribe/add
GET /api/bsap/v1/report/download
POST /api/bsap/v1/report/send
DELETE /api/bsap/v1/report/del
PUT /api/nssa/v1/assets/edit
PUT /api/nssa/v1/risky-assets/risk-level/edit
PUT /api/nssa/v1/alarms/handle-state/edit
POST /api/nssa/v1/report/task/add
PUT /api/nssa/v1/assets
POST /api/nssa/v1/micro-segmentation/rule/add
DELETE /api/nssa/v1/micro-segmentation/rule/del
PUT /api/nssa/v1/micro-segmentation/rule/edit
POST /api/nssa/v1/micro-segmentation/rule/relate-assets
POST /api/nssa/v1/vulnerability-scan/task/add
DELETE /api/nssa/v1/vulnerability-scan/task/del
PUT /api/nssa/v1/vulnerability-scan/task/edit
POST /api/nssa/v1/vulnerability-scan/white/add
DELETE /api/nssa/v1/vulnerability-scan/white/del
POST /api/nssa/v1/baseline-examination/task/add
DELETE /api/nssa/v1/baseline-examination/task/del
PUT /api/nssa/v1/baseline-examination/task/edit
PUT /api/nssa/v1/waf/rule-status
PUT /api/nssa/v1/waf/custom-rule/edit
POST /api/nssa/v1/waf/instance-custom-rule/add
PUT /api/nssa/v1/waf/instance-custom-rule/edit
POST /api/nssa/v1/waf/instance-custom-rule/del
POST /api/nssa/v1/waf/ip-black/form-add
POST /api/nssa/v1/waf/ip-black/del
POST /api/nssa/v1/waf/ip-black/txt-add
POST /api/nssa/v1/waf/ip-white/form-add
POST /api/nssa/v1/waf/ip-white/del
POST /api/nssa/v1/waf/ip-white/txt-add
POST /api/nssa/v1/report/subscribe/add
GET /api/nssa/v1/report/download
POST /api/nssa/v1/report/send
DELETE /api/nssa/v1/report/del
PUT /api/nssa/v1/waf/rule-edit