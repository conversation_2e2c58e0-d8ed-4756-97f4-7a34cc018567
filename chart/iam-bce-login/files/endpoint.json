{
    "regions": [
        {
            "region": "default",
            "services": [
                {
                    "service": "IAM",
                    {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
                    "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
                },
                {
                    "service": "STS",
                    {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
                    "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iamSts.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iamSts.port }}/v1"
                },
                {
                    "service": "BcePass",
                    "endpoint": "http://{{.Values.appspace.charts.requires.iamBcepass.domain}}:{{.Values.appspace.charts.requires.iamBcepass.port}}/v4"
                },
                {
                    "service": "UserSettings",
                    "endpoint": "http://{{.Values.appspace.charts.requires.platUserConfig.domain}}:{{.Values.appspace.charts.requires.platUserConfig.port}}/v1"
                },
                {
                    "service": "IAM_RISK",
                    "endpoint": "http://{{.Values.appspace.charts.requires.iamRisk.domain}}:{{.Values.appspace.charts.requires.iamRisk.port}}/v1"
                },
                {
                    "service": "InvitedCode",
                    "endpoint": ""
                },
                {
                    "service": "ServiceType",
                    "endpoint": ""
                },
                {
                    "service": "Price",
                    "endpoint": ""
                },
                {
                    "service": "Order",
                    "endpoint": ""
                },
                {
                    "service": "Resource",
                    "endpoint": ""
                },
                {
                    "service": "Finance",
                    "endpoint": ""
                },
                {
                    "service": "BceFinance",
                    "endpoint": ""
                },
                {
                    "service": "BCC",
                    "endpoint": ""
                },
                {
                    "service": "CDS",
                    "endpoint": ""
                },
                {
                    "service": "SECURITYGROUP",
                    "endpoint": ""
                },
                {
                    "service": "BLB",
                    "endpoint": ""
                },
                {
                    "service": "BOS",
                    "endpoint": ""
                },
                {
                    "service": "SCS",
                    "endpoint": ""
                },
                {
                    "service": "RDS.migration",
                    "endpoint": ""
                },
                {
                    "service": "RDS",
                    "endpoint": ""
                },
                {
                    "service": "BMR",
                    "endpoint": ""
                },
                {
                    "service": "BCM2",
                    "endpoint": ""
                },
                {
                    "service": "CDN",
                    "endpoint": ""
                },
                {
                    "service": "SES",
                    "endpoint": ""
                },
                {
                    "service": "SMS",
                    "endpoint": ""
                },
                {
                    "service": "BSS",
                    "endpoint": ""
                }
            ]
        }
    ]
}
