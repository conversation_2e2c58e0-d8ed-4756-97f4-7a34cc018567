appspace:
  charts:
    provides:
      iamBcepass:
        interface: iam-bcepass
        domain: bcepass.agilecloud.com
        port: 8587
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      hacluster:
        interface: hacluster
        optional: false
        domain: '{{ hacluster_domain }}'
        port: '{{ hacluster_port }}'
      iamSts:
        interface: iam-sts
        optional: false
        domain: '{{ iam_sts_domain }}'
        port: '{{ iam_sts_port }}'
      iamRisk:
        interface: iam-risk
        optional: true
        domain: '{{ iam_risk_domain }}'
        port: '{{ iam_risk_port }}'
      iam:
        interface: iam-nginx
        optional: false
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        sts:
          ak: '{{ sts_ak }}'
          sk: '{{ sts_sk }}'
          userId: '{{ sts_user_id }}'
          password: '{{ sts_password }}'
        proxy:
          ak: '{{ proxy_ak }}'
          sk: '{{ proxy_sk }}'
          userId: '{{ proxy_user_id }}'
          password: '{{ proxy_password }}'
      privateSkywalkingGrpc:
        interface: private-skywalking-grpc
        optional: true
        domain: '{{ private_skywalking_grpc_domain }}'
        port: '{{ private_skywalking_grpc_port }}'
    configVals:
      session_expire: 3600
      iam_bcia_mfa_enabled: true
      actuator_port: -1
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
    securityContext: {}
    security:
      enable: false
    middleware:
      redis:
        clusterName: '{{ redis_cluster_name }}'
        domain: '{{ redis_domain }}'
        password: '{{ redis_password }}'
        version: 5
        port: 6379
      xdb:
        bce_iam:
          clusterName: '{{ bce_iam_cluster_name }}'
          user: '{{ bce_iam_user }}'
          password: '{{ bce_iam_password }}'
          domain: '{{ bce_iam_domain }}'
          port: 6203
          permission: write
    replica: 3
    namespace: console
    images:
      iam-bcepass:
        repository: abc-stack/iam-bcepass
        imageTag: ${iam-bcepass.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-bcepass
      command: []
      args: []
      log:
        - dirPaths:
            - "/home/<USER>/bcepass/log/access"
            - "/home/<USER>/bcepass/log/debug"
            - "/home/<USER>/bcepass/log/error"
          logType: local
          logVolume: iam-bcepass-log
          rule:
            duration: 3
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 4000m
        limitMemory: 4096Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-bcepass
        mountPath: /home/<USER>/bcepass/conf/endpoint.json
        subPath: endpoint.json
        name: iam-bcepass
      - type: CONFIG_MAP
        configName: iam-bcepass
        mountPath: /home/<USER>/bcepass/conf/application.properties
        subPath: application.properties
        name: iam-bcepass
      - type: CONFIG_MAP
        configName: iam-bcepass
        mountPath: /home/<USER>/bcepass/conf/iamConf.properties
        subPath: iamConf.properties
        name: iam-bcepass
      - type: CONFIG_MAP
        configName: iam-bcepass
        mountPath: /home/<USER>/bcepass/bin/skywalking-agent/config/agent.config
        subPath: agent.config
        name: iam-bcepass
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8587
          initialDelaySeconds: 15
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-bcepass-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/bcepass/log/
          storageType: local-path
          size: 30Gi
      ports:
      - name: 8587-tcp
        containerPort: 8587
        targetPort: 8587
        servicePort: 8587
        protocol: TCP
        nodePort: ''
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
