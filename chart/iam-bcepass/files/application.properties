server.port={{ .Values.appspace.charts.provides.iamBcepass.port }}
logging.has_access_log=false
logging.has_request-id=false

endpoint.default.regionName:{{.Values.appspace.charts.platformConfigVals.region}}
bce_plat_web_framework.sc.enabled=false
endpoint.has_configuration=false
# sts相关配置
iam.service.id={{.Values.appspace.charts.requires.iam.sts.userId}}
iam.accessKey.id={{.Values.appspace.charts.requires.iam.sts.ak}}
iam.accessKey.secret={{.Values.appspace.charts.requires.iam.sts.sk}}


endpoint.config=file:../conf/endpoint.json
iam.client.conf=../conf/iamConf.properties
iam.region={{.Values.appspace.charts.platformConfigVals.region}}
iam.filter.authenticate=true
iam.mfa.enabled=true
passport.app.id=1240
uc.app.id=285
cookie.domain={{.Values.appspace.charts.global.domain}}
iam.privileged.services=console_scs,console_bss,console_waf,console_bcm,console_bcc,console_bch,bch,console_bbc,console_bmr,console_home,logic_bbc,console_billing
token.discard.time=1500
sts.discard.time=1500
iam.keystone.encryption.primary_key=fimos87ejdusedke
iam.keystone.encryption.backup_key=fimos87ejdusedke
iam.forward.action.whitelist=/api/system/constants/v3;/api/region/available;/api/region/get;\
/api/iam/account/detail;/api/iam/organization/detail;/api/account/get_myaccount;\
/api/mc/letter;/api/ticket/unfinished;/api/iam/organization/detail;\
/api/user/invite/detail;/api/user/invite/validate;\
/api/iam/organization/list;/api/log/csi;/favicon.ico;/api/iam/user/policy/list;\
/api/iam/account/whitelist/status;/api/iam/account/framework/contract/status;\
/api/iam/account/activate/status;/api/iam/user/mfa/devices/create;/api/iam/user/group/policy/list;\
/api/iam/noforward/user/detail;/api/iam/user/audit/log;/api/iam/user/update;\
/api/iam/user/detail;/api/iam/contact_info;\
/api/iam/account/framework/contract/permit

uuap.login.service.url=https://login.bce.baidu.com/collaborator/uuappostlogin
icm.login.service.url:https://login.bce.baidu.com/postlogin/icmuuap
uuap.sso.endpoint=https://uuap.baidu.com
uuap.uic.endpoint=https://uuap.baidu.com/uic/rest/users/getUserByField
uuap.app.key=uuapclient-6-Fh3rdeM4jlnfRSb0dKMn

passport.passgateEndpoint:http://passport.iam.sdns.baidu.com:8300/passgate
passport.app.username:bceplat
passport.app.password:bceplat
uc.secure.host:http://ones-b.sdns.baidu.com:8625/uc-sec-svc-web/usersecureinfo
uc.userinfo.host:http://ones-b.sdns.baidu.com:8625/uc-svc/services/AcctService.php
uc.gateway.username:bceplat
uc.gateway.password:1bxmC7AVRy5Sn2D8
passport.auth.stoken.web.enable=false
passport.auth.stoken.android.enable=true
passport.auth.stoken.ios.enable=false
passport.auth.trusttoken.android.enable=true
passport.auth.trusttoken.ios.enable=true

app.login.referer=https://login.bcetest.baidu.com

# 日志级别调整
#logging.level.org.springframework.web=DEBUG
logging.level.com.baidu.bce.internalsdk=DEBUG

# 数据库访问配置
# 主数据源，默认的
spring.datasource.master.url=jdbc:mysql://{{.Values.appspace.charts.middleware.xdb.bce_iam.domain}}:{{.Values.appspace.charts.middleware.xdb.bce_iam.port}}/bce_iam
spring.datasource.master.username={{.Values.appspace.charts.middleware.xdb.bce_iam.user}}
spring.datasource.master.password={{.Values.appspace.charts.middleware.xdb.bce_iam.password}}
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
# 下面为连接池的补充设置，应用到上面所有数据源中
# 初始化大小，最小，最大
spring.datasource.initialSize=20
spring.datasource.minIdle=20
spring.datasource.maxActive=100
spring.datasource.maxWait=60000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.timeBetweenEvictionRunsMillis=60000
# 配置一个连接在池中最小生存的时间，单位是毫秒
spring.datasource.minEvictableIdleTimeMillis=300000
# 打开PSCache，并且指定每个连接上PSCache的大小
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
# 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
spring.datasource.filters=stat,wall
# 通过connectProperties属性来打开mergeSql功能；慢SQL记录
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.validation-query=SELECT 1
spring.datasource.validation-interval=30000
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false
spring.datasource.log-abandoned=true
spring.datasource.remove-abandoned=true
spring.datasource.remove-abandoned-timeout=60

# 缓存相关配置
spring.cache.type=redis
spring.cache.cache-names=session
spring.redis.host={{.Values.appspace.charts.middleware.redis.domain}}
spring.redis.port={{.Values.appspace.charts.middleware.redis.port}}
spring.redis.password={{.Values.appspace.charts.middleware.redis.password}}
spring.redis.pool.max-wait=1000
spring.redis.jedis.pool.max-active=100
spring.redis.jedis.pool.max-wait=3000ms
session.ttl.seconds={{.Values.appspace.charts.configVals.session_expire}}
spring.redis.timeout=3000ms
session.refresh.ttl.ignore.uri:/api/mc/letter/unreadoneminute;/api/bcm/alarm/state/summary
iam.trails.enabled=false

management.security.enabled=false

iam.root.enabled=true
login.notice.enable=false
iam.organization.detect.urls:/api/iam;/iam

# 全局mfa相关配置
iam.global.mfa.quota.condition=globalMfaConfig
iam.global.mfa.quota.id=e5e76f2d0e2aa16894dc8a8bddbef750
iam.bcia.mfa.enabled={{.Values.appspace.charts.configVals.iam_bcia_mfa_enabled}}


spring.second.redis.host={{.Values.appspace.charts.middleware.redis.domain}}
spring.second.redis.port={{.Values.appspace.charts.middleware.redis.port}}
spring.second.redis.password={{.Values.appspace.charts.middleware.redis.password}}
spring.second.redis.jedis.pool.max-wait=1000
spring.second.redis.jedis.pool.max-idle=100
spring.second.redis.timeout=200

sc.webframework.ScLoggerConfiguration.enable=false
webframework.register.center.env.vip.enabled=false
webframework.register.center.tianlu.enabled=false
management.health.formula-discovery.enabled=false
spring.cloud.discovery.client.health-indicator.enabled=false
bcepass.client.feign.enable=false
webframework.SecurityAutoConfiguration.disable.flag=true
spring.application.name=bcepass-{{.Values.appspace.charts.platformConfigVals.region}}

#================ disable actuator ================#
management.server.port={{.Values.appspace.charts.configVals.actuator_port}}