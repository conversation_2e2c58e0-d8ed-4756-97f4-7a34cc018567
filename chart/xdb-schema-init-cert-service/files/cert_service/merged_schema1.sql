--
-- Table structure for table `cert`
--

CREATE TABLE `cert` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` char(32) NOT NULL COMMENT '用户 uuid',
  `cert_id` char(32) NOT NULL COMMENT '用户证书ID',
  `cert_name` varchar(128) NOT NULL COMMENT '证书用户自定义名称',
  `cert_server_data` TEXT DEFAULT NULL,
  `cert_private_data` TEXT DEFAULT NULL,
  `cert_link_data` BLOB NOT NULL COMMENT '证书链数据',
  `cert_common_name` varchar(128) NOT NULL COMMENT '证书内容中域名',
  `cert_start_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '证书生效时间 UTC',
  `cert_stop_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '证书失效时间 UTC',
  `create_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间 UTC',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录修改时间 UTC',
  `notified` tinyint(1) NOT NULL DEFAULT '0',
  `internal` tinyint(1) NOT NULL DEFAULT '0' COMMENT '内部证书标志',
  `cert_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '证书类型',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_cert_cert_id` (`cert_id`),
  KEY `uk_cert_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='证书详细信息表';

--
-- Table structure for table `quota`
--
CREATE TABLE `quota` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` char(32) NOT NULL COMMENT '用户 uuid',
  `quota` bigint(20) NOT NULL COMMENT '用户证书数量限制',
  `create_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间 UTC',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录修改时间 UTC',
  PRIMARY KEY (`id`),
  KEY `uk_quota_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='证书数量限制表';

--
-- Table structure for table `service`
--
CREATE TABLE `service` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` char(32) NOT NULL COMMENT '用户 uuid',
  `cert_id` char(32) NOT NULL COMMENT '用户证书ID',
  `service_name` char(32) NOT NULL COMMENT '绑定证书服务名称',
  `resource_id` char(64) NOT NULL COMMENT '绑定证书资源ID',
  `create_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间 UTC',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录修改时间 UTC',
  PRIMARY KEY (`id`),
  KEY `uk_cert_service_resource_id` (`resource_id`),
  KEY `idx_cert_id` (`cert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='证书服务详细信息表';



--
-- Table structure for table `extra_cert`
--
CREATE TABLE `extra_cert` (
                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id值',
                        `cert_id` char(32) NOT NULL COMMENT '用户证书ID',
                        `cert_server_data` TEXT NOT NULL COMMENT '服务器证书',
                        `cert_private_data` TEXT NOT NULL COMMENT '证书私钥，加密存储',
                        `cert_link_data` BLOB NOT NULL COMMENT '证书链数据',
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `uk_ex_cert_cert_id` (`cert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8 COMMENT='额外证书信息表';