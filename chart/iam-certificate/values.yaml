appspace:
  charts:
    provides:
      iamCertificate:
        interface: iam-certificate
        domain: certificate.agilecloud.com
        port: 8581
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      iam:
        interface: iam-nginx
        optional: false
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        cas:
          ak: '{{ cas_ak }}'
          sk: '{{ cas_sk }}'
          userId: '{{ cas_user_id }}'
          password: '{{ cas_password }}'
      hacluster:
        interface: hacluster
        optional: false
        domain: '{{ hacluster_domain }}'
        port: '{{ hacluster_port }}'
      privateSkywalkingGrpc:
        interface: private-skywalking-grpc
        optional: true
        domain: '{{ private_skywalking_grpc_domain }}'
        port: '{{ private_skywalking_grpc_port }}'
    configVals:
      actuator_port: -1
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
    securityContext: {}
    security:
      enable: false
    middleware:
      xdb:
        cert_service:
          clusterName: '{{ cert_service_cluster_name }}'
          user: '{{ cert_service_user }}'
          password: '{{ cert_service_password }}'
          domain: '{{ cert_service_domain }}'
          port: 6203
          permission: write
    replica: 3
    namespace: console
    images:
      iam-certificate:
        repository: abc-stack/iam-certificate
        imageTag: ${iam-certificate.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-certificate
      command: []
      args: []
      log:
        - dirPaths:
            - /home/<USER>/certificate/log/
          logType: local
          logVolume: iam-certificate-log
          rule:
            duration: 7
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 4000m
        limitMemory: 2800Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-certificate
        mountPath: /home/<USER>/certificate/conf/endpoint.json
        subPath: endpoint.json
        name: iam-certificate
      - type: CONFIG_MAP
        configName: iam-certificate
        mountPath: /home/<USER>/certificate/conf/application.properties
        subPath: application.properties
        name: iam-certificate
      - type: CONFIG_MAP
        configName: iam-certificate
        mountPath: /home/<USER>/certificate/bin/skywalking-agent/config/agent.config
        subPath: agent.config
        name: iam-certificate
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8581
          initialDelaySeconds: 225
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-certificate-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/certificate/log/
          storageType: local-path
          size: 30Gi
      ports:
      - name: 8581-tcp
        containerPort: 8581
        targetPort: 8581
        servicePort: 8581
        protocol: TCP
        nodePort: ''
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
