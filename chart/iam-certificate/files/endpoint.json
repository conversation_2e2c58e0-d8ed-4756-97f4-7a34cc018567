{
    "regions": [
        {
            "region": "default",
            "services": [
                {
                    "service": "IAM",
                    {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
                    "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
                }
            ]
        }
    ]
}
