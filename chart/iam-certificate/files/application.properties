bce_plat_web_framework.is_service_application=true
iam.service_token.paths=/v1/internal/**
server.port={{ .Values.appspace.charts.provides.iamCertificate.port }}
swagger.start:false
spring.application.name=certificate
bce_plat_web_framework.sc.enabled=false

# spring issue: https://github.com/mybatis/spring/issues/186
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.master.url=jdbc:mysql://{{.Values.appspace.charts.middleware.xdb.cert_service.domain}}:{{.Values.appspace.charts.middleware.xdb.cert_service.port}}/cert_service?useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&connectTimeout=5000&socketTimeout=30000
spring.datasource.master.username={{.Values.appspace.charts.middleware.xdb.cert_service.user}}
spring.datasource.master.password={{.Values.appspace.charts.middleware.xdb.cert_service.password}}
# 下面为连接池的补充设置，应用到上面所有数据源中
# 初始化大小，最小，最大
spring.datasource.initialSize=20
spring.datasource.minIdle=20
spring.datasource.maxActive=100
# 执行sql的超时时间
spring.datasource.queryTimeout=3
spring.datasource.loginTimeout=3
spring.datasource.socketTimeout=3000
# 配置获取连接等待超时的时间
spring.datasource.maxWait=60000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.timeBetweenEvictionRunsMillis=60000
# 配置一个连接在池中最小生存的时间，单位是毫秒
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false
# 打开PSCache，并且指定每个连接上PSCache的大小
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
# 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
spring.datasource.filters=stat,wall

endpoint.config=file:../conf/endpoint.json

iam.security_token.support=true
iam.subuser.support=true

iam.console.username=cas
iam.console.password={{.Values.appspace.charts.requires.iam.cas.password }}

bae.accesskey={{.Values.appspace.charts.requires.iam.cas.ak}}
bae.secretkey={{.Values.appspace.charts.requires.iam.cas.sk}}

log.dir=/home/<USER>/certificate/log
logging.has_console_appender=false
logging.has_web_debug_appender=false
logging.info_log_file_path=/home/<USER>/certificate/log/api.info.log
logging.error_log_file_path=/home/<USER>/certificate/log/api.error.log
logging.warn_log_file_path=/home/<USER>/certificate/log/api.warn.log
logging.debug_log_file_path=/home/<USER>/certificate/log/api.debug.log
logging.access_log_file_path=/home/<USER>/certificate/log/api.access.log
logging.access_debug_log_file_path=/home/<USER>/certificate/log/api.access_debug.log
logging.info_log_max_history_in_hours=720
logging.error_log_max_history_in_days=30
logging.warn_log_max_history_in_days=30
logging.debug_log_max_history_in_days=30
logging.access_log_max_history_in_hours=720
logging.access_debug_log_max_history_in_days=30

cert.auth.internal.service.names=blb,lss,console,baepro,bae,cdn,bch,bos,bss,apigw,apigateway
cert.max.per.user=200
cert.cert_chain_exam.pass=true
#================ disable actuator ================#
management.server.port={{.Values.appspace.charts.configVals.actuator_port}}