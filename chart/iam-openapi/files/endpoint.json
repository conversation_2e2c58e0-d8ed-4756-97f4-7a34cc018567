{
  "regions": [
    {
      "region": "default",
      "services": [
        {
          "service": "cloud-trail",
          "endpoint": "http://{{.Values.appspace.charts.requires.cloudTrail.domain}}:{{.Values.appspace.charts.requires.cloudTrail.port}}/v1"
        },
        {
          "service": "IAM",
          {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
          "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
        },
	      {
          "service": "CRM",
          "endpoint": "http://nouse/v1"
	      },
	      {
          "service": "QualifyV2",
          "endpoint": "http://nouse/v2"
	      },
        {
          "service": "Messages",
          "endpoint": "http://{{.Values.appspace.charts.requires.bceMessages.domain}}:{{.Values.appspace.charts.requires.bceMessages.port}}/v1"
        },
        {
          "service": "FinanceV3",
          "endpoint": "http://{{.Values.appspace.charts.requires.fpFinanceV2.domain}}:{{.Values.appspace.charts.requires.fpFinanceV2.port}}"
        },
        {
          "service": "InvoiceV2",
          "endpoint": "http://nouse"
        },
        {
          "service": "BpResourceManager",
          "endpoint": "http://nouse"
        },
        {
          "service": "OrderV2",
          "endpoint": "http://nouse"
        },
        {
          "service": "BillChargeService",
          "endpoint": "http://nouse"
        },
        {
          "service": "CRM-Contract",
          "endpoint": "http://nouse"
        },
        {
          "service": "UserSettings",
          "endpoint": "http://{{.Values.appspace.charts.requires.platUserConfig.domain}}:{{.Values.appspace.charts.requires.platUserConfig.port}}/v1"
        }
      ]
    }
  ]
}
