bce_plat_web_framework.is_service_application:true
exception.has_service_exception_handler:false
iam.security_token.support:true

# subuser support
iam.subuser.support:true

endpoint.config:file:../conf/endpoint.json
{{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
iam.endpoint:http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3
iam.console.username:iam
iam.console.password:{{.Values.appspace.charts.requires.iam.iam.password}}
iam.signature.paths:/v1/**;/scim/**

organization.account.confirm.template:Tpl_27ef7203-6afc-449e-9e0d-5a989e11d2d0

organization.account.confirm.url:https://{{.Values.appspace.charts.requires.nginxLogin.domain}}:{{.Values.appspace.charts.requires.nginxLogin.port}}/api/iam/org/confirm
organization.account.confirm.secret:A6wwYbcCK0ALyd6wCTHNYEo29xCtyevW

#=================== logging config ===================#
logging.requestId_urlPattern:/*
logging.has_console_appender: true

### logback rolling log, uncomment to open appender ###
logging.info_log_file_path:  ../log/info/iam-openapi.info.log
logging.error_log_file_path: ../log/error/iam-openapi.error.log
logging.warn_log_file_path:  ../log/warn/iam-openapi.warn.log
logging.debug_log_file_path: ../log/debug/iam-openapi.debug.log

### access log, uncomment to open appender ###
logging.access_debug_uri_prefix:/*
logging.access_log_file_path: ../log/access/iam-openapi.access.log
logging.access_debug_log_file_path: ../log/access_debug/bce-console-iam.access_debug.log

logging.has_access_log:true
logging.access_dir=log/access

mvc.has_error_page_configuration:false

### useless but necessary:
### framework-iam injected these values but never used them
login.cookie.md5.key:123
cookie.domain:{{.Values.appspace.charts.global.domain}}
login.url:http://{{.Values.appspace.charts.requires.nginxLogin.domain}}:{{.Values.appspace.charts.requires.nginxLogin.port}}

#openapi 创建子账号白名单相关配置
iam.openapi.license.create.account.whitelist:nouse

#openapi 特权服务号用户密码
iam.privileged.user.name=console_iam
iam.privileged.user.password={{.Values.appspace.charts.requires.iam.console_iam.password}}

uc.register.host:http://nouse:nouse/uc-svc/services/AcctService.php
uc.app.id:285
uc.gateway.username:nouse
uc.gateway.password:nouse

#=============== uuap ===========================#
uuap.uic.endpoint:https://nouse:nouse/uic/rest/users/getUserByField
uuap.uic.appkey:nouse

#================ passport modify ================#
passport.passgateEndpoint:nouse:nouse
passport.app.username:nouse
passport.app.password:nouse

#================ oversea ================#
iam.update.overseas.flag=true
iam.member_role.id=8a8a9c5f9ff94391b0e39ee339d2a92d
iam.uc.enabled=false

# 行业云环境没有CRM，自动激活账户
iam.scim.crm.enabled:false
iam.scim.auto.activate.account:true

#=============== user info config ==========#
user.login.earlyDays={{.Values.appspace.charts.configVals.user_login_earlyDays}}
user.login.lateHours={{.Values.appspace.charts.configVals.user_login_lateHours}}
user.login.hestiaTimeout={{.Values.appspace.charts.configVals.user_login_hestiaTimeout}}
#============== thread pool config =========#
threadPool.batchCoreSize={{.Values.appspace.charts.configVals.threadPool_batchCoreSize}}
threadPool.batchMaximumSize={{.Values.appspace.charts.configVals.threadPool_batchMaximumSize}}
threadPool.batchTaskQueueSize={{.Values.appspace.charts.configVals.threadPool_batchTaskQueueSize}}
#=============== disable springcloud =======#
sc.webframework.ScLoggerConfiguration.enable=false
webframework.register.center.env.vip.enabled=false
webframework.register.center.tianlu.enabled=false
management.health.formula-discovery.enabled=false
spring.cloud.discovery.client.health-indicator.enabled=false

#================ disable actuator ================#
management.server.port={{.Values.appspace.charts.configVals.actuator_port}}