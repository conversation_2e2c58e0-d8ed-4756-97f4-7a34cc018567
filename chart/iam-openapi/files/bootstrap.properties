#
# Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
#
spring.application.name=openapi
server.port={{.Values.appspace.charts.provides.iamOpenapi.port}}

iam.client.feign.enable=false
#iam.client.feign.enable=true
#bce_plat_web_framework.feignClient.iam=iam
#bce-plat-web-framework.region-switch.services.iam.defaultChosenRegion=bj01
#bce-plat-web-framework.region-switch.services.iam.supportRegions=bj01

bce_plat_web_framework.sc.enabled=false

#=================== cnap ===================#
EM_WORKSPACE_ID=nouse
EM_WORKSPACE_NAME=iam
EM_ENV_ID=nouse
EM_ENV_NAME=nouse
EM_APP=nouse
EM_PLATFORM=nouse
EM_PRODUCT_LINE=nouse
EM_SERVICE_CENTER=http://nosue:nouse

spring.cloud.discovery.client.health-indicator.enabled=false
#=================== admin ===================#
spring.boot.admin.client.url=http://nouse:nouse
spring.boot.admin.client.instance.prefer-ip=false
#修复admin注册问题
spring.boot.admin.client.instance.name=${spring.application.name}

bce_plat_web_framework.sc.admin.enabled=false
bce_plat_web_framework.sc.admin.admins=nouse
bce_plat_web_framework.sc.admin.password=nouse
bce_plat_web_framework.sc.admin.profile=nouse
bce_plat_web_framework.sc.admin.iam.endpoint=nouse
bce_plat_web_framework.sc.admin.messages.endpoint=nouse
