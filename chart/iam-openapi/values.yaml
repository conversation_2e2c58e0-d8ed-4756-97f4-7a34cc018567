appspace:
  charts:
    provides:
      iamOpenapi:
        interface: iam-openapi
        domain: iam-openapi.agilecloud.com
        port: 8480
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      bceMessages:
        interface: bce-messages
        optional: false
        domain: '{{ bce_messages_domain }}'
        port: '{{ bce_messages_port }}'
      iam:
        interface: iam-nginx
        optional: false
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        console_iam:
          ak: '{{ console_iam_ak }}'
          sk: '{{ console_iam_sk }}'
          userId: '{{ console_iam_user_id }}'
          password: '{{ console_iam_password }}'
        iam:
          ak: '{{ iam_ak }}'
          sk: '{{ iam_sk }}'
          userId: '{{ iam_user_id }}'
          password: '{{ iam_password }}'
      nginxConsole:
        interface: nginx-console
        optional: true
        domain: '{{ nginx_console_domain }}'
        port: '{{ nginx_console_port }}'
      nginxLogin:
        interface: nginx-login
        optional: true
        domain: '{{ nginx_login_domain }}'
        port: '{{ nginx_login_port }}'
      platUserConfig:
        interface: plat-user-config
        optional: false
        domain: '{{ plat_user_config_domain }}'
        port: '{{ plat_user_config_port }}'
      privateSkywalkingGrpc:
        interface: private-skywalking-grpc
        optional: true
        domain: '{{ private_skywalking_grpc_domain }}'
        port: '{{ private_skywalking_grpc_port }}'
      fpFinanceV2:
        interface: fp-finance-v2
        optional: true
        domain: '{{ fp_finance_v2_domain }}'
        port: '{{ fp_finance_v2_port }}'
      cloudTrail:
        interface: cloud-trail
        optional: true
        domain: '{{ cloud_trail_domain }}'
        port: '{{ cloud_trail_port }}'
    configVals:
      user_login_earlyDays: 180
      user_login_lateHours: 1
      user_login_hestiaTimeout: 15
      threadPool_batchCoreSize: 100
      threadPool_batchMaximumSize: 150
      threadPool_batchTaskQueueSize: 1000
      actuator_port: -1
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
    securityContext: {}
    security:
      enable: false
    middleware: {}
    replica: 3
    namespace: console
    images:
      iam-openapi:
        repository: abc-stack/iam-openapi
        imageTag: ${iam-openapi.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-openapi
      command: []
      args: []
      log:
        - dirPaths:
            - "/home/<USER>/openapi/log/access"
            - "/home/<USER>/openapi/log/access_debug"
            - "/home/<USER>/openapi/log/debug"
            - "/home/<USER>/openapi/log/error"
            - "/home/<USER>/openapi/log/info"
            - "/home/<USER>/openapi/log/warn"
          logType: local
          logVolume: iam-openapi-log
          rule:
            duration: 3
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 10000m
        limitMemory: 10240Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-openapi
        mountPath: /home/<USER>/openapi/conf/endpoint.json
        subPath: endpoint.json
        name: iam-openapi
      - type: CONFIG_MAP
        configName: iam-openapi
        mountPath: /home/<USER>/openapi/conf/bootstrap.properties
        subPath: bootstrap.properties
        name: iam-openapi
      - type: CONFIG_MAP
        configName: iam-openapi
        mountPath: /home/<USER>/openapi/conf/application.properties
        subPath: application.properties
        name: iam-openapi
      - type: CONFIG_MAP
        configName: iam-openapi
        mountPath: /home/<USER>/openapi/bin/skywalking-agent/config/agent.config
        subPath: agent.config
        name: iam-openapi
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8480
          initialDelaySeconds: 15
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-openapi-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/openapi/log/
          storageType: local-path
          size: 30Gi
      ports:
      - name: 8480-tcp
        containerPort: 8480
        targetPort: 8480
        servicePort: 8480
        protocol: TCP
        nodePort: ''
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
