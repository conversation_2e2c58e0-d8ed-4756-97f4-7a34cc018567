lua_shared_dict prometheus_metrics 50M;

init_by_lua_block {
    require "metric_utils"
}

log_by_lua_block {
    local metric_utils = require "metric_utils"
    if metric_utils.should_record_metric() then
        local api = metric_utils.map_url()
        -- api_status计数
        metric_requests:inc(1, {ngx.var.status, api})

        -- api_service
        waf_service_api_counter:inc(1, {metric_utils.get_service(), api})

        -- akType计数
        waf_ak_type_counter:inc(1, {metric_utils.get_ak_type()})

        -- 请求来源计数: 前置机或other
        waf_via_counter:inc(1, {metric_utils.get_via()})

        -- 记录接口响应延迟
        local latency = (tonumber(ngx.var.request_time) or 0) * 1000
        metric_latency:observe(latency, {api})
    end

}