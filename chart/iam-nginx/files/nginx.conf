worker_processes  {{ .Values.appspace.charts.configVals.worker_processes}};
error_log  logs/error.log {{ .Values.appspace.charts.configVals.log_level}};
pid        status/nginx.pid;
daemon off;
events {
  use epoll;
  worker_connections  {{ .Values.appspace.charts.configVals.worker_connections}};
}
http {
  lua_package_path "/home/<USER>/nginx/site/lualib/?.lua;/home/<USER>/nginx/site/lualib/nginx-lua-prometheus-main/?.lua;/home/<USER>/nginx/lualib/?.lua;";
  lua_package_cpath '/home/<USER>/nginx/lualib/?.so;';
  init_worker_by_lua_file /home/<USER>/nginx/site/lualib/initial.lua;
  client_body_buffer_size {{ .Values.appspace.charts.configVals.client_body_buffer_size}};
  client_max_body_size {{ .Values.appspace.charts.configVals.client_max_body_size}};
  include       mime.types;
  include       prometheus.conf;
  default_type  application/octet-stream;
  client_body_temp_path tmp/client_body;
  fastcgi_temp_path tmp/fastcgi_temp;
  proxy_temp_path tmp/proxy_temp;
  scgi_temp_path tmp/scgi_temp;
  uwsgi_temp_path tmp/uwsgi_temp;
  log_format  main  '[$remote_addr] [$remote_user] [$time_iso8601] [$request_time] '
        '[$http_x_bce_request_id] [$request] [$status] [$body_bytes_sent] '
        '[$http_referer] [$http_user_agent] [$http_x_forwarded_for] [$upstream_http_x_bce_request_service]';
  access_log  logs/access.log  main;

  upstream proxy{
    server {{.Values.appspace.charts.requires.iamProxy.domain}}:{{.Values.appspace.charts.requires.iamProxy.port}} max_fails={{ .Values.appspace.charts.configVals.proxy_max_fails}};
        keepalive {{ .Values.appspace.charts.configVals.proxy_keepalive}};
        keepalive_timeout {{ .Values.appspace.charts.configVals.proxy_keepalive_timeout}};
    }
  upstream manage{
    server {{.Values.appspace.charts.requires.iamManage.domain}}:{{.Values.appspace.charts.requires.iamManage.port}} max_fails={{ .Values.appspace.charts.configVals.manage_max_fails}};
        keepalive {{ .Values.appspace.charts.configVals.manage_keepalive}};
    }
    server {
        listen   {{.Values.appspace.charts.provides.iamNginx.port}};
        # use manage for default
        location / {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }

       location /metrics {
            content_by_lua_block {
                metric_connections:set(tonumber(ngx.var.connections_active) or 0, {"active"})
                metric_connections:set(tonumber(ngx.var.connections_reading) or 0, {"reading"})
                metric_connections:set(tonumber(ngx.var.connections_waiting) or 0, {"waiting"})
                metric_connections:set(tonumber(ngx.var.connections_writing) or 0, {"writing"})
                prometheus:collect()
            }
        }

        location /v3/users/invitation {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location /v3/icm/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location /v3/accounts {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location /v3/batch/accounts {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/accesskeys/[A-Za-z0-9]+/user$ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/users/[A-Za-z0-9]+/accesskeys/?$ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/users/[A-Za-z0-9]+/accesskeys/[A-Za-z0-9]+/?$ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location /v3/resources/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }

	location ~ /v3/sts/role/[A-Za-z0-9]+/grant$ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location /v3/sts/role {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location /v3/bcepolicy {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/users/[A-Za-z0-9]+/bcepolicy/?[A-Za-z0-9]*$ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/groups/[A-Za-z0-9]+/bcepolicy/?[A-Za-z0-9]*$ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/users/?$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/users/[A-Za-z0-9]+/?$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/users/[A-Za-z0-9]+/password$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/users/[A-Za-z0-9]+/groups/?$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }

        location ~ /v3/groups/?$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/groups/[A-Za-z0-9]+/?$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/groups/[A-Za-z0-9]+/users/?.*$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }

        location = /v3/BCE-CRED/tokens {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
    		if ($request_method = GET ) {
    			proxy_pass http://manage;
    		}
            proxy_pass http://manage;
        }
        location ~ /v3/resources/.+/[A-Za-z0-9]+/.+/acl$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            if ($request_method = GET ) {
                proxy_pass http://manage;
            }
            proxy_pass http://manage;
        }
        location ~ /v3/users/[A-Za-z0-9]+/usergroupacl$ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/users/[A-Za-z0-9]+/groups$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            if ($request_method = GET ) {
                proxy_pass http://manage;
            }
            proxy_pass http://manage;
        }
        location ~ /v3/users/[A-Za-z0-9]+/accesskeys$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            if ($request_method = GET ) {
    			proxy_pass http://manage;
    		}
            proxy_pass http://manage;
        }
        location ~ /v3/organization/?.*$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }

        location ~ /v3/users/[A-Za-z0-9]+/signatures$ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/users/[A-Za-z0-9]+$ {
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            if ($request_method = GET ) {
    			proxy_pass http://manage;
    		}
            proxy_pass http://manage;
        }
        location ~ /v3/domains/[A-Za-z0-9]+/services$ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ ^/v3/BCE-CRED/accesskeys/.+$ {
            if ($request_method = GET ) {
                rewrite ^/v3/BCE-CRED/accesskeys/(.*)$ /IamService/http_get_sk_from_ak/$1 last;
            }
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ ^/IamService/http_get_sk_from_ak/(.*) {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            set $url_prefix '/v3/BCE-CRED/accesskeys/';
            proxy_set_header iam.proxy.method 'get_sk_from_ak';
            proxy_set_header iam.proxy.service 'IamService';
            proxy_set_header Content-Type 'text/plain';
            proxy_set_header Org-Uri $url_prefix$1;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://proxy;
        }
        # use proxy for auth and acl
        location = /v3/auth/tokens {
            if ($request_method = GET ) {
                rewrite ^ /IamService/http_authenticate/auth/tokens last;
            }
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location = /IamService/http_authenticate/auth/tokens {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header iam.proxy.method 'authenticate';
            proxy_set_header iam.proxy.service 'IamService';
            proxy_set_header Content-Type 'text/plain';
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_set_header Org-Uri '/v3/auth/tokens';
            proxy_pass http://proxy;
        }
        location = /v3/BCE-CRED/accesskeys {
            if ($request_method = POST ) {
                rewrite ^ /IamService/http_authenticate/BCE-CRED/accesskeys last;
            }
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location = /IamService/http_authenticate/BCE-CRED/accesskeys {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header iam.proxy.method 'authenticate';
            proxy_set_header iam.proxy.service 'IamService';
            proxy_set_header Content-Type 'text/plain';
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_set_header Org-Uri '/v3/BCE-CRED/accesskeys';
            proxy_pass http://proxy;
        }
        location = /v3/BCE-AWS/signature {
                if ($request_method = POST ) {
                 rewrite ^ /IamService/http_authenticate/BCE-AWS/signature last;

                }
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-Ip $remote_addr;
             proxy_pass http://manage;

        }
        location = /IamService/http_authenticate/BCE-AWS/signature {
             proxy_http_version 1.1;
             proxy_set_header Connection "";
             proxy_set_header Content-Type 'text/plain';
             proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
             proxy_set_header Org-Uri '/v3/BCE-AWS/signature';
             proxy_pass http://proxy;

        }
        location = /v3/BCE-CRED/permissions {
            if ($request_method = POST ) {
                rewrite ^ /IamService/http_verify/BCE-CRED/permissions last;
            }
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location = /IamService/http_verify/BCE-CRED/permissions {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header iam.proxy.method 'verify';
            proxy_set_header iam.proxy.service 'IamService';
            proxy_set_header Content-Type 'text/plain';
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_set_header Org-Uri '/v3/BCE-CRED/permissions';
            proxy_pass http://proxy;
        }
        location ~ ^/v3/users/[A-Za-z0-9]+/permissions$ {
            if ($request_method = POST ) {
                rewrite ^/v3/users/([A-Za-z0-9]+)/permissions$ /IamService/http_user_verify/$1 last;
            }
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ ^/IamService/http_user_verify/(.*) {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            set $url_prefix '/v3/users/';
            set $url_suffix '/permissions';
            proxy_set_header iam.proxy.method 'user_verify';
            proxy_set_header iam.proxy.service 'IamService';
            proxy_set_header Content-Type 'text/plain';
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_set_header Org-Uri $url_prefix$1$url_suffix;
            proxy_pass http://proxy;
        }
        location ~ ^/v3/users/[A-Za-z0-9]+/batch_permissions$ {
	        client_body_buffer_size 1m;
            if ($request_method = POST ) {
                rewrite ^/v3/users/([A-Za-z0-9]+)/batch_permissions$ /IamService/http_user_batch_verify/$1 last;
            }
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ ^/IamService/http_user_batch_verify/(.*) {
            client_body_buffer_size 1m;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            set $url_prefix '/v3/users/';
            set $url_suffix '/batch_permissions';
            proxy_set_header iam.proxy.method 'user_batch_verify';
            proxy_set_header iam.proxy.service 'IamService';
            proxy_set_header Content-Type 'text/plain';
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_set_header Org-Uri $url_prefix$1$url_suffix;
            proxy_pass http://proxy;
        }
        location = /v3/BCE-CRED/federate_permissions {
            if ($request_method = POST ) {
                rewrite ^ /IamService/http_federate_verify/BCE-CRED/federate_permissions last;
            }
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location = /IamService/http_federate_verify/BCE-CRED/federate_permissions {
            proxy_set_header iam.proxy.method 'federate_verify';
            proxy_set_header iam.proxy.service 'IamService';
            proxy_set_header Content-Type 'text/plain';
            proxy_set_header Host $http_host;
	    proxy_set_header X-Real-Ip $remote_addr;
            proxy_set_header Org-Uri '/v3/BCE-CRED/federate_permissions';
            proxy_pass http://proxy;
        }
        location ~ /v3/mfa {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~ /v3/notifyparties {
            limit_except GET {
                deny ************/24;
                deny ************/24;
                deny ************/24;
            }
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            if ($request_method = GET ) {
                proxy_pass http://manage;
            }
            proxy_pass http://manage;
        }
        location ~ /v3/notifygroups/[A-Za-z0-9]+/notifyparties/[A-Za-z0-9]+ {
            limit_except GET {
                deny ************/24;
                deny ************/24;
                deny ************/24;
            }
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            if ($request_method = HEAD ) {
                proxy_pass http://manage;
            }
            proxy_pass http://manage;
        }
        location ~ /v3/notifygroups {
            limit_except GET {
                deny ************/24;
                deny ************/24;
                deny ************/24;
            }
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            if ($request_method = GET ) {
               proxy_pass http://manage;
            }
            proxy_pass http://manage;
        }
        location ~/v3/notifygroups/[A-Za-z0-9]/notifyparties {
            limit_except GET {
                deny ************/24;
                deny ************/24;
                deny ************/24;
            }
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            if ($request_method = GET ) {
                proxy_pass http://manage;
            }
            proxy_pass http://manage;
        }
        location ~/v3/contactinfo {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
        location ~/v3/overseas {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_pass http://manage;
        }
    }
}