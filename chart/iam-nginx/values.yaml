appspace:
  charts:
    provides:
      iamNginx:
        interface: iam-nginx
        domain: iam.agilecloud.com
        port: 35357
        views: '{{ views }}'
    requires:
      iamManage:
        interface: iam-manage
        optional: false
        domain: '{{ iam_manage_domain }}'
        port: '{{ iam_manage_port }}'
      iamProxy:
        interface: iam-proxy
        optional: false
        domain: '{{ iam_proxy_domain }}'
        port: '{{ iam_proxy_port }}'
    configVals:
      log_level: notice
      worker_processes: 4
      worker_connections: 25600
      proxy_keepalive: 512
      proxy_keepalive_timeout: 550
      proxy_max_fails: 3
      manage_keepalive: 512
      manage_max_fails: 3
      client_body_buffer_size: 8m
      client_max_body_size: 8m
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      applb:
        enable: false
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
        serviceMonitor:
          enable: true
          endpoints:
            - port: 35357-tcp # 暴露指标的容器端口名
              path: /metrics # 暴露指标的 HTTP 端点路径
          interval: 10s
    securityContext: {}
    security:
      enable: false
    middleware: {}
    replica: 3
    namespace: console
    images:
      iam-nginx:
        repository: abc-stack/iam-nginx
        imageTag: ${iam-nginx.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-nginx
      command: []
      args: []
      log:
          - filePaths:         # 切分清理, 既对日志进行切割，又要对切割完的日志进行清理
            - "/home/<USER>/nginx/logs/access.log"
            - "/home/<USER>/nginx/logs/error.log"
            logType: local    # 遵循公有云规范，未来可能扩展 efk/bls，当前仅支持 local，即本地存储日志。
            logVolume: iam-nginx-log  # 日志 Volume Name，按需填写。如果不填，默认将所有持久化路径都挂载到日志切分容器中。
            rule:   # 按照大小切分,以下配置含义是当日志大小为1024M 时进行切分，保留10份切分后的日志。即总空间10G。
              rotateSizeMB: 1024M
              rotate: 10
              duration: 3 # 保留日志天数
      imagePullPolicy: Always
      resources:
        cpu: 2000m
        memory: 1024Mi
        limitCPU: 7000m # 预留 2 个核
        limitMemory: 2048Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-nginx
        mountPath: /home/<USER>/nginx/conf/nginx.conf
        subPath: nginx.conf
        name: iam-nginx
      - type: CONFIG_MAP
        configName: iam-nginx
        mountPath: /home/<USER>/nginx/conf/prometheus.conf
        subPath: prometheus.conf
        name: iam-nginx
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 35357
          initialDelaySeconds: 15
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-nginx-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/nginx/logs/
          storageType: local-path
          size: 40Gi
      ports:
      - name: 35357-tcp
        containerPort: 35357
        protocol: TCP
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
