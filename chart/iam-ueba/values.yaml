appspace:
  charts:
    provides:
      iamUeba:
        interface: iam-ueba
        domain: iam-ueba.agilecloud.com
        port: 8567
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      iam:
        interface: iam-nginx
        optional: false
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        cloud_trail:
          ak: '{{ cloud_trail_ak }}'
          sk: '{{ cloud_trail_sk }}'
          userId: '{{ cloud_trail_user_id }}'
          password: '{{ cloud_trail_password }}'
        console_iam:
          ak: '{{ console_iam_ak }}'
          sk: '{{ console_iam_sk }}'
          userId: '{{ console_iam_user_id }}'
          password: '{{ console_iam_password }}'
    configVals:
      proxyTopic: 0cd901398654410caeb745dc8d0c9957__bce_iam_core
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
    securityContext: {}
    security:
      enable: false
    middleware:
      kafka:
        topic:
          clusterName: '{{ kafka_cluster_name }}'
          domain: '{{ kafka_domain }}'
          topicName: topic
          replication: 3
          partitions: 30
          port: 8959
      redis:
        clusterName: '{{ redis_cluster_name }}'
        domain: '{{ redis_domain }}'
        password: '{{ redis_password }}'
        version: 5
        port: 6379
      xdb:
        bce_iam:
          clusterName: '{{ bce_iam_cluster_name }}'
          user: '{{ bce_iam_user }}'
          password: '{{ bce_iam_password }}'
          domain: '{{ bce_iam_domain }}'
          port: 6203
          permission: write
    replica: 3
    namespace: console
    images:
      iam-ueba:
        repository: abc-stack/iam-ueba
        imageTag: ${iam-ueba.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-ueba
      command: []
      args: []
      log: []
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 4000m
        limitMemory: 10240Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-ueba
        mountPath: /home/<USER>/ueba/conf/application.properties
        subPath: application.properties
        name: iam-ueba
      - type: CONFIG_MAP
        configName: iam-ueba
        mountPath: /home/<USER>/ueba/conf/endpoint.json
        subPath: endpoint.json
        name: iam-ueba
      - type: CONFIG_MAP
        configName: iam-ueba
        mountPath: /home/<USER>/ueba/conf/log4j2.xml
        subPath: log4j2.xml
        name: iam-ueba
      - type: CONFIG_MAP
        configName: iam-ueba
        mountPath: /home/<USER>/ueba/activeService/activeService.sh
        subPath: activeService.sh
        name: iam-ueba
      - type: CONFIG_MAP
        configName: iam-ueba
        mountPath: /home/<USER>/ueba/activeService/serviceMeta
        subPath: serviceMeta
        name: iam-ueba
      postStart:
        enable: true
        type: EXEC
        command:
          - /bin/bash
          - -c
          - cd /home/<USER>/ueba/activeService && bash activeService.sh > ../activeService.log 2>&1
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8567
          initialDelaySeconds: 15
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume: []
      ports:
      - name: 8567-tcp
        containerPort: 8567
        targetPort: 8567
        servicePort: 8567
        protocol: TCP
        nodePort: ''
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
