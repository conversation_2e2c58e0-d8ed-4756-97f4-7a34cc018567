<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" name="UEBA" packages="">
    <Properties>
        <Property name="logging.debug_log_file_path">../log/debug/ueba.debug.log</Property>
        <Property name="logging.error_log_file_path">../log/error/ueba.error.log</Property>
        <Property name="logging.has_console_appender">
            ${logging.has_console_appender:-true}
        </Property>
        <Property name="logging.log_pattern">
            %d{yyyy-MM-dd HH:mm:ss.SSS}{GMT+8} %-5level ${PID:- } [%t] --- %-40.40logger{39} : [%X{x-bce-request-id}][%X{currentUser}] %m%n
        </Property>
        <Property name="debugAppender">DebugLogFileAppender</Property>
    </Properties>

    <!-- Appenders配置 -->
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout>
                <Pattern>${logging.log_pattern}</Pattern>
            </PatternLayout>
        </Console>

        <RollingRandomAccessFile name="DebugLogFileAppender" fileName="${logging.debug_log_file_path}"
                                 filePattern="${logging.debug_log_file_path}.%d{yyyyMMddHH}{GMT+8}%i"
                                 immediateFlush="false" append="true">
            <PatternLayout charset="UTF-8">
                <Pattern>${logging.log_pattern}</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="1 GB"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="../log/debug" maxDepth="2">
                    <IfFileName glob="*.log.*" />
                    <IfLastModified age="7d" />
                </Delete>
            </DefaultRolloverStrategy>

        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="ErrorLogFileAppender" fileName="${logging.error_log_file_path}"
                                 filePattern="${logging.error_log_file_path}.%d{yyyyMMddHH}{GMT+8}%i"
                                 immediateFlush="false" append="true">
            <PatternLayout charset="UTF-8">
                <Pattern>${logging.log_pattern}</Pattern>
            </PatternLayout>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="1 GB"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="../log/error" maxDepth="2">
                    <IfFileName glob="*.log.*" />
                    <IfLastModified age="7d" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
    </Appenders>

    <!-- Loggers配置 -->
    <Loggers>
        <!--<Root level="error" includeLocation="false">-->
            <!--<AppenderRef ref="Console"/>-->
        <!--</Root>-->

        <Logger name="com.baidu.bce" level="debug"/>
        <Logger name="com.baidubce" level="debug"/>
        <Logger name="org.apache.http" level="info"/>
        <Logger name="com.netflix" level="info"/>
        <Logger name="com.baidu.bce.iam.keystone.tokenmapper.TokenModelMapper.insertToken" level="info"/>
        <Logger name="org.springframework" level="info"/>
        <Logger name="com.baidu.bce.iam.keystone.security.IamPreAuthenticationFilter" level="info"/>
        <AsyncLogger name="root" level="info"
                     includeLocation="false" additivity="false">
            <AppenderRef ref="${sys:debugAppender}"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ErrorLogFileAppender"/>
        </AsyncLogger>
    </Loggers>
</Configuration>