#
# Copyright (C) 2022 Baidu, Inc. All Rights Reserved.
#
spring.application.name=ueba-online
server.port={{ .Values.appspace.charts.provides.iamUeba.port }}
bce_plat_web_framework.sc.enabled=false
webframework.register.center.tianlu.enabled=false

iam.console.username=cloud_trail
iam.console.password={{.Values.appspace.charts.requires.iam.cloud_trail.password}}
iam.privileged.user.name:console_iam
iam.privileged.user.password:{{.Values.appspace.charts.requires.iam.console_iam.password}}

iam.signature.exclude.paths=
iam.security_token.support=true
bce_plat_web_framework.is_utc_timezone=false
bce_plat_web_framework.is_service_application=true

server.max-http-header-size=1024000
spring.mvc.throw-exception-if-no-handler-found=true
spring.resources.add-mappings=false

spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.master.url=jdbc:mysql://{{.Values.appspace.charts.middleware.xdb.bce_iam.domain}}:{{.Values.appspace.charts.middleware.xdb.bce_iam.port}}/bce_iam?useLegacyDatetimeCode=false&serverTimezone=UTC&&characterEncoding=UTF-8
spring.datasource.master.username={{.Values.appspace.charts.middleware.xdb.bce_iam.user}}
spring.datasource.master.password={{.Values.appspace.charts.middleware.xdb.bce_iam.password}}
spring.datasource.initialSize=20
spring.datasource.minIdle=20
spring.datasource.maxActive=100
spring.datasource.maxWait=60000
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=true
spring.datasource.testOnReturn=false
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.filters=stat
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

spring.datasource.queryTimeout=60
spring.datasource.loginTimeout=60
spring.datasource.socketTimeout=60000

#cnapregistry getService error, disable discovery health check
spring.cloud.discovery.client.health-indicator.enabled=false

#redis
spring.redis.host={{.Values.appspace.charts.middleware.redis.domain}}
spring.redis.port={{.Values.appspace.charts.middleware.redis.port}}
spring.redis.password={{.Values.appspace.charts.middleware.redis.password}}
spring.redis.jedis.pool.max-wait=1000ms
spring.redis.jedis.pool.max-idle=100

spring.aop.proxy-target-class=true
spring.boot.admin.client.instance.service-base-url=http://localhost:8666


logging.has_access_log=false
logging.has_log_interceptor=false

schedule.task.enabled:true
endpoint.config=file:../conf/endpoint.json

# iam kafka config
iam.kafka.consumer.count=10
iam.kafka.topic={{.Values.appspace.charts.configVals.proxyTopic}}
iam.kafka.bootstrapServers={{.Values.appspace.charts.middleware.kafka.topic.domain}}:{{.Values.appspace.charts.middleware.kafka.topic.port}}
iam.kafka.consumer.enable=true