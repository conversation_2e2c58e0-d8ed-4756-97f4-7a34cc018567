#!/bin/bash

set -ex

# 0 init
# iam-nginx 的 endpoint
{{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
iamNginxEndpoint=http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}
# console_iam 服务号的用户名
iamTokenUsername=console_iam
# console_iam 服务号的密码
iamTokenPassword={{.Values.appspace.charts.requires.iam.console_iam.password}}

# 1 get iam token
authToken=$(curl -s -D - -XPOST ${iamNginxEndpoint}/v3/auth/tokens -H'content-type:application/json' -d'{ "auth": { "identity": { "methods": ["password" ], "password": { "user": { "domain": { "name": "Default" }, "name": "'"${iamTokenUsername}"'", "password": "'"${iamTokenPassword}"'" } } }, "scope": { "domain": { "id": "default" } } } }' -o /dev/null | grep X-Subject-Token | awk '{print $2}')
if [ -z "${authToken}" ]; then
    echo "Failed to get the IAM token; possibly the iamTokenUserName or iamTokenPassword is incorrect."
    exit 1
fi

# 2 activate BOS service
serviceMetaFile="serviceMeta"
while IFS= read -r line || [[ -n "$line" ]]; do
    IFS=',' read -r accountId serviceId policyId roleName <<< "$line"
    # 2.0 activate bos
    httpCode=$(curl -s -o /dev/null -w "%{http_code}" -XPUT ''"${iamNginxEndpoint}"'/v3/domains/'"${accountId}"'/services?name=bos&activate' \
    -H 'x-auth-token:'"${authToken}"'' \
    -H 'content-type:application/json' -d '{}')
    if [ ${httpCode} -ne 204 ] && [ ${httpCode} -ne 200 ]; then
        echo "Failed to activate BOS for account: ${accountId}"
        exit 1
    fi

    # 2.1 create role for accountId
    roleId=$(curl -XGET ''"${iamNginxEndpoint}"'/v3/sts/role?name='"${roleName}"'&domain_id='"${accountId}"'' \
    -H 'x-auth-token:'"${authToken}"'' \
    -H 'content-type:application/json' | jq -r '.roles[0].id')
    if [ ${roleId} != "null" ]; then
        echo "already activated role: ${roleName} for account: ${accountId}, roleId is ${roleId}"
    else
        roleId=$(curl -XPOST ''"${iamNginxEndpoint}"'/v3/sts/role' \
        -H 'x-auth-token:'"${authToken}"'' \
        -H 'content-type:application/json' -d '{
            "role":{
                "name":"'"${roleName}"'",
                "description":"System created role: '"${roleName}"'",
                "domain_id":"'"${accountId}"'"
            }
        }' | jq '.role.id')
    fi
    if [ ${roleId} == "null" ]; then
        echo "Failed to create role:${roleName} for account:${accountId}"
        exit 1
    fi

    # 2.2 grant role for policyId
    httpCode=$(curl -s -o /dev/null -w "%{http_code}" -XPUT ''"${iamNginxEndpoint}"'/v3/sts/role/'"${roleId}"'/bcepolicies/'"${policyId}"'' \
    -H 'x-auth-token:'"${authToken}"'' \
    -H 'content-type:application/json' -d '{}')
    if [ ${httpCode} -ne 204 ] && [ ${httpCode} -ne 200 ]; then
        echo "Failed to grant role:${roleId} for policy:${policyId}"
        exit 1
    fi

    # 2.3 grant role for serviceId
    httpCode=$(curl -s -o /dev/null -w "%{http_code}" -XPUT ''"${iamNginxEndpoint}"'/v3/sts/role/'"${roleId}"'/grant/'"${serviceId}"'' \
    -H 'x-auth-token:'"${authToken}"'' \
    -H 'content-type:application/json' -d '{}')
    if [ ${httpCode} -ne 204 ] && [ ${httpCode} -ne 200 ]; then
        echo "Failed to grant role:${roleId} for service: ${serviceId}"
        exit 1
    fi
    echo "Successfully activated BOS in IAM. accountId:${accountId}, serviceId:${serviceId}, roleName:${roleName}, policyId:${policyId}"

done < "${serviceMetaFile}"

exit 0