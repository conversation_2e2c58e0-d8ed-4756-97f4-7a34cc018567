appspace:
  charts:
    provides: {}
    requires:
      bccproxyCinder:
        interface: bccproxy-cinder
        optional: true
        domain: '{{ bccproxy_cinder_domain }}'
        port: '{{ bccproxy_cinder_port }}'
      bccproxyNova:
        interface: bccproxy-nova
        optional: true
        domain: '{{ bccproxy_nova_domain }}'
        port: '{{ bccproxy_nova_port }}'
      bccproxyLogical:
        interface: bccproxy-logical
        optional: true
        domain: '{{ bccproxy_logical_domain }}'
        port: '{{ bccproxy_logical_port }}'
      blbMeta:
        interface: blb-meta
        optional: true
        domain: '{{ blb_meta_domain }}'
        port: '{{ blb_meta_port }}'
      hosteye:
        interface: hosteye
        optional: true
        domain: '{{ hosteye_domain }}'
        port: '{{ hosteye_port }}'
      bccproxy:
        interface: bccproxy
        optional: true
        domain: '{{ bccproxy_domain }}'
        port: '{{ bccproxy_port }}'
      glanceApi:
        interface: glance-api
        optional: true
        domain: '{{ glance_api_domain }}'
        port: '{{ glance_api_port }}'
      iam:
        interface: iam-nginx
        optional: true
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        iam:
          ak: '{{ iam_ak }}'
          sk: '{{ iam_sk }}'
          userId: '{{ iam_user_id }}'
          password: '{{ iam_password }}'
      bccproxyNeutron:
        interface: bccproxy-neutron
        optional: true
        domain: '{{ bccproxy_neutron_domain }}'
        port: '{{ bccproxy_neutron_port }}'
      eiplogicInternal:
        interface: eiplogic-internal
        optional: true
        domain: '{{ eiplogic_internal_domain }}'
        port: '{{ eiplogic_internal_port }}'
      apiLogicMongodbInternal:
        interface: api-logic-mongodb-internal
        optional: true
        domain: '{{ api_logic_mongodb_internal_domain }}'
        port: '{{ api_logic_mongodb_internal_port }}'
      vpclogicInternal:
        interface: vpclogic-internal
        optional: true
        domain: '{{ vpclogic_internal_domain }}'
        port: '{{ vpclogic_internal_port }}'
      scslogicInternal:
        interface: scslogic-internal
        optional: true
        domain: '{{ scslogic_internal_domain }}'
        port: '{{ scslogic_internal_port }}'
      cfsFilemaster:
        interface: cfs-filemaster
        optional: true
        domain: '{{ cfs_filemaster_domain }}'
        port: '{{ cfs_filemaster_port }}'
      bceConsoleDns:
        interface: bce-console-dns
        optional: true
        domain: '{{ bce_console_dns_domain }}'
        port: '{{ bce_console_dns_port }}'
      bosNginx:
        interface: bos-nginx
        optional: true
        domain: '{{ bos_nginx_domain }}'
        port: '{{ bos_nginx_port }}'
      apiLogicVolume:
        interface: api-logic-volume
        optional: true
        domain: '{{ api_logic_volume_domain }}'
        port: '{{ api_logic_volume_port }}'
      apiLogicRds:
        interface: api-logic-rds
        optional: true
        domain: '{{ api_logic_rds_domain }}'
        port: '{{ api_logic_rds_port }}'
      blblogicInternal:
        interface: blblogic-internal
        optional: true
        domain: '{{ blblogic_internal_domain }}'
        port: '{{ blblogic_internal_port }}'
      apiLogicBcc:
        interface: api-logic-bcc
        optional: true
        domain: '{{ api_logic_bcc_domain }}'
        port: '{{ api_logic_bcc_port }}'
      bmrProxy:
        interface: bmr-proxy
        optional: true
        domain: '{{ bmr_proxy_domain }}'
        port: '{{ bmr_proxy_port }}'
      edapGateway:
        interface: edap-gateway
        optional: true
        domain: '{{ edap_gateway_domain }}'
        port: '{{ edap_gateway_port }}'
      bceLogicalEip:
        interface: bce-logical-eip
        optional: true
        domain: '{{ bce_logical_eip_domain }}'
        port: '{{ bce_logical_eip_port }}'
      bceLogicalVpc:
        interface: bce-logical-vpc
        optional: true
        domain: '{{ bce_logical_vpc_domain }}'
        port: '{{ bce_logical_vpc_port }}'
      kmsService:
        interface: kms-service
        optional: true
        domain: '{{ kms_service_domain }}'
        port: '{{ kms_service_port }}'
      apiLogicDts:
        interface: api-logic-dts
        optional: true
        domain: '{{ api_logic_dts_domain }}'
        port: '{{ api_logic_dts_port }}'
      apiLogicBbc:
        interface: api-logic-bbc
        optional: true
        domain: '{{ api_logic_bbc_domain }}'
        port: '{{ api_logic_bbc_port }}'
      apiLogicRabbitmq:
        interface: api-logic-rabbitmq
        optional: true
        domain: '{{ api_logic_rabbitmq_domain }}'
        port: '{{ api_logic_rabbitmq_port }}'
      dbauditApi:
        interface: dbaudit-api
        optional: true
        domain: '{{ dbaudit_api_domain }}'
        port: '{{ dbaudit_api_port }}'
      apiLogicalCsn:
        interface: api-logical-csn
        optional: true
        domain: '{{ api_logical_csn_domain }}'
        port: '{{ api_logical_csn_port }}'
      kafkaAdminServer:
        interface: kafka-admin-server
        optional: true
        domain: '{{ kafka_admin_server_domain }}'
        port: '{{ kafka_admin_server_port }}'
      bceKafkaBms:
        interface: bce-kafka-bms
        optional: true
        domain: '{{ bce_kafka_bms_domain }}'
        port: '{{ bce_kafka_bms_port }}'
      apigateway:
        interface: apigateway
        optional: true
        domain: '{{ apigateway_domain }}'
        port: '{{ apigateway_port }}'
      apiLogicMq:
        interface: api-logic-mq
        optional: true
        domain: '{{ api_logic_mq_domain }}'
        port: '{{ api_logic_mq_port }}'
      apiLogicDrds:
        interface: api-logic-drds
        optional: true
        domain: '{{ api_logic_drds_domain }}'
        port: '{{ api_logic_drds_port }}'
      paloconsole:
        interface: paloconsole
        optional: true
        domain: '{{ paloconsole_domain }}'
        port: '{{ paloconsole_port }}'
    configVals: {}
    log:
      local:
        enable: false
    ingress:
      dns:
        enable: false
      service:
        enable: false
        type: LoadBalancer
        vipType: underlay
      hostNetwork:
        enable: false
      apptree:
        enable: false
    monitor:
      noaheePro:
        enable: false
    securityContext: {}
    security:
      enable: false
    middleware:
      xdb:
        bce_iam:
          clusterName: '{{ bce_iam_cluster_name }}'
          user: '{{ bce_iam_user }}'
          password: '{{ bce_iam_password }}'
          domain: '{{ bce_iam_domain }}'
          port: 6203
          permission: all
    replica: 1
    namespace: xdb-data-init
    images:
      xdb-data-init-bce-iam:
        repository: abc-stack/xdb-data-init-bce-iam
        imageTag: ${xdb-data-init-bce-iam.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: xdb-data-init-bce-iam
      command: []
      args: []
      log: []
      imagePullPolicy: Always
      resources:
        resourceType: VCPU
        cpu: 1000m
        memory: 1500Mi
        ephemeralStorage: 1024Mi
        limitCPU: 2000m
        limitMemory: 1500Mi # 大一些，避免执行 flyway 时 OOM
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: bce-iam
        name: bce-iam
        mountPath: /home/<USER>/xdb-data-init/bce_iam/bce_iam.conf
        subPath: bce_iam.conf
      - type: CONFIG_MAP
        configName: bce-iam-dblist-data
        name: bce-iam-dblist-data
        mountPath: /home/<USER>/xdb-data-init/dblist
        subPath: dblist
      - type: CONFIG_MAP
        configName: bce-iam-aksk
        name: bce-iam-aksk
        mountPath: /home/<USER>/aksk-sql/
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: false
        readinessProbe:
          enable: true
          type: EXEC
          cmd: [ "/bin/bash", "-c", "/home/<USER>/xdb-data-init/control.sh status" ]
          initialDelaySeconds: 5
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
          timeoutSeconds: 5
        startupProbe:
          enable: false
      volume: # 增加升级备份持久卷，以防版本升级问题导致，表结构丢失问题。注意：只能备份表结构。
        - name: backups
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/xdb-data-init/_backups_
          defaultMode: 0755
          storageType: local-path
          size: 50Gi
        - hostFileType: ""              #服务依赖gcc、java环境 可以挂载物理机的路径
          hostPath: /opt
          name: opt
          mountPath: /opt
          type: HOST_PATH
      ports: []
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
