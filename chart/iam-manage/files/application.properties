spring.application.name=manage
server.port={{.Values.appspace.charts.provides.iamManage.port}}

# region
endpoint.default.regionName={{ .Values.appspace.charts.platformConfigVals.region }}

#trick.server.port={{.Values.appspace.charts.provides.iamManage.port}}
bce_plat_web_framework.sc.enabled=false

## passport
passport.passgateEndpoint:http://***********:8300/passgate
passport.app.username:bceplat
passport.app.password:bceplat
# uc
uc.secure.host:http://aq-off.baidu.com:8992/usersecureinfo
uc.userinfo.host:http://cas-off.baidu.com:8008/services/AcctService.php

iam.client.conf=../conf/iamConf.properties
iam.client.enable=true
endpoint.config=file:../conf/endpoint.json

iam.service.id={{.Values.appspace.charts.requires.iam.sts.userId}}
iam.trails.enabled={{.Values.appspace.charts.configVals.iam_trails_enabled}}
iam.event.cache.enabled={{.Values.appspace.charts.configVals.iam_event_cache_enabled}}
iam.event.trail.enabled={{.Values.appspace.charts.configVals.iam_event_trail_enabled}}
iam.event.changeInfo.enabled={{.Values.appspace.charts.configVals.iam_event_changeInfo_enabled}}
kafka.config=file:../conf/kafka.properties
iam.filter.authenticate=false
iam.common.cache.enable=false

iam.deny.authenticate.returned.token=true
iam.authenticate.returned.token.whitelist:console_iam;console_bch

#iam.keystone.cache.enable=false
iam.keystone.lockedTimeInSeconds=10
iam.keystone.transaction.enabled=true
iam.keystone.os_inherit.enabled=true
iam.keystone.encryption.primary_key={{.Values.appspace.charts.configVals.iam_encrypt_key}}
iam.keystone.encryption.backup_key={{.Values.appspace.charts.configVals.iam_encrypt_key}}
iam.keystone.cache.prefix=bce_iam_{{ .Values.appspace.charts.platformConfigVals.region }}_2_manage_
iam.keystone.cache.expiredTime:600
iam.keystone.cache.longer.prefix=bce_iam_{{ .Values.appspace.charts.platformConfigVals.region }}_2_manage_l_
iam.keystone.cache.longer.expiredTime=1200
iam.keystone.issue_service_token_white_list=baepro,billing,bos,bae,bml,multimedia,apigateway,ceph
iam.keystone.token.expiration=7200
iam.keystone.token.iam_region={{ .Values.appspace.charts.platformConfigVals.region }}
iam.keystone.cipher.service={{.Values.appspace.charts.configVals.keystone_cipher_service}}
iam.proxy.cache.prefix:bce_iam_{{ .Values.appspace.charts.platformConfigVals.region }}_2_
accountInOrg.cache.sync.microsecond=0
consolidated.billing.account.join.time.minimum=5
# logging.level.org.springframework.security=DEBUG

contact.info.allow.service.consume=false
iam.user.security.info.consume.service.whitelist:console_iam;messages;sts;proxy;xiaolvyun

#icm
icm.ip.whitelist=***********;***********;************

iam.provider.sync.whitelist:15c64321074a45e08fbd6c9021785e99;c893f65c80bd42ca8c1f61da6718c98b;4ee548fa7e5c460b9408359ababc56fc;3917aeda719d4ad5b7ff1b0c2c1c91bd;5d7e14dd4475443f9659c3adf8f374e7;456f8b9e809846048de2d5c9fcc11dc3;32d4215d20b840ef9350b366332fe7de;b2eaf7f9168c49148fd1c8a03cf5ebc7;fa073eafb46245d990396ef4e161cc20;b9f64bdee0434b238f39eaf3b492de56;ce75e955ba004b7a922fd2428e751ea1;ef1afca6f22e4275bf5840d01f55c9d4;a249831ad200479ebf0baef5d5533b4d;94cd78b4f6c8461a947fe3208bae51dd;69fd69d7740042ccbb72fba6fef02a2f

limits.key.bch.accounts:b1f91fbe6fe54d2eaf70ef0025f1c3c2

logging.level.com.baidu.bce={{.Values.appspace.charts.configVals.loggingLevel}}
logging.access_dir=/home/<USER>/manage/log/access

limits.nolimit.groupmembership.:

limits.users.per.domain:{{.Values.appspace.charts.configVals.limits_users_per_domain}}

# 数据库访问配置
# 主数据源，默认的
spring.aop.proxy-target-class=true
spring.datasource.enabled=false
# spring issue: https://github.com/mybatis/spring/issues/186
spring.datasource.initialize=false
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.jdbc.Driver

{{if .Values.appspace.charts.configVals.enable_security_assessment }}
spring.datasource.master.url=jdbc:mysql://{{.Values.appspace.charts.configVals.gateway_host}}:{{.Values.appspace.charts.configVals.gateway_port}}/bce_iam?useLegacyDatetimeCode=false&serverTimezone=UTC&&characterEncoding=UTF-8
{{ else }}
spring.datasource.master.url=jdbc:mysql://{{.Values.appspace.charts.middleware.xdb.bce_iam.domain}}:{{.Values.appspace.charts.middleware.xdb.bce_iam.port}}/bce_iam?useLegacyDatetimeCode=false&serverTimezone=UTC&&characterEncoding=UTF-8
{{- end }}
spring.datasource.master.username={{.Values.appspace.charts.middleware.xdb.bce_iam.user}}
spring.datasource.master.password={{.Values.appspace.charts.middleware.xdb.bce_iam.password}}
spring.datasource.secondSource=true
spring.datasource.token.url=jdbc:mysql://{{.Values.appspace.charts.middleware.xdb.bce_iam_token.domain}}:{{.Values.appspace.charts.middleware.xdb.bce_iam_token.port}}/bce_iam_token?useLegacyDatetimeCode=false&serverTimezone=UTC
spring.datasource.token.username={{.Values.appspace.charts.middleware.xdb.bce_iam_token.user}}
spring.datasource.token.password={{.Values.appspace.charts.middleware.xdb.bce_iam_token.password}}

# 下面为连接池的补充设置，应用到上面所有数据源中
# 初始化大小，最小，最大
spring.datasource.initialSize=20
spring.datasource.minIdle=20
spring.datasource.maxActive=100
# 配置获取连接等待超时的时间
spring.datasource.maxWait=60000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.timeBetweenEvictionRunsMillis=6000
# 配置一个连接在池中最小生存的时间，单位是毫秒
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle={{.Values.appspace.charts.configVals.datasource_testWhileIdle}}
spring.datasource.testOnBorrow={{.Values.appspace.charts.configVals.datasource_testOnBorrow}}
spring.datasource.testOnReturn={{.Values.appspace.charts.configVals.datasource_testOnReturn}}
# 打开PSCache，并且指定每个连接上PSCache的大小
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
# 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
spring.datasource.filters=stat,wall
# 通过connectProperties属性来打开mergeSql功能；慢SQL记录
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
# 合并多个DruidDataSource的监控数据
#spring.datasource.useGlobalDataSourceStat=true
useJDBCCompliantTimezoneShift=true;

#指定bean所在包
mybatis.type-aliases-package=com.baidu.bce.iam.keystone
#指定映射文件
mybatis.mapperLocations=classpath:mapper/*.xml

# 缓存相关配置
spring.cache.type=redis
#spring.cache.type=NONE
#spring.cache.cache-names=keystone
spring.redis.host={{.Values.appspace.charts.middleware.redis.domain}}
spring.redis.port={{.Values.appspace.charts.middleware.redis.port}}
spring.redis.password={{.Values.appspace.charts.middleware.redis.password}}
spring.redis.timeout=100ms
spring.redis.jedis.pool.max-wait=1000ms
spring.redis.jedis.pool.max-active=100
#debug=true
# 监控
#spring.boot.admin.url=http://localhost:8811

# 海外region控制联系信息写入
iam.contactinfo.write.to.contact.enabled=true
iam.contactinfo.write.to.user.enabled=false
iam.contactinfo.read.contact.enabled=true

#policy
limits.policies.per.domain={{.Values.appspace.charts.configVals.policy_domain_limits}}

iam.schedule.redis.host={{.Values.appspace.charts.middleware.redis.domain}}
iam.schedule.redis.port={{.Values.appspace.charts.middleware.redis.port}}
iam.schedule.redis.password={{.Values.appspace.charts.middleware.redis.password}}

# spring cloud
sc.webframework.ScLoggerConfiguration.enable=false
webframework.register.center.env.vip.enabled=false
webframework.register.center.tianlu.enabled=false
management.health.formula-discovery.enabled=false
spring.cloud.discovery.client.health-indicator.enabled=false


# ueba config

ueba.riskBehavior.page=http://{{.Values.appspace.charts.requires.nginxConsole.domain}}/iam/#/iam/ueba/list
ueba.riskEvent.notify.messages.templateId={{.Values.appspace.charts.configVals.uebaTemplateId}}

iam.flume.bootstrapServers={{.Values.appspace.charts.middleware.kafka.topic.domain}}:{{.Values.appspace.charts.middleware.kafka.topic.port}}
iam.flume.topic={{.Values.appspace.charts.configVals.proxyTopic}}
iam.flume.consumer.count={{.Values.appspace.charts.configVals.uebaConsumerCount}}

iam.service.username=proxy
iam.service.password={{.Values.appspace.charts.requires.iam.proxy.password}}

# vsAccount config
osp.service.policy.id=f2546218506a4183ab83f52a34ad5fe3
osp.service.id=4eb460d1a9fe4d81b5bea1324c2e5649
sts.accessKey.secret={{.Values.appspace.charts.requires.iam.proxy.sk}}

#================ disable actuator ================#
management.server.port={{.Values.appspace.charts.configVals.actuator_port}}

# security assessment
iam.bcia.mfa.baidusecurity.usbkey.cache.prefix=bce_iam_mfa_baidusecurity_usbkey_
iam.bcia.mfa.baidusecurity.usbkey.cache.ttl=300
security-assessment.enable={{.Values.appspace.charts.configVals.enable_security_assessment}}
