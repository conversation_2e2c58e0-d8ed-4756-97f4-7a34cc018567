appspace:
  charts:
    provides:
      iamManage:
        interface: iam-manage
        domain: iam-manage.agilecloud.com
        port: 8468
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      iam:
        interface: iam-nginx
        optional: true
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        sts:
          ak: '{{ sts_ak }}'
          sk: '{{ sts_sk }}'
          userId: '{{ sts_user_id }}'
          password: '{{ sts_password }}'
        proxy:
          ak: '{{ proxy_ak }}'
          sk: '{{ proxy_sk }}'
          userId: '{{ proxy_user_id }}'
          password: '{{ proxy_password }}'
      bceMessages:
        interface: bce-messages
        optional: true
        domain: '{{ bce_messages_domain }}'
        port: '{{ bce_messages_port }}'
      iamUeba:
        interface: iam-ueba
        optional: true
        domain: '{{ iam_ueba_domain }}'
        port: '{{ iam_ueba_port }}'
      nginxConsole:
        interface: nginx-console
        optional: true
        domain: '{{ nginx_console_domain }}'
        port: '{{ nginx_console_port }}'
      privateSkywalkingGrpc:
        interface: private-skywalking-grpc
        optional: true
        domain: '{{ private_skywalking_grpc_domain }}'
        port: '{{ private_skywalking_grpc_port }}'
    configVals:
      iam_encrypt_key: fimos87ejdusedke
      limits_users_per_domain: 1000
      keystone_cipher_service: risk;logic_bcc;logic_rds;logic_bbc;bmr;logic_rabbitmq;logical_eip;console_bmr;logic_drds;logic_scs;blb;logic_mongodb;cce;logic_volume;kafka;bec
      iam_trails_enabled: true
      iam_event_cache_enabled: true
      iam_event_trail_enabled: false
      iam_event_changeInfo_enabled: false
      use_token_database: true
      use_bce_iam_database: false
      datasource_testWhileIdle: true
      datasource_testOnBorrow: true
      datasource_testOnReturn: false
      policy_domain_limits: 10000
      proxyTopic: 0cd901398654410caeb745dc8d0c9957__bce_iam_core
      uebaConsumerCount: 5
      uebaTemplateId: Tpl_09cb1679-b024-4b92-8364-2220bd336d9f
      transaction_enabled: true
      actuator_port: -1
      loggingLevel: INFO
      enable_security_assessment: false
      gateway_host: localhost
      gateway_port: 8080
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      applb:
        enable: false
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
    securityContext: {}
    security:
      enable: false
    middleware:
      redis:
        clusterName: '{{ redis_cluster_name }}'
        domain: '{{ redis_domain }}'
        password: '{{ redis_password }}'
        version: 5
        port: 6379
      xdb:
        bce_iam:
          clusterName: '{{ bce_iam_cluster_name }}'
          user: '{{ bce_iam_user }}'
          password: '{{ bce_iam_password }}'
          domain: '{{ bce_iam_domain }}'
          port: 6203
          permission: write
        bce_iam_token:
          clusterName: '{{ bce_iam_token_cluster_name }}'
          user: '{{ bce_iam_token_user }}'
          password: '{{ bce_iam_token_password }}'
          domain: '{{ bce_iam_token_domain }}'
          port: 6203
          permission: write
      kafka:
        topic:
          clusterName: '{{ kafka_cluster_name }}'
          domain: '{{ kafka_domain }}'
          topicName: xxxx
          replication: 3
          partitions: 30
          port: 8959
    replica: 3
    namespace: console
    images:
      iam-manage:
        repository: abc-stack/iam-manage
        imageTag: ${iam-manage.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-manage
      command: []
      args: []
      log:
          - dirPaths:         # 仅清理，仅仅表示日志清理, 一般是业务自己会按天切分，目前日志匹配正则表达式列表，大家可以看看是不是能匹配到自己的日志
              - "/home/<USER>/manage/log/debug" # 日志清理配置一定是容器中日志所在文件的最后一级目录
              - "/home/<USER>/manage/log/access"
              - "/home/<USER>/manage/log/error"
            logType: local
            logVolume: iam-manage-log
            rule:
              duration: 3 #只保留三天的日志
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 30000m
        limitMemory: 10000Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-manage
        mountPath: /home/<USER>/manage/conf/application.properties
        subPath: application.properties
        name: iam-manage
      - type: CONFIG_MAP
        configName: iam-manage
        mountPath: /home/<USER>/manage/conf/iamConf.properties
        subPath: iamConf.properties
        name: iam-manage
      - type: CONFIG_MAP
        configName: iam-manage
        mountPath: /home/<USER>/manage/conf/kafka.properties
        subPath: kafka.properties
        name: iam-manage
      - type: CONFIG_MAP
        configName: iam-manage
        mountPath: /home/<USER>/manage/conf/endpoint.json
        subPath: endpoint.json
        name: iam-manage
      - type: CONFIG_MAP
        configName: iam-manage
        mountPath: /home/<USER>/manage/bin/skywalking-agent/config/agent.config
        subPath: agent.config
        name: iam-manage
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8468
          initialDelaySeconds: 15
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-manage-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/manage/log/
          storageType: local-path
          size: 80Gi
      ports:
      - name: 8468-tcp
        containerPort: 8468
        targetPort: 8468
        servicePort: 8468
        protocol: TCP
        nodePort: ''
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
