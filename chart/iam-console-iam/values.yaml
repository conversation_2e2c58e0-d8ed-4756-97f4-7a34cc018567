appspace:
  charts:
    provides:
      iamConsoleIam:
        interface: iam-console-iam
        domain: consoleiam.agilecloud.com
        port: 8100
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
      bceLogstash:
        interface: bce-logstash
        domain: bce-logstash.agilecloud.com
        port: 9600
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      nginxConsole:
        interface: nginx-console
        optional: true
        domain: '{{ nginx_console_domain }}'
        port: '{{ nginx_console_port }}'
      nginxLogin:
        interface: nginx-login
        optional: true
        domain: '{{ nginx_login_domain }}'
        port: '{{ nginx_login_port }}'
      iam:
        interface: iam-nginx
        optional: false
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        console_iam:
          ak: '{{ console_iam_ak }}'
          sk: '{{ console_iam_sk }}'
          userId: '{{ console_iam_user_id }}'
          password: '{{ console_iam_password }}'
        logic_bcc:
          ak: '{{ logic_bcc_ak }}'
          sk: '{{ logic_bcc_sk }}'
          userId: '{{ logic_bcc_user_id }}'
          password: '{{ logic_bcc_password }}'
      iamCertificate:
        interface: iam-certificate
        optional: false
        domain: '{{ iam_certificate_domain }}'
        port: '{{ iam_certificate_port }}'
      iamBcepass:
        interface: iam-bcepass
        optional: false
        domain: '{{ iam_bcepass_domain }}'
        port: '{{ iam_bcepass_port }}'
      iamRisk:
        interface: iam-risk
        optional: false
        domain: '{{ iam_risk_domain }}'
        port: '{{ iam_risk_port }}'
      taglogicInternal:
        interface: taglogic-internal
        optional: false
        domain: '{{ taglogic_internal_domain }}'
        port: '{{ taglogic_internal_port }}'
      billingOrder:
        interface: billing-order
        optional: false
        domain: '{{ billing_order_domain }}'
        port: '{{ billing_order_port }}'
      platUserConfig:
        interface: plat-user-config
        optional: false
        domain: '{{ plat_user_config_domain }}'
        port: '{{ plat_user_config_port }}'
      organizationlogicInternal:
        interface: organizationlogic-internal
        optional: false
        domain: '{{ organizationlogic_internal_domain }}'
        port: '{{ organizationlogic_internal_port }}'
      platServiceProduct:
        interface: plat-service-product
        optional: true
        domain: '{{ plat_service_product_domain }}'
        port: '{{ plat_service_product_port }}'
      blb:
        interface: blb-meta
        optional: true
        domain: '{{ blb_meta_domain }}'
        port: '{{ blb_meta_port }}'
      bceMessages:
        interface: bce-messages
        optional: false
        domain: '{{ bce_messages_domain }}'
        port: '{{ bce_messages_port }}'
      cloudTrail:
        interface: cloud-trail
        optional: true
        domain: '{{ cloud_trail_domain }}'
        port: '{{ cloud_trail_port }}'
      authcode:
        interface: bce-sms-api-authcode
        optional: true
        domain: '{{ bce_sms_api_authcode_domain }}'
        port: '{{ bce_sms_api_authcode_port }}'
      bcclogicInternal:
        interface: bcclogic-internal
        optional: true
        domain: '{{ bcclogic_internal_domain }}'
        port: '{{ bcclogic_internal_port }}'
      bpResourceManager:
        interface: bp-resource-manager
        optional: false
        domain: '{{ bp_resource_manager_domain }}'
        port: '{{ bp_resource_manager_port }}'
      fpFinanceV2:
        interface: fp-finance-v2
        optional: false
        domain: '{{ fp_finance_v2_domain }}'
        port: '{{ fp_finance_v2_port }}'
      bceOspAuth:
        interface: bce-osp-auth
        optional: true
        domain: '{{ bce_osp_auth_domain }}'
        port: '{{ bce_osp_auth_port }}'
      frontend:
        interface: frontend
        optional: true
        domain: '{{ frontend_domain }}'
        port: '{{ frontend_port }}'
      bceLogicalVpc:
        interface: bce-logical-vpc
        optional: true
        domain: '{{ bce_logical_vpc_domain }}'
        port: '{{ bce_logical_vpc_port }}'
      bosNginx:
        interface: bos-nginx
        optional: true
        domain: '{{ bos_nginx_domain }}'
        port: '{{ bos_nginx_port }}'
      privateSkywalkingGrpc:
        interface: private-skywalking-grpc
        optional: true
        domain: '{{ private_skywalking_grpc_domain }}'
        port: '{{ private_skywalking_grpc_port }}'
      resourceManager:
        interface: resource-manager
        optional: true
        domain: '{{ resource_manager_domain }}'
        port: '{{ resource_manager_port }}'
    configVals:
      cookie_domain: .agilecloud.com
      authcode_mock: true
      bos_accept_operation: WRITE;RestoreObject;RenameObject;READ;PutObjectVersionAcl;PutObjectTagging;PutObjectAcl;PutObject;PutNotification;PutCopyRightProtection;PutBucketVersioning;PutBucketTrash;PutBucketTagging;PutBucketStyle;PutBucketStorageClass;PutBucketStorageAnalysis;PutBucketStaticWebsite;PutBucketRequestPayment;PutBucketReplication;PutBucketQuota;PutBucketObjectLock;PutBucketMirroring;PutBucketLogging;PutBucketLifecycle;PutBucketInventory;PutBucketEncryption;PutBucketCors;PutBucketAcl;PutBucket;PutBucket;MODIFY;ListParts;ListObjectVersions;ListBuckets;ListBucket;LIST;GetObjectVersionAcl;GetObjectVersion;GetObjectTagging;GetObjectMeta;GetObjectAcl;GetObject;GetNotification;GetCopyRightProtection;GetBucketVersioning;GetBucketTrash;GetBucketTagging;GetBucketStyle;GetBucketStorageClass;GetBucketStorageAnalysis;GetBucketStaticWebsite;GetBucketRequestPayment;GetBucketReplication;GetBucketQuota;GetBucketObjectLock;GetBucketMirroring;GetBucketLogging;GetBucketLocation;GetBucketLifecycle;GetBucketInventory;GetBucketEncryption;GetBucketCors;GetBucketAcl;GetBucket;FULL_CONTROL;DeleteObjectVersion;DeleteObjectTagging;DeleteObject;DeleteObject;*
      account_cancel_serviceMeta: '[{"service":"BOS","endpointKey":"BOS_NGINX","listHttpMethod":"GET","listHttpPath":"/","listHttpParam":{},"listHttpPayload":{},"arrayLocation":"buckets"},{"service":"ENIC","endpointKey":"LOGICAL_VPC","listHttpMethod":"POST","listHttpPath":"/v1/api/logical/network/eni/list","listHttpPayload":{"keyword":"","keywordType":"name","pageNo":1,"pageSize":10},"listHttpParam":{},"arrayLocation":"result"},{"service":"DEDICATION_GATEWAY","endpointKey":"LOGICAL_VPC","listHttpMethod":"POST","listHttpPath":"/v1/api/logical/network/dc/gw/list","listHttpPayload":{"keyword":"","keywordType":"name","vpcId":"","pageNo":1,"pageSize":10},"listHttpParam":{},"arrayLocation":"result"}]'
      account_cancel_waitSeconds: 30
      account_cancel_useRawRequestId: false
      iam_policy_tag_products: CDN,CDS,DCC,BCC,EIP,SCS,RDS,BBC,BLB,MQ,MONGODB,BBC,BOS,LSS,NETWORK,IPVSIXGW,PEERCONN,SNIC,ENIC,LBDC,VPN,NAT
      iam_policy_resource_group_products: BOS
      usbKeyJarName: quickapi-client-java-1.0.8-SNAPSHOT-jar-with-dependencies.jar
      usbKeyAuthority: quickservice
      usbKeyCaCertPemPath: /home/<USER>/console-iam/usbkey/cacert.pem
      usbKeyClientKeyStorePath: /home/<USER>/console-iam/usbkey/client.pfx
      usbKeyServerEndpoint: ************:8443
      usbKeyCertId: afc61ce67e65468e89e40a95687077c9
      usbKeyRpcScriptName: ./usbKeyRpc.sh
      iam_policyTemplate_useShortId: true
      qax_csmp_endpoint: qax-csmp.agilecloud.com:443
      qax_csmp_endpoint_protocol: https
      jwt_expireSeconds: 60
      sso_path: '{"CSMP":"/api/cloud-login/auth"}'
      uebaWhiteList: 8aff25c03841414a80284c46df29369a,ead7bf75147c403e9cd82bac387363b7
      sliding_block_check: false
      login_protocol: http
      svs_verify_url: https://localhost:8080/SignServerForm/VerifySignedData
      svs_verify_level: 1
      svs_random_url: https://localhost:8080/SignServerForm/GenRandomData
      svs_timeout: 3000
      usb_key_randomNumberLength: 16
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
    securityContext: {}
    security:
      enable: false
    middleware:
      kafka:
        topic:
          clusterName: '{{ kafka_cluster_name }}'
          domain: '{{ kafka_domain }}'
          topicName: agilecloud-access-debug
          replication: 3
          partitions: 3
          port: 8959
    replica: 3
    namespace: console
    images:
      iam-console-iam:
        repository: abc-stack/iam-console-iam
        imageTag: ${iam-console-iam.image.tag}
      bce-logstash:
        repository: abc-stack/bce-logstash
        imageTag: ${bce-logstash.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-console-iam
      command: []
      args: []
      log:
        - dirPaths:
            - "/home/<USER>/console-iam/log/access"
            - "/home/<USER>/console-iam/log/access_debug"
            - "/home/<USER>/console-iam/log/debug"
            - "/home/<USER>/console-iam/log/error"
            - "/home/<USER>/console-iam/log/info"
            - "/home/<USER>/console-iam/log/warn"
          logType: local
          logVolume: iam-console-iam-log
          rule:
            duration: 3
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 4000m
        limitMemory: 2800Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-console-iam
        mountPath: /home/<USER>/console-iam/conf/endpoint.json
        subPath: endpoint.json
        name: iam-console-iam
      - type: CONFIG_MAP
        configName: iam-console-iam
        mountPath: /home/<USER>/console-iam/conf/application.properties
        subPath: application.properties
        name: iam-console-iam
      - type: CONFIG_MAP
        configName: iam-console-iam
        mountPath: /home/<USER>/console-iam/conf/globalConditions.json
        subPath: globalConditions.json
        name: iam-console-iam
      - type: CONFIG_MAP
        configName: iam-console-iam
        mountPath: /home/<USER>/console-iam/conf/policyDependency.json
        subPath: policyDependency.json
        name: iam-console-iam
      - type: CONFIG_MAP
        configName: iam-console-iam
        mountPath: /home/<USER>/console-iam/bin/skywalking-agent/config/agent.config
        subPath: agent.config
        name: iam-console-iam
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8100
          initialDelaySeconds: 225
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-console-iam-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/console-iam/log/
          storageType: local-path
          size: 30Gi
      ports:
      - name: 8100-tcp
        containerPort: 8100
        targetPort: 8100
        servicePort: 8100
        protocol: TCP
        nodePort: ''
    - name: bce-logstash
      command: []
      args: []
      log:
        - dirPaths:
            - /home/<USER>/logstash/logs
          logType: local
          logVolume: bce-logstash-log
          rule:
            duration: 7
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 2Gi
        ephemeralStorage: 1024Mi
        limitCPU: 2000m
        limitMemory: 4Gi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
        - type: CONFIG_MAP
          configName: iam-console-iam
          mountPath: /home/<USER>/logstash/config/logstash.conf.kafka
          subPath: logstash.conf.kafka
          name: iam-console-iam
        - type: CONFIG_MAP
          configName: iam-console-iam
          mountPath: /home/<USER>/logstash/config/logstash.yml
          subPath: logstash.yml
          name: iam-console-iam
        - type: CONFIG_MAP
          configName: iam-console-iam
          mountPath: /home/<USER>/logstash/config/profile_api.conf
          subPath: profile_api.conf
          name: iam-console-iam
        - type: CONFIG_MAP
          configName: iam-console-iam
          mountPath: /home/<USER>/logstash/bin/logstash_control.sh
          subPath: logstash_control.sh
          name: iam-console-iam
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 9600
          initialDelaySeconds: 5      # 表示容器启动后，等待多少时间之后再启动各类探针,默认10s
          periodSeconds: 30           # 表示探针执行检测的间隔时间，默认10s
          failureThreshold: 5         # 表示探针执行检测时，连续失败多少次，容器状态就会被确认不健康，默认3次
          successThreshold: 1         # 表示探针执行检测失败之后，如果容器状态想再次被标记为健康，至少需要经过多少次连续成功检测，默认1次
          timeoutSeconds: 5           # 表示探针执行检测的超时后的等待时间，1s
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: bce-logstash-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/logstash/logs/
          storageType: local-path
          size: 30Gi
        - name: iam-console-iam-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/console-iam/log/
          storageType: local-path
          size: 30Gi
      ports: []
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
