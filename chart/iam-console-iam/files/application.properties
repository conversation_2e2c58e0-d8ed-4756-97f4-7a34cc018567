spring.application.name=console-iam
server-host:{{ .Values.appspace.charts.requires.nginxConsole.domain}}
server.port={{.Values.appspace.charts.provides.iamConsoleIam.port}}
bce_plat_web_framework.sc.enabled=false
#=================== HTTP / HTTPS ===================#
{{if .Values.appspace.charts.global.AbcstackGlobalUseHttps }}
login.protocol:https
{{ else }}
login.protocol:{{.Values.appspace.charts.configVals.login_protocol}}
{{- end }}
#=================== Swagger portal ===================#
swagger.start:false
swagger.app.docs: ${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}:{{.Values.appspace.charts.provides.iamConsoleIam.port}}

portal.url:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}
error_page.404:/iam/404.html
#=================== message center ====================#
user.message_center.enabled:true
user.authcode.mock:{{.Values.appspace.charts.configVals.authcode_mock}}
user.authcode.messages.enabled=true
user.authcode.phone.message_template_id:ffc54482-3528-41f8-85d8-5ad44c9e53d7
user.authcode.email.message_template_id:8708ed1b-1178-4574-8da8-fa2f94acfb96
authcode.limit.ip:100
messages.category.readonly:01001,01002,03,04001,04002,04003,04004,04006
messages.category.readonly.set:{\"01001\": [\"EMAIL\", \"MOBILE\", \"APP\"], \
  \"01002\": [\"EMAIL\", \"MOBILE\", \"APP\"], \
  \"02006\": [\"EMAIL\", \"MOBILE\", \"APP\"], \
  \"02007\": [\"EMAIL\", \"MOBILE\", \"APP\"], \
  \"03\": [\"EMAIL\", \"MOBILE\", \"APP\"], \
  \"04002\": [\"EMAIL\", \"MOBILE\", \"APP\"], \
  \"04003\": [\"EMAIL\", \"MOBILE\", \"APP\"], \
  \"04004\": [\"EMAIL\", \"MOBILE\", \"APP\"], \
  \"04006\": [\"EMAIL\", \"MOBILE\", \"APP\"]}

#=================== login & access ====================#
login.url:${login.protocol}://{{ .Values.appspace.charts.requires.nginxLogin.domain}}
cookie.domain:{{.Values.appspace.charts.global.domain}}
login.cookie.md5.key:bcetest
login.urls.not.need.auth:/iam/index;/iam/asset/**;/iam/dep/**;/iam/esl.js;/api-docs;/api-docs/**;/iam/version.txt;\
	/api/iam/account/register;\
    /api/iam/user/invite/detail;\
    /api/iam/user/invite/validate;\
	/api/iam/account/checkNameDup/**;\
    /api/iam/authcode/reg/send;\
    /api/iam/org/confirm/**;\
    /api/iam/mail/track/**;/actuator;/actuator/**;/favicon.ico;/error;\
    /api/iam/n/v;\
    /api/iam/cipher/getpublickey;\
    /api/iam/authcode/password/forget/send;/api/iam/authcode/password/forget/verify;/api/iam/bcia/account/password/update

login.urls.need.auth:/**
passport.appid=1240
passport.session.endpoint:${login.protocol}://************:7801
uc.register.host:none
uc.secure.host:none
uc.app.id:285
uc.server:none
login.admittance.action.urls:\
  AccountActivate:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}/iam/#/iam/user/v2/activate~hideBar=1;\
  CollaboratorActivate:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}/iam/#/collaborator/baseinfo;\
  NeedResetPassword:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}/iam/#/iam/user/reset;\
  BindMobilePhone:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}/iam/#/collaborator/baseinfo;\
  BindMfaDevice:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}/iam/#/iam/user/v2/mfa/bind;\
  MFA:RISK_LEVEL:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}/iam/#/iam/user/v2/verify;\
  MFA:LOGIN:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}/iam/#/iam/user/v2/verify/login;\
  Notice:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}/iam/#/iam/user/v2/verify/login/hint


iam.resolve.policy.detail:false
iam.access.failed.jump.url:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}/iam/access?redirect={referredUrl}
iam.console.username=console_iam
iam.console.password={{ .Values.appspace.charts.requires.iam.console_iam.password}}
iam.console.accesskey={{ .Values.appspace.charts.requires.iam.console_iam.ak}}
iam.console.secret={{ .Values.appspace.charts.requires.iam.console_iam.sk}}
iam.access.paths:/**
iam.access.exclude.paths:\
  /iam;\
  /iam/;\
  /user/access;\
  /iam/access;\
  /api/iam/authcode/**;\
  /api/iam/auth_info;\
  /api/iam/basicinfo/**;\
  /api-docs;/api-docs/**;\
  /api/iam/collaborator/baseinfo;\
  /api/iam/user/invite/detail;\
  /api/iam/user/invite/validate;\
  /api/iam/agent/info;\
  /api/iam/user/binding/detail;\
  /api/iam/account/activate;\
  /api/iam/user/changePassword;\
  /api/iam/user/mfa/bind;\
  /api/iam/org/confirm/**;\
  /qa/**;\
  /api/region/available;\
  /api/region/get;\
  /api/iam/account/register;\
  /api/iam/account/checkNameDup/**;\
  /api/system/constants/**;\
  /api/iam/account/whitelist/status;\
  /api/iam/n/v;\
  /api/iam/cipher/getpublickey;\
  /api/iam/account/notice/confirm;\
  /api/iam/account/mfa/detail;\
  /api/iam/user/mfa/devices/status

iam.csrf.paths:/api/**
iam.csrf.exclude.paths:\
  /api/iam/basicinfo/**;\
  /api/iam/authcode/**;\
  /api/iam/user/invite/detail;\
  /api/iam/user/invite/validate;\
  /api/iam/auth_info;\
  /api/iam/checkCode;\
  /api/iam/logout**;\
  /api-docs;/api-docs/**;\
  /api/iam/collaborator/baseinfo;\
  /api/iam/user/binding/detail;\
  /api/iam/account/activate;\
  /api/iam/user/changePassword;\
  /api/iam/account/checkNameDup/**;\
  /api/iam/account/register;\
  /qa/**;\
  /api/iam/cipher/getpublickey;\
  /api/iam/authcode/password/forget/send;/api/iam/authcode/password/forget/verify;/api/iam/bcia/account/password/update

iam.accountinfo.source:iam
iam.account.fast_create_type:BCIA
organization.realname.validate:false
iam.mfa.need:true
iam.csrf.is.need.check.valid=true
iam.permission.is.need.check.valid=false
user.authcode.phone.template_id:smsTpl:516023ef-61f2-4758-a7fc-3694f4b231e2
user.authcode.mail.from:<EMAIL>
user.activate.phone.templateId=smsTpl:6c7b2e37-e1ec-4369-8d61-4ed411b71203

iam.access.use_account_api:true
iam.qahelper:false
login.cookie.expires.time=60

iam.user.invitation.url:${login.protocol}://{{ .Values.appspace.charts.requires.nginxLogin.domain}}/invite.html

#======================================================#

#================ authZ ================#

policy.accept.regions={{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}};{{- end }}{{ $region.region}}{{- end }};global
policy.accept.service: BAEPRO;BCC;BOS;IOT;TSDB
bos.accept.operation:{{.Values.appspace.charts.configVals.bos_accept_operation}}
policy.accept.operation: OPERATE;READ;ADMIN;AppRead*;AppCode*;AppCreate;AppDelete;EnvRead*;\
                         EnvUpdateStatus*;EnvUpdateDeploy;EnvSetting*;EnvMonitor*;EnvLog*;\
                         IotHubManage;IotHubRead;\
                         Database/Read/*;Database/Write/*;Datapoint/Read/*;Datapoint/Write/*
policy.bcc.regions={{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}{{ $region.region}}{{- end }}
iam.policy.template.cache.seconds:3600
iam.policy.iothub.list.use.template:true

#================ passport modify ================#
passport.passgateEndpoint:none
passport.app.username:none
passport.app.password:none 

#=============== uuap ===========================#
uuap.uic.endpoint:none
uuap.uic.appkey:none

#=================== logging config ===================#
logging.requestId_urlPattern:/iam/;/iam/access;/api/*
logging.has_console_appender: true
logging.access.maxLength: 16384

logging.has_web_debug_appender: true
logging.web_debug_path: /iam/debug
logging.web_debug.level: INFO

### logback rolling log, uncomment to open appender ###
logging.info_log_file_path:  ../log/info/bce-console-iam.info.log
logging.error_log_file_path: ../log/error/bce-console-iam.error.log
logging.warn_log_file_path:  ../log/warn/bce-console-iam.warn.log
logging.debug_log_file_path: ../log/debug/bce-console-iam.debug.log

### access log, uncomment to open appender ###
logging.access_debug_uri_prefix:/api
logging.access_log_file_path: ../log/access/bce-console-iam.access.log
logging.access_debug_log_file_path: ../log/access_debug/bce-console-iam.access_debug.log
logging.access.desensitize_expression: `"(receiver|account|mobile|phone|mobilePhone|accountManagerPhone)"\\\s*:\\\s*"(\\\+?\\\d*)(\\\d{3})\\\d{6}(\\\d{2})"`:`"$1":"$2$3******$4"`;\
                                    `"(email|target|account|inviterEmail|receiver|accountManagerEmail)"\\\s*:\\\s*"([^"]*)...@..([^"]*)"`:`"$1":"$2***@**$3"`;\
                                    `"(password|original_password)"\\\s*:\\\s*"[^"]*?"`:`"$1":"*****"`;\
                                    `"(secret)"\\\s*:\\\s*"(..)[^"]*?(..)"`:`"$1":"$2*****$3"`;
logging.log.desensitize_expression: `"(receiver|account|mobile|phone|mobilePhone|accountManagerPhone)"\\\s*:\\\s*"(\\\+?\\\d*)(\\\d{3})\\\d{6}(\\\d{2})"`:`"$1":"$2$3******$4"`;\
                                    `"(email|account|target|inviterEmail|receiver|accountManagerEmail)"\\\s*:\\\s*"([^"]*)...@..([^"]*)"`:`"$1":"$2***@**$3"`;\
                                    `(mobilePhone)='(\\\d{3})\\\d{6}(\\\d{2})'`:`$1='$2******$3'`;\
                                    `(email)='([^']*)...@..([^']*)'`:`$1='$2***@**$3'`;\
                                    `"(password|original_password)"\\\s*:\\\s*"[^"]*?"`:`"$1":"*****"`;\
                                    `"(secret)"\\\s*:\\\s*"(..)[^"]*?(..)"`:`"$1":"$2*****$3"`;

monitor.latencyRecorder.enable:true
#======================================================#

finance.account.type.available:100
finance.account.type.freeze:101

iam.access.not_invited_jump_url:${login.protocol}://{{ .Values.appspace.charts.requires.nginxConsole.domain}}/iam/#/iam/user/v2/activate~hideBar=1

#=================== trail config ===================#
trail.event.enable:false
trail.event.table_name:event
trail.event.agent_data_dir:../../../tidedb/agent/bce-console/

endpoint.default.regionName:{{.Values.appspace.charts.platformConfigVals.region}}

https.auto_redirect: false
https.hosts: {{ .Values.appspace.charts.requires.nginxConsole.domain}}

#=================== osp sts ===================#
osp.service.policy.id=01ed6ba5cd7d40778b82a63a2d47688f
osp.service.id=368d0723f0e547858881830a81c61c0e
osp.sts.role.name=BceServiceRole_OSP

developer.endpoint=http://nouse:nouse

#=================== kafka ===================#
kafka.config=file:../conf/kafka.properties
iam.service.id=ef76c3e22b164ded96056310eb64829b
product.support.operate.scp.list=BBC;BCC;BLB;BOS;CDN;DCC;EIP;EIPGROUP;HOSTEYE;NAT;NETWORK;RDS;VPN;ET;PEERCONN;EIP_BP;IPVSIXGW;CDS;IDS;CFS;BTS;MONGODB;SCS;BES;PALO;CCE;BSC;BMR;KAFKA;BLS;ACA;LD
scp.permission.kafka.operate.list:TopicReadOper;CertificateReadOper;AlterTopic;AddTopicPartition;UpdateTopicCertificate;\
                                  AlterCertificate;RebuildCertificate;UpdateCertificateTopic;ConsumerGroupReadOper;\
                                  DeleteConsumerGroupCert;AddConsumerGroupCert
scp.billing=FinanceCenterScp;FCFullAccessScp;FCOrderAccessScp;FCReadAccessScp;FCReadDenyScp
scp.all.service.list=FullAccessScp;OperateScpForCMCC;CommonModuleFullAccessScp
static.cdn.endpoint:${login.protocol}://static{{.Values.appspace.charts.global.domain}}
iam.policy.resource.group.products:{{.Values.appspace.charts.configVals.iam_policy_resource_group_products}}
iam.policy.tag.products:{{.Values.appspace.charts.configVals.iam_policy_tag_products}}
#================== notify migrate ===========#
iam.notifyparty.readonly:false
iam.notifyparty.new.interface.available:true
iam.notifyparty.double.write:false

#=============== csrf referer ===============#
vcr.bos.auth.header.referer.domain:{{.Values.appspace.charts.requires.nginxConsole.domain}}
vca.bos.auth.header.referer.domain:{{.Values.appspace.charts.requires.nginxConsole.domain}}
bos.auth.header.referer.domain:{{.Values.appspace.charts.global.domain}}

reg.send.max.qps:20


#=============== bcepolicy ===============#
iam.global.system.policy.names:GlobalOperatePolicy;GlobalReadPolicy;FCOrderAccessPolicy;FCFullControlPolicy;FCReadAccessPolicy
iam.policy.system.cache.seconds:600

#=============== notify party ===============#
iam.notifyparty.invitation.email.templateId:Tpl_20b2a00a-98d8-4002-88d2-f648af55a884
iam.notifyparty.invitation.mobile.templateId:Tpl_6e2e906c-94a7-48ea-8024-8d54b0111d9a
iam.notifyparty.invitation.url:${login.protocol}://{{.Values.appspace.charts.requires.nginxLogin.domain}}/api/iam/n/v
iam.notifyparty.mobile.invitation.url:${login.protocol}://{{.Values.appspace.charts.requires.nginxLogin.domain}}/api/iam/n/v
iam.notifyparty.remove.email.templateId:Tpl_908b452b-e093-4b14-a2fc-351ed6540e29
iam.notifyparty.remove.mobile.templateId:Tpl_0fc25b6f-6e58-440b-964a-2f3954e02c4d
iam.notifyparty.invitation.success.url:${login.protocol}://{{.Values.appspace.charts.requires.nginxLogin.domain}}/message/success.html?name=
iam.notifyparty.invitation.failed.url:${login.protocol}://{{.Values.appspace.charts.requires.nginxLogin.domain}}/message/fail.html
iam.notify.user.verify:true

#============== group =================#
group.attach.policy.key.accounts:none

iam.update.overseas.flag=true
iam.console.token.refresh.before.expires.in.minute=15
organization.account.confirm.secret:none
organization.account.confirm.redirect:none

#============= account register approval======#
account.register.auto.active=false

#===全局mfa相关配置===#
iam.global.mfa.quota.condition=globalMfaConfig
iam.global.mfa.quota.id=e5e76f2d0e2aa16894dc8a8bddbef750

#==主账号注销后，重定向地址===#
redirectUrl:${login.protocol}://{{.Values.appspace.charts.requires.nginxLogin.domain}}/login?bcia

#==账号注册后，发送邮件通知管理员审批===#
account.notify.manager.mail.enable:true
account.notify.manager.email.message_template_id:Tpl_bf289941-ee85-4109-9c43-43bf0ff8c5a2

#==主账号注销时 获取非 billing 资源用到的元数据===#
account.cancel.waitSeconds:{{.Values.appspace.charts.configVals.account_cancel_waitSeconds}}
account.cancel.serviceMeta:{{.Values.appspace.charts.configVals.account_cancel_serviceMeta}}
account.cancel.useRawRequestId:{{.Values.appspace.charts.configVals.account_cancel_useRawRequestId}}

# sts assume instance role
sts.role.grantee.support.service={BCC:'{{.Values.appspace.charts.requires.iam.logic_bcc.userId}}'}

# usbKey config
usbKeyJarName:{{.Values.appspace.charts.configVals.usbKeyJarName}}
usbKeyAuthority:{{.Values.appspace.charts.configVals.usbKeyAuthority}}
usbKeyCaCertPemPath:{{.Values.appspace.charts.configVals.usbKeyCaCertPemPath}}
usbKeyClientKeyStorePath:{{.Values.appspace.charts.configVals.usbKeyClientKeyStorePath}}
usbKeyServerEndpoint:{{.Values.appspace.charts.configVals.usbKeyServerEndpoint}}
usbKeyCertId:{{.Values.appspace.charts.configVals.usbKeyCertId}}
usbKeyRpcScriptName:{{.Values.appspace.charts.configVals.usbKeyRpcScriptName}}

# bcc policyTemplate use shortId, in 3.2.X
iam.policyTemplate.useShortId:{{.Values.appspace.charts.configVals.iam_policyTemplate_useShortId}}

# csmp jwt sso
jwt.signKey:{{.Values.appspace.charts.requires.iam.console_iam.password}}
jwt.expireSeconds:{{.Values.appspace.charts.configVals.jwt_expireSeconds}}
ssoPath:{{.Values.appspace.charts.configVals.sso_path}}

# close spring cloud and compass
compass.use.close=true
sc.webframework.ScLoggerConfiguration.enable=false
webframework.register.center.env.vip.enabled=false
webframework.register.center.tianlu.enabled=false
management.health.formula-discovery.enabled=false
spring.cloud.discovery.client.health-indicator.enabled=false

# ueba accountId whitelist
riskBehavior.whitelist.accounts={{.Values.appspace.charts.configVals.uebaWhiteList}}
#sliding block check
sliding.block.need.check={{.Values.appspace.charts.configVals.sliding_block_check}}
# check referer
login.referer.pattern=^https?://{{.Values.appspace.charts.requires.nginxLogin.domain}}.*

# security assessment
baidusecurity.svs.verify.url={{.Values.appspace.charts.configVals.svs_verify_url}}
baidusecurity.svs.verify.level={{.Values.appspace.charts.configVals.svs_verify_level}}
baidusecurity.svs.random.url={{.Values.appspace.charts.configVals.svs_random_url}}
baidusecurity.svs.random.len={{.Values.appspace.charts.configVals.usb_key_randomNumberLength}}
baidusecurity.svs.timeout={{.Values.appspace.charts.configVals.svs_timeout}}
