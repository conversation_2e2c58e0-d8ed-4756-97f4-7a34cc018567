{"keys": [{"name": "time", "description": "发起调用的时间", "type": "Date"}, {"name": "ip<PERSON><PERSON><PERSON>", "description": "发起调用的IP地址", "type": "IP"}, {"name": "referer", "description": "发起调用的访问来源", "type": "String"}, {"name": "sourceVpc", "description": "发起调用的访问来源VPC", "type": "String"}, {"name": "secureTransport", "description": "调用是否使用HTTPS/SSL协议", "type": "Bool"}], "types": [{"name": "Date", "operators": ["GreaterThan", "<PERSON><PERSON><PERSON>"]}, {"name": "IP", "operators": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"name": "String", "operators": ["StringEquals", "StringLike"]}, {"name": "Bool", "operators": ["Boolean"]}]}