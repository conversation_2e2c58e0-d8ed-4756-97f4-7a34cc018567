# Sample Logstash configuration for creating a simple
# Beats -> Logstash -> Elasticsearch pipeline.

input {
  file {
    path => "/home/<USER>/console-iam/log/access_debug/bce-console-iam.access_debug.log"
    codec => multiline {
      pattern => "^((\d+\.\d+\.\d+\.\d+)|-)\s+((\d+\.\d+\.\d+\.\d+)|-).*\[\d{2}"
      negate => true
      what => "previous"
      auto_flush_interval => 1
    }
    add_field => { "agentType" => "bce-private-cloud_logstash" }
  }
}
filter {
  grok {
    match => { "message" => "======>\n%{WORD:method} %{NOTSPACE:request}" }
  }
  ruby {
    init => "
      require 'set'
      @@urlList = File.readlines('/home/<USER>/logstash/config/profile_api.conf').map(&:chomp)
      @@api_set = Set.new
      @@urlList.each do |api|
        pattern = api.gsub(/{.*?}/, '[^/]+').gsub('-', '\\-')   # 1）将占位符 {.*?} 替换为正则表达式[^/]+，匹配一个或多个非/字符；2）转义中划线
        @@api_set.add(Regexp.new('^' + pattern + '$'))
      end
      logger.info('api_set elements: ' + @@api_set.to_a.map(&:inspect).join(', '))
    "
    code => "
      method = event.get('method')
      request = event.get('request').split('?')[0].chomp('/')  # 去掉?及后面的内容，之后如果结尾有'/'，也一并去除
      target_api = method + ' ' + request
      match = @@api_set.any? { |regex| regex.match?(target_api) }
      event.set('match', match)
    "
  }
}
output {
  if [match] {
    kafka {
      codec => json
      topic_id => "agilecloud-access-debug"
      bootstrap_servers => "{{.Values.appspace.charts.middleware.kafka.topic.domain}}:{{.Values.appspace.charts.middleware.kafka.topic.port}}"
    }
  }
}
