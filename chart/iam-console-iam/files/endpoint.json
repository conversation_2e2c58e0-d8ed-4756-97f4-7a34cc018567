{
  "regions": [
    {
      "region": "default",
      "services": [
        {
          "service": "IAM",
          {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
          "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
        },
        {
          "service": "CSMP",
          "endpoint": "{{.Values.appspace.charts.configVals.qax_csmp_endpoint_protocol}}://{{.Values.appspace.charts.configVals.qax_csmp_endpoint}}"
        },
        {
          "service": "BpResourceManager",
          "endpoint": "http://{{ .Values.appspace.charts.requires.bpResourceManager.domain}}:{{ .Values.appspace.charts.requires.bpResourceManager.port}}"
        },
        {
          "service": "FinanceV3",
          "endpoint": "http://{{ .Values.appspace.charts.requires.fpFinanceV2.domain}}:{{ .Values.appspace.charts.requires.fpFinanceV2.port}}"
        },
        {
          "service": "OrderV2",
          "endpoint": "http://{{ .Values.appspace.charts.requires.billingOrder.domain}}:{{ .Values.appspace.charts.requires.billingOrder.port}}"
        },
        {
          "service": "IAM_RISK",
          "endpoint": "http://{{ .Values.appspace.charts.requires.iamRisk.domain}}:{{ .Values.appspace.charts.requires.iamRisk.port}}/v1"
        },
        {
          "service": "Certificate",
          "endpoint": "http://{{ .Values.appspace.charts.requires.iamCertificate.domain}}:{{ .Values.appspace.charts.requires.iamCertificate.port}}/v1"
        },
        {
          "service": "Cert",
          "endpoint": "http://{{ .Values.appspace.charts.requires.iamCertificate.domain}}:{{ .Values.appspace.charts.requires.iamCertificate.port}}/v1"
        },
        {
          "service": "Messages",
          "endpoint": "http://{{ .Values.appspace.charts.requires.bceMessages.domain}}:{{ .Values.appspace.charts.requires.bceMessages.port}}/v1"
        },
        {
          "service": "BcePass",
          "endpoint": "http://{{ .Values.appspace.charts.requires.iamBcepass.domain}}:{{ .Values.appspace.charts.requires.iamBcepass.port}}/v4"
        },
        {
          "service": "LogicalTag",
          "endpoint": "http://{{ .Values.appspace.charts.requires.taglogicInternal.domain}}:{{ .Values.appspace.charts.requires.taglogicInternal.port}}"
        },
        {
          "service": "Order",
          "endpoint": "http://{{ .Values.appspace.charts.requires.billingOrder.domain}}:{{ .Values.appspace.charts.requires.billingOrder.port}}/orders"
        },
        {
          "service": "Resource",
          "endpoint": "http://{{ .Values.appspace.charts.requires.billingOrder.domain}}:{{ .Values.appspace.charts.requires.billingOrder.port}}/resources"
        },
        {
          "service": "BLB",
          {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
          "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.blb.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.blb.port }}/json-api/v1"
        },
        {
          "service": "UserSettings",
          "endpoint": "http://{{ .Values.appspace.charts.requires.platUserConfig.domain}}:{{ .Values.appspace.charts.requires.platUserConfig.port}}/v1"
        },
        {
          "service": "product-center",
          "endpoint": "http://{{ .Values.appspace.charts.requires.platServiceProduct.domain}}:{{ .Values.appspace.charts.requires.platServiceProduct.port}}"
        },
        {
          "service": "LOGICAL_BCC",
          {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
          "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bcclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bcclogicInternal.port }}"
        },
        {
          "service": "NotifyParty",
          {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
          "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
        },
        {
          "service": "AuthCode",
          "endpoint": "http://{{ .Values.appspace.charts.requires.authcode.domain}}:{{ .Values.appspace.charts.requires.authcode.port}}/v1"
        },
        {
          "service": "cloud-trail",
          "endpoint": "http://{{ .Values.appspace.charts.requires.cloudTrail.domain}}:{{ .Values.appspace.charts.requires.cloudTrail.port}}/v1"
        },
        {
          "service": "IamRisk",
          "endpoint": "http://{{ .Values.appspace.charts.requires.iamRisk.domain}}:{{ .Values.appspace.charts.requires.iamRisk.port}}/v1"
        },
        {
          "service": "OspAuth",
          "endpoint": "http://{{ .Values.appspace.charts.requires.bceOspAuth.domain}}:{{ .Values.appspace.charts.requires.bceOspAuth.port}}"
        },
        {
          "service": "Frontend",
          "endpoint": "http://{{ .Values.appspace.charts.requires.frontend.domain}}:{{ .Values.appspace.charts.requires.frontend.port}}"
        },
        {
          "service": "BOS_NGINX",
          {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
          "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bosNginx.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bosNginx.port }}"
        },
        {
          "service": "LOGICAL_VPC",
          {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
          "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceLogicalVpc.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceLogicalVpc.port }}"
        },
        {
          "service": "ResourceManager",
          "endpoint": "http://{{ .Values.appspace.charts.requires.resourceManager.domain}}:{{ .Values.appspace.charts.requires.resourceManager.port}}"
        }
      ]
    },
    {
      "region": "bj",
      "services": [
        {
          "service": "IAM",
        {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
        "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
        },
        {
          "service": "CSMP",
          "endpoint": "{{.Values.appspace.charts.configVals.qax_csmp_endpoint_protocol}}://{{.Values.appspace.charts.configVals.qax_csmp_endpoint}}"
        },
        {
          "service": "BpResourceManager",
          "endpoint": "http://{{ .Values.appspace.charts.requires.bpResourceManager.domain}}:{{ .Values.appspace.charts.requires.bpResourceManager.port}}"
        },
        {
          "service": "FinanceV3",
          "endpoint": "http://{{ .Values.appspace.charts.requires.fpFinanceV2.domain}}:{{ .Values.appspace.charts.requires.fpFinanceV2.port}}"
        },
        {
          "service": "OrderV2",
          "endpoint": "http://{{ .Values.appspace.charts.requires.billingOrder.domain}}:{{ .Values.appspace.charts.requires.billingOrder.port}}"
        },
        {
          "service": "IAM_RISK",
          "endpoint": "http://{{ .Values.appspace.charts.requires.iamRisk.domain}}:{{ .Values.appspace.charts.requires.iamRisk.port}}/v1"
        },
        {
          "service": "Certificate",
          "endpoint": "http://{{ .Values.appspace.charts.requires.iamCertificate.domain}}:{{ .Values.appspace.charts.requires.iamCertificate.port}}/v1"
        },
        {
          "service": "Cert",
          "endpoint": "http://{{ .Values.appspace.charts.requires.iamCertificate.domain}}:{{ .Values.appspace.charts.requires.iamCertificate.port}}/v1"
        },
        {
          "service": "Messages",
          "endpoint": "http://{{ .Values.appspace.charts.requires.bceMessages.domain}}:{{ .Values.appspace.charts.requires.bceMessages.port}}/v1"
        },
        {
          "service": "BcePass",
          "endpoint": "http://{{ .Values.appspace.charts.requires.iamBcepass.domain}}:{{ .Values.appspace.charts.requires.iamBcepass.port}}/v4"
        },
        {
          "service": "LogicalTag",
          "endpoint": "http://{{ .Values.appspace.charts.requires.taglogicInternal.domain}}:{{ .Values.appspace.charts.requires.taglogicInternal.port}}"
        },
        {
          "service": "Order",
          "endpoint": "http://{{ .Values.appspace.charts.requires.billingOrder.domain}}:{{ .Values.appspace.charts.requires.billingOrder.port}}/orders"
        },
        {
          "service": "Resource",
          "endpoint": "http://{{ .Values.appspace.charts.requires.billingOrder.domain}}:{{ .Values.appspace.charts.requires.billingOrder.port}}/resources"
        },
        {
          "service": "BLB",
        {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
        "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.blb.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.blb.port }}/json-api/v1"
        },
        {
          "service": "UserSettings",
          "endpoint": "http://{{ .Values.appspace.charts.requires.platUserConfig.domain}}:{{ .Values.appspace.charts.requires.platUserConfig.port}}/v1"
        },
        {
          "service": "product-center",
          "endpoint": "http://{{ .Values.appspace.charts.requires.platServiceProduct.domain}}:{{ .Values.appspace.charts.requires.platServiceProduct.port}}"
        },
        {
          "service": "LOGICAL_BCC",
        {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
        "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bcclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bcclogicInternal.port }}"
        },
        {
          "service": "NotifyParty",
        {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
        "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
        },
        {
          "service": "AuthCode",
          "endpoint": "http://{{ .Values.appspace.charts.requires.authcode.domain}}:{{ .Values.appspace.charts.requires.authcode.port}}/v1"
        },
        {
          "service": "cloud-trail",
          "endpoint": "http://{{ .Values.appspace.charts.requires.cloudTrail.domain}}:{{ .Values.appspace.charts.requires.cloudTrail.port}}/v1"
        },
        {
          "service": "IamRisk",
          "endpoint": "http://{{ .Values.appspace.charts.requires.iamRisk.domain}}:{{ .Values.appspace.charts.requires.iamRisk.port}}/v1"
        },
        {
          "service": "OspAuth",
          "endpoint": "http://{{ .Values.appspace.charts.requires.bceOspAuth.domain}}:{{ .Values.appspace.charts.requires.bceOspAuth.port}}"
        },
        {
          "service": "Frontend",
          "endpoint": "http://{{ .Values.appspace.charts.requires.frontend.domain}}:{{ .Values.appspace.charts.requires.frontend.port}}"
        },
        {
          "service": "BOS_NGINX",
        {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
        "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bosNginx.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bosNginx.port }}"
        },
        {
          "service": "LOGICAL_VPC",
        {{- $curRegion := .Values.appspace.charts.platformConfigVals.region }}
        "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceLogicalVpc.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceLogicalVpc.port }}"
        },
        {
          "service": "ResourceManager",
          "endpoint": "http://{{ .Values.appspace.charts.requires.resourceManager.domain}}:{{ .Values.appspace.charts.requires.resourceManager.port}}"
        }
      ]
    },
    {{- range $i, $region := $.Values.appspace.charts.global.regions }}
    {{- if $i -}}
    ,
    {{- end }}
    {{- $curRegion :=$region.region }}
    {
      "region": "{{ $region.region}}",
      "services": [
      {
        "service": "IAM",
        "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
      },
      {
        "service": "CSMP",
        "endpoint": "{{ $.Values.appspace.charts.configVals.qax_csmp_endpoint_protocol}}://{{ $.Values.appspace.charts.configVals.qax_csmp_endpoint}}"
      },
      {
        "service": "BpResourceManager",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.bpResourceManager.domain}}:{{ $.Values.appspace.charts.requires.bpResourceManager.port}}"
      },
      {
        "service": "FinanceV3",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.fpFinanceV2.domain}}:{{ $.Values.appspace.charts.requires.fpFinanceV2.port}}"
      },
      {
        "service": "OrderV2",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.billingOrder.domain}}:{{ $.Values.appspace.charts.requires.billingOrder.port}}"
      },
      {
        "service": "IAM_RISK",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.iamRisk.domain}}:{{ $.Values.appspace.charts.requires.iamRisk.port}}/v1"
      },
      {
        "service": "Certificate",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.iamCertificate.domain}}:{{ $.Values.appspace.charts.requires.iamCertificate.port}}/v1"
      },
      {
        "service": "Cert",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.iamCertificate.domain}}:{{ $.Values.appspace.charts.requires.iamCertificate.port}}/v1"
      },
      {
        "service": "Messages",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.bceMessages.domain}}:{{ $.Values.appspace.charts.requires.bceMessages.port}}/v1"
      },
      {
        "service": "BcePass",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.iamBcepass.domain}}:{{ $.Values.appspace.charts.requires.iamBcepass.port}}/v4"
      },
      {
        "service": "LogicalTag",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.taglogicInternal.domain}}:{{ $.Values.appspace.charts.requires.taglogicInternal.port}}"
      },
      {
        "service": "Order",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.billingOrder.domain}}:{{ $.Values.appspace.charts.requires.billingOrder.port}}/orders"
      },
      {
        "service": "Resource",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.billingOrder.domain}}:{{ $.Values.appspace.charts.requires.billingOrder.port}}/resources"
      },
      {
        "service": "BLB",
      "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.blb.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.blb.port }}/json-api/v1"
      },
      {
        "service": "UserSettings",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.platUserConfig.domain}}:{{ $.Values.appspace.charts.requires.platUserConfig.port}}/v1"
      },
      {
        "service": "product-center",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.platServiceProduct.domain}}:{{ $.Values.appspace.charts.requires.platServiceProduct.port}}"
      },
      {
        "service": "LOGICAL_BCC",
      "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bcclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bcclogicInternal.port }}"
      },
      {
        "service": "LogicalProject",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.taglogicInternal.domain}}:{{ $.Values.appspace.charts.requires.taglogicInternal.port}}"
      },
      {
        "service": "NotifyParty",
      "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3"
      },
      {
        "service": "AuthCode",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.authcode.domain}}:{{ $.Values.appspace.charts.requires.authcode.port}}/v1"
      },
      {
        "service": "cloud-trail",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.cloudTrail.domain}}:{{ $.Values.appspace.charts.requires.cloudTrail.port}}/v1"
      },
      {
        "service": "IamRisk",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.iamRisk.domain}}:{{ $.Values.appspace.charts.requires.iamRisk.port}}/v1"
      },
      {
        "service": "OspAuth",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.bceOspAuth.domain}}:{{ $.Values.appspace.charts.requires.bceOspAuth.port}}"
      },
      {
        "service": "Frontend",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.frontend.domain}}:{{ $.Values.appspace.charts.requires.frontend.port}}"
      },
      {
        "service": "BOS_NGINX",
      "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bosNginx.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bosNginx.port }}"
      },
      {
        "service": "LOGICAL_VPC",
      "endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceLogicalVpc.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceLogicalVpc.port }}"
      },
      {
        "service": "ResourceManager",
        "endpoint": "http://{{ $.Values.appspace.charts.requires.resourceManager.domain}}:{{ $.Values.appspace.charts.requires.resourceManager.port}}"
      }
      ]
    }
    {{- end }}

  ]
}
