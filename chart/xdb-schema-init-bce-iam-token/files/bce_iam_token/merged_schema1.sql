--
-- Table structure for table `token`
--
CREATE TABLE `token`
(
    `id`       varchar(64) NOT NULL,
    `expires`  datetime    DEFAULT NULL,
    `extra`    text,
    `valid`    tinyint(1)  NOT NULL,
    `trust_id` varchar(64) DEFAULT NULL,
    `user_id`  varchar(64) DEFAULT NULL,
    PRIMARY KEY (`id`),
    <PERSON><PERSON>Y `ix_token_expires` (`expires`),
    KEY `ix_token_expires_valid` (`expires`, `valid`),
    KEY `idx_user_id` (`user_id`, `valid`, `expires`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;