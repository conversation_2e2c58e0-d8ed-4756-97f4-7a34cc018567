#全局参数 
generator=skeema:1.11.2-community 
# 下面是针对大表ddl变更的配置
alter-wrapper="/usr/bin/pt-online-schema-change --execute --alter {CLAUSES} D={SCHEMA},t={TABLE},h={HOST},P={PORT},u={USER},p={PASSWORDX}"
alter-wrapper-min-size=500M    # use pt-osc if table 500 MB
alter-algorithm=inplace         # otherwise use online DDL
alter-lock=none
ignore-table=^(flyway_schema_history|atlas_schema_revisions|.*_new|.*_old|_.*_gh[oc])$  # ignore pt-osc shadow tables
# 下面参数是因为业务使用不规范，为了让业务能成功灌入 特此放开的 更多参数参考：https://www.skeema.io/docs/options/#allow-charset
# allow-charset="latin1,utf8mb4,utf8"     # utf8 为单独加白名单放开
# allow-auto-inc="int unsigned, bigint unsigned, int, bigint"    # int， bigint 为单独加白名单放开
# lint-zero-date="ignore"   # 忽略强制检查，默认是时间为
# lint-dupe-index="ignore"
# 下列参数有业务维护,业务内的优先级高于全集，例如忽略表参数如果有新增tables，需要完整复制全局的value 
schema=bce_iam_token       # 改成db name
temp-schema=_skeema_tmp_bce_iam_token
[production]
flavor=mysql:5.7
host={{.Values.appspace.charts.middleware.xdb.bce_iam_token.domain}}
password={{.Values.appspace.charts.middleware.xdb.bce_iam_token.password}}     # 注意通过init工具生成的 没有该key ，如果后面使用任何命令都不行输入密码，可以在这里把密码写上
port={{.Values.appspace.charts.middleware.xdb.bce_iam_token.port}}
user={{.Values.appspace.charts.middleware.xdb.bce_iam_token.user}}
