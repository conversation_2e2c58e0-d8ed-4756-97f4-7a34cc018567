appspace:
  charts:
    provides: { }
    requires: { }       #注意 一般schema 是没有渲染逻辑的，也不存在依赖，这里可以给默认生成的删除（按需）
    configVals: { }
    log:
      local:
        enable: false
    ingress:
      dns:
        enable: false         #关闭，本身也没对外提供域名
      service:
        enable: false         #关闭，本身也没对外提供域名
        type: LoadBalancer
        vipType: underlay
      hostNetwork:
        enable: false        #关闭，本身也没对外提供域名
      apptree:
        enable: false        #关闭，本身也没对外提供域名
    monitor:
      noaheePro:
        enable: false       #关闭，本身也没对外提供域名
    securityContext: { }
    security:
      enable: false
    middleware:
      xdb:
        bce_iam_token:
          clusterName: '{{ bce_iam_token_cluster_name }}'
          user: '{{ bce_iam_token_user }}'
          password: '{{ bce_iam_token_password }}'
          domain: '{{ bce_iam_token_domain }}'
          port: 6203
          permission: all
    replica: 1
    namespace: xdb-schema-init
    images:
      xdb-schema-init-bce-iam-token:
        repository: abc-stack/xdb-init-base
        imageTag: ${xdb-init-base.image.tag} 
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: xdb-schema-init-bce-iam-token
      command: ["/bin/bash", "-c", "/home/<USER>/xdb-schema-init/control.sh upgrade && sleep infinity"]
      args: []
      log: []
      imagePullPolicy: Always
      resources:
        resourceType: VCPU
        cpu: 750m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 1000m
        limitMemory: 1000Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: bce-iam-token-merged-schema1
        mountPath: /home/<USER>/xdb-schema-init/bce_iam_token/merged_schema1.sql
        subPath: merged_schema1.sql
        name: bce-iam-token-merged-schema1
      - type: CONFIG_MAP
        configName: bce-iam-token-skeema
        mountPath: /home/<USER>/xdb-schema-init/bce_iam_token/.skeema
        subPath: skeema
        name: bce-iam-token-skeema
      - type: CONFIG_MAP
        configName: bce-iam-token-dblist-schema
        name: bce-iam-token-dblist-schema
        mountPath: /home/<USER>/xdb-schema-init/dblist
        subPath: dblist
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: false
        readinessProbe:
          enable: true
          type: EXEC
          cmd: [ "/bin/bash", "-c", "/home/<USER>/xdb-schema-init/control.sh status" ]
          initialDelaySeconds: 5
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
          timeoutSeconds: 5
        startupProbe:
          enable: false
      volume: # 增加升级备份持久卷，以防版本升级问题导致，表结构丢失问题。注意：只能备份表结构。
        - name: backups
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/xdb-schema-init/_backups_
          defaultMode: 0755
          storageType: local-path
          size: 1Gi
        - hostFileType: ""              #服务依赖gcc、java环境 可以挂载物理机的路径
          hostPath: /opt
          name: opt
          mountPath: /opt
          type: HOST_PATH
      ports: []
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
