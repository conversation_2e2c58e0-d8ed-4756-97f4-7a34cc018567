agent.sources = ExecSrc
agent.channels = memoryChannel
agent.sinks = KafkaSink

# For each one of the sources, the type is defined
agent.sources.ExecSrc.type = exec
agent.sources.ExecSrc.shell = /bin/bash -c
agent.sources.ExecSrc.restart = true
agent.sources.ExecSrc.restartThrottle = 10000
agent.sources.ExecSrc.command = tail -F /home/<USER>/proxy/log/proxy.log|grep -Po '\\{\\"eventVersion\\".*'

# The channel can be defined as follows.
agent.sources.ExecSrc.channels = memoryChannel

# Each sink's type must be defined
agent.sinks.KafkaSink.type = org.apache.flume.sink.kafka.KafkaSink
agent.sinks.KafkaSink.kafka.topic = {{.Values.appspace.charts.configVals.proxyTopic}}
agent.sinks.KafkaSink.kafka.bootstrap.servers = {{.Values.appspace.charts.middleware.kafka.topic.domain}}:{{.Values.appspace.charts.middleware.kafka.topic.port}}
agent.sinks.KafkaSink.kafka.flumeBatchSize = 1000

# Specify the channel the sink should use
agent.sinks.KafkaSink.channel = memoryChannel

# Each channel's type is defined.
agent.channels.memoryChannel.type = memory

# Other config values specific to each type of channel(sink or source)
# can be defined as well
# In this case, it specifies the capacity of the memory channel
agent.channels.memoryChannel.capacity = {{.Values.appspace.charts.configVals.memoryChannelCapacity}}