[common]
pidfile: ./proxy.pid

[auth_service]
authenticate_check_acl: false
if_auth_service: true
auth_list: bos
products_support_verify: vpc,nat,bch,blb,eip,network,bcm,cds,et,peerconn,vpn,ipvsixgw,eip_bp,eipgroup,ids,bcc,cert,bbc,cfs,palo,bes,mongodb,bts,scs,cce,rds,xiaolvyun,bcd,bmr,bls,bos,cnap,kafka,aca,bct,edap
white_list_of_activated_services: bae,bcc,bcm,beian,billing,blb,bmr,bqs,bss,cdn,cds,cinder,console,glance,logical,multimedia,neutron,nova,portal,rds,scs,ses,sms,webmaster,yunying
# e.g. bce,^bos,*
white_list_for_returning_token:
subuser_support_no_check_value: bch,vca,risk,iot,doc,user_config,lss,console_bcc,console_iam,console_home,console_bcm,console_billing,console_bss
auth_header: X-Auth-Service
region: {{ .Values.appspace.charts.platformConfigVals.region }}

[verify]
cross_account: bce:bos,bce:iam
service_resource_acl: bce:iam,bce:bae
sts_secret_owner_id: {{.Values.appspace.charts.requires.iam.proxy.userId}}
# e.g. logical,^bos,*
white_list_of_user_permission_on_other_services: proxy
# e.g. warning
white_list_report_level: warning

[rpc_server]
port: {{.Values.appspace.charts.provides.iamProxy.port}}
net_threads_num: {{.Values.appspace.charts.configVals.rpc_net_threads_num}}
worker_threads_num: {{.Values.appspace.charts.configVals.rpc_worker_threads_num}}
http_service_keyname : iam.proxy.service
http_method_keyname : iam.proxy.method

[keystone]
host: {{.Values.appspace.charts.requires.iamManage.domain}}
port: {{.Values.appspace.charts.requires.iamManage.port}}
username: proxy
password: {{.Values.appspace.charts.requires.iam.proxy.password}}
default_domain: default
renew_time_advance: 30

[akfilter]
data_file_name: ./data/accesskey_filter.data
meta_file_name: ./data/accesskey_filter.meta
# range (0, 60]
scan_interval_time: 60
time_window: 5

[mysql_server]
host: {{.Values.appspace.charts.middleware.xdb.bce_iam.domain}}
port: {{.Values.appspace.charts.middleware.xdb.bce_iam.port}}
user: {{.Values.appspace.charts.middleware.xdb.bce_iam.user}}
password: {{.Values.appspace.charts.middleware.xdb.bce_iam.password}}
database: bce_iam


[cache]
use_cache: true
#alternatively use this two prefix for compatible problem
#prefix: bce_iam_bj01_
prefix: bce_iam_{{ .Values.appspace.charts.platformConfigVals.region }}_2_
host: {{.Values.appspace.charts.middleware.redis.domain}}
port: {{.Values.appspace.charts.middleware.redis.port}}
password: {{.Values.appspace.charts.middleware.redis.password}}
connection_timeout_in_ms : {{.Values.appspace.charts.configVals.cache_connection_timeout_in_ms}}
time_to_live: {{.Values.appspace.charts.configVals.cache_time_to_live}}
token_discard_ttl: {{.Values.appspace.charts.configVals.cache_token_discard_ttl}}
sk_encrypt_enabled: true
sk_primary_key: {{.Values.appspace.charts.configVals.iam_encrypt_key}}
sk_backup_key: {{.Values.appspace.charts.configVals.iam_encrypt_key}}
max_size: {{.Values.appspace.charts.configVals.cache_max_size}}
@local_uid: {{.Values.appspace.charts.configVals.cache_local_uid}}
@local_uid: {{.Values.appspace.charts.configVals.cache_local_uid_1}}
@local_uid: {{.Values.appspace.charts.configVals.cache_local_uid_2}}
@local_uid: {{.Values.appspace.charts.configVals.cache_local_uid_3}}
@local_uid: {{.Values.appspace.charts.configVals.cache_local_uid_4}}


[comlog]
device_num : 1
level : {{.Values.appspace.charts.configVals.comlogLevel}}
procname : proxy
time_format : %Y-%m-%d %H:%M:%S
log_length:{{.Values.appspace.charts.configVals.log_length}}

[.@device]
type : {{.Values.appspace.charts.configVals.deviceType}}
path : ./log
file : proxy.log
open : 1
layout : [%L] [%Y] [%P] [%T] %R
log_size : {{.Values.appspace.charts.configVals.deviceLogSize}}
split_type : {{.Values.appspace.charts.configVals.deviceSplitType}}
cuttime : {{.Values.appspace.charts.configVals.deviceCuttime}}
quota_hour : {{.Values.appspace.charts.configVals.deviceQuotaHour}}

[.@device]
type : {{.Values.appspace.charts.configVals.deviceType}}
syslevel : {{.Values.appspace.charts.configVals.wfLogLevel}}
path : ./log
file : proxy.log.wf
open : 1
layout : [%L] [%Y] [%P] [%T] %R
log_size : {{.Values.appspace.charts.configVals.deviceLogSize}}
split_type : {{.Values.appspace.charts.configVals.deviceSplitType}}
cuttime : {{.Values.appspace.charts.configVals.deviceCuttime}}
quota_hour : {{.Values.appspace.charts.configVals.deviceQuotaHour}}