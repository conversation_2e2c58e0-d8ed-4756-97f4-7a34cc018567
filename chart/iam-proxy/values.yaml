appspace:
  charts:
    provides:
      iamProxy:
        interface: iam-proxy
        domain: iam-proxy.agilecloud.com
        port: 8588
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
      iamFlume:
        interface: iam-flume
        domain: iam-flume.agilecloud.com
        port: 8299
        vipType: underlay
        portType: TCP
        dnsBindType: vip
        views: '{{ views }}'
    requires:
      iam:
        interface: iam-nginx
        optional: true
        domain: '{{ iam_nginx_domain }}'
        port: '{{ iam_nginx_port }}'
        proxy:
          ak: '{{ proxy_ak }}'
          sk: '{{ proxy_sk }}'
          userId: '{{ proxy_user_id }}'
          password: '{{ proxy_password }}'
        console_iam:
          ak: '{{ console_iam_ak }}'
          sk: '{{ console_iam_sk }}'
          userId: '{{ console_iam_user_id }}'
          password: '{{ console_iam_password }}'
      iamManage:
        interface: iam-manage
        optional: false
        domain: '{{ iam_manage_domain }}'
        port: '{{ iam_manage_port }}'
    configVals:
      iam_encrypt_key: fimos87ejdusedke
      rpc_net_threads_num: 8
      rpc_worker_threads_num: 32
      cache_connection_timeout_in_ms: 200000
      cache_time_to_live: 300
      cache_token_discard_ttl: 30
      cache_max_size: 5000
      cache_local_uid: 8bcbf422ffd84b36a8181ae90aa1e9cc
      cache_local_uid_1: f7ea427efab14358bbd29a28b4111601
      cache_local_uid_2: cb63485da7f2432999f89f23353e94a5
      cache_local_uid_3: 5a08a33099764897b73c775fc340e546
      cache_local_uid_4: 2c483e7b7d1d4ad396dffb8faf072d00
      comlogLevel: 4
      wfLogLevel: 2
      deviceType: AFILE
      deviceLogSize: 1024
      log_length: 102400
      deviceSplitType: DATECUT
      deviceCuttime: 60
      deviceQuotaHour: 24
      proxyTopic: 0cd901398654410caeb745dc8d0c9957__bce_iam_core
      memoryChannelCapacity: 40000
    log:
      local:
        enable: true
    ingress:
      dns:
        enable: true
      service:
        enable: true
        type: LoadBalancer
        vipType: underlay
      applb:
        enable: false
      hostNetwork:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
        serviceMonitor:
          enable: true
          endpoints:
            - port: 8588-tcp # 暴露指标的容器端口名
              path: /rpc_metrics # 暴露指标的 HTTP 端点路径
          interval: 10s
    securityContext: {}
    security:
      enable: false
    middleware:
      xdb:
        bce_iam:
          clusterName: '{{ bce_iam_cluster_name }}'
          user: '{{ bce_iam_user }}'
          password: '{{ bce_iam_password }}'
          domain: '{{ bce_iam_domain }}'
          port: 6203
          permission: write
      redis:
        clusterName: '{{ redis_cluster_name }}'
        domain: '{{ redis_domain }}'
        password: '{{ redis_password }}'
        version: 5
        port: 6379
      kafka:
        topic:
          clusterName: '{{ kafka_cluster_name }}'
          domain: '{{ kafka_domain }}'
          topicName: 0cd901398654410caeb745dc8d0c9957__bce_iam_core
          replication: 3
          partitions: 30
          port: 8959
    replica: 3
    namespace: console
    images:
      iam-proxy:
        repository: abc-stack/iam-proxy
        imageTag: ${iam-proxy.image.tag}
      iam-flume:
        repository: abc-stack/iam-flume
        imageTag: ${iam-flume.image.tag}
    workload:
      kind: ISTATEFUL
      istatefulset:
        updateType: RollingUpdate
        maxUnavailable: 25%
        updateStrategy: InPlaceIfPossible
        podManagementPolicy: Parallel
    initContainers: []
    containers:
    - name: iam-proxy
      command: []
      args: []
      log:
        - dirPaths:
            - /home/<USER>/proxy/log/
          logType: local
          logVolume: iam-proxy-log
          rule:
            duration: 7
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1024Mi
        ephemeralStorage: 1024Mi
        limitCPU: 32000m
        limitMemory: 2048Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-proxy
        mountPath: /home/<USER>/proxy/conf/akscan.conf
        subPath: akscan.conf
        name: iam-proxy
      - type: CONFIG_MAP
        configName: iam-proxy
        mountPath: /home/<USER>/proxy/conf/gflags.conf
        subPath: gflags.conf
        name: iam-proxy
      - type: CONFIG_MAP
        configName: iam-proxy
        mountPath: /home/<USER>/proxy/conf/proxy.conf
        subPath: proxy.conf
        name: iam-proxy
      postStart:
        enable: false
      preStop:
        enable: false
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8588
          initialDelaySeconds: 15
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-proxy-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/proxy/log/
          storageType: local-path
          size: 30Gi
      ports:
      - name: 8588-tcp
        containerPort: 8588
        targetPort: 8588
        servicePort: 8588
        protocol: TCP
        nodePort: ''
    - name: iam-flume
      command: []
      args: []
      log:
        - dirPaths:
            - /home/<USER>/flume/log/
          logType: local
          logVolume: iam-flume-log
          rule:
            duration: 7
      imagePullPolicy: Always
      resources:
        cpu: 1000m
        memory: 1000Mi
        ephemeralStorage: 1024Mi
        limitCPU: 48000m
        limitMemory: 5120Mi
        limitEphemeralStorage: 1024Mi
      securityContext: {}
      envs: []
      configs:
      - type: CONFIG_MAP
        configName: iam-proxy
        mountPath: /home/<USER>/flume/conf/flume-conf.properties.proxy
        subPath: flume_conf.properties
        name: iam-proxy
      - type: CONFIG_MAP
        configName: iam-proxy
        mountPath: /home/<USER>/flume/conf/log4j2.xml
        subPath: flume_log4j2.xml
        name: iam-proxy
      - type: CONFIG_MAP
        configName: iam-proxy
        mountPath: /home/<USER>/flume/bin/flume_control
        subPath: flume_control
        name: iam-proxy
      probe:
        livenessProbe:
          enable: true
          type: TCP
          port: 8299
          initialDelaySeconds: 15
          periodSeconds: 30
          failureThreshold: 3
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          enable: false
        startupProbe:
          enable: false
      volume:
        - name: iam-flume-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/flume/log/
          storageType: local-path
          size: 30Gi
        - name: iam-proxy-log
          type: LOCAL_VOLUME
          mountPath: /home/<USER>/proxy/log/
          storageType: local-path
          size: 30Gi
      ports: []
    hostAliases: []
    podAnnotation: []
    podLabel: []
    commonAnnotation: []
    commonLabel: []
    restartPolicy: Always
    terminationGracePeriodSeconds: 15
    podSchedule: {}
    podAvailable:
      enable: true
    nodeSelector: {}
    tolerations: {}
