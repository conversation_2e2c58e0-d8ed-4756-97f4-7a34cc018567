CREATE TABLE `account`
(
    `name`          varchar(64) NOT NULL COMMENT 'account name',
    `email`         varchar(300)         DEFAULT NULL COMMENT 'account email',
    `phone`         varchar(256)         DEFAULT NULL COMMENT 'account phone',
    `mobile_phone`  varchar(256)         DEFAULT NULL COMMENT 'account mobile',
    `company`       varchar(300)         DEFAULT NULL,
    `register_time` datetime             DEFAULT NULL COMMENT 'account register time',
    `account_type`  varchar(64)          DEFAULT NULL COMMENT 'account type',
    `extra`         text COMMENT 'account extra fields',
    `domain_id`     varchar(64) NOT NULL COMMENT 'account domain',
    `activate_time` datetime    NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'time of activation',
    `modify_time`   datetime    NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'account modify time',
    PRIMARY KEY (`name`),
    KEY `domain_id` (`domain_id`),
    <PERSON>EY `ix_account_email` (`email`(255)),
    <PERSON><PERSON><PERSON> `ix_account_mobile_phone` (`mobile_phone`(255)),
    <PERSON><PERSON><PERSON> `ix_account_register_time` (`register_time`),
    KEY `account_activate_time` (`activate_time`),
    KEY `account_modify_time` (`modify_time`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='account table';

CREATE TABLE `active_service`
(
    `domain_id`  varchar(64) NOT NULL,
    `service_id` varchar(64) NOT NULL,
    `enabled`    tinyint(1) DEFAULT NULL,
    PRIMARY KEY (`domain_id`, `service_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE `bcepolicy`
(
    `id`          varchar(64)    NOT NULL COMMENT 'bcepolicy id',
    `name`        varchar(255)   NOT NULL COMMENT 'bcepolicy policy name',
    `policy_type` varchar(64)    NOT NULL COMMENT 'bcepolicy policy type',
    `domain_id`   varchar(64)    NOT NULL COMMENT 'bcepolicy domain id',
    `acl`         mediumtext     NOT NULL COMMENT 'bcepolicy acl json',
    `create_time` datetime       NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'bcepolicy create time',
    `update_time` datetime       NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'bcepolicy update time',
    `description` varchar(1000) NOT NULL DEFAULT '' COMMENT 'bcepolicy description',
    `extra`       mediumtext     NOT NULL COMMENT 'bcepolicy extension field',
    PRIMARY KEY (`id`),
    UNIQUE KEY `name_domain` (`domain_id`, `name`),
    KEY `policy_type` (`policy_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='bcepolicy table';


CREATE TABLE `bcepolicy_assignment`
(
    `actor_id`     varchar(64) NOT NULL COMMENT 'user or group id',
    `bcepolicy_id` varchar(64) NOT NULL COMMENT 'bcepolicy id',
    `grant_type`   varchar(64) NOT NULL COMMENT 'assignment grant type',
    `domain_id`    varchar(64) NOT NULL COMMENT 'assignment domain id',
    `attach_time`  datetime    NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'assignment attach time',
    PRIMARY KEY (`actor_id`, `bcepolicy_id`),
    KEY `bcepolicy_id` (`bcepolicy_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='bcepolicy assignment table';

CREATE TABLE `credential`
(
    `id`          varchar(64)  NOT NULL,
    `user_id`     varchar(64)  NOT NULL,
    `project_id`  varchar(64)           DEFAULT NULL,
    `blob`        text         NOT NULL,
    `type`        varchar(255) NOT NULL,
    `extra`       text,
    `modify_time` datetime              DEFAULT NULL,
    `trust_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT 'bound trust id',
    `overseas`    smallint(6)  NULL DEFAULT 0 COMMENT 'overseas user flag',
    PRIMARY KEY (`id`),
    KEY `modify_time_index` (`modify_time`),
    KEY `trust_id_idx` (`trust_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `domain`
(
    `id`      varchar(64) NOT NULL,
    `name`    varchar(64) NOT NULL,
    `enabled` tinyint(1)  NOT NULL,
    `extra`   text,
    PRIMARY KEY (`id`),
    UNIQUE KEY `name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `endpoint`
(
    `id`                 varchar(64) NOT NULL,
    `legacy_endpoint_id` varchar(64)          DEFAULT NULL,
    `interface`          varchar(8)  NOT NULL,
    `region`             varchar(255)         DEFAULT NULL,
    `service_id`         varchar(64) NOT NULL,
    `url`                text        NOT NULL,
    `extra`              text,
    `enabled`            tinyint(1)  NOT NULL DEFAULT '1',
    PRIMARY KEY (`id`),
    KEY `endpoint_service_id_fkey` (`service_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;



CREATE TABLE `group`
(
    `id`          varchar(64) NOT NULL,
    `domain_id`   varchar(64) NOT NULL,
    `name`        varchar(255) NOT NULL,
    `description` text,
    `extra`       text,
    PRIMARY KEY (`id`),
    UNIQUE KEY `domain_id` (`domain_id`, `name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `group_acl`
(
    `id`     varchar(64) NOT NULL COMMENT 'group id',
    `policy` mediumtext  NOT NULL COMMENT 'policy statement',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='group to policy assignment';

CREATE TABLE `idp`
(
    `id`              varchar(64)   NOT NULL COMMENT 'idp id',
    `name`            varchar(255)  NOT NULL COMMENT 'idp name',
    `domain_id`       varchar(64)   NOT NULL COMMENT 'idp domain id',
    `idp_type`        varchar(64)   NOT NULL DEFAULT '' COMMENT 'idp type',
    `create_time`     datetime      NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'idp create time',
    `update_time`     datetime      NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'idp update time',
    `encode_metadata` text          NOT NULL COMMENT 'idp metadata',
    `extra`           varchar(5000) NOT NULL COMMENT 'idp extension field',
    PRIMARY KEY (`id`),
    UNIQUE KEY `name_domain` (`domain_id`, `name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='bce idp table';

CREATE TABLE `migrate_version`
(
    `repository_id`   varchar(250) NOT NULL,
    `repository_path` text,
    `version`         int(11) DEFAULT NULL,
    PRIMARY KEY (`repository_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `notify_party_group_membership`
(
    `notifyparty_id` varchar(64) NOT NULL,
    `notifygroup_id` varchar(64) NOT NULL,
    PRIMARY KEY (`notifyparty_id`, `notifygroup_id`),
    KEY `notifygroup_id` (`notifygroup_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `notifygroup`
(
    `id`          varchar(64) NOT NULL,
    `name`        varchar(255) NOT NULL,
    `description` mediumtext,
    `domain_id`   varchar(64) NOT NULL,
    `extra`       mediumtext,
    PRIMARY KEY (`id`),
    UNIQUE KEY `name_domain` (`domain_id`, `name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `notifyparty`
(
    `id`        varchar(64) NOT NULL,
    `name`      varchar(255) NOT NULL,
    `email`     varchar(300) DEFAULT NULL COMMENT 'notifyparty email',
    `phone`     varchar(256) DEFAULT NULL COMMENT 'notifyparty phone',
    `domain_id` varchar(64) NOT NULL,
    `extra`     mediumtext,
    PRIMARY KEY (`id`),
    UNIQUE KEY `name_domain` (`domain_id`, `name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `permission_group`
(
    `id`          varchar(64) NOT NULL COMMENT 'permission group id',
    `service`     varchar(64) NOT NULL COMMENT 'service name for permissions',
    `name`        varchar(64) NOT NULL COMMENT 'group name from permissions',
    `permissions` mediumtext  NOT NULL COMMENT 'list of permissions',
    `extra`       text        NOT NULL COMMENT 'extra json',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_service_name` (`service`, `name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='permission groups table';

CREATE TABLE `policy`
(
    `id`    varchar(64)  NOT NULL,
    `type`  varchar(255) NOT NULL,
    `blob`  text         NOT NULL,
    `extra` text,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `policy_template`
(
    `auto_id`               bigint(20)    NOT NULL AUTO_INCREMENT COMMENT 'auto increment id',
    `id`                    varchar(64)   NOT NULL COMMENT 'template id',
    `name`                  varchar(254)  NOT NULL COMMENT 'template name',
    `service_name`          varchar(64)   NOT NULL COMMENT 'service name in iam',
    `product_name`          varchar(64)   NOT NULL DEFAULT '' COMMENT 'product name',
    `template_type`         varchar(64)   NOT NULL DEFAULT '' COMMENT 'template type: generator/jsonEditor',
    `generator_config`      text          NOT NULL COMMENT 'configuration of generator',
    `json_editor_templates` text          NOT NULL COMMENT 'templates of json editor',
    `create_time`           datetime      NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'template create time',
    `update_time`           datetime      NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'template update time',
    `effective_time`        datetime      NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'template effective time',
    `extra`                 varchar(5000) NOT NULL COMMENT 'template extra field',
    `granularity`           varchar(32)   NOT NULL DEFAULT 'NORMAL' COMMENT '权限粒度',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    KEY `product_name` (`product_name`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10
  DEFAULT CHARSET = utf8mb4 COMMENT ='bce policy template table';

CREATE TABLE `project`
(
    `id`          varchar(64) NOT NULL,
    `name`        varchar(64) NOT NULL,
    `extra`       text,
    `description` text,
    `enabled`     tinyint(1) DEFAULT NULL,
    `domain_id`   varchar(64) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `domain_id` (`domain_id`, `name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `region`
(
    `id`               varchar(64)  NOT NULL,
    `description`      varchar(255) NOT NULL,
    `parent_region_id` varchar(64) DEFAULT NULL,
    `extra`            text,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `resource`
(
    `id`    varchar(128) NOT NULL,
    `owner` varchar(64)  NOT NULL,
    `extra` text,
    PRIMARY KEY (`id`),
    KEY `owner` (`owner`),
    KEY `owner_index` (`owner`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `resource_policy`
(
    `id`     varchar(128) NOT NULL,
    `policy` text         NOT NULL,
    `extra`  text,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `role`
(
    `id`    varchar(64)  NOT NULL,
    `name`  varchar(255) NOT NULL,
    `extra` text,
    PRIMARY KEY (`id`),
    UNIQUE KEY `name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `role_policy`
(
    `id`     varchar(64) NOT NULL,
    `policy` text        NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `service`
(
    `id`      varchar(64) NOT NULL,
    `type`    varchar(255)         DEFAULT NULL,
    `extra`   text,
    `enabled` tinyint(1)  NOT NULL DEFAULT '1',
    PRIMARY KEY (`id`),
    KEY `ix_service_type` (`type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `sts_credential`
(
    `accessKeyId`     varchar(64)   NOT NULL COMMENT 'accesskey id',
    `secretAccessKey` varchar(64)   NOT NULL COMMENT 'secret access key',
    `userId`          varchar(64)   NOT NULL COMMENT 'user id',
    `createTime`      datetime      NOT NULL COMMENT 'create time',
    `expiration`      datetime      NOT NULL COMMENT 'expiration time',
    `acl`             varchar(2048) NOT NULL COMMENT 'credential acl in json',
    `roleId`          varchar(64)   NOT NULL DEFAULT '' COMMENT 'role id',
    `assumer`         varchar(64)   NOT NULL DEFAULT '' COMMENT 'assumer id',
    `federationId`    varchar(64)   NOT NULL DEFAULT '' COMMENT 'federation id',
    `overseas`        smallint(6)   NOT NULL DEFAULT '0' COMMENT 'overseas user flag',
    PRIMARY KEY (`accessKeyId`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='sts credential table';

CREATE TABLE `stsrole`
(
    `id`          varchar(64)   NOT NULL COMMENT 'role id',
    `name`        varchar(255)  NOT NULL COMMENT 'role name',
    `domain_id`   varchar(64)   NOT NULL COMMENT 'sts role domain id',
    `create_time` datetime      NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'sts role create time',
    `description` varchar(3000) NOT NULL DEFAULT '' COMMENT 'sts role description',
    `extra`       varchar(5000) NOT NULL COMMENT 'sts role extension field',
    `type`        varchar(32)   NOT NULL DEFAULT 'SERVICE' COMMENT 'SERVICE/USER',
    `grant_type`  varchar(256)  NOT NULL DEFAULT '' COMMENT 'sts role grant type, ACCOUNT/IDP/SERVICE',
    `update_time` datetime      NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'stsrole update time',
    PRIMARY KEY (`id`),
    UNIQUE KEY `name_domain` (`domain_id`, `name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='bce sts role table';

CREATE TABLE `token`
(
    `id`       varchar(64) NOT NULL,
    `expires`  datetime    DEFAULT NULL,
    `extra`    text,
    `valid`    tinyint(1)  NOT NULL,
    `trust_id` varchar(64) DEFAULT NULL,
    `user_id`  varchar(64) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `ix_token_expires` (`expires`),
    KEY `ix_token_expires_valid` (`expires`, `valid`),
    KEY `idx_user_id` (`user_id`, `valid`, `expires`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `trust`
(
    `id`              varchar(64) NOT NULL,
    `trustor_user_id` varchar(64) NOT NULL,
    `trustee_user_id` varchar(64) NOT NULL,
    `project_id`      varchar(64) DEFAULT NULL,
    `impersonation`   tinyint(1)  NOT NULL,
    `deleted_at`      datetime    DEFAULT NULL,
    `expires_at`      datetime    DEFAULT NULL,
    `extra`           text,
    `remaining_uses`  int(11)     DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `trust_role`
(
    `trust_id` varchar(64) NOT NULL,
    `role_id`  varchar(64) NOT NULL,
    PRIMARY KEY (`trust_id`, `role_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `user`
(
    `id`                 varchar(64)  NOT NULL,
    `name`               varchar(255) NOT NULL,
    `extra`              text,
    `password`           varchar(600)          DEFAULT NULL,
    `enabled`            tinyint(1)            DEFAULT NULL,
    `domain_id`          varchar(64)  NOT NULL,
    `default_project_id` varchar(64)           DEFAULT NULL,
    `provider`           varchar(64)  NOT NULL DEFAULT '' COMMENT 'user provider',
    `public_id`          varchar(64)  NOT NULL DEFAULT '' COMMENT 'user public_id',
    `create_time`        datetime     NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'user create time',
    `update_time`        datetime     NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'user update time',
    `subuser`            tinyint(1)            DEFAULT '0' COMMENT 'subuser flag',
    `type`               varchar(32)  NOT NULL DEFAULT 'identity' COMMENT 'user type',
    PRIMARY KEY (`id`),
    UNIQUE KEY `domain_id` (`domain_id`, `name`),
    KEY `user_provider_id` (`provider`, `public_id`),
    KEY `user_create_time` (`create_time`),
    KEY `subuser` (`subuser`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `user_acl`
(
    `id`     varchar(64) NOT NULL COMMENT 'user id',
    `policy` mediumtext  NOT NULL COMMENT 'policy statement',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='user to policy assignment';

CREATE TABLE `user_group_membership`
(
    `user_id`     varchar(64) NOT NULL,
    `group_id`    varchar(64) NOT NULL,
    `create_time` datetime    NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'group member join time',
    PRIMARY KEY (`user_id`, `group_id`),
    KEY `group_id` (`group_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `user_login_log`
(
    `id`          bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '唯一编号',
    `request_id`  varchar(50)         NOT NULL DEFAULT '' COMMENT '请求ID',
    `uid`         varchar(100)                 DEFAULT '' COMMENT '子用户ID',
    `ip`          varchar(50)                  DEFAULT '' COMMENT '请求IP',
    `session_id`  varchar(200)                 DEFAULT '' COMMENT 'bce session id',
    `operate`     int(11)                      DEFAULT '1' COMMENT '1:登录  2:登出',
    `result_code` int(11)             NOT NULL COMMENT '结果状态码',
    `channel`     varchar(50)                  DEFAULT '' COMMENT '登录渠道',
    `device`      varchar(50)                  DEFAULT '' COMMENT '登录设备',
    `user_agent`  varchar(500)                 DEFAULT '' COMMENT '客户端代理',
    `referer`     varchar(500)                 DEFAULT '' COMMENT 'referer地址',
    `create_time` timestamp           NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `update_time` timestamp           NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '????',
    PRIMARY KEY (`id`),
    KEY `uid_date` (`uid`, `create_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 6815
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `contact_info`
(
    `auto_id`         bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'auto id',
    `domain_id`       varchar(64)  NOT NULL COMMENT 'domain id',
    `user_id`         varchar(64)  NOT NULL COMMENT 'user id',
    `email`           varchar(300) NOT NULL DEFAULT '' COMMENT 'user email',
    `email_verified`  tinyint(1)   NOT NULL DEFAULT '0' COMMENT 'user email verified status',
    `mobile_phone`    varchar(256) NOT NULL DEFAULT '' COMMENT 'user mobile_phone',
    `mobile_verified` tinyint(1)   NOT NULL DEFAULT '0' COMMENT 'user mobile verified status',
    `overseas`        smallint(6)  NOT NULL DEFAULT '0' COMMENT 'overseas user flag',
    `create_time`     timestamp    NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT 'create time',
    `update_time`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
    `wechat_id`       varchar(256) NOT NULL DEFAULT '' COMMENT '微信ID',
    `infoflow_id`     varchar(256) NOT NULL DEFAULT '' COMMENT '如流ID',
    `robot_id`        varchar(512) NOT NULL DEFAULT '' COMMENT '机器人webhook',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `user_id` (`user_id`),
    KEY `domain_id` (`domain_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 21397
  DEFAULT CHARSET = utf8mb4 COMMENT ='user contact info';

CREATE TABLE `distribute_lock`
(
    `auto_id` bigint(20)   NOT NULL AUTO_INCREMENT,
    `tag`     varchar(200) NOT NULL,
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `distribute_lock_tag_uindex` (`tag`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 22895
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `mfa_device`
(
    `auto_id`     bigint(20)    NOT NULL AUTO_INCREMENT COMMENT 'è‡ªå¢žid',
    `id`          varchar(64)   NOT NULL COMMENT 'mfa device id',
    `name`        varchar(128)  NOT NULL COMMENT 'mfa device name',
    `user_id`     varchar(64)   NOT NULL COMMENT 'user id',
    `device_type` varchar(64)   NOT NULL COMMENT 'mfa device type',
    `secret_seed` varchar(512)  NOT NULL COMMENT 'mfa secret seed',
    `binded`      tinyint(1)    NOT NULL DEFAULT '0' COMMENT 'mfa device bind flag',
    `comment`     varchar(1024) NOT NULL DEFAULT '' COMMENT 'comment',
    `extra`       text          NOT NULL COMMENT 'extra info',
    `create_time` timestamp     NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT 'åˆ›å»ºæ—¶é—´',
    `update_time` timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'æ›´æ–°æ—¶é—´',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    UNIQUE KEY `user_id_device_type` (`user_id`, `device_type`),
    KEY `name` (`name`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 225
  DEFAULT CHARSET = utf8mb4 COMMENT ='mfa device';

CREATE TABLE `organization`
(
    `auto_id`           bigint(20)    NOT NULL AUTO_INCREMENT COMMENT 'è‡ªå¢žid',
    `id`                varchar(64)   NOT NULL COMMENT 'organization id',
    `master_account_id` varchar(64)   NOT NULL COMMENT 'æ‰€å±žorganization id',
    `name`              varchar(256)  NOT NULL DEFAULT '' COMMENT 'organization name',
    `comment`           varchar(1024) NOT NULL DEFAULT '' COMMENT 'comment',
    `permission`        varchar(1024) NOT NULL DEFAULT '' COMMENT '["ConsolidateBilling"]',
    `create_time`       timestamp     NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT 'åˆ›å»ºæ—¶é—´',
    `update_time`       timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'æ›´æ–°æ—¶é—´',
    `invitation_quota`  smallint(6)   NOT NULL DEFAULT '20' COMMENT '最大邀请帐号个数',
    `register_quota`    smallint(6)   NOT NULL DEFAULT '5' COMMENT '最大创建帐号个数',
    `status`            varchar(16)   NOT NULL DEFAULT 'NORMAL',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    UNIQUE KEY `master_account_organization_id` (`master_account_id`, `id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 36933
  DEFAULT CHARSET = utf8mb4 COMMENT ='organizationè¡¨';


CREATE TABLE `organization_invitation`
(
    `auto_id`     bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `id`          varchar(64)   NOT NULL COMMENT 'invitation id',
    `org_id`      varchar(64)   NOT NULL COMMENT 'organization id',
    `account_id`  varchar(64)   NOT NULL COMMENT 'account id',
    `status`      varchar(32)   NOT NULL COMMENT 'invitation status(Open, Canceled, Accepted, Declined, Expired)',
    `comment`     varchar(1024) NOT NULL DEFAULT '' COMMENT 'comment',
    `expire_time` timestamp     NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT '过期时间',
    `create_time` timestamp     NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT '创建时间',
    `update_time` timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    KEY `org_id` (`org_id`),
    KEY `account_id` (`account_id`),
    KEY `status` (`status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 9900
  DEFAULT CHARSET = utf8mb4 COMMENT ='organization_invitation表';

CREATE TABLE `organization_unit`
(
    `auto_id`     bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `id`          varchar(64)   NOT NULL COMMENT 'organization_unit id',
    `org_id`      varchar(64)   NOT NULL COMMENT 'organization id',
    `name`        varchar(200)  NOT NULL DEFAULT '' COMMENT 'organization_unit name',
    `comment`     varchar(1024) NOT NULL DEFAULT '' COMMENT 'comment',
    `create_time` timestamp     NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT '创建时间',
    `update_time` timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    UNIQUE KEY `organization_unit` (`org_id`, `name`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 61711
  DEFAULT CHARSET = utf8mb4 COMMENT ='organization_unit表';

CREATE TABLE `organization_unit_assignment`
(
    `auto_id`              bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `organization_unit_id` varchar(64)   NOT NULL COMMENT 'ou id',
    `actor_id`             varchar(64)   NOT NULL COMMENT 'ou or account id',
    `org_id`               varchar(64)   NOT NULL COMMENT 'organization id',
    `assignment_type`      varchar(32)   NOT NULL COMMENT 'OU/ACCOUNT',
    `status`               varchar(32)   NOT NULL COMMENT 'ou assignment status(Invited/Create/Leave/Delete)',
    `comment`              varchar(1024) NOT NULL DEFAULT '' COMMENT 'comment',
    `permission`           varchar(1024) NOT NULL DEFAULT '' COMMENT '["ConsolidateBilling", "AllowAdmin"]',
    `create_time`          timestamp     NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT '创建时间',
    `update_time`          timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`org_id`, `organization_unit_id`, `actor_id`, `assignment_type`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 45310
  DEFAULT CHARSET = utf8mb4 COMMENT ='organization_unit_assignment表';

CREATE TABLE `product_mapping`
(
    `auto_id`     bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `product`     varchar(64)   NOT NULL COMMENT '产品',
    `service`     varchar(64)   NOT NULL COMMENT '服务',
    `comment`     varchar(1024) NOT NULL DEFAULT '' COMMENT 'comment',
    `create_time` timestamp     NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT '创建时间',
    `update_time` timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `product_service` (`product`, `service`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 87
  DEFAULT CHARSET = utf8mb4 COMMENT ='product_mapping';

CREATE TABLE `scp`
(
    `auto_id`     bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `id`          varchar(64)   NOT NULL COMMENT 'id主键',
    `name`        varchar(200)  NOT NULL DEFAULT '' COMMENT 'SCP名称',
    `type`        varchar(32)   NOT NULL COMMENT 'SCP类型, SYSTEM/CUSTOM',
    `comment`     varchar(1024) NOT NULL DEFAULT '' COMMENT 'comment',
    `org_id`      varchar(64)   NOT NULL DEFAULT '' COMMENT '所属组织id',
    `acl`         mediumtext    NOT NULL COMMENT 'acl',
    `create_time` timestamp     NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT '创建时间',
    `update_time` timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `detachable`  tinyint(1)    NOT NULL COMMENT 'detachable',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    UNIQUE KEY `org` (`org_id`, `name`),
    KEY `org_id` (`org_id`),
    KEY `name` (`name`),
    KEY `type` (`type`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 16857
  DEFAULT CHARSET = utf8mb4 COMMENT ='scp表';


CREATE TABLE `scp_assignment`
(
    `auto_id`     bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `scp_id`      varchar(64) NOT NULL COMMENT 'scp id',
    `actor_id`    varchar(64) NOT NULL COMMENT 'ou or account id',
    `grant_type`  varchar(32) NOT NULL COMMENT 'OU/ACCOUNT',
    `org_id`      varchar(64) NOT NULL COMMENT '所属组织id',
    `create_time` timestamp   NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT '创建时间',
    `update_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `detachable`  tinyint(1)  NOT NULL COMMENT 'detachable',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`org_id`, `scp_id`, `actor_id`, `grant_type`),
    KEY `org_id` (`org_id`),
    KEY `actor_id` (`org_id`, `actor_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 145126
  DEFAULT CHARSET = utf8mb4 COMMENT ='scp_assignment表';


CREATE TABLE `assignment`
(
    `type`      enum ('UserProject','GroupProject','UserDomain','GroupDomain') NOT NULL,
    `actor_id`  varchar(64)                                                    NOT NULL,
    `target_id` varchar(64)                                                    NOT NULL,
    `role_id`   varchar(64)                                                    NOT NULL,
    `inherited` tinyint(1)                                                     NOT NULL,
    PRIMARY KEY (`type`, `actor_id`, `target_id`, `role_id`),
    KEY `role_id` (`role_id`),
    KEY `actor_id` (`actor_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `user_invitation_info`
(
    `auto_id`     bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `id`          varchar(32)  NOT NULL COMMENT 'id',
    `user_id`     varchar(64)  NOT NULL COMMENT 'user_id',
    `name`        varchar(255) NOT NULL COMMENT 'name',
    `verified`    tinyint(1)   NOT NULL DEFAULT '0' COMMENT 'verified',
    `domain_id`   varchar(64)  NOT NULL COMMENT 'domain_id',
    `create_time` datetime     NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'info create time',
    `expire_time` datetime     NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'info expire time',
    `verify_time` datetime     NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'info verify time',
    `invite_type` varchar(16)  NOT NULL COMMENT 'invite type',
    `target`      varchar(300) NOT NULL COMMENT 'invite target',
    `redirect`    varchar(500) NOT NULL COMMENT 'redirect url when invitation approved',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `quota_rule`
(
    `auto_id`     bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `id`          varchar(64) NOT NULL COMMENT 'rule id',
    `service`     varchar(64) NOT NULL COMMENT 'service',
    `duration`    int(32)     NOT NULL COMMENT 'duration',
    `quota`       int(32)     NOT NULL COMMENT 'quota',
    `type`        varchar(64) NOT NULL DEFAULT '' COMMENT 'duration_type(SLIDING/FIXED/PERMANENT)',
    `create_time` timestamp   NOT NULL DEFAULT '1999-12-31 08:00:00' COMMENT '创建时间',
    `update_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      varchar(32) NOT NULL COMMENT 'status(ACTIVE/DEACTIVE)',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 240
  DEFAULT CHARSET = utf8mb4 COMMENT ='quota_rule表';

CREATE TABLE `quota_rule_term`
(
    `auto_id`    bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `rule_id`    varchar(64)  NOT NULL COMMENT 'rule id',
    `term_key`   varchar(64)  NOT NULL COMMENT ' term name',
    `term_value` varchar(200) NOT NULL DEFAULT '0' COMMENT 'term value',
    `term_min`   varchar(200) NOT NULL DEFAULT '0' COMMENT 'term min',
    `term_max`   varchar(200) NOT NULL DEFAULT '0' COMMENT 'term max',
    PRIMARY KEY (`auto_id`),
    KEY `rule_id` (`rule_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 494
  DEFAULT CHARSET = utf8mb4 COMMENT ='quota_term表';


CREATE TABLE `quota_term_info`
(
    `auto_id`       bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `service`       varchar(64) NOT NULL COMMENT 'service name',
    `term_key`      varchar(64)          DEFAULT NULL,
    `term_type`     varchar(32) NOT NULL DEFAULT '' COMMENT 'term type',
    `support_range` tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否支持range',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `service_term` (`service`, `term_key`),
    KEY `service` (`service`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 704
  DEFAULT CHARSET = utf8mb4 COMMENT ='quota_service表';

CREATE TABLE `user_switch_stsrole` (
  `id` varchar(64) NOT NULL COMMENT 'id',
  `user_id` varchar(64) NOT NULL COMMENT '用户Id',
  `role_id` varchar(64) NOT NULL COMMENT '角色ID',
  `actor_id` varchar(64) NOT NULL COMMENT '切换目标账号ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
	KEY `actor_id` (`actor_id`),
	KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账户可切换角色数据表';

CREATE TABLE `account_assignment` (
  `type` varchar(32) NOT NULL COMMENT 'account type',
  `actor_id` varchar(64) NOT NULL COMMENT 'actor id',
  `target_id` varchar(64) NOT NULL COMMENT 'traget id',
  `extra` text COMMENT 'extra',
  `extend_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'extend_id',
  PRIMARY KEY (`type`,`actor_id`,`target_id`),
  KEY `target_id` (`target_id`),
  KEY `idx_target_id_type_extend_id` (`target_id`,`type`,`extend_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ec2_credential` (
  `access` varchar(64) NOT NULL,
  `secret` varchar(64) DEFAULT NULL,
  `user_id` varchar(64) DEFAULT NULL,
  `tenant_id` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`access`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8;

CREATE TABLE `metadata` (
  `user_id` varchar(64) NOT NULL,
  `tenant_id` varchar(64) NOT NULL,
  `data` text,
  PRIMARY KEY (`user_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8;

CREATE TABLE `open_plat_assignment` (
  `auto_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'auto id',
  `type` varchar(32) NOT NULL COMMENT 'account type',
  `actor_id` varchar(64) NOT NULL COMMENT 'actor id',
  `target_id` varchar(64) NOT NULL COMMENT 'traget id',
  `extra` text COMMENT 'extra',
  `extend_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'extend id',
  PRIMARY KEY (`auto_id`),
  UNIQUE KEY `type_target_extend_id` (`type`,`target_id`,`extend_id`),
  KEY `idx_actor_id` (`actor_id`)
) ENGINE=InnoDB AUTO_INCREMENT=81038 DEFAULT CHARSET=utf8mb4 COMMENT='c端账号挂接关系表';


CREATE TABLE `operation_log` (
  `auto_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'auto id',
  `id` varchar(64) NOT NULL COMMENT 'id',
  `extend_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'extend_id',
  `type` varchar(32) NOT NULL COMMENT 'operation type',
  `extra` text COMMENT 'extra',
  `create_time` timestamp NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT 'create time',
  PRIMARY KEY (`auto_id`),
  KEY `idx_id` (`id`),
  KEY `idx_type_extend_id` (`type`,`extend_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1096 DEFAULT CHARSET=utf8mb4 COMMENT='操作快照表';

CREATE TABLE `org_billing_info` (
  `auto_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `master_account_id` varchar(64) NOT NULL COMMENT 'master account id',
  `actor_id` varchar(64) NOT NULL COMMENT 'sub account id',
  `org_id` varchar(64) NOT NULL COMMENT 'organization id',
  `type` varchar(32) NOT NULL COMMENT 'IN/OUT',
  `comment` varchar(1024) NOT NULL DEFAULT '' COMMENT 'comment',
  `record_time` timestamp NOT NULL DEFAULT '1999-12-31 16:00:00' COMMENT '加入or退出时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`auto_id`),
  KEY `idx_master_actor_type_record` (`master_account_id`,`actor_id`,`type`,`record_time`),
  KEY `idx_master_actor_record` (`master_account_id`,`actor_id`,`record_time`),
  KEY `idx_master_type_record` (`master_account_id`,`type`,`record_time`),
  KEY `idx_master_record` (`master_account_id`,`record_time`),
  KEY `idx_sub_record` (`actor_id`,`record_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1164 DEFAULT CHARSET=utf8mb4 COMMENT='org_billing_info表,记录子账号加入退出财务圈时间';


CREATE TABLE `region_bns_whitelist` (
  `auto_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(64) NOT NULL DEFAULT '',
  `region` varchar(32) NOT NULL DEFAULT '',
  `bns_whitelist` text NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` varchar(64) NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_by` varchar(64) NOT NULL,
  PRIMARY KEY (`auto_id`),
  KEY `idx_region` (`region`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_id_region` (`user_id`,`region`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `region_bns_whitelist_config` (
  `auto_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(64) NOT NULL,
  `limit_type` varchar(32) NOT NULL,
  `notice_receivers` varchar(256) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` varchar(64) NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_by` varchar(64) NOT NULL,
  PRIMARY KEY (`auto_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `request_bns_whitelist` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(64) NOT NULL,
  `bns_whitelist` text NOT NULL,
  `limit_type` varchar(32) NOT NULL,
  `notice_receivers` varchar(256) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` varchar(64) NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_by` varchar(64) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IX_USER_ID` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;


 CREATE TABLE `risk_behavior` (
  `auto_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `id` varchar(64) NOT NULL COMMENT 'UUID',
  `display_name` varchar(64) NOT NULL COMMENT '风险名称',
  `risk_type` varchar(64) NOT NULL COMMENT '风险大类',
  `comment` varchar(64) NOT NULL COMMENT '描述',
  `config_type` varchar(64) NOT NULL COMMENT '风险行为类别',
  `risk_level` varchar(64) NOT NULL COMMENT '风向等级',
  `type` varchar(64) NOT NULL COMMENT '风险类型',
  `extra` text COMMENT '额外属性',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`auto_id`),
  UNIQUE KEY `risk_behavior_id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `risk_behavior_account` (
  `auto_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `risk_behavior_id` varchar(64) NOT NULL COMMENT 'UUID',
  `domain_id` varchar(64) NOT NULL COMMENT 'domainId',
  `status` tinyint(1) NOT NULL COMMENT '开启状态',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`auto_id`),
  UNIQUE KEY `risk_behavior_domain_id` (`risk_behavior_id`,`domain_id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tenant` (
  `id` varchar(64) NOT NULL,
  `name` varchar(64) NOT NULL,
  `extra` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8;


CREATE TABLE `user_tenant_membership` (
  `user_id` varchar(64) NOT NULL,
  `tenant_id` varchar(64) NOT NULL,
  PRIMARY KEY (`user_id`,`tenant_id`),
  KEY `tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8;


CREATE TABLE `task_graph`
(
    `auto_id`      bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `id`           varchar(32)   NOT NULL COMMENT '唯一32位UUID',
    `domain_id`    varchar(32)   NOT NULL COMMENT '租户id',
    `name`         varchar(128)  NOT NULL COMMENT '任务名',
    `root_node_id`   varchar(32)   NOT NULL DEFAULT '' COMMENT '根执行节点id',
    `batch_process` tinyint       NOT NULL DEFAULT 1 COMMENT '是否是批处理任务',
    `comment`      varchar(1024) NOT NULL DEFAULT '' COMMENT '描述',
    `extra`        TEXT          NOT NULL COMMENT '扩展属性',
    `create_time`  datetime      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `create_by`    varchar(128)  NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time`  datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`    varchar(128)  NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务执行图';


CREATE TABLE `task_graph_node`
(
    `auto_id`       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `id`            varchar(32)   NOT NULL COMMENT '唯一32位UUID',
    `task_graph_id` varchar(32)   NOT NULL COMMENT '所属TaskGraph的id',
    `domain_id`     varchar(32)   NOT NULL COMMENT '租户id',
    `type`          varchar(128)  NOT NULL COMMENT '任务名',
    `next_node_ids` varchar(1024)   NOT NULL DEFAULT '[]' COMMENT '后驱执行节点的id列表',
    `custom`        tinyint       NOT NULL DEFAULT 0 COMMENT '是否是自定义执行器',
    `executor_type`  varchar(64)   NOT NULL DEFAULT '' COMMENT '执行器类型/名称',
    `config_id`     varchar(32)   NOT NULL DEFAULT '' COMMENT '执行器配置id',
    `comment`       varchar(1024) NOT NULL DEFAULT '' COMMENT '描述',
    `extra`         TEXT          NOT NULL COMMENT '扩展属性',
    `create_time`   datetime      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `create_by`     varchar(128)  NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time`   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`     varchar(128)  NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    INDEX `node_task_graph_id` (`task_graph_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务执行图节点';

CREATE TABLE `task_schedule`
(
    `auto_id`         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `id`              varchar(32)   NOT NULL COMMENT '唯一32位UUID',
    `task_graph_id`   varchar(32)   NOT NULL COMMENT '要执行的TaskGraph的id',
    `domain_id`       varchar(32)   NOT NULL COMMENT '租户id',
    `stream`          tinyint       NOT NULL DEFAULT 0 COMMENT '是否是流处理任务',
    `cron_expression` varchar(32)   NOT NULL COMMENT 'cron表达式',
    `execute_node`    varchar(128)  NOT NULL DEFAULT '' COMMENT '后驱执行节点的id列表',
    `schedule_status` varchar(32)   NOT NULL DEFAULT '' COMMENT '任务状态',
    `last_start_time` datetime      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `status`          tinyint       NOT NULL DEFAULT 1 COMMENT '执行器配置id',
    `comment`         varchar(1024) NOT NULL DEFAULT '' COMMENT '描述',
    `extra`           TEXT          NOT NULL COMMENT '扩展属性',
    `create_time`     datetime      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `create_by`       varchar(128)  NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time`     datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`       varchar(128)  NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    INDEX `schedule_task_graph_id` (`task_graph_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务定时调度配置';

CREATE TABLE `task_executor_config`
(
    `auto_id`       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `id`            varchar(32)   NOT NULL COMMENT '唯一32位UUID',
    `task_graph_id` varchar(32)   NOT NULL COMMENT '要执行的TaskGraph的id',
    `domain_id`     varchar(32)   NOT NULL COMMENT '租户id',
    `config_map`    TEXT          NOT NULL COMMENT '配置map',
    `partition_num` int(6)        NOT NULL DEFAULT 0 COMMENT '描述',
    `comment`       varchar(1024) NOT NULL DEFAULT '' COMMENT '描述',
    `risk_behavior_id`   varchar(256)   NOT NULL COMMENT '风险行为id',
    `extra`         TEXT          NOT NULL COMMENT '扩展属性（内部用）',
    `create_time`   datetime      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `create_by`     varchar(128)  NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time`   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`     varchar(128)  NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    index `config_task_graph_id` (`task_graph_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务执行器配置表';

CREATE TABLE `risk_event`
(
    `auto_id`       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `id`            varchar(32)   NOT NULL COMMENT '唯一32位UUID',
    `domain_id`     varchar(32)   NOT NULL COMMENT '租户id',
    `subject_id`    varchar(32)   NOT NULL COMMENT '事件主体id',
    `risk_behavior_id`  varchar(256)   NOT NULL COMMENT '风险行为id',
    `event_type`    varchar(32)   NOT NULL COMMENT '事件类型',
    `event_content`  TEXT          NOT NULL COMMENT '事件详情',
    `event_time`    datetime      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '事件时间',
    `create_time`   datetime      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`),
    index `domain_subject_id` (`domain_id`, `subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风险事件表';


CREATE TABLE `profile`
(
    `auto_id`       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `actor_id`      varchar(32)   NOT NULL COMMENT '主体id',
    `domain_id`     varchar(32)   NOT NULL COMMENT '租户id',
    `profile_type`  varchar(32)   NOT NULL COMMENT '配置类型',
    `risk_behavior_id`   varchar(128)   NOT NULL COMMENT '风险行为id',
    `extra`         TEXT          NOT NULL COMMENT '数据',
    `update_time`   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `domain_actor_behavior_id` (`domain_id`, `risk_behavior_id`, `actor_id`),
    index `actor_id` (`actor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型数据';

CREATE TABLE `notify_schedule`
(
    `auto_id`         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `id`              varchar(32)   NOT NULL COMMENT '唯一32位UUID',
    `domain_id`       varchar(32)   NOT NULL COMMENT '租户id',
    `host`            varchar(32)   NOT NULl COMMENT '接收风险事件的服务地址',
    `cron_expression` varchar(32)   NOT NULL COMMENT 'cron表达式',
    `execute_node`    varchar(128)  NOT NULL DEFAULT '' COMMENT '最后一次执行该任务的几点',
    `schedule_status` varchar(32)   NOT NULL DEFAULT '' COMMENT '任务状态',
    `last_start_time` datetime      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `status`          tinyint       NOT NULL DEFAULT 1 COMMENT '执行器配置id',
    `comment`         varchar(1024) NOT NULL DEFAULT '' COMMENT '描述',
    `extra`           TEXT          NOT NULL COMMENT '扩展属性',
    `create_time`     datetime      NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `create_by`       varchar(128)  NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time`     datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`       varchar(128)  NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`auto_id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风险事件通知任务配置';

DROP TABLE IF EXISTS `bearer_token`;
CREATE TABLE `bearer_token` (
    `id` varchar(32) NOT NULL,
    `token_id` varchar(256) NOT NULL,
    `user_id` varchar(64) NOT NULL,
    `app_id` varchar(64) NOT NULL,
    `access_key` varchar(64) NOT NULL,
    `secret_key` varchar(64) NOT NULL,
    `status` tinyint(4) NOT NULL,
    `extra` text,
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `expire_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '到期时间，默认值为不过期',
    PRIMARY KEY (`id`),
    KEY `idx_access_key` (`access_key`),
    KEY `idx_app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `bearer_token_delete`;
CREATE TABLE `bearer_token_delete` (
   `id` varchar(32) NOT NULL,
   `token_id` varchar(256) NOT NULL,
   `user_id` varchar(64) NOT NULL,
   `app_id` varchar(64) NOT NULL,
   `access_key` varchar(64) NOT NULL,
   `secret_key` varchar(64) NOT NULL,
   `extra` text,
   `create_time` datetime NOT NULL,
   `delete_time` datetime NOT NULL,
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `credential_extra`;
CREATE TABLE `credential_extra` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'pk',
    `credential_id` varchar(64) NOT NULL,
    `user_id` varchar(64) NOT NULL,
    `last_used_time` datetime NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `credential_id` (`credential_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1312621869 DEFAULT CHARSET=utf8mb4 COMMENT='credetial表extra字段结构化';

DROP TABLE IF EXISTS `service_stsrole_info`;
CREATE TABLE `service_stsrole_info` (
    `auto_id` bigint(20) NOT NULL AUTO_INCREMENT,
    `role_name` varchar(255) NOT NULL COMMENT 'sts role name',
    `policy_id` varchar(64) NOT NULL COMMENT 'policy id',
    `product` varchar(32) NOT NULL COMMENT '绑定产品',
    `service` varchar(32) NOT NULL COMMENT '绑定服务, 由用户手填，无校验',
    `role_status` varchar(32) NOT NULL COMMENT '启用、禁用等状态',
    `publish_status` varchar(32) NOT NULL,
    `description` varchar(3000) DEFAULT NULL,
    `extra` text,
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`auto_id`),
    KEY `idx_publish_status_index` (`publish_status`),
    KEY `idx_role_name` (`role_name`)
) ENGINE=InnoDB AUTO_INCREMENT=263 DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `service_stsrole_log`;
CREATE TABLE `service_stsrole_log` (
   `auto_id` bigint(20) NOT NULL AUTO_INCREMENT,
   `role_name` varchar(255) NOT NULL COMMENT 'sts role name',
   `extra` text,
   `last_used_time` datetime NOT NULL,
   PRIMARY KEY (`auto_id`),
   KEY `idx_role_name` (`role_name`)
) ENGINE=InnoDB AUTO_INCREMENT=195 DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `totp_device`;
CREATE TABLE `totp_device` (
   `auto_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
   `id` varchar(64) NOT NULL COMMENT '设备ID',
   `vendor` varchar(64) NOT NULL COMMENT '设备供应商',
   `serial_number` varchar(64) NOT NULL COMMENT '设备序列号，提供给客户用于绑定',
   `secret_seed_hex` varchar(128) NOT NULL COMMENT 'HEX编码的设备种子KEY',
   `account_issued_to` varchar(64) NOT NULL DEFAULT '' COMMENT '分配给的客户accountId，设备可能交给客户但尚未绑定',
   `time_step` int(11) NOT NULL DEFAULT '30' COMMENT '设备的时间步数，即验证码刷新的时间周期，单位为秒',
   `acquire_time` datetime NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '采购时间',
   `issue_time` datetime NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '分配时间',
   `comment` varchar(300) NOT NULL DEFAULT '' COMMENT '说明',
   `extra` text COMMENT '其他',
   PRIMARY KEY (`auto_id`),
   UNIQUE KEY `id` (`id`),
   UNIQUE KEY `serialNumber` (`serial_number`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='TOTP硬件设备信息表';

DROP TABLE IF EXISTS `sm3_hmac_assignment`;
CREATE TABLE `sm3_hmac_assignment`
(
    `message_type`          varchar(64) NOT NULL COMMENT '要加密的消息类型（MOBILE_PHONE/EMAIL/PASSWORD/BCE_POLICY）',
    `actor_id`         varchar(300)         NOT  NULL COMMENT '消息 Id',
    `digest`         varchar(500)         NOT NULL COMMENT '摘要',
    `attach_time` datetime             DEFAULT NULL COMMENT '关联时间',
    UNIQUE KEY `actor_type` (`actor_id`, `message_type`)
) ENGINE = InnoDB  DEFAULT CHARSET = utf8mb4 COMMENT ='sm3_hmac_assignment table';
