FROM iregistry.baidu-int.com/abc-stack/xdb-init-base:latest




#根据不同应用修改产出路径
ENV DOCKERFILE_OUTPUT baidu/product-chart/iam/output/dockerfile/xdb-data-init-bce-iam
ENV CHART_TOOLS_OUTPUT baidu/cloudbed/chart-tool/output/chart_tool/data/common/templates
ENV INSIDE_TEMPLATE /home/<USER>/inside_template

COPY $CHART_TOOLS_OUTPUT/_helpers.tpl    $INSIDE_TEMPLATE/
COPY $DOCKERFILE_OUTPUT/files/*   $INSIDE_TEMPLATE/

COPY $DOCKERFILE_OUTPUT/preinstall.sh /home/<USER>/xdb-data-init/
COPY $DOCKERFILE_OUTPUT/entrypoint.sh /home/<USER>/xdb-data-init/

RUN chmod a+x /home/<USER>/xdb-data-init/*.sh

ENTRYPOINT [ "/home/<USER>/xdb-data-init/entrypoint.sh" ]