SET names utf8;

DELETE FROM risk_behavior;

insert into risk_behavior (id, display_name, risk_type, comment, config_type, risk_level, type, extra, create_time, update_time)
values ('8b47a681764a292fbcc89957b9728391' , 'AKSK调用异常', 'AKSK_RISK', 'AKSK在10分钟周期内存在调用频率大于30qps', 'SYSTEM', 'MID', 'AKSK_HIGH_FREQUENCY_USE', '{"checkDurationInMinutes":10,"maxAcessFrequency":30,"type":"AKSK_HIGH_FREQUENCY_USE"}',
now(), now());

insert into risk_behavior (id, display_name, risk_type, comment, config_type, risk_level, type, extra, create_time, update_time)
values ('957b97283918b47a681764a292fbcc89' , 'AKSK未使用', 'AKSK_RISK', '启用的AKSK超过30天未使用', 'SYSTEM', 'MID', 'AKSK_NO_USE', '{"ideTimeInDay":30,"type":"AKSK_NO_USE"}',
now(), now());

insert into risk_behavior (id, display_name, risk_type, comment, config_type, risk_level, type, extra, create_time, update_time)
values ('764a292fbcc89957b97283918b47a681' , 'AKSK调用异常', 'AKSK_RISK', 'AKSK在10分钟周期内存在调用频率大于30qps', 'TEMPLATE', 'MID', 'AKSK_HIGH_FREQUENCY_USE', '{"checkDurationInMinutes":10,"maxAcessFrequency":30,"type":"AKSK_HIGH_FREQUENCY_USE"}',
now(), now());

insert into risk_behavior (id, display_name, risk_type, comment, config_type, risk_level, type, extra, create_time, update_time)
values ('c89957b97283918b47a681764a292fbc' , 'AKSK未使用', 'AKSK_RISK', '启用的AKSK超过30天未使用', 'TEMPLATE', 'MID', 'AKSK_NO_USE', '{"ideTimeInDay":30,"type":"AKSK_NO_USE"}',
now(), now());