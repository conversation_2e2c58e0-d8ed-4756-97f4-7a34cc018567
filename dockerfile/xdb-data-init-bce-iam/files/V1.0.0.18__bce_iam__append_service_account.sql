-- 后续在此文件追加 服务号 密码、ak、sk 的模版，原因详见 README.md
-- init aihc-data
set names utf8;
INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("dbee8377f500456eb0a5ed3a8d604aea", "aihc-data", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$ZMHVXtcS$mFWoWt6V0TBexqR6eQXtE/uz27fKjA/76nafnHk1W5HVu6mK7ZLdfh2ZqscDdsxU.otQnKruibMuB.ODLw4JJ1",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "dbee8377f500456eb0a5ed3a8d604aea", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "dbee8377f500456eb0a5ed3a8d604aea", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO `active_service`
VALUES ('dbee8377f500456eb0a5ed3a8d604aea', 'e274118227b24725b57a3d089a39c3f8', NULL);


INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time) VALUES ("a2f29d4717574bbf841a55bd06957f28402db86998fc3376665a9ddba336b304", "dbee8377f500456eb0a5ed3a8d604aea", "0557fd0d0d244cf7801dba4cadf214c5", "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"2200ebdbb4004a12bf6e69d8a10952e7\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b882be5e06357147e435555cdd0910683c9df3ee17fc6cbc08193fbf63506be18dc8a9e2baa13b5d4d22eb36015317998\\\"}\"", "ec2", "{}", "2025-06-24 08:53:30");


-- init aihc-serverless
set names utf8;
INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("f14669b17c094f9f9e419f19549b7355", "aihc-serverless", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$RXOEOFde$0R/LDVa6PjC0o3IycRCw/TRB0XqROTWs3Us06aWPrlY1rJ1pObN6Lot3ql6juZTemv/LVlrCMP9sH4./6vNWB0",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "f14669b17c094f9f9e419f19549b7355", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "f14669b17c094f9f9e419f19549b7355", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO `active_service`
VALUES ('f14669b17c094f9f9e419f19549b7355', 'e274118227b24725b57a3d089a39c3f8', NULL);


INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time) VALUES ("72442fce6eb9c26af3ee739db5310090eb560e1c371f970f581c24054fdb2dd6", "f14669b17c094f9f9e419f19549b7355", "0557fd0d0d244cf7801dba4cadf214c5", "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"7fb59d3156aa42729ca6e8cbc08f06ec\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb73c7b626da301253d6827fc180d0026c41cd534469a3408ee38db74f1f5b7b854a69b23efcb9e531758e7f14c2befc3\\\"}\"", "ec2", "{}", "2025-06-24 08:53:30");

-- init bcm_chpc_iam_monitor
set names utf8;
INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("ad7a72a6bb9d4bd3bff787486e9c96ec", "bcm_chpc_iam_monitor", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$eN3OfGy9$F5jTF6VA11CN5cLdO09/KL04TYRFraHR4eKpi0.PR6DFlvD9plMmfIgm0PNU83cmOdyxe6B1MWPkTLv7FqXUh0",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "ad7a72a6bb9d4bd3bff787486e9c96ec", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "ad7a72a6bb9d4bd3bff787486e9c96ec", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO `active_service`
VALUES ('ad7a72a6bb9d4bd3bff787486e9c96ec', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time) VALUES ("7a70dc0caea73d3429b047352b58090f2055dd699a83f20a3f3d4c4b6b55df5b", "ad7a72a6bb9d4bd3bff787486e9c96ec", "0557fd0d0d244cf7801dba4cadf214c5", "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"555ab4e20ca940a18cc1819b519e2733\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57be8b43ddf3f71931840e5172dabbe282e2a5762000b36cb835915cbfce069ed53ed3ee1a602470bd41ee0102a84b7b12a\\\"}\"", "ec2", "{}", "2025-01-22 02:45:02");

