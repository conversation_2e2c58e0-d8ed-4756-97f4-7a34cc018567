DELETE FROM `scp`;

SET names utf8;

INSERT INTO `scp`
VALUES (71, '80389efdd7a94133808d673a8cb7b6e0', 'FinanceCenterScp', 'SYSTEM', '禁止充值提现、管理发票、查看资金操作', 'default',
        '{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:billing\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Deny\",\"permission\":[\"FC:InvoiceMgt\",\"FC:FundView\"],\"condition\":{\"stringEquals\":{\"ConsolidateBilling\":[\"true\"],\"OrganizationDelegate\":[\"false\"]}}},{\"service\":\"bce:billing\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Deny\",\"permission\":[\"FC:ChargeAndWithdraw\",\"FC:FinanceSupervise\"],\"condition\":{\"stringEquals\":{\"ConsolidateBilling\":[\"true\"]}}}]}',
        '2018-03-01 00:48:49', '2019-06-25 07:20:19', 0);
INSERT INTO `scp`
VALUES (72, '33105897c1e54eafa30b2702b42e7a78', 'FullAccessScp', 'SYSTEM', '允许所有操作', 'default',
        '{\"accessControlList\":[{\"service\":\"*\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"]}]}',
        '2018-03-01 01:06:01', '2019-06-25 07:24:33', 1);
INSERT INTO `scp`
VALUES (261, 'c154e7c9c81d4f149fce467773e83fd9', 'FCOrderAccessScp', 'SYSTEM', '查看、支付、取消订单的权限', 'default',
        '{\"id\":\"FCOrderAccessPolicy\",\"version\":\"v1\",\"accessControlList\":[{\"service\":\"bce:billing\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"FC:PayOrder\",\"FC:DescribeOrderDetail\",\"FC:DescribeOrderList\",\"FC:CancelOrder\",\"FC:CreateOrder\"]}]}',
        '2018-03-01 05:23:27', '2019-06-25 07:54:05', 1);
INSERT INTO `scp`
VALUES (5468, '4d5f7b091f034dcc890903be34649974', 'ProductReadScp', 'SYSTEM', 'read scp for all products', 'default',
        '{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:*\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\"]},{\"service\":\"bce:bbc\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\",\"LIST\",\"DOWNLOAD\"]},{\"service\":\"bce:bcc\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"VM_READ\",\"SNAPSHOT_READ\",\"SECURITYGROUP_READ\",\"IMAGE_READ\",\"READ\"]},{\"service\":\"bce:blb\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"BLB_READ\"]},{\"service\":\"bce:bos\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\",\"LIST\",\"ListBuckets\"]},{\"service\":\"bce:cdn\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\",\"LIST\"]},{\"service\":\"bce:dcc\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\"]},{\"service\":\"bce:eip\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\"]},{\"service\":\"bce:eipgroup\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\"]},{\"service\":\"bce:hosteye\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\",\"LIST\"]},{\"service\":\"bce:network\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"VPC_READ\",\"SUBNET_READ\",\"SECURITYGROUP_READ\",\"ROUTE_READ\",\"ACL_READ\",\"DEDICATEDCONN_READ\"]},{\"service\":\"bce:rds\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\",\"LIST\"]}]}',
        '2018-11-20 00:32:32', '2018-11-28 07:53:39', 1);
INSERT INTO `scp`
VALUES (9912, '0cc135954f4c45eb92a6a527f02b7736', 'FCFullAccessScp', 'SYSTEM', '允许财务中心所有操作', 'default',
        '{\"accessControlList\":[{\"service\":\"bce:billing\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"]}]}',
        '2019-02-25 20:13:45', '2019-06-25 07:34:44', 1);
INSERT INTO `scp`
VALUES (10201, '7a0c8097bb254e4198fddd66462dfd43', 'OperateScpForCMCC', 'SYSTEM', '移动客户运维权限', 'default',
        '{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"*\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"],\"condition\":{\"ipAddress\":[\"************\"]}},{\"service\":\"*\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"VM_OPERATE\",\"VM_READ\",\"SNAPSHOT_READ\",\"CreateVmSnapshot\",\"SECURITYGROUP_READ\",\"IMAGE_READ\",\"CreateVolumeSnapshot\",\"CreateSelfImage\",\"RenameVolume\",\"UpdateVolumeMetaData\",\"READ\",\"OPERATE\"]}]}',
        '2019-03-05 01:05:58', '2019-04-04 10:28:42', 0);
INSERT INTO `scp`
VALUES (14476, 'fdb8def8e9784fde932ef939f328cda1', 'FCReadAccessScp', 'SYSTEM', '财务中心只读权限', 'default',
        '{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:billing\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\"]}]}',
        '2019-06-24 23:37:53', '2019-06-25 07:54:30', 1);
INSERT INTO `scp`
VALUES (14517, '03167de7543b47eca6fdedb4f3fda58f', 'FCReadDenyScp', 'SYSTEM', '禁止财务中心只读权限', 'default',
        '{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:billing\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Deny\",\"permission\":[\"READ\"]}]}',
        '2019-06-25 20:14:24', '2019-06-25 20:14:24', 1);
INSERT INTO `scp`
VALUES (17110, 'd33fb3934d5d47ad88b1af9a72129341', 'GlobalOperateScp', 'SYSTEM', '允许所有运维操作(仅限支持运维权限的服务)', 'default',
        '{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:*\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"OPERATE\"]},{\"service\":\"bce:bcc\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"VM_OPERATE\",\"CreateVmSnapshot\",\"CreateVolumeSnapshot\",\"CreateSelfImage\",\"RenameVolume\",\"UpdateVolumeMetaData\"]},{\"service\":\"bce:blb\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"BLB_OPERATE\"]},{\"service\":\"bce:bos\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"WRITE\"]},{\"service\":\"bce:cdn\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"WRITE\"]},{\"service\":\"bce:network\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"VPC_OPERATE\",\"SUBNET_OPERATE\",\"SECURITYGROUP_OPERATE\",\"ROUTE_OPERATE\",\"ACL_OPERATE\",\"DEDICATEDCONN_OPERATE\"]},{\"service\":\"bce:rds\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"WRITE\"]},{\"service\":\"bce:kafka\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"TopicReadOper\",\"CertificateReadOper\",\"AlterTopic\",\"AddTopicPartition\",\"UpdateTopicCertificate\",\"AlterCertificate\",\"RebuildCertificate\",\"UpdateCertificateTopic\",\"ConsumerGroupReadOper\"]}]}',
        '2018-12-11 18:27:57', '2018-12-11 18:27:57', 1);

INSERT INTO `scp` (id, name, type, comment, org_id, acl, create_time, update_time, detachable)
VALUES ('9e380dd7af898d607b638cbe041338a7', 'FinanceCenterReadDenyScp', 'SYSTEM', '禁止子账户查看账户余额及消费支付信息', 'default',
        '{"version":"v2","accessControlList":[{"service":"bce:billing","region":"*","resource":["*"],"effect":"Deny","permission":["FC:FundView"],"condition":{"stringEquals":{"ConsolidateBilling":["true"],"OrganizationDelegate":["false"]}}}]}',
        now(), now(), 1);

-- 新增
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (834,'53abc89046c44d149bbd5a6dafbdd95b','CommonModuleFullAccessScp','SYSTEM','控制台公共模块管理权限，当前包含：工单系统','default','{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:ticket\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"]}]}','2019-07-29 04:58:10','2019-07-29 04:58:10',1);
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (980,'163b25db79fd48ac82198f01823be051','AIFullControlScp','SYSTEM','管理所有AI产品的权限','default','{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:ai\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"]},{\"service\":\"bce:console_ai\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"]}]}','2020-03-30 05:20:37','2020-03-30 05:20:37',1);
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (985,'68a76c803c734aa68eb189b555c35fbf','IamFullControlScp','SYSTEM','full control scp for iam','default','{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:iam\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"]}]}','2020-04-02 23:45:01','2020-04-02 23:45:01',1);
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (1049,'f8a2b6455e2c4138a2805dd4bb757678','FCOrderConsoleDenyScp','SYSTEM','拒绝控制台创建、支付订单的权限','default','{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:billing\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Deny\",\"permission\":[\"FC:PayOrder\",\"FC:CancelOrder\",\"FC:CreateOrder\"],\"condition\":{\"stringEquals\":{\"OrganizationDelegate\":[\"true\",\"false\"]}}}]}','2020-09-02 18:56:58','2020-09-02 18:56:58',1);
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (1050,'acb03ba488bf4480a7ceb0fc2d5d9036','IamAccessKeyConsoleDenyScp','SYSTEM','拒绝控制台查看、管理AcessKey的权限','default','{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:iam\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Deny\",\"permission\":[\"CreateAccessKey\",\"DeleteAccessKey\",\"UpdateAccessKey\",\"ReadAccessKey\"],\"condition\":{\"stringEquals\":{\"OrganizationDelegate\":[\"true\",\"false\"]}}}]}','2020-09-02 19:01:47','2020-09-02 19:01:47',1);
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (1055,'05855872c0b84af2a63e4a5ad5834d4b','BccFullAccessScp','SYSTEM','允许云服务器 BCC所有操作','default','{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:bcc\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"]}]}','2020-09-04 00:39:38','2020-09-04 00:39:38',1);
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (1056,'46d3365ca284443d8dc6f35ed6994c31','VpcFullAccessScp','SYSTEM','允许私有网络 VPC所有操作','default','{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:network\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"]}]}','2020-09-04 00:40:39','2020-09-04 00:40:39',1);
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (1057,'7e23d97781c645ffa8873d956371fc7f','CdsFullAccessScp','SYSTEM','允许云磁盘 CDS所有操作','default','{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:cds\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"]}]}','2020-09-04 00:40:49','2020-09-04 00:40:49',1);
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (1062,'2e41fb1be7474dcca8081dd448aa3e52','EipFullAccessScp','SYSTEM','允许弹性公网IP EIP所有操作','default','{\"version\":\"v2\",\"accessControlList\":[{\"service\":\"bce:eip\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"*\"]}]}','2020-09-11 05:30:18','2020-09-11 05:30:18',1);
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (2214,'c3ea4fg5db294tbba696ed66rb2art7a','FCOrderCreateAccessScp','SYSTEM','允许创建订单(不包括支付订单)','default','{ \"accessControlList\": [ { \"service\": \"bce:billing\", \"region\": \"*\", \"resource\": [ \"*\" ], \"effect\": \"Allow\", \"permission\": [ \"FC:OrderCreate\" ] } ] }','2019-02-26 04:13:47','2019-07-12 08:54:38',1);
INSERT INTO `scp` (`auto_id`, `id`, `name`, `type`, `comment`, `org_id`, `acl`, `create_time`, `update_time`, `detachable`) VALUES (2215,'d2ea4775db2944bba6962d663b2art7b','FCOrderCreateDenyScp','SYSTEM','禁止创建订单','default','{ \"version\": \"v2\", \"accessControlList\": [ { \"service\": \"bce:billing\", \"region\": \"*\", \"resource\": [ \"*\" ], \"effect\": \"Deny\", \"permission\": [ \"FC:OrderCreate\" ] } ] }','2019-02-26 04:13:48','2019-07-12 08:54:41',1);
