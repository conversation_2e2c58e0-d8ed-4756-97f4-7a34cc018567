set names utf8;

INSERT INTO domain (id, name, enabled, extra)
VALUES ("32cf77650bb0459aac6cee0e11562681", "res_iot", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("1ce8dcd589f14acaa31459f395a7aaad", "default", "{}", "Default project for domain", "1",
        "32cf77650bb0459aac6cee0e11562681");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("32cf77650bb0459aac6cee0e11562681", "root", "{}",
        "$6$rounds=40000$fOUcrs6q$ep4YED6thMNxiAPG6NXp16LJuEMahKz4GepIjVp0ZM0I.fQ7Een8RuscH.Mz3KzmKnSZv.WLOg93dld01PO6T.",
        "1", "32cf77650bb0459aac6cee0e11562681", "1ce8dcd589f14acaa31459f395a7aaad", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "32cf77650bb0459aac6cee0e11562681", "1ce8dcd589f14acaa31459f395a7aaad",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "32cf77650bb0459aac6cee0e11562681", "32cf77650bb0459aac6cee0e11562681",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("32cf77650bb0459aac6cee0e11562681", "32cf77650bb0459aac6cee0e11562681",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_iot", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "32cf77650bb0459aac6cee0e11562681", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("c9e8723dbbc30715ebd18988067c533129b5dd42431227642c51ad4263cf0129", "32cf77650bb0459aac6cee0e11562681",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"8187ffd155cc45f5ab15d906529e9867\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b386978865b6de93db3122ef669e187641406b61249494694a78e3d0ed0cb234e0beb64b75a0dd9e0f89698b11e5cad09\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("09c16fb3b61d4f87a6b38bc86921e46c", "res_iothisk", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("d8399ab076774cb69f2d1acf2eca6fe4", "default", "{}", "Default project for domain", "1",
        "09c16fb3b61d4f87a6b38bc86921e46c");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("09c16fb3b61d4f87a6b38bc86921e46c", "root", "{}",
        "$6$rounds=40000$yUUT13v0$OmxdvACIpRgR2PZTxMN6WpfaEiS/ICFGvSXlCW4IKmocByIHsRWyGCCk3wB/hIxX6By1nevk1o4ABc2wIzeqW0",
        "1", "09c16fb3b61d4f87a6b38bc86921e46c", "d8399ab076774cb69f2d1acf2eca6fe4", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "09c16fb3b61d4f87a6b38bc86921e46c", "d8399ab076774cb69f2d1acf2eca6fe4",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "09c16fb3b61d4f87a6b38bc86921e46c", "09c16fb3b61d4f87a6b38bc86921e46c",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("09c16fb3b61d4f87a6b38bc86921e46c", "09c16fb3b61d4f87a6b38bc86921e46c",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_iothisk", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "09c16fb3b61d4f87a6b38bc86921e46c", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("7d0acac992b5551c319dfa2cad8b2125931745b36a76e451102862069b1dd482", "09c16fb3b61d4f87a6b38bc86921e46c",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"9833adb434424b51a185cf11f547e94c\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b9fc49ff9153e6dff10065511d377e294435cc45edb1fdbe6bfe435e2576e728a44bdde90f12dbfab73589b94d3a9fe4d\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("97fd2a3ae2774a1799742288d3b51dd0", "res_iotre", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("f4c7ca029e3b4078afd336307039f070", "default", "{}", "Default project for domain", "1",
        "97fd2a3ae2774a1799742288d3b51dd0");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("97fd2a3ae2774a1799742288d3b51dd0", "root", "{}",
        "$6$rounds=40000$sSgYAhfJ$fGPxkSFAjoxbEqLgU8OsRfSX9HlcctmLIxLQ/HQBYgLimasbXDzx6IqUTHae.kN8NW4DLFrglyBhun1X816Cu1",
        "1", "97fd2a3ae2774a1799742288d3b51dd0", "f4c7ca029e3b4078afd336307039f070", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "97fd2a3ae2774a1799742288d3b51dd0", "f4c7ca029e3b4078afd336307039f070",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "97fd2a3ae2774a1799742288d3b51dd0", "97fd2a3ae2774a1799742288d3b51dd0",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("97fd2a3ae2774a1799742288d3b51dd0", "97fd2a3ae2774a1799742288d3b51dd0",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_iotre", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "97fd2a3ae2774a1799742288d3b51dd0", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("6588ed9e4ff0058d14755f7a4c7f76ac19870d53091130bc914af68b81243be2", "97fd2a3ae2774a1799742288d3b51dd0",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"1324715f058848e0b25745f9856c1c61\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b17844fd59e38dfb73092d0f3251d1b89a1cf367207cb0d4b622283f49d382647e26e0aa7d83c132c5ff4e32293b6fb5a\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("3ef1531e894c4a529789636a2c75cec2", "res_iottsdb", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("9626b135a30041a78f200b5517f7347b", "default", "{}", "Default project for domain", "1",
        "3ef1531e894c4a529789636a2c75cec2");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("3ef1531e894c4a529789636a2c75cec2", "root", "{}",
        "$6$rounds=40000$0x4jWyEC$SHjIgWOTcGCREPhpfGslH4phIIfsl2RT82P.6sg.5IwyPhL4ZnAfSBZ.xzpHW1Xa6qZE0FWpq4DkthL5ON4AP.",
        "1", "3ef1531e894c4a529789636a2c75cec2", "9626b135a30041a78f200b5517f7347b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "3ef1531e894c4a529789636a2c75cec2", "9626b135a30041a78f200b5517f7347b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "3ef1531e894c4a529789636a2c75cec2", "3ef1531e894c4a529789636a2c75cec2",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("3ef1531e894c4a529789636a2c75cec2", "3ef1531e894c4a529789636a2c75cec2",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_iottsdb", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "3ef1531e894c4a529789636a2c75cec2", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("4bb32eb63a8bb862c3f738ef1de7694afeae2b3775dfb211b3ee324623481b2f", "3ef1531e894c4a529789636a2c75cec2",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"9cfb0af043ed4271a4746a3be3124cc6\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bbdcdb07126eeb018b0f6c67ea071925deff4c3b98d3a0d4fea92706a08a5f1995a519afb0342b3f15a892e112b2f68b4\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("12bb07a1eee64b3f86830d0d9d72e400", "res_iot_flow_control", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("c79cd58649cf45d192efab53f372cda2", "default", "{}", "Default project for domain", "1",
        "12bb07a1eee64b3f86830d0d9d72e400");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("12bb07a1eee64b3f86830d0d9d72e400", "root", "{}",
        "$6$rounds=40000$A784VcuC$Lm/G8ME.xNham8S/Rx99xDdP9O526n6UI8wXzYSrzeESlCIrp0YPEcTnwPDrzXZBqAOS8N5qG5aK9UGw4ghS3/",
        "1", "12bb07a1eee64b3f86830d0d9d72e400", "c79cd58649cf45d192efab53f372cda2", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "12bb07a1eee64b3f86830d0d9d72e400", "c79cd58649cf45d192efab53f372cda2",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "12bb07a1eee64b3f86830d0d9d72e400", "12bb07a1eee64b3f86830d0d9d72e400",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("12bb07a1eee64b3f86830d0d9d72e400", "12bb07a1eee64b3f86830d0d9d72e400",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_iot_flow_control", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "12bb07a1eee64b3f86830d0d9d72e400", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("84de23bfa127ff06dc18af3dc074fa628f48d612931ac6e870d9aa8cb6e978c2", "12bb07a1eee64b3f86830d0d9d72e400",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"5dd21ccea1254a96840d097b54e3282d\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bdd79d735b255110e96654fd83e307fc2edaa57e4c220192337e092ba26133cadb19ab4b6e67321cdc39c98127461421c\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("81a0f93fb899476fb091a9061a7b4b22", "res_iot_hubble", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("85c3658212124ca6b5a83e4c88428910", "default", "{}", "Default project for domain", "1",
        "81a0f93fb899476fb091a9061a7b4b22");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("81a0f93fb899476fb091a9061a7b4b22", "root", "{}",
        "$6$rounds=40000$dL9eq/Hg$nbfG3JVXoY624keD4CeNDRGCFpBSSs7QKhWiXNpqOtcXleYWG6sjzcZpRHBRaxuJz3iL42bBXREg7Ea71d9CF0",
        "1", "81a0f93fb899476fb091a9061a7b4b22", "85c3658212124ca6b5a83e4c88428910", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "81a0f93fb899476fb091a9061a7b4b22", "85c3658212124ca6b5a83e4c88428910",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "81a0f93fb899476fb091a9061a7b4b22", "81a0f93fb899476fb091a9061a7b4b22",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("81a0f93fb899476fb091a9061a7b4b22", "81a0f93fb899476fb091a9061a7b4b22",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_iot_hubble", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "81a0f93fb899476fb091a9061a7b4b22", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("34fed8d18f1ae22b564c0aa38fbc8bfd211c64919eb13c8b62d7966515ddfefa", "81a0f93fb899476fb091a9061a7b4b22",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"dc22834fa9af49bb85672a70651a3bf2\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b5eced708d4e61fff1704b70a63655912e412511cf94785e3c4aa7d9d718b5a262684304fc0650d9215547631590b5a93\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("51b881ab55aa4d2296e8173cfa88c991", "res_itm", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("3db85cc76e3f48aa88c19294d36e4f4d", "default", "{}", "Default project for domain", "1",
        "51b881ab55aa4d2296e8173cfa88c991");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("51b881ab55aa4d2296e8173cfa88c991", "root", "{}",
        "$6$rounds=40000$5cUwZPM4$xDK2W4Atmei1y7QiKN.Q9SAUwbRUwm887uZoVB65z6Sf2Ymc/h14bd7nFntcOj7JEDmqcQggsT6Cla4n8se1u1",
        "1", "51b881ab55aa4d2296e8173cfa88c991", "3db85cc76e3f48aa88c19294d36e4f4d", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "51b881ab55aa4d2296e8173cfa88c991", "3db85cc76e3f48aa88c19294d36e4f4d",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "51b881ab55aa4d2296e8173cfa88c991", "51b881ab55aa4d2296e8173cfa88c991",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("51b881ab55aa4d2296e8173cfa88c991", "51b881ab55aa4d2296e8173cfa88c991",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_itm", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "51b881ab55aa4d2296e8173cfa88c991", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8de512cd4b51d0bbc59a265d18d9ae725cdea23f16984319fc93892ec54aba24", "51b881ab55aa4d2296e8173cfa88c991",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"4e8c41a9d24548dea1a707396db79eab\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bd36fdaba378d4a5d99a1abdd213965457a19f251c8e0f168d4127579737c9b9d601182184eccdb945a734a591e4f3d05\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("9907791fde7b424d853583b5cd83ae6b", "res_ivc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("724474d3c05945b0b675a987dd58a344", "default", "{}", "Default project for domain", "1",
        "9907791fde7b424d853583b5cd83ae6b");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("9907791fde7b424d853583b5cd83ae6b", "root", "{}",
        "$6$rounds=40000$iNdPwmVw$LkkNYTI.Sf4IElHNDt4gKXtp5VsNNcxR3o3vsOTnSQIuXBG3RT49rMOeyQXSWNWJQS9U/hgBZSxfgfpxj.2250",
        "1", "9907791fde7b424d853583b5cd83ae6b", "724474d3c05945b0b675a987dd58a344", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "9907791fde7b424d853583b5cd83ae6b", "724474d3c05945b0b675a987dd58a344",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "9907791fde7b424d853583b5cd83ae6b", "9907791fde7b424d853583b5cd83ae6b",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("9907791fde7b424d853583b5cd83ae6b", "9907791fde7b424d853583b5cd83ae6b",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_ivc", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "9907791fde7b424d853583b5cd83ae6b", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0c38a42c6ea6dfbe0770b9365f48329503ec04b618da0b15940a022fd3e98768", "9907791fde7b424d853583b5cd83ae6b",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"b7d79535aebc4893998e939c2ee06644\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba87dde6487a7a5d75dd4e7eca6ed1dfb3ce29a8d5fe459b1fbc00b68c3b808321bd8471426c54021ffeb8ae5d8f03a0a\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("6f729db6ca2047c3a2c235b680185df2", "res_jarvis", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("b333c2ae0bc64098a63a427959a87096", "default", "{}", "Default project for domain", "1",
        "6f729db6ca2047c3a2c235b680185df2");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("6f729db6ca2047c3a2c235b680185df2", "root", "{}",
        "$6$rounds=40000$HRelUYV9$KOQTDQt4sit47gxZVNMe2k6xq8IAahqykHKrq/HeO/p3pEBgY7VUm2wz1L9NL0hFAl71DJK/CzkMxXc8Ja7p10",
        "1", "6f729db6ca2047c3a2c235b680185df2", "b333c2ae0bc64098a63a427959a87096", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "6f729db6ca2047c3a2c235b680185df2", "b333c2ae0bc64098a63a427959a87096",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "6f729db6ca2047c3a2c235b680185df2", "6f729db6ca2047c3a2c235b680185df2",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("6f729db6ca2047c3a2c235b680185df2", "6f729db6ca2047c3a2c235b680185df2",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_jarvis", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "6f729db6ca2047c3a2c235b680185df2", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8af4c0f1c33d26658cc719076756f27e5442231759d72d509e591181e7e347df", "6f729db6ca2047c3a2c235b680185df2",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"a21fca4f8090404eb2ef2445cac952fe\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bbd27f8dd5582f98c942127c83b8131d3364bd8943701f30468d9cb29f6db2e3b0241bc81ababfea9773cb8b1dad961bc\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("df6c12d23bea4150b17284e20386b62c", "res_kafka", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("28ae9fcf5b594066b758cfeb739376f6", "default", "{}", "Default project for domain", "1",
        "df6c12d23bea4150b17284e20386b62c");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("df6c12d23bea4150b17284e20386b62c", "root", "{}",
        "$6$rounds=40000$cgJF6ALr$U1Vsh7t6.W.2c75wiHbhy0sIoNZfA2EN1hNzkzqdUja6QtanPUihg73vM6uriWip698E8G0a6ktKHk.A/C135.",
        "1", "df6c12d23bea4150b17284e20386b62c", "28ae9fcf5b594066b758cfeb739376f6", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "df6c12d23bea4150b17284e20386b62c", "28ae9fcf5b594066b758cfeb739376f6",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "df6c12d23bea4150b17284e20386b62c", "df6c12d23bea4150b17284e20386b62c",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("df6c12d23bea4150b17284e20386b62c", "df6c12d23bea4150b17284e20386b62c",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_kafka", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "df6c12d23bea4150b17284e20386b62c", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("c7ad4dd10dba9bd8f629cc47f1cc52ee3ef41d1849d0a769e929f9e1ac6f18b3", "df6c12d23bea4150b17284e20386b62c",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"f354396edab349a0974115a681d65df4\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b655d9f42f51fe04742d6878a0f64b347555b5a7034f6974212b1edbe6b2405710e9fd075e07846c54a7c151f98ac72cc\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("83c296f306ce49f8bcca6a1a4040d218", "res_kms", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("c38aeeb4679a4658ac26d890def36272", "default", "{}", "Default project for domain", "1",
        "83c296f306ce49f8bcca6a1a4040d218");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("83c296f306ce49f8bcca6a1a4040d218", "root", "{}",
        "$6$rounds=40000$RtW/s5i4$yePaG6jIW5z3145eijG4Nl4NzruVrNsjP0Dl6CKPIOonWIQc3FCigQ/FTGilzkC1aDNkhEwenmlJOhYQEArIG0",
        "1", "83c296f306ce49f8bcca6a1a4040d218", "c38aeeb4679a4658ac26d890def36272", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "83c296f306ce49f8bcca6a1a4040d218", "c38aeeb4679a4658ac26d890def36272",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "83c296f306ce49f8bcca6a1a4040d218", "83c296f306ce49f8bcca6a1a4040d218",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("83c296f306ce49f8bcca6a1a4040d218", "83c296f306ce49f8bcca6a1a4040d218",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_kms", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "83c296f306ce49f8bcca6a1a4040d218", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("c1ace747bcabf02364841f8c6a980cd57ef40c36a3b1e1912f4034e0e6d1b8b6", "83c296f306ce49f8bcca6a1a4040d218",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"c11b601af38a4d2299274a5ae8d46165\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bc7fb7492003e381990011e1ccf9c4ecae5f3170472e0c4b4f98ec2f6074ef00f1614829b92ec07d8ae8d865845e73f88\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("febb3f14db8b4b63b2bcb855f51479ba", "res_logic-cdn", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("07b5533f7b1e4df8bdac68b4dd100426", "default", "{}", "Default project for domain", "1",
        "febb3f14db8b4b63b2bcb855f51479ba");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("febb3f14db8b4b63b2bcb855f51479ba", "root", "{}",
        "$6$rounds=40000$mXCjZKc0$ndel1oduCcIsRbnN3mfnVpTXNeTbNOSgk./j5m5cCXBLR1IT69eG9OMjJDtmQ5/6O6IGsxS50CTyLfDuiSVZJ1",
        "1", "febb3f14db8b4b63b2bcb855f51479ba", "07b5533f7b1e4df8bdac68b4dd100426", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "febb3f14db8b4b63b2bcb855f51479ba", "07b5533f7b1e4df8bdac68b4dd100426",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "febb3f14db8b4b63b2bcb855f51479ba", "febb3f14db8b4b63b2bcb855f51479ba",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("febb3f14db8b4b63b2bcb855f51479ba", "febb3f14db8b4b63b2bcb855f51479ba",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic-cdn", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "febb3f14db8b4b63b2bcb855f51479ba", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("c220d0f6ce6fec8b32c8273f8a6fc47fd74b3c3236be288e86ec671f108f112b", "febb3f14db8b4b63b2bcb855f51479ba",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"00ea6937abbf4ba2ad96e0ed392ea502\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bacfd6f336e3c90887b8b77166421f32e3b9b512f87d7f4d1527d3f7724b508f7bfc9f0205ca2e5311477ebc82450010f\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("4707620fd8e04ff7b1a0ae6021f9f952", "res_logical", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("2c87305cafaa4ddbb2b3b8a2b8b9c91b", "default", "{}", "Default project for domain", "1",
        "4707620fd8e04ff7b1a0ae6021f9f952");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("4707620fd8e04ff7b1a0ae6021f9f952", "root", "{}",
        "$6$rounds=40000$558L8u74$UgXh2f5SLpSFXvUICq6yeNVbGg8lmBX3AmccibS.f./FoPEk1MHhXqFVuejX.njIrWOLTaUpdm6UPCKEiJXCc/",
        "1", "4707620fd8e04ff7b1a0ae6021f9f952", "2c87305cafaa4ddbb2b3b8a2b8b9c91b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "4707620fd8e04ff7b1a0ae6021f9f952", "2c87305cafaa4ddbb2b3b8a2b8b9c91b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "4707620fd8e04ff7b1a0ae6021f9f952", "4707620fd8e04ff7b1a0ae6021f9f952",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("4707620fd8e04ff7b1a0ae6021f9f952", "4707620fd8e04ff7b1a0ae6021f9f952",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logical", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "4707620fd8e04ff7b1a0ae6021f9f952", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("fb67803abc72c4312b55ff397ceae34f489bcf585e56b48255c82f9810001803", "4707620fd8e04ff7b1a0ae6021f9f952",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"947aded1c60f4176bafa5f4f209e94dc\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b5d195104dd0ad2ec19f76a87ea628a21992f79262d247dfc49fe9de4a873414471cd65cd5f9a7e2869176b39177450ab\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("493e48ba3f3949e7b4d54e81f032cf49", "res_logical-mq", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("8144afa04763426a830c30b7c6101c97", "default", "{}", "Default project for domain", "1",
        "493e48ba3f3949e7b4d54e81f032cf49");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("493e48ba3f3949e7b4d54e81f032cf49", "root", "{}",
        "$6$rounds=40000$QVLfTaFV$dsgfyMf8Gw8FYqKlE/hJcJMc.DOdtnDC/OtF1xtP.TLtFezNQ0pa8flF5CJVQLB8DhAAiOySmfVY7OizLRCXq1",
        "1", "493e48ba3f3949e7b4d54e81f032cf49", "8144afa04763426a830c30b7c6101c97", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "493e48ba3f3949e7b4d54e81f032cf49", "8144afa04763426a830c30b7c6101c97",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "493e48ba3f3949e7b4d54e81f032cf49", "493e48ba3f3949e7b4d54e81f032cf49",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("493e48ba3f3949e7b4d54e81f032cf49", "493e48ba3f3949e7b4d54e81f032cf49",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logical-mq", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "493e48ba3f3949e7b4d54e81f032cf49", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("9432afb61c82f823db5e1b744268d9668b982bd67a4350ad8f225aafb6b908ba", "493e48ba3f3949e7b4d54e81f032cf49",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"f9f2b83c324149e7ae7814fdbb4c4128\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bd0e453286461432ccd70c142a29a350304e508bb1320032c51186c3fdb257cb25dca49eb024524e84bfc6164b87102fb\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("39cd50ad63574d9596569b8c7abc2857", "res_logical-organization", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("5c43d194a0b94b2baea2bd3872575bd9", "default", "{}", "Default project for domain", "1",
        "39cd50ad63574d9596569b8c7abc2857");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("39cd50ad63574d9596569b8c7abc2857", "root", "{}",
        "$6$rounds=40000$KIyePsRy$9zAohH/lmRLMVumCeFJ/AmNEYQWUijcQw8yynmGHmF01pfg5M7DkT9J0IC/px1jdefLg6vgpdW.IODf0Ziewa/",
        "1", "39cd50ad63574d9596569b8c7abc2857", "5c43d194a0b94b2baea2bd3872575bd9", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "39cd50ad63574d9596569b8c7abc2857", "5c43d194a0b94b2baea2bd3872575bd9",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "39cd50ad63574d9596569b8c7abc2857", "39cd50ad63574d9596569b8c7abc2857",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("39cd50ad63574d9596569b8c7abc2857", "39cd50ad63574d9596569b8c7abc2857",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logical-organization", "2019-12-30 15:33:31", "individual",
        "{\"description\":{\"codeChecked\":true}}", "39cd50ad63574d9596569b8c7abc2857", "2019-12-30 15:33:31",
        "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("b24d31c7e01166622847ee203ea3ff63dc5db8219fce727959972169a25db5ab", "39cd50ad63574d9596569b8c7abc2857",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"e8ebc7a6aae44979901f5bbd9c0bdc92\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b70e4f557242a1ae91f468045befd9b4479204c90dfc1f66ec52673722158b49745850941bdab356dd4734065d56957d5\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("2c17e1e645c1438e910282cba0e33803", "res_logical_blb", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("fd733be4610847acbd72088ce8ca9174", "default", "{}", "Default project for domain", "1",
        "2c17e1e645c1438e910282cba0e33803");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("2c17e1e645c1438e910282cba0e33803", "root", "{}",
        "$6$rounds=40000$FIVRxND/$iIskTpYBf/WfpTTiWtHj4zPTwsM18PZ80NyjV1GBl6.t4OzEGtp4vmr7tb64ExiPxOxSWJe3ncZh.9Q9BJlUG/",
        "1", "2c17e1e645c1438e910282cba0e33803", "fd733be4610847acbd72088ce8ca9174", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "2c17e1e645c1438e910282cba0e33803", "fd733be4610847acbd72088ce8ca9174",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "2c17e1e645c1438e910282cba0e33803", "2c17e1e645c1438e910282cba0e33803",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("2c17e1e645c1438e910282cba0e33803", "2c17e1e645c1438e910282cba0e33803",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logical_blb", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "2c17e1e645c1438e910282cba0e33803", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("6950ebd4d022f45953e94a8226c09aecfd9f9ccfd5797da1215821ab94168f6a", "2c17e1e645c1438e910282cba0e33803",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"b70370f6dfca4f6b8dac944252cb76fa\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bd639d77064334cd6008c6db0c9e251c513c4ce986e44d7b682623cdc5861322462e2081b0864a409627ef4d53637931b\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("6f7e2b068dfb486cbc329dd368ec3d53", "res_logical_bos", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("ea2f7d3a8fab497f8c519cf97f1db7a3", "default", "{}", "Default project for domain", "1",
        "6f7e2b068dfb486cbc329dd368ec3d53");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("6f7e2b068dfb486cbc329dd368ec3d53", "root", "{}",
        "$6$rounds=40000$V4yKcGpm$d.6FZ8gkCq5LKvqTadwqwhWXEZOu6ppk8YAwkinoRv0h7zpcJjUmac4UHznGtLcJ4d4fkWO0QTmnm.ICm7kCX.",
        "1", "6f7e2b068dfb486cbc329dd368ec3d53", "ea2f7d3a8fab497f8c519cf97f1db7a3", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "6f7e2b068dfb486cbc329dd368ec3d53", "ea2f7d3a8fab497f8c519cf97f1db7a3",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "6f7e2b068dfb486cbc329dd368ec3d53", "6f7e2b068dfb486cbc329dd368ec3d53",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("6f7e2b068dfb486cbc329dd368ec3d53", "6f7e2b068dfb486cbc329dd368ec3d53",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logical_bos", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "6f7e2b068dfb486cbc329dd368ec3d53", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("f091ff11328048c5c53ea38dae920470198fb89d57bd19cda2b95ecdd5ddb99b", "6f7e2b068dfb486cbc329dd368ec3d53",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"83510f0604bd45f59c97d95b2dcb8950\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b83504747535549b3869f71b6a89215da7e547db062f61bbdf8796aa3b827d6aa0d2b828da3b2a5e158a2610525b01501\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("85241b427ee649db8700e9cb7f684779", "res_logical_eip", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("e24565225bfd404bbf223f00a688f585", "default", "{}", "Default project for domain", "1",
        "85241b427ee649db8700e9cb7f684779");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("85241b427ee649db8700e9cb7f684779", "root", "{}",
        "$6$rounds=40000$EFADOzdM$Ofnq1xc4e0jHM1fUuVYaBN/hJuLqGHyYqDe4sY6zuOS4Y2/q694.YfwbmdA86mFewIt.sGO5ZjIEjjb0pA01j0",
        "1", "85241b427ee649db8700e9cb7f684779", "e24565225bfd404bbf223f00a688f585", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "85241b427ee649db8700e9cb7f684779", "e24565225bfd404bbf223f00a688f585",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "85241b427ee649db8700e9cb7f684779", "85241b427ee649db8700e9cb7f684779",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("85241b427ee649db8700e9cb7f684779", "85241b427ee649db8700e9cb7f684779",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logical_eip", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "85241b427ee649db8700e9cb7f684779", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("03b95b428b4e11fbe46a502cff4d8f1f3550ec405e8d1b69f06ae2826919e070", "85241b427ee649db8700e9cb7f684779",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"6a7cb049fdc24e31a409779b0713cd46\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b23b9e79dbbb165ab439166ccf1d79326a456bdde7bcd35809fc99ba1cddf5151bfd6c41d1e373f1e0512ac0e01b98c0d\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("11a748913cba4312bd5640f2a8b08250", "res_logical_project", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("2bbddcd3db974ac2ab3d7d835e8a4982", "default", "{}", "Default project for domain", "1",
        "11a748913cba4312bd5640f2a8b08250");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("11a748913cba4312bd5640f2a8b08250", "root", "{}",
        "$6$rounds=40000$7qwa6CrH$gdUDFUKlGkce9/wTvyhRhRLTsO/h145csYBOOd2.u8NkRF9tOWmQlQHXb0v.D/SNZ9whmu0xy5/8TOw.qtOWM/",
        "1", "11a748913cba4312bd5640f2a8b08250", "2bbddcd3db974ac2ab3d7d835e8a4982", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "11a748913cba4312bd5640f2a8b08250", "2bbddcd3db974ac2ab3d7d835e8a4982",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "11a748913cba4312bd5640f2a8b08250", "11a748913cba4312bd5640f2a8b08250",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("11a748913cba4312bd5640f2a8b08250", "11a748913cba4312bd5640f2a8b08250",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logical_project", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "11a748913cba4312bd5640f2a8b08250", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("6b9dd7393956c1b750aff22942d7c259dbd16bd5416b00c730077dbb9b829b56", "11a748913cba4312bd5640f2a8b08250",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"5fa5fe234ba54398bc1c5b76f31952cb\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb718af29a4a1b5090e05c92b9ef8f417a15de0cc6c520cc9732742531be2c86a746451f32bf00dad21284d7d841570fa\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("cd459e35bec2452bb7fb697b2da25946", "res_logical_quota", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("aa066c8ae2904706987ac5f7075947d7", "default", "{}", "Default project for domain", "1",
        "cd459e35bec2452bb7fb697b2da25946");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("cd459e35bec2452bb7fb697b2da25946", "root", "{}",
        "$6$rounds=40000$34r.qDmW$j/vuKeRnezOeLAfMfoh527yLlxfp.UR5RI/dyrjSC711xeQPHTM8FyPi.6Ut.nl0jIHsf/X4WBCvq3F4VBeRg1",
        "1", "cd459e35bec2452bb7fb697b2da25946", "aa066c8ae2904706987ac5f7075947d7", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "cd459e35bec2452bb7fb697b2da25946", "aa066c8ae2904706987ac5f7075947d7",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "cd459e35bec2452bb7fb697b2da25946", "cd459e35bec2452bb7fb697b2da25946",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("cd459e35bec2452bb7fb697b2da25946", "cd459e35bec2452bb7fb697b2da25946",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logical_quota", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "cd459e35bec2452bb7fb697b2da25946", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("43344e7fa49b2b4b45bdd3e23d187581770f33e0a9d731036bd7d82452324e9c", "cd459e35bec2452bb7fb697b2da25946",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"73980c6981084bd7afe7125c9ed43c64\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b7225dd1a4c67efdeff8ca2d8d0124d6b0d2c3423ba52f9d6f30c9301774c265b192fc74d2a94a76259338f4e52e7c5f5\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("54514f8adaee4ae29b98d751ac45fd79", "res_logical_vpc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("5acbc2059f2d4a9da9dcf8c9fcc4b135", "default", "{}", "Default project for domain", "1",
        "54514f8adaee4ae29b98d751ac45fd79");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("54514f8adaee4ae29b98d751ac45fd79", "root", "{}",
        "$6$rounds=40000$nSiCSr.l$DRNivA4ZPOtGeUbYON04MMHzS/sc6NLdW.A5IDgujrU9tWmENPIA.VIRownDF9CszFoUfAer82h06UpeVwAz0/",
        "1", "54514f8adaee4ae29b98d751ac45fd79", "5acbc2059f2d4a9da9dcf8c9fcc4b135", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "54514f8adaee4ae29b98d751ac45fd79", "5acbc2059f2d4a9da9dcf8c9fcc4b135",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "54514f8adaee4ae29b98d751ac45fd79", "54514f8adaee4ae29b98d751ac45fd79",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("54514f8adaee4ae29b98d751ac45fd79", "54514f8adaee4ae29b98d751ac45fd79",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logical_vpc", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "54514f8adaee4ae29b98d751ac45fd79", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("874d997dbfa50117eb086fe4a272047c2f1a391f3219e771a14fabd14423d8e5", "54514f8adaee4ae29b98d751ac45fd79",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"533e78270f434c86a96f5527e50bfcae\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b5af6beac6f6a5b3e8800a470c6665ced974d5159464f88d270a1383b12c9489a1d27c5f9cb4cd76bb5345097003e34ff\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("a4df01df47064dfca402799805d0b494", "res_logic_bbc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("9a8a5fe98f314a5b969e54f45d747fe1", "default", "{}", "Default project for domain", "1",
        "a4df01df47064dfca402799805d0b494");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("a4df01df47064dfca402799805d0b494", "root", "{}",
        "$6$rounds=40000$20X28unC$PMmQBiNwSmmG/aK9QGSB/kTZELZUmer9oucOtDXLEZG.6e.BfORFKTupSCZiAewCKuhNepPPlYX2iDcrsVAXo0",
        "1", "a4df01df47064dfca402799805d0b494", "9a8a5fe98f314a5b969e54f45d747fe1", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "a4df01df47064dfca402799805d0b494", "9a8a5fe98f314a5b969e54f45d747fe1",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "a4df01df47064dfca402799805d0b494", "a4df01df47064dfca402799805d0b494",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("a4df01df47064dfca402799805d0b494", "a4df01df47064dfca402799805d0b494",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_bbc", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "a4df01df47064dfca402799805d0b494", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("fea956a0e31d912597ae1ca4beabb61cc1441cae5aba16ae95b65b0225ffc988", "a4df01df47064dfca402799805d0b494",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"2ae44d641af14789b5f4b21abb228889\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b83713ef13027636b19ae495aa9c944f0512717cae5563e8b8fb88cdbf594eccd8b296fc7782021a61d2ec0bfb8a5d0b9\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("bdb7b7b5c6b84b37bc596d9602695795", "res_logic_bcc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("c5f7b32ba6cb435ea3a2cb5b9b6c8021", "default", "{}", "Default project for domain", "1",
        "bdb7b7b5c6b84b37bc596d9602695795");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("bdb7b7b5c6b84b37bc596d9602695795", "root", "{}",
        "$6$rounds=40000$qDFF2QvG$gT85eeLqKTjTejCcWO2sUEKtcB4uNyXPxcul6t0jxrgLdjuOR4KeOJU.tm6sJrjbqShNJ4pFj3itl0yukgj7O.",
        "1", "bdb7b7b5c6b84b37bc596d9602695795", "c5f7b32ba6cb435ea3a2cb5b9b6c8021", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "bdb7b7b5c6b84b37bc596d9602695795", "c5f7b32ba6cb435ea3a2cb5b9b6c8021",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "bdb7b7b5c6b84b37bc596d9602695795", "bdb7b7b5c6b84b37bc596d9602695795",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("bdb7b7b5c6b84b37bc596d9602695795", "bdb7b7b5c6b84b37bc596d9602695795",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_bcc", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "bdb7b7b5c6b84b37bc596d9602695795", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("06e0a79712d5898ca857286b6f7e10a1e0ade51f59c61d04cc1f13f208e58787", "bdb7b7b5c6b84b37bc596d9602695795",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"81ab17477df54230889f3ef4065f3047\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bfd57fbc52e880cbdbda58b42a700303e62088a982c9757d18fccd629a161c46750a393d18b2bd63f5f356e743a35c6d1\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("73629460a15b4db4ad8a0bd06f76da96", "res_logic_bms", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("cb05004b58dd41d3941bdb1f418970aa", "default", "{}", "Default project for domain", "1",
        "73629460a15b4db4ad8a0bd06f76da96");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("73629460a15b4db4ad8a0bd06f76da96", "root", "{}",
        "$6$rounds=40000$jNZI3Tak$M7Qh9jCSIypYbgCTlK9G7VKw3N5h4TJ18XideL4adfzzA5QioQ60LBbHjjsrs0RnAmZnFkTdiqKU3XmPiyd2O/",
        "1", "73629460a15b4db4ad8a0bd06f76da96", "cb05004b58dd41d3941bdb1f418970aa", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "73629460a15b4db4ad8a0bd06f76da96", "cb05004b58dd41d3941bdb1f418970aa",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "73629460a15b4db4ad8a0bd06f76da96", "73629460a15b4db4ad8a0bd06f76da96",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("73629460a15b4db4ad8a0bd06f76da96", "73629460a15b4db4ad8a0bd06f76da96",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_bms", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "73629460a15b4db4ad8a0bd06f76da96", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("2720b324101b5a7a1a2e3640def3bb46346396b20b905b6a42e5f9467a36cf55", "73629460a15b4db4ad8a0bd06f76da96",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"d0f60fde55894178a02d5b3355878eea\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bbcce0148be6d508dfb629e4a6c86f1e5edbc3aad8dae0c87a5949ab3ce5b2891b9f4175b2f8842e407643ee7f971b9ff\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("3870816449f14f049fa4d7e51e229297", "res_logic_drds", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("5b17e42dad114b38be86902bbf69c400", "default", "{}", "Default project for domain", "1",
        "3870816449f14f049fa4d7e51e229297");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("3870816449f14f049fa4d7e51e229297", "root", "{}",
        "$6$rounds=40000$yoVd2Pbm$iygppX.FXKe6qamA4kS1z0LbRlklNLDqBm0DJVXnz2so8d.JsbDlX2H5F14hB0Ni2xi5cJ0ly14pwErxSeldi.",
        "1", "3870816449f14f049fa4d7e51e229297", "5b17e42dad114b38be86902bbf69c400", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "3870816449f14f049fa4d7e51e229297", "5b17e42dad114b38be86902bbf69c400",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "3870816449f14f049fa4d7e51e229297", "3870816449f14f049fa4d7e51e229297",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("3870816449f14f049fa4d7e51e229297", "3870816449f14f049fa4d7e51e229297",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_drds", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "3870816449f14f049fa4d7e51e229297", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("49f2fe204cb2ba60a65b2564455c24df5f0f1b8139a7e631272557e029e973c1", "3870816449f14f049fa4d7e51e229297",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"c1a75a83fb9e4c61b540181773fbb2e5\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bc228dbca9f224fe370110790610c66aa6e39c8bf14ed6af97ced62f0d4cb3c09d199dfa083775096454ff24b1738c144\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("33bef63f618a4a7cb876b5be02d11563", "res_logic_home", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("f550dc237b5144629e6cb3cc5edb6c47", "default", "{}", "Default project for domain", "1",
        "33bef63f618a4a7cb876b5be02d11563");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("33bef63f618a4a7cb876b5be02d11563", "root", "{}",
        "$6$rounds=40000$Nw3ZR6bG$qkkBkaExde9A/kxWUGNEMla/gg4FeVbWOljHCvQF7.LAmIPB9E/BVOQTbM3OjAFztDSufq9emWc03zZoCctn50",
        "1", "33bef63f618a4a7cb876b5be02d11563", "f550dc237b5144629e6cb3cc5edb6c47", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "33bef63f618a4a7cb876b5be02d11563", "f550dc237b5144629e6cb3cc5edb6c47",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "33bef63f618a4a7cb876b5be02d11563", "33bef63f618a4a7cb876b5be02d11563",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("33bef63f618a4a7cb876b5be02d11563", "33bef63f618a4a7cb876b5be02d11563",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_home", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "33bef63f618a4a7cb876b5be02d11563", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("f403c1ada31bcb26583ee45699fe4c8b1af08bc794e65bcd27c2c0a05f6f53a6", "33bef63f618a4a7cb876b5be02d11563",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"f837545f2ba04b10bacf66f992f34358\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bae66be7ae4a8f2fdf650d18f2e36e8531e11859ce40d0ce613b11b0ce333520afc26c87d0c2ba4064b48ecaea23ac1bd\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("1ac17bae6a22463f8a7d6d9e3943c219", "res_logic_image", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("3db6648b55ee40988bf8a0c61a4c5ffc", "default", "{}", "Default project for domain", "1",
        "1ac17bae6a22463f8a7d6d9e3943c219");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("1ac17bae6a22463f8a7d6d9e3943c219", "root", "{}",
        "$6$rounds=40000$e1Cp5q2f$AyJyCOTsklryptjMAVNP05eB44/CZl9tAqaXEX50hqJYtfR9A1SsWpxexgMMyD.fY0GUHq7jE/1UOBdSXf9x00",
        "1", "1ac17bae6a22463f8a7d6d9e3943c219", "3db6648b55ee40988bf8a0c61a4c5ffc", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "1ac17bae6a22463f8a7d6d9e3943c219", "3db6648b55ee40988bf8a0c61a4c5ffc",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "1ac17bae6a22463f8a7d6d9e3943c219", "1ac17bae6a22463f8a7d6d9e3943c219",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("1ac17bae6a22463f8a7d6d9e3943c219", "1ac17bae6a22463f8a7d6d9e3943c219",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_image", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "1ac17bae6a22463f8a7d6d9e3943c219", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("f7df2c0461c44391398903e73cbb65b3b8d6b080f43ffa32e052a60d25e3c065", "1ac17bae6a22463f8a7d6d9e3943c219",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"4ea2797f4d53403e82f913b2fb9a3eaf\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b74b5780a2533e4b60994c3ac84cce2803619e1917e4212b3e63d7859667b88cd619e4005ef33f7ce671f2cafd46ca877\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("585e59597e4444bf8a154c51081bc117", "res_logic_imm", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("fda4b3fd74b942259b7ec28fe46b9fec", "default", "{}", "Default project for domain", "1",
        "585e59597e4444bf8a154c51081bc117");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("585e59597e4444bf8a154c51081bc117", "root", "{}",
        "$6$rounds=40000$stWK0vAx$xTnr1VJD7JftFIVFbyp8hxXmhM4juuWg4S0gaHwa4vw6g4NJCLdzW3Y/aNOG5C1WBI68Lx6IiJxtD7xH9XQQL1",
        "1", "585e59597e4444bf8a154c51081bc117", "fda4b3fd74b942259b7ec28fe46b9fec", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "585e59597e4444bf8a154c51081bc117", "fda4b3fd74b942259b7ec28fe46b9fec",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "585e59597e4444bf8a154c51081bc117", "585e59597e4444bf8a154c51081bc117",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("585e59597e4444bf8a154c51081bc117", "585e59597e4444bf8a154c51081bc117",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_imm", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "585e59597e4444bf8a154c51081bc117", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("9e1c27fd88834b3fa90e666e58a52cfb99f5f11ec54992e77ff8016bff6a0771", "585e59597e4444bf8a154c51081bc117",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"49b0c2bb4ad94f139db163834e28fdd2\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bbfaace52ae0efcd0a66fd34aac45c6b4c25e876537d914a944012d9ea44b0c85a8f7ee050f422acf3809a1302063c062\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("713e0c01649840999aeb20a282079885", "res_logic_mongodb", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("7703189d475249b688277c90f944e7c0", "default", "{}", "Default project for domain", "1",
        "713e0c01649840999aeb20a282079885");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("713e0c01649840999aeb20a282079885", "root", "{}",
        "$6$rounds=40000$5KVdXtNq$csAUWfyDDr/tjQWT37sKRe0pCxmoZPkTsMbTB4FObBAbhSB3.Wl6JJwbqWCqESxYZY9h/WyDyHRlhJqN/ORve0",
        "1", "713e0c01649840999aeb20a282079885", "7703189d475249b688277c90f944e7c0", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "713e0c01649840999aeb20a282079885", "7703189d475249b688277c90f944e7c0",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "713e0c01649840999aeb20a282079885", "713e0c01649840999aeb20a282079885",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("713e0c01649840999aeb20a282079885", "713e0c01649840999aeb20a282079885",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_mongodb", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "713e0c01649840999aeb20a282079885", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("2be6d95c982e615d8db9fdfe7b538f54cd6b9e7e880c05661c2e2ece227ed6af", "713e0c01649840999aeb20a282079885",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"298047cfab674c2eb1fe209616e1b9b8\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bd05ca7974f3ac56f843478d2c67c1490ed3dc377897ae1e9f47893cb51bea6a332208bfc3057dbba6f55a7acdf49a57e\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("c194c58710e74bebb9ef86ef7b195981", "res_logic_mtp", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("c0da590a2bd3443d9aa990a6cef8eba1", "default", "{}", "Default project for domain", "1",
        "c194c58710e74bebb9ef86ef7b195981");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("c194c58710e74bebb9ef86ef7b195981", "root", "{}",
        "$6$rounds=40000$MW8CxuPP$5iPJ5gzDPbd7RHxYWZyVesZzhzGDvbMlMMdZQSnG5rT53ouNaliHrHp3sIyoHhtO30YvbTweMGMCJlczb4PhV0",
        "1", "c194c58710e74bebb9ef86ef7b195981", "c0da590a2bd3443d9aa990a6cef8eba1", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "c194c58710e74bebb9ef86ef7b195981", "c0da590a2bd3443d9aa990a6cef8eba1",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "c194c58710e74bebb9ef86ef7b195981", "c194c58710e74bebb9ef86ef7b195981",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("c194c58710e74bebb9ef86ef7b195981", "c194c58710e74bebb9ef86ef7b195981",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_mtp", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "c194c58710e74bebb9ef86ef7b195981", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("700473cec7852e4012cca1779005fe3c8ccaebcf25f8c0011b544edc12cd2514", "c194c58710e74bebb9ef86ef7b195981",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"975338058cb44a2ba695398bf6257094\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b0de036bef7cdd0f0654ee70c7b3e4b45ed1eb41c8e30e59935e2e38e0b2661203382c6bf9b766841805ee7c260c3ac00\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("c4c42799ef0a4ff2a72f42a32684243e", "res_logic_pcdn", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("8f2cfb7ccaaa4c50a05a52835761a44b", "default", "{}", "Default project for domain", "1",
        "c4c42799ef0a4ff2a72f42a32684243e");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("c4c42799ef0a4ff2a72f42a32684243e", "root", "{}",
        "$6$rounds=40000$IJcfTCOa$tRojXEusrOyl3rnoQ5KYNIROkbYg9S1VfBY9VuchnDn2u/8ymS9LTA1oknsm4/8R8gBqrYxYMyWZrCckTdPRw1",
        "1", "c4c42799ef0a4ff2a72f42a32684243e", "8f2cfb7ccaaa4c50a05a52835761a44b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "c4c42799ef0a4ff2a72f42a32684243e", "8f2cfb7ccaaa4c50a05a52835761a44b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "c4c42799ef0a4ff2a72f42a32684243e", "c4c42799ef0a4ff2a72f42a32684243e",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("c4c42799ef0a4ff2a72f42a32684243e", "c4c42799ef0a4ff2a72f42a32684243e",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_pcdn", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "c4c42799ef0a4ff2a72f42a32684243e", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("86f617d88be9c245bdc88265a8bf7c2373b1c66bf0d5bab89617f8d3cd4b8e34", "c4c42799ef0a4ff2a72f42a32684243e",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"e744ba993a5046308b7ec4bd36d3c7d1\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b2b754590e8ed97521ab18b16cc2fa17b0f634eec13eba6b3f186429b757722ec4078345b1e00d252e49b4e979b557301\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("46b69257e76148a4811f2c8a0986c9b1", "res_logic_rds", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("56fb24770a8c4f96b2c9add07df96028", "default", "{}", "Default project for domain", "1",
        "46b69257e76148a4811f2c8a0986c9b1");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("46b69257e76148a4811f2c8a0986c9b1", "root", "{}",
        "$6$rounds=40000$A/GIzjOf$2AG.u30ayo3Ski4LWQGNmG2ikCqUbuIK4dUH1rbO9PGzt8xs562BFwou7p3ULGXhQMl9Y2W4RkN5U7fK7vQDH0",
        "1", "46b69257e76148a4811f2c8a0986c9b1", "56fb24770a8c4f96b2c9add07df96028", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "46b69257e76148a4811f2c8a0986c9b1", "56fb24770a8c4f96b2c9add07df96028",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "46b69257e76148a4811f2c8a0986c9b1", "46b69257e76148a4811f2c8a0986c9b1",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("46b69257e76148a4811f2c8a0986c9b1", "46b69257e76148a4811f2c8a0986c9b1",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_rds", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "46b69257e76148a4811f2c8a0986c9b1", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("fc26ecd2236bd98b8a2e37e216b221de7410e593086f70502b544a21cecdf751", "46b69257e76148a4811f2c8a0986c9b1",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"1f736a60692e42adb8bc50b79862b296\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b45582e5b48282111388d3cc08c6f1d83c76f776d57f20d09b3000c879557d2c88f6081d1668496a7e7c68951492ab0a5\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("c0b0f8eed33c46409311b6b3e553407d", "res_logic_recycle", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("cc0412845c7348cf903efcbde19fe1fb", "default", "{}", "Default project for domain", "1",
        "c0b0f8eed33c46409311b6b3e553407d");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("c0b0f8eed33c46409311b6b3e553407d", "root", "{}",
        "$6$rounds=40000$nfn.7iqn$BjtfLtaiYVsQciU//dnvBnBYxluPSeCROXHViy8aA.JSzfRx/il0FZw./t6HM/Eqvv/9ZMsaclcO5MKBBAppK0",
        "1", "c0b0f8eed33c46409311b6b3e553407d", "cc0412845c7348cf903efcbde19fe1fb", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "c0b0f8eed33c46409311b6b3e553407d", "cc0412845c7348cf903efcbde19fe1fb",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "c0b0f8eed33c46409311b6b3e553407d", "c0b0f8eed33c46409311b6b3e553407d",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("c0b0f8eed33c46409311b6b3e553407d", "c0b0f8eed33c46409311b6b3e553407d",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_recycle", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "c0b0f8eed33c46409311b6b3e553407d", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("cba30e91ec01dfa937fca7fb9ca79c292d6448ebd7161daec751f81705e564c9", "c0b0f8eed33c46409311b6b3e553407d",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"ff99427adcd34b4cbd3919c5e3b24306\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bed82a4cb7013a6b9412a55a52487d52ba7a955333007ada9b92f35809518bdd6cc41c8b42c465651ad25c5aa9d7a63ce\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("a3901a3eb5e04727bb8d6a8e73af343f", "res_logic_scs", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("95de59106ff54c1c8e05926f13eef58b", "default", "{}", "Default project for domain", "1",
        "a3901a3eb5e04727bb8d6a8e73af343f");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("a3901a3eb5e04727bb8d6a8e73af343f", "root", "{}",
        "$6$rounds=40000$rz8jzRC9$3FtK4wA10WKbzlx/MNu6VM3ofm0jhCZZYjvGtdiwULvsfEXbmQn/XoRvkNg5DNP5kVzslLq0eOrPUAsCnJ4QV1",
        "1", "a3901a3eb5e04727bb8d6a8e73af343f", "95de59106ff54c1c8e05926f13eef58b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "a3901a3eb5e04727bb8d6a8e73af343f", "95de59106ff54c1c8e05926f13eef58b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "a3901a3eb5e04727bb8d6a8e73af343f", "a3901a3eb5e04727bb8d6a8e73af343f",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("a3901a3eb5e04727bb8d6a8e73af343f", "a3901a3eb5e04727bb8d6a8e73af343f",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_scs", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "a3901a3eb5e04727bb8d6a8e73af343f", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("fc9c1648d206fe93e0c4716f8d5983209ed8427948a249805d70d24dd2cbe9bf", "a3901a3eb5e04727bb8d6a8e73af343f",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"b92c3ccf2925459f8086ba53e7b46d26\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b867223d7ce36b0b5997770db0e1e05b9d70a6641aa78d0525642fff52e4f86117e39ef0fdd5540692dd56cef8494ae41\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("2c7ea337f3414967b6093b3ff3dc5c0c", "res_logic_snapshot", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("bfc25e3bfc48482ea723dccf2a9d9b81", "default", "{}", "Default project for domain", "1",
        "2c7ea337f3414967b6093b3ff3dc5c0c");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("2c7ea337f3414967b6093b3ff3dc5c0c", "root", "{}",
        "$6$rounds=40000$Nhg2avoR$dyUNJCG91i.pF6yIWju.467XE7/ZD9UDlOCJ8huBBla3H.ON6gIt8BoNH5wmIoURLNMZwXJDjHWtmxDHLmpoV1",
        "1", "2c7ea337f3414967b6093b3ff3dc5c0c", "bfc25e3bfc48482ea723dccf2a9d9b81", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "2c7ea337f3414967b6093b3ff3dc5c0c", "bfc25e3bfc48482ea723dccf2a9d9b81",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "2c7ea337f3414967b6093b3ff3dc5c0c", "2c7ea337f3414967b6093b3ff3dc5c0c",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("2c7ea337f3414967b6093b3ff3dc5c0c", "2c7ea337f3414967b6093b3ff3dc5c0c",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_snapshot", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "2c7ea337f3414967b6093b3ff3dc5c0c", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("587b403e6fb120b82038874f17361f8fbf09693027ee22dc42151211462b56bb", "2c7ea337f3414967b6093b3ff3dc5c0c",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"0290ac800485432dad33a36b09b4ca5f\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b138c9538a0c8acabc687146635e00cbf1b7976bb0233cb0ad95d58e517e8f5325e06067dc9f17199ec90a6f1b46fa538\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("a726136393af4962834f138a0e18ad73", "res_logic_volume", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("8ac787df5769491d88580d1e1426ac38", "default", "{}", "Default project for domain", "1",
        "a726136393af4962834f138a0e18ad73");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("a726136393af4962834f138a0e18ad73", "root", "{}",
        "$6$rounds=40000$pDBkW1Xu$rmifjf8X7m6HjJfb0dfKIW40fEhu3q60sO.xi4hmT5crlmAdRfyv9dg5IORSmZpTIEQNN6JyiyWtGttnDT6MM/",
        "1", "a726136393af4962834f138a0e18ad73", "8ac787df5769491d88580d1e1426ac38", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "a726136393af4962834f138a0e18ad73", "8ac787df5769491d88580d1e1426ac38",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "a726136393af4962834f138a0e18ad73", "a726136393af4962834f138a0e18ad73",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("a726136393af4962834f138a0e18ad73", "a726136393af4962834f138a0e18ad73",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_volume", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "a726136393af4962834f138a0e18ad73", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("99f698513fdb32e8d7f82ad0ecb493d06040581af632e4ff4b0fce776cdee9fe", "a726136393af4962834f138a0e18ad73",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"f2ac8ef25f194c699857b7721d5ed331\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bf239e8f07dd6408ca13068cbc8ef4145a758b8ba5c122aa046894d09439383e9aa647b26aae46ddf674e80e8b0c15993\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("41f9aedd055e48ca8c9484b5ba5b3f7f", "res_log_trace", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("1d2168995bef47d9b46d844b2fd682ad", "default", "{}", "Default project for domain", "1",
        "41f9aedd055e48ca8c9484b5ba5b3f7f");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("41f9aedd055e48ca8c9484b5ba5b3f7f", "root", "{}",
        "$6$rounds=40000$1CpsEVU5$YuEnUIRw4xqJTRXQpRh2TTVkpyy30RMpf/VDDHDRTU6QdrKvp/4RKASXq6mU08uSprdUbO3yBGzAb/BRLdY1n1",
        "1", "41f9aedd055e48ca8c9484b5ba5b3f7f", "1d2168995bef47d9b46d844b2fd682ad", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "41f9aedd055e48ca8c9484b5ba5b3f7f", "1d2168995bef47d9b46d844b2fd682ad",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "41f9aedd055e48ca8c9484b5ba5b3f7f", "41f9aedd055e48ca8c9484b5ba5b3f7f",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("41f9aedd055e48ca8c9484b5ba5b3f7f", "41f9aedd055e48ca8c9484b5ba5b3f7f",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_log_trace", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "41f9aedd055e48ca8c9484b5ba5b3f7f", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("1882db92b6cbcfc38ee5d911986db59a69fa27f23072be25041c2371cff20c59", "41f9aedd055e48ca8c9484b5ba5b3f7f",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"c2784456202a4a7e9574eb970afe3356\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b4a28dab165dbf629c9df3cc56a8f73d7b004254a2ef3cb79963280a359cbb209462f832129384f4bdec4bc2d94a87d0b\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("533522a8a4084bc295e023d4d92fbbda", "res_log_trail", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("f7c5e4313ff04696a94bba6ed4eb7d1f", "default", "{}", "Default project for domain", "1",
        "533522a8a4084bc295e023d4d92fbbda");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("533522a8a4084bc295e023d4d92fbbda", "root", "{}",
        "$6$rounds=40000$FmsI14QT$6L95VqhlP3vmqADTVnmaGujjLvOBWSVHmb5HyfxPpTw65zqmyuo60K6BzV/3ifT.ewLKb4A7KYVD4JWX4xpzw1",
        "1", "533522a8a4084bc295e023d4d92fbbda", "f7c5e4313ff04696a94bba6ed4eb7d1f", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "533522a8a4084bc295e023d4d92fbbda", "f7c5e4313ff04696a94bba6ed4eb7d1f",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "533522a8a4084bc295e023d4d92fbbda", "533522a8a4084bc295e023d4d92fbbda",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("533522a8a4084bc295e023d4d92fbbda", "533522a8a4084bc295e023d4d92fbbda",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_log_trail", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "533522a8a4084bc295e023d4d92fbbda", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0a790bd092e31e9c92c68b199e1ed0fb49503fbd30d092fb31072d8396f0cccc", "533522a8a4084bc295e023d4d92fbbda",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"0d3d7c9fab2b499bbd833a7679f32b23\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b80a5dfdadcf1d254e82a3bdb112c4be7bd62cfd4f9ac001add45c02b6edf276466497443237ce141dec77c7c1579fe54\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("8e7b2a81ec19403b8fee6a40b2b8f096", "res_lps", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("41230f9118cb42e6be8b9a09e2cb7959", "default", "{}", "Default project for domain", "1",
        "8e7b2a81ec19403b8fee6a40b2b8f096");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("8e7b2a81ec19403b8fee6a40b2b8f096", "root", "{}",
        "$6$rounds=40000$XvnVtURe$8rnMjrBeJBAmQk6CxPIGVOn0vMpfLRXrB/aXXiV1dEEsnsfjM32cVCXpoxyJNKWubUVyfyK3c/maejJJNTnVd0",
        "1", "8e7b2a81ec19403b8fee6a40b2b8f096", "41230f9118cb42e6be8b9a09e2cb7959", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "8e7b2a81ec19403b8fee6a40b2b8f096", "41230f9118cb42e6be8b9a09e2cb7959",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "8e7b2a81ec19403b8fee6a40b2b8f096", "8e7b2a81ec19403b8fee6a40b2b8f096",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("8e7b2a81ec19403b8fee6a40b2b8f096", "8e7b2a81ec19403b8fee6a40b2b8f096",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_lps", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "8e7b2a81ec19403b8fee6a40b2b8f096", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8159a85a4abda0ca8bd1658ef3b5ec4cfc1964dad1e889ee828ed81db10354da", "8e7b2a81ec19403b8fee6a40b2b8f096",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"0b1676d8dab9453d81f59870e34df532\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b3bee46b3a21bdb5d47f01d5cb6e1c566c0519279d69888609590ce7d033fd4954591818272e2cdd2318d1bbc91dd24d6\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("17f00a75067c4d24a66e0cf350b5d9c7", "res_lss", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("0b5bd9b559d143a6ae8d1eceb72cd061", "default", "{}", "Default project for domain", "1",
        "17f00a75067c4d24a66e0cf350b5d9c7");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("17f00a75067c4d24a66e0cf350b5d9c7", "root", "{}",
        "$6$rounds=40000$4R/FvqD0$nI6BFWZW7qp.x3yz7Wd7uqDGHlWsaLiC4sXmreDPbzl6cuv0zX6hW4bMg6JAp6ff21byw0QJ7Ip6ZhA0I9xb20",
        "1", "17f00a75067c4d24a66e0cf350b5d9c7", "0b5bd9b559d143a6ae8d1eceb72cd061", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "17f00a75067c4d24a66e0cf350b5d9c7", "0b5bd9b559d143a6ae8d1eceb72cd061",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "17f00a75067c4d24a66e0cf350b5d9c7", "17f00a75067c4d24a66e0cf350b5d9c7",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("17f00a75067c4d24a66e0cf350b5d9c7", "17f00a75067c4d24a66e0cf350b5d9c7",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_lss", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "17f00a75067c4d24a66e0cf350b5d9c7", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("d2870a83017a8540624fff22dfb1970aa19000305f2b744b153ee344b06a7f2f", "17f00a75067c4d24a66e0cf350b5d9c7",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"30cc781e05d141079d8016a1afe53933\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b16866f1543e3e217fdbff521ef77c1f547b2f8253e76881c5d42bf5bafd186ef15c84053cd9ae865e947a2b2098d70b1\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("a7afb8ddf4c34d5796b0ccd47f1ad0c7", "res_lunar", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("11082c42824a48c08f91d2d547cd5aaf", "default", "{}", "Default project for domain", "1",
        "a7afb8ddf4c34d5796b0ccd47f1ad0c7");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("a7afb8ddf4c34d5796b0ccd47f1ad0c7", "root", "{}",
        "$6$rounds=40000$oQb5UBqJ$/rh/zTNxyFMve0qfKACE6Rr73oMAeDDAIDUM0TUA4s.bFLr2jo59D8xeb65zNH2Y29Y7mgCG9o3Bg3WsLGTnr0",
        "1", "a7afb8ddf4c34d5796b0ccd47f1ad0c7", "11082c42824a48c08f91d2d547cd5aaf", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "a7afb8ddf4c34d5796b0ccd47f1ad0c7", "11082c42824a48c08f91d2d547cd5aaf",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "a7afb8ddf4c34d5796b0ccd47f1ad0c7", "a7afb8ddf4c34d5796b0ccd47f1ad0c7",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("a7afb8ddf4c34d5796b0ccd47f1ad0c7", "a7afb8ddf4c34d5796b0ccd47f1ad0c7",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_lunar", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "a7afb8ddf4c34d5796b0ccd47f1ad0c7", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("b430d1412003bc29490c1c5b465662a76e7874fdd00a4ef92eb75e5fab3ff860", "a7afb8ddf4c34d5796b0ccd47f1ad0c7",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"9629f04bb5ee4aa88741ce7024444a39\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b7de08675e180e41a74bbb5b0a0c1694ef1191b30af5b57666d7e070cd2aa2e7bb839da4b3c5c7f0b838b2861aa5cac0b\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("2d8e0c0d0fa5402786a8f656dbc003c7", "res_market", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("11a14c301d3f4f83a13984d49c7e6b77", "default", "{}", "Default project for domain", "1",
        "2d8e0c0d0fa5402786a8f656dbc003c7");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("2d8e0c0d0fa5402786a8f656dbc003c7", "root", "{}",
        "$6$rounds=40000$.JT31/YR$8.H76KljFUnjAccdpp3d/4HA7iSRTgaLFzI8CNaY9ceQ2XOuWVUFZwmYgy4u6l0Zu5mDzZhPpz.sa.m3Bwnui.",
        "1", "2d8e0c0d0fa5402786a8f656dbc003c7", "11a14c301d3f4f83a13984d49c7e6b77", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "2d8e0c0d0fa5402786a8f656dbc003c7", "11a14c301d3f4f83a13984d49c7e6b77",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "2d8e0c0d0fa5402786a8f656dbc003c7", "2d8e0c0d0fa5402786a8f656dbc003c7",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("2d8e0c0d0fa5402786a8f656dbc003c7", "2d8e0c0d0fa5402786a8f656dbc003c7",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_market", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "2d8e0c0d0fa5402786a8f656dbc003c7", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("510ddf303f3d761865a184b3d700a82fbd21daf73386c63fa895111080d3bbed", "2d8e0c0d0fa5402786a8f656dbc003c7",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"88e53a53f7eb448caa8056d177c88143\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b9942273967c24c4e1104cd2502ab190459b52f9bff4db2b8b5edcb9c8c75999c33ca98b0a23230aee329b4aee9478f13\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("5695261f14694fe198fbc03a617066bb", "res_mc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("11bef630179d4e9c816f75ec1ab74b29", "default", "{}", "Default project for domain", "1",
        "5695261f14694fe198fbc03a617066bb");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("5695261f14694fe198fbc03a617066bb", "root", "{}",
        "$6$rounds=40000$smizdjMU$4/6pOeO2IXWIKKhbmejTDDHfQR5QhbdkZ6jxquDdrLoyN58gxWW9u8BWE8gOqYQl1iOKP6sUjKwZGaHE1NTEX0",
        "1", "5695261f14694fe198fbc03a617066bb", "11bef630179d4e9c816f75ec1ab74b29", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "5695261f14694fe198fbc03a617066bb", "11bef630179d4e9c816f75ec1ab74b29",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "5695261f14694fe198fbc03a617066bb", "5695261f14694fe198fbc03a617066bb",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("5695261f14694fe198fbc03a617066bb", "5695261f14694fe198fbc03a617066bb",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_mc", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "5695261f14694fe198fbc03a617066bb", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("9783ece3f86c84527f30a18d944d425ac99a141c9c87e2147f11e6cc65ba446e", "5695261f14694fe198fbc03a617066bb",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"0b2ca9cde41f4d459d866dd337bb4ae1\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b2f1f88d28d81730396a839d66f1d4f205b3965d78433a04b4b72065e6d7baeb3a421030992ee1c5d3f0ef213a124fde3\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("6890c58fdab249ab8d3b2bbf92ce8749", "res_mediacloud", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("fae4670fd4c945f984a53623d932e33a", "default", "{}", "Default project for domain", "1",
        "6890c58fdab249ab8d3b2bbf92ce8749");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("6890c58fdab249ab8d3b2bbf92ce8749", "root", "{}",
        "$6$rounds=40000$vxQ6bHlz$6n/RjwmBaSEqO0NtFksT.uhY4N0r9rL/0JBYhXdnJYlyrjCG.Lx4yCFV07oxhxdjvVEq9fonvcQQ4cAdlnhFf0",
        "1", "6890c58fdab249ab8d3b2bbf92ce8749", "fae4670fd4c945f984a53623d932e33a", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "6890c58fdab249ab8d3b2bbf92ce8749", "fae4670fd4c945f984a53623d932e33a",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "6890c58fdab249ab8d3b2bbf92ce8749", "6890c58fdab249ab8d3b2bbf92ce8749",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("6890c58fdab249ab8d3b2bbf92ce8749", "6890c58fdab249ab8d3b2bbf92ce8749",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_mediacloud", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "6890c58fdab249ab8d3b2bbf92ce8749", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("c2a1306bac4931793ff0ba18deb298b4563c06a0c90e3ca41e8c99cdde3bba59", "6890c58fdab249ab8d3b2bbf92ce8749",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"502dc9bfd57e4b45938738b756bac783\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b1e2b71f66dc82e9695c0c53d853cd73dc7b27df516eb19f14f771f70c46086d2f243b175ff19f4fcb2c7725b037edcdc\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("4060084e094f441daadb88d406f52c3e", "res_messages", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("f0346f7dec1642719f222bce3a15da1b", "default", "{}", "Default project for domain", "1",
        "4060084e094f441daadb88d406f52c3e");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("4060084e094f441daadb88d406f52c3e", "root", "{}",
        "$6$rounds=40000$xdAFqhwB$ieHywbJNieM9qj4QFM2LTh0gzgvVAMvI8yRBskdbKhDP36GY33sAxY5CriNZvuk1M5ToYsAWpv5cIGReEBUzc.",
        "1", "4060084e094f441daadb88d406f52c3e", "f0346f7dec1642719f222bce3a15da1b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "4060084e094f441daadb88d406f52c3e", "f0346f7dec1642719f222bce3a15da1b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "4060084e094f441daadb88d406f52c3e", "4060084e094f441daadb88d406f52c3e",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("4060084e094f441daadb88d406f52c3e", "4060084e094f441daadb88d406f52c3e",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_messages", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "4060084e094f441daadb88d406f52c3e", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("4a73f49591285290824b82872ac10e78a09aa52075f1d5560366536f28a7b0c4", "4060084e094f441daadb88d406f52c3e",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"8841793fb4484f268a1c914e4f4eda2f\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b0991ceffff0a2acb85a9956d36e84e045d95a0ff364992859d99f9dbf0605f855fa69a35d3465d15a8639ab059d290c1\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("16b5a641c94a4a8490a4fe066ae8a0a7", "res_modocube", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("63270ab28d3741529da2ff154b6e0bba", "default", "{}", "Default project for domain", "1",
        "16b5a641c94a4a8490a4fe066ae8a0a7");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("16b5a641c94a4a8490a4fe066ae8a0a7", "root", "{}",
        "$6$rounds=40000$qYVNEan5$SvRDsp6YxDzOsddrgdE/e.jcqV8OfWgtkaTcvB8qDF8GNd4d9rJR074E33WOPnyiFIxZICwb/nsk6b2Ff/cSa.",
        "1", "16b5a641c94a4a8490a4fe066ae8a0a7", "63270ab28d3741529da2ff154b6e0bba", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "16b5a641c94a4a8490a4fe066ae8a0a7", "63270ab28d3741529da2ff154b6e0bba",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "16b5a641c94a4a8490a4fe066ae8a0a7", "16b5a641c94a4a8490a4fe066ae8a0a7",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("16b5a641c94a4a8490a4fe066ae8a0a7", "16b5a641c94a4a8490a4fe066ae8a0a7",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_modocube", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "16b5a641c94a4a8490a4fe066ae8a0a7", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("22e7c2722d387705ffef3079ef1bfa6482322ea76c56a01120218bb7f922c2f0", "16b5a641c94a4a8490a4fe066ae8a0a7",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"a1ac40aac8764f4890f6ea1bdf0bbb21\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bce57896311dcb88bcaa8081b5d02bd3418a8dd59d27b8ec2345461c710d015f2346db1c4d27023597e5af04d4d3ffa5d\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("041e72b02a3f4829929b0831a0b1bee9", "res_moladb", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("b57c27ca144b415a9bb18027bd524660", "default", "{}", "Default project for domain", "1",
        "041e72b02a3f4829929b0831a0b1bee9");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("041e72b02a3f4829929b0831a0b1bee9", "root", "{}",
        "$6$rounds=40000$v9mfWeXO$uhUbU2WvTMPW3tZJqqjnzWlNVdb48XbY6sB8nVgJpYGT0XVV9u/HbqLClp29Zar5ow9Ed/olEIWunUZwQTsM3/",
        "1", "041e72b02a3f4829929b0831a0b1bee9", "b57c27ca144b415a9bb18027bd524660", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "041e72b02a3f4829929b0831a0b1bee9", "b57c27ca144b415a9bb18027bd524660",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "041e72b02a3f4829929b0831a0b1bee9", "041e72b02a3f4829929b0831a0b1bee9",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("041e72b02a3f4829929b0831a0b1bee9", "041e72b02a3f4829929b0831a0b1bee9",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_moladb", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "041e72b02a3f4829929b0831a0b1bee9", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("c1321b3a0ae9faf47ff879e14f0120056ae64abbe9b622010694a12884cc118e", "041e72b02a3f4829929b0831a0b1bee9",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"00965f3396de4a2cadbe4a07bc1dd846\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b9d55cf687e354d06c94d0c31bd28bf21c5cb5e64d57e712171f5640d65fd8e1dd77a8f5188b78aeecf16ad94d046ec80\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("9f601cb9ed6c4d8f9706549ba5d1a577", "res_mongodb", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("d8900d53fc0349b6a7719ae560994ae4", "default", "{}", "Default project for domain", "1",
        "9f601cb9ed6c4d8f9706549ba5d1a577");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("9f601cb9ed6c4d8f9706549ba5d1a577", "root", "{}",
        "$6$rounds=40000$LC20F1AW$4d/O2YI1RcYs2ybul.X.mMKQv0fEHGU5bSYYEI1Cps5X6FeairVfGNH7UAOYYtIyC0RjGncx8SRdDVx2iKeNS0",
        "1", "9f601cb9ed6c4d8f9706549ba5d1a577", "d8900d53fc0349b6a7719ae560994ae4", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "9f601cb9ed6c4d8f9706549ba5d1a577", "d8900d53fc0349b6a7719ae560994ae4",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "9f601cb9ed6c4d8f9706549ba5d1a577", "9f601cb9ed6c4d8f9706549ba5d1a577",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("9f601cb9ed6c4d8f9706549ba5d1a577", "9f601cb9ed6c4d8f9706549ba5d1a577",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_mongodb", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "9f601cb9ed6c4d8f9706549ba5d1a577", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("e9da36a880546969c8b66625a7825162c917ab3b8fe45b6e605c172d7fb73f08", "9f601cb9ed6c4d8f9706549ba5d1a577",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"8d9e73057c75487e961b8dafd0b5988d\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b9fdaad71eebcfc1d3260a2fd494b887cdccbc6ea4cfc4dcdc7a64b44b6807a6155f178e95daf618c1a30d36ad610879e\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("e961152bbe1e4bcd88f08ec04b6573c3", "res_mpc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("b376b0771d384ce0b4b1053e00006903", "default", "{}", "Default project for domain", "1",
        "e961152bbe1e4bcd88f08ec04b6573c3");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("e961152bbe1e4bcd88f08ec04b6573c3", "root", "{}",
        "$6$rounds=40000$vSQUa0Q9$SQDOWRQZfhAaIEKziMp1HzmguIyj6j6z5JPPT16KOUvTtRGa7b02KuqsVGvgz62Kz3MTDSDvTmui9gGZQyKFe1",
        "1", "e961152bbe1e4bcd88f08ec04b6573c3", "b376b0771d384ce0b4b1053e00006903", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "e961152bbe1e4bcd88f08ec04b6573c3", "b376b0771d384ce0b4b1053e00006903",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "e961152bbe1e4bcd88f08ec04b6573c3", "e961152bbe1e4bcd88f08ec04b6573c3",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("e961152bbe1e4bcd88f08ec04b6573c3", "e961152bbe1e4bcd88f08ec04b6573c3",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_mpc", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "e961152bbe1e4bcd88f08ec04b6573c3", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("56b3a490da0c9049e772b345b0f7c2ae5c225410aee20c30d6445b597df174ee", "e961152bbe1e4bcd88f08ec04b6573c3",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"43fdc04b6113471d85fe9a6428c416b2\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bafc8a133a35640c3fe79c10e4ba49eeba292129f8ceb32ca97424ed244fec245800e1dcfb21b1debf770c5e3c64876ac\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("71ebc8b6880c492f8dfcf48cd08a880d", "res_mtc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("952f7340420943678619de25b94fdb64", "default", "{}", "Default project for domain", "1",
        "71ebc8b6880c492f8dfcf48cd08a880d");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("71ebc8b6880c492f8dfcf48cd08a880d", "root", "{}",
        "$6$rounds=40000$jhRghziQ$JFZ43oc1yzwUWJ/eJX39AbmeXpWWCeHdq3SYygFYN3k.QK3t8qCZwC2ZdbjxBCFgQjwfjeYmmfrd1Q8lqpQsz.",
        "1", "71ebc8b6880c492f8dfcf48cd08a880d", "952f7340420943678619de25b94fdb64", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "71ebc8b6880c492f8dfcf48cd08a880d", "952f7340420943678619de25b94fdb64",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "71ebc8b6880c492f8dfcf48cd08a880d", "71ebc8b6880c492f8dfcf48cd08a880d",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("71ebc8b6880c492f8dfcf48cd08a880d", "71ebc8b6880c492f8dfcf48cd08a880d",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_mtc", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "71ebc8b6880c492f8dfcf48cd08a880d", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("744016606c76e03b8b441130f1b185b6afd09f3781c533f8891c5f78ad32698e", "71ebc8b6880c492f8dfcf48cd08a880d",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"5b001e58c655469d9008a4691f7a9f25\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb4c14ef1cbf145afbc59ca57a76c2577e7494f804d53b6c78e1ac4c05a2084aeaddb768fc273ebc5cbce4f9f315527f2\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("7ea1043993aa45468d3b66ef2c1d7b9c", "res_multimedia", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("25e913207b1a46df954bf2eaae9850e2", "default", "{}", "Default project for domain", "1",
        "7ea1043993aa45468d3b66ef2c1d7b9c");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("7ea1043993aa45468d3b66ef2c1d7b9c", "root", "{}",
        "$6$rounds=40000$3lpmhfrv$iZqkgHSpAXfQUXa6AbXJW3Oyr7m4oIHG2lbtk9bOqhRLufhs1fS46NRbjsidpI9JOS2i19KAjOYdWtVcGOnQc0",
        "1", "7ea1043993aa45468d3b66ef2c1d7b9c", "25e913207b1a46df954bf2eaae9850e2", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "7ea1043993aa45468d3b66ef2c1d7b9c", "25e913207b1a46df954bf2eaae9850e2",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "7ea1043993aa45468d3b66ef2c1d7b9c", "7ea1043993aa45468d3b66ef2c1d7b9c",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("7ea1043993aa45468d3b66ef2c1d7b9c", "7ea1043993aa45468d3b66ef2c1d7b9c",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_multimedia", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "7ea1043993aa45468d3b66ef2c1d7b9c", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0e87801dc3ef677f6e1e281d080e9006b78fd7f8a9a70c39b7775d5b2bb51bc5", "7ea1043993aa45468d3b66ef2c1d7b9c",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"a03da8440d794f8c944399ab5a2f85c2\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b2eac136c82d5e3c811a2da42508f26ef81aebebc06c8bddc31b61197e43f14be5af59792b2f529c05305732b01298c8e\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("b07e4019e44847a88df1410888f6fc50", "res_multimedia-risk", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("0ec1d55bbcb844049e2826f3ac997067", "default", "{}", "Default project for domain", "1",
        "b07e4019e44847a88df1410888f6fc50");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("b07e4019e44847a88df1410888f6fc50", "root", "{}",
        "$6$rounds=40000$VyOy2ytL$Qp7IYznYkrxUKBn8zDuhRdxq25MWt8./9UbngIdQk/wHWZw0VEJ5Sxxqjbz9vKmAQvF1u4SSj5u3ZD28P3PVc0",
        "1", "b07e4019e44847a88df1410888f6fc50", "0ec1d55bbcb844049e2826f3ac997067", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "b07e4019e44847a88df1410888f6fc50", "0ec1d55bbcb844049e2826f3ac997067",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "b07e4019e44847a88df1410888f6fc50", "b07e4019e44847a88df1410888f6fc50",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("b07e4019e44847a88df1410888f6fc50", "b07e4019e44847a88df1410888f6fc50",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_multimedia-risk", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "b07e4019e44847a88df1410888f6fc50", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0d5933d3a9d2006d68b4636c94baee632fe6b5d437233ceafde237575c8a4eac", "b07e4019e44847a88df1410888f6fc50",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"51357e03804344b191370b7a29e21f12\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57be8e9e5ead9578d7ea46de6ccac1b209f492866c48fb3e725935a5db608e59d777ca557a819d1040410d92b263db2b49d\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("2205af921114463e868cf9b4f5290d89", "res_multimedia-user", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("d43c714d711846c3acfaabcdfb0cf9bf", "default", "{}", "Default project for domain", "1",
        "2205af921114463e868cf9b4f5290d89");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("2205af921114463e868cf9b4f5290d89", "root", "{}",
        "$6$rounds=40000$XGIOyZiA$AotQRQt6woWs9QJxrtcXShspSiewpdJBf/0/F0bMmELai99Lr2vlgDuNywcuKU0Q8QhwfBHI9sVVFgNZjj3Oi0",
        "1", "2205af921114463e868cf9b4f5290d89", "d43c714d711846c3acfaabcdfb0cf9bf", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "2205af921114463e868cf9b4f5290d89", "d43c714d711846c3acfaabcdfb0cf9bf",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "2205af921114463e868cf9b4f5290d89", "2205af921114463e868cf9b4f5290d89",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("2205af921114463e868cf9b4f5290d89", "2205af921114463e868cf9b4f5290d89",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_multimedia-user", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "2205af921114463e868cf9b4f5290d89", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("75c68e9206f548ed6f7911e6422f2f7ae5e6dcc427c163b38a2269dcb2dc4b7d", "2205af921114463e868cf9b4f5290d89",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"f59daf3bcfa64a2b9ea2ea35ff28e5ee\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b999df9015a79491fc57481d47c6d11d932c2a45f8e2bd150f4c4f02c82dbcfebb14058889d54d0183cd74c64f6eadfae\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("21c5c774453e4c1a99d0370a4782c7a4", "res_name", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("dbacf51fdafb4962873c7cd50a34ab54", "default", "{}", "Default project for domain", "1",
        "21c5c774453e4c1a99d0370a4782c7a4");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("21c5c774453e4c1a99d0370a4782c7a4", "root", "{}",
        "$6$rounds=40000$M2o5AC/0$xkT9vEBbNy8ljv9ffyJ7qkfuJp2u3TpAmizu3LTj87UzmIJ2bDreTQhyI5B2LWcBvtDgCUhx3hJ4kvLD7CnXm0",
        "1", "21c5c774453e4c1a99d0370a4782c7a4", "dbacf51fdafb4962873c7cd50a34ab54", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "21c5c774453e4c1a99d0370a4782c7a4", "dbacf51fdafb4962873c7cd50a34ab54",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "21c5c774453e4c1a99d0370a4782c7a4", "21c5c774453e4c1a99d0370a4782c7a4",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("21c5c774453e4c1a99d0370a4782c7a4", "21c5c774453e4c1a99d0370a4782c7a4",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_name", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "21c5c774453e4c1a99d0370a4782c7a4", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("5867725211a445465995a9292d60992269e95d1d5aa2a8b09e16d75a09a9b302", "21c5c774453e4c1a99d0370a4782c7a4",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"ec381b8155fe4fbea7b0c2e4c0560c45\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b6d18d5e9dbe10c6b7f35f77eda240011602d319b6c5b97e996b693f7890dea010be07b4b8aaafefc649630cd597514df\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("be80d1ae8cd4462f8860e19bb3296e5d", "res_nat", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("131a4c4a9f3f4a8284f5a10decac8809", "default", "{}", "Default project for domain", "1",
        "be80d1ae8cd4462f8860e19bb3296e5d");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("be80d1ae8cd4462f8860e19bb3296e5d", "root", "{}",
        "$6$rounds=40000$Rt5DeVbT$wqCpagg9c.xvhjFpBTEZCq4vxec.YnrMRZB7MgwDKaDV8tZdVf8QfbZECw4x3yrF/Hbc.lmm/ZsKzWgwZAMF01",
        "1", "be80d1ae8cd4462f8860e19bb3296e5d", "131a4c4a9f3f4a8284f5a10decac8809", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "be80d1ae8cd4462f8860e19bb3296e5d", "131a4c4a9f3f4a8284f5a10decac8809",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "be80d1ae8cd4462f8860e19bb3296e5d", "be80d1ae8cd4462f8860e19bb3296e5d",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("be80d1ae8cd4462f8860e19bb3296e5d", "be80d1ae8cd4462f8860e19bb3296e5d",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_nat", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "be80d1ae8cd4462f8860e19bb3296e5d", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("6138fe3dc8c777a9b9ee7f95980983ac6c37f0d806222cd6825353aedddbc13f", "be80d1ae8cd4462f8860e19bb3296e5d",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"9b33f4c0d4d3430a9ae1de126b98f773\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b144d891f8923579bc4c40a13edd3422434f50ad83ef3078363f5ce8279e37939975abb45aafb5b7aaf70e7bbd14752fc\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("a845dc455654464cb7d04f02c453a694", "res_network_gateways", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("84fc0e2b0d74479b8bee3ca900927f73", "default", "{}", "Default project for domain", "1",
        "a845dc455654464cb7d04f02c453a694");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("a845dc455654464cb7d04f02c453a694", "root", "{}",
        "$6$rounds=40000$Az.LSxbG$yvDlWyDqDSCgS07k0Rc5yChlWLDQrga88RvDe4YHcEoZSZaADQVrP1hf33oLhnBrkhFNsDbj8FGqFjzjvEF/V1",
        "1", "a845dc455654464cb7d04f02c453a694", "84fc0e2b0d74479b8bee3ca900927f73", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "a845dc455654464cb7d04f02c453a694", "84fc0e2b0d74479b8bee3ca900927f73",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "a845dc455654464cb7d04f02c453a694", "a845dc455654464cb7d04f02c453a694",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("a845dc455654464cb7d04f02c453a694", "a845dc455654464cb7d04f02c453a694",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_network_gateways", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "a845dc455654464cb7d04f02c453a694", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("c76aa8f426788728fe25539211271123e264e40c5ae405997aa52475f9debe7f", "a845dc455654464cb7d04f02c453a694",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"eff3ba8658d342179b381a2eec387a8d\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb80523d5cc5493357e23a677ec11423cae99ecdafa1806208789a8ea7e94ee365a1f10de64d9eddf111955945da874df\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("6368781efa1241c9ba8e1d17c8bfbaa6", "res_neutron", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("d30c6b8b51594d86b8e637e49d4184e6", "default", "{}", "Default project for domain", "1",
        "6368781efa1241c9ba8e1d17c8bfbaa6");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("6368781efa1241c9ba8e1d17c8bfbaa6", "root", "{}",
        "$6$rounds=40000$Zp1/2rB1$bDeX5jS/G2Aq7/mOu0XxphfWOIaP22gN0MzoTCrOSGb90d9ILjOTZf.kf4UT0lv8AEWyuZapDY6vrWTN7Uo4x1",
        "1", "6368781efa1241c9ba8e1d17c8bfbaa6", "d30c6b8b51594d86b8e637e49d4184e6", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "6368781efa1241c9ba8e1d17c8bfbaa6", "d30c6b8b51594d86b8e637e49d4184e6",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "6368781efa1241c9ba8e1d17c8bfbaa6", "6368781efa1241c9ba8e1d17c8bfbaa6",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("6368781efa1241c9ba8e1d17c8bfbaa6", "6368781efa1241c9ba8e1d17c8bfbaa6",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_neutron", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "6368781efa1241c9ba8e1d17c8bfbaa6", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("a7d2e8dc18db05c7fee88eec37410e7ce20b914d916508ebf53505032f95f310", "6368781efa1241c9ba8e1d17c8bfbaa6",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"c8d2906571684955b653270f1a2588e5\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bc00f5414930a2094a793ed00c694b147a203b6a5355a861a22949c2283d59dfa5032c9ddf50ad1a90bfbf1594b74a047\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("a1cac2646f014d93bb27fcead3d92bd9", "res_noahee", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("ee89400604a04fb09fa55a55b274e62a", "default", "{}", "Default project for domain", "1",
        "a1cac2646f014d93bb27fcead3d92bd9");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("a1cac2646f014d93bb27fcead3d92bd9", "root", "{}",
        "$6$rounds=40000$wAEWlvvy$j6wcDYwWIsfQ40hOCiAsXNwXtOJsshG6rocoIqC5jUgNaM8jIAEUERj17Yc2usT4YD89IbJX86u3kFztK//I7.",
        "1", "a1cac2646f014d93bb27fcead3d92bd9", "ee89400604a04fb09fa55a55b274e62a", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "a1cac2646f014d93bb27fcead3d92bd9", "ee89400604a04fb09fa55a55b274e62a",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "a1cac2646f014d93bb27fcead3d92bd9", "a1cac2646f014d93bb27fcead3d92bd9",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("a1cac2646f014d93bb27fcead3d92bd9", "a1cac2646f014d93bb27fcead3d92bd9",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_noahee", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "a1cac2646f014d93bb27fcead3d92bd9", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8fc82db852d58677ac8c9cd43006d8462de929d8cc529741a46481d48aab6aab", "a1cac2646f014d93bb27fcead3d92bd9",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"603430604d7048d1bd806a1b2e9494ad\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b646f9bbfc888596a03f360b3355171439b93dae90cabe34457b72c820db36bb15050fe5ecf605da6269b59215bfce384\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("b1d56549ecf6464f9a0ebfb125da2353", "res_noahee_apptree", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("c4007210523d469385ff1815b229975b", "default", "{}", "Default project for domain", "1",
        "b1d56549ecf6464f9a0ebfb125da2353");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("b1d56549ecf6464f9a0ebfb125da2353", "root", "{}",
        "$6$rounds=40000$.WWBoqA8$VV2MTN08GYStfx123jmmr62UXUv6gaV7QCXezunSV1/hzvhoaDcsrMRJHfItv96aBco0dzYZtF7dBA2uIQo.j0",
        "1", "b1d56549ecf6464f9a0ebfb125da2353", "c4007210523d469385ff1815b229975b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "b1d56549ecf6464f9a0ebfb125da2353", "c4007210523d469385ff1815b229975b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "b1d56549ecf6464f9a0ebfb125da2353", "b1d56549ecf6464f9a0ebfb125da2353",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("b1d56549ecf6464f9a0ebfb125da2353", "b1d56549ecf6464f9a0ebfb125da2353",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_noahee_apptree", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "b1d56549ecf6464f9a0ebfb125da2353", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("77e62fb24be39e79c6f1a208c6f9e35aa78aaf135891e9132bfe9d385de4a84f", "b1d56549ecf6464f9a0ebfb125da2353",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"1019611f6e0e48a28c04a8db7536034c\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b7bcc96b7e4ca230711cde708f036125de307a9c6c5e7d77d70553af441d44668cd9fa92f64b61501b7114b50a3027b1d\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("ef07d7512b704e668a4afd36f1f262db", "res_notebook", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("60b5db32b63c4f5e8c68d89eaa2c8d9d", "default", "{}", "Default project for domain", "1",
        "ef07d7512b704e668a4afd36f1f262db");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("ef07d7512b704e668a4afd36f1f262db", "root", "{}",
        "$6$rounds=40000$cL.i18ri$VyF4DgYuTwn9NLHF1SxT.qtOR9TtCDJHAHd.ERkRstQJXoRduW/ANVBlwJoQsHOM8/T.W0iVdx3brnW7oJIO1/",
        "1", "ef07d7512b704e668a4afd36f1f262db", "60b5db32b63c4f5e8c68d89eaa2c8d9d", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "ef07d7512b704e668a4afd36f1f262db", "60b5db32b63c4f5e8c68d89eaa2c8d9d",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "ef07d7512b704e668a4afd36f1f262db", "ef07d7512b704e668a4afd36f1f262db",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("ef07d7512b704e668a4afd36f1f262db", "ef07d7512b704e668a4afd36f1f262db",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_notebook", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "ef07d7512b704e668a4afd36f1f262db", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("f703a39e9cb114e933bdac6af8d763c2bc02f144225604ba270ba2adc418aaed", "ef07d7512b704e668a4afd36f1f262db",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"4c2fce84539f435bbb82d2cf53ebb1f1\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b91652f1224c23727dd8db54affbbef0e0f27ed1e357c21a49d8065074c4ea0efa2fc7f279039f84c15dfe905a34d92ac\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("90e493bc989e4eeeaa3f38854120bc8d", "res_nova", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("7b6e3cfb70b04677be43dbf95a3dda28", "default", "{}", "Default project for domain", "1",
        "90e493bc989e4eeeaa3f38854120bc8d");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("90e493bc989e4eeeaa3f38854120bc8d", "root", "{}",
        "$6$rounds=40000$v/ydtyqN$myc68zcnX5mXzW/vIUOPxwowVUGYXbsvdcvn6IWCwXFia9lDdAs57wnxY6Iw113epAAKvkcflVf1N3OL713jr/",
        "1", "90e493bc989e4eeeaa3f38854120bc8d", "7b6e3cfb70b04677be43dbf95a3dda28", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "90e493bc989e4eeeaa3f38854120bc8d", "7b6e3cfb70b04677be43dbf95a3dda28",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "90e493bc989e4eeeaa3f38854120bc8d", "90e493bc989e4eeeaa3f38854120bc8d",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("90e493bc989e4eeeaa3f38854120bc8d", "90e493bc989e4eeeaa3f38854120bc8d",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_nova", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "90e493bc989e4eeeaa3f38854120bc8d", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("552b540fd1beee70dabdd5ddbd7fc656482ad3a5486af50e76b4f463931e0c8b", "90e493bc989e4eeeaa3f38854120bc8d",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"4d3284bcaae5414f892a5858568b6f14\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b305733fb47fe7e76d48f8c42dad486554cfd0fb525b827aeb2c64a0d2910c52a01235edd5f28be7879bec963542a4f37\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("404278e52ff24dbb8d12dbe4c24c4877", "res_object_store_rqy", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("7d37f9349ffb488b8c23ed0fdec88837", "default", "{}", "Default project for domain", "1",
        "404278e52ff24dbb8d12dbe4c24c4877");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("404278e52ff24dbb8d12dbe4c24c4877", "root", "{}",
        "$6$rounds=40000$NLpQ5gMt$K1zZ9Fk3qWxvQ300iE8ztMGkKKBFldUZDGVdAdltO/9qxSS547SpvSR/pft4ho/n75triW5rbXGTwKdoCZI490",
        "1", "404278e52ff24dbb8d12dbe4c24c4877", "7d37f9349ffb488b8c23ed0fdec88837", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "404278e52ff24dbb8d12dbe4c24c4877", "7d37f9349ffb488b8c23ed0fdec88837",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "404278e52ff24dbb8d12dbe4c24c4877", "404278e52ff24dbb8d12dbe4c24c4877",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("404278e52ff24dbb8d12dbe4c24c4877", "404278e52ff24dbb8d12dbe4c24c4877",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_object_store_rqy", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "404278e52ff24dbb8d12dbe4c24c4877", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("52575a2d907a6a349616a77f25a817a6b90e7da6a438e13e4bf6d009d1da84a7", "404278e52ff24dbb8d12dbe4c24c4877",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"bda93db6b7344e019dcbcbb0174667fa\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b953e432b27efc017d8224ee6058d3d446b203bef9d5ce0679537a4664614c585de3afe11fbe9d792be4a97880bb5112c\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("ded70b6676be428bb1a5a80a38e4aebe", "res_ocr", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("42964316bb594cfa9cb2b65e0636e422", "default", "{}", "Default project for domain", "1",
        "ded70b6676be428bb1a5a80a38e4aebe");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("ded70b6676be428bb1a5a80a38e4aebe", "root", "{}",
        "$6$rounds=40000$87ptk6cm$JDXs.yNqP5yAeyc7Rfd5gICv.hr1f4KZUxViD52LSbf1db81vdsOvxE4mrpmeepip2B7mrkGszVU1D7tecCtS/",
        "1", "ded70b6676be428bb1a5a80a38e4aebe", "42964316bb594cfa9cb2b65e0636e422", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "ded70b6676be428bb1a5a80a38e4aebe", "42964316bb594cfa9cb2b65e0636e422",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "ded70b6676be428bb1a5a80a38e4aebe", "ded70b6676be428bb1a5a80a38e4aebe",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("ded70b6676be428bb1a5a80a38e4aebe", "ded70b6676be428bb1a5a80a38e4aebe",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_ocr", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "ded70b6676be428bb1a5a80a38e4aebe", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("a3f68692b1a92095a82f933de12000f002060e1b1c4a573cdf4d42e7a58cea44", "ded70b6676be428bb1a5a80a38e4aebe",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"2e79d4316f8a45bcb45cf3be3b2ef166\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bd2f1644a4de9bc6ebf597683163b9bfc578bab6bad3cac9f951bf2eb59b93ca5b880a31eeb68816eeafe876931794d8b\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("d58a3dbd2ec6465488da8be804089aad", "res_olapengine", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("7a3ca2cccd17456788de9c9111e2ca61", "default", "{}", "Default project for domain", "1",
        "d58a3dbd2ec6465488da8be804089aad");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("d58a3dbd2ec6465488da8be804089aad", "root", "{}",
        "$6$rounds=40000$0x1ZQwrl$fdNkZBKZE5h5AB.V0h20ablqaWZOQqQ/eXZWSp9XLXQzxN6M/YgvzPZGMlvBtYaPmeHs2LqDvRwzUnSs/pinb/",
        "1", "d58a3dbd2ec6465488da8be804089aad", "7a3ca2cccd17456788de9c9111e2ca61", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "d58a3dbd2ec6465488da8be804089aad", "7a3ca2cccd17456788de9c9111e2ca61",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "d58a3dbd2ec6465488da8be804089aad", "d58a3dbd2ec6465488da8be804089aad",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("d58a3dbd2ec6465488da8be804089aad", "d58a3dbd2ec6465488da8be804089aad",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_olapengine", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "d58a3dbd2ec6465488da8be804089aad", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("633363de312aff1e6c4927a025b85561e75f417bb0b9d82124384f6a98526ccc", "d58a3dbd2ec6465488da8be804089aad",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"527c14a32f2c4752a56d4a8da67f56cc\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bd914d1e31a43926cd89f044ca007b1d2ba8507f6e7281d611efaf92d4c94285d062ca4390885a85d08a075a7a83e2350\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("34023e44e2ca411d96ce9a7b6b76fbf1", "res_openapi", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("ea3774beda1b4e659750fadc5ae8836f", "default", "{}", "Default project for domain", "1",
        "34023e44e2ca411d96ce9a7b6b76fbf1");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("34023e44e2ca411d96ce9a7b6b76fbf1", "root", "{}",
        "$6$rounds=40000$OloWuP13$SGoqlhX7bRdEbMXxhoBovpcCQ0EorSf2J8guep2QUdGGaaTOLUmh7DJbMeSh85ZbvaaCNFtpZqPgmzdMQ8O6/0",
        "1", "34023e44e2ca411d96ce9a7b6b76fbf1", "ea3774beda1b4e659750fadc5ae8836f", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "34023e44e2ca411d96ce9a7b6b76fbf1", "ea3774beda1b4e659750fadc5ae8836f",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "34023e44e2ca411d96ce9a7b6b76fbf1", "34023e44e2ca411d96ce9a7b6b76fbf1",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("34023e44e2ca411d96ce9a7b6b76fbf1", "34023e44e2ca411d96ce9a7b6b76fbf1",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_openapi", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "34023e44e2ca411d96ce9a7b6b76fbf1", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("710172548c2a228ef8bd40280ec393ee21c763af13412b85565a5cfb4dd8774a", "34023e44e2ca411d96ce9a7b6b76fbf1",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"bbd32e2526dc4d3daf208ad4423905d4\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b822214b591c1a5615bfc1b0123e8e50db85e2ec6e99517b4c57f56995d9783f3c7d5e0bc173c99d6f99976ccecb84b6e\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("f3106c9ad8b740e48124bf41ff4f09d9", "res_openstack-admin", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("8004f40982524850bf601677c6fc581b", "default", "{}", "Default project for domain", "1",
        "f3106c9ad8b740e48124bf41ff4f09d9");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("f3106c9ad8b740e48124bf41ff4f09d9", "root", "{}",
        "$6$rounds=40000$3.H6vLgy$UrOdYKLyMFJA2Fqlo0tD9Mf.emxc3UKFg08h4dawSstD/IAt6KfKog9T/1cbyuIBRTlakyEcp9WniYF1Wx3IA.",
        "1", "f3106c9ad8b740e48124bf41ff4f09d9", "8004f40982524850bf601677c6fc581b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "f3106c9ad8b740e48124bf41ff4f09d9", "8004f40982524850bf601677c6fc581b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "f3106c9ad8b740e48124bf41ff4f09d9", "f3106c9ad8b740e48124bf41ff4f09d9",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("f3106c9ad8b740e48124bf41ff4f09d9", "f3106c9ad8b740e48124bf41ff4f09d9",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_openstack-admin", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "f3106c9ad8b740e48124bf41ff4f09d9", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("e6e28f9f052bf20edfcd6f421f299051de4f412750890776567b712271b3f16d", "f3106c9ad8b740e48124bf41ff4f09d9",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"cd5c4adcb63f4191829dd4a421a7438c\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57beb1176f8b19caded979af6649a29e7ae0008b90317db67c02c72def0ab08de931a08d1908436555d19d6d5cac16598e9\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("cc9ac8afa5844fb386d83b7bcec5a479", "res_order_execute", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("f27c9fbeb914446baddb52423ff5c0a0", "default", "{}", "Default project for domain", "1",
        "cc9ac8afa5844fb386d83b7bcec5a479");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("cc9ac8afa5844fb386d83b7bcec5a479", "root", "{}",
        "$6$rounds=40000$eILv8IIK$P46CJeHzK1b8L/FCrbBZ0l2GsofJLh8gwp2K8yP80zpt5TeFxHUDb6O5WBjPsd/uoPNS.ToMy.6GhdSMc89FA/",
        "1", "cc9ac8afa5844fb386d83b7bcec5a479", "f27c9fbeb914446baddb52423ff5c0a0", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "cc9ac8afa5844fb386d83b7bcec5a479", "f27c9fbeb914446baddb52423ff5c0a0",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "cc9ac8afa5844fb386d83b7bcec5a479", "cc9ac8afa5844fb386d83b7bcec5a479",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("cc9ac8afa5844fb386d83b7bcec5a479", "cc9ac8afa5844fb386d83b7bcec5a479",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_order_execute", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "cc9ac8afa5844fb386d83b7bcec5a479", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("6eeeaad9acc7da0737c628de9488109805644c1400ce6b9a095fcd872d4a63b1", "cc9ac8afa5844fb386d83b7bcec5a479",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"447fe5f724e14fc3bd7e6ae93966a87a\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba1e9b97cd4007e8ad63d4d642a94e37eceb14e602cc6b30fe9ec571a658d82e9fd733db1a2ca00181f7746fdd69e5e85\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("7bffc04fff044ef39463b4410fc863cf", "res_order_executor", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("cdea1b7218404148ab1ca01d17446e07", "default", "{}", "Default project for domain", "1",
        "7bffc04fff044ef39463b4410fc863cf");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("7bffc04fff044ef39463b4410fc863cf", "root", "{}",
        "$6$rounds=40000$UiddC8oo$R6gB1maWGjBhv1AD9jIBb8x0pz1fOe0RLqJHxIwoVnRZ1hSblssxkr1tSJ7i5T8Rpz/90KCCryqs6AgC.Hpnc1",
        "1", "7bffc04fff044ef39463b4410fc863cf", "cdea1b7218404148ab1ca01d17446e07", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "7bffc04fff044ef39463b4410fc863cf", "cdea1b7218404148ab1ca01d17446e07",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "7bffc04fff044ef39463b4410fc863cf", "7bffc04fff044ef39463b4410fc863cf",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("7bffc04fff044ef39463b4410fc863cf", "7bffc04fff044ef39463b4410fc863cf",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_order_executor", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "7bffc04fff044ef39463b4410fc863cf", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0017258be149fc02484c29e197a8be9210b5f3a7af21be3562e23382a7d82c7b", "7bffc04fff044ef39463b4410fc863cf",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"09071e73245140a8b269e6bda3e250d3\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57be4ef8c8b1303403b9b5f0579811a465cbc47d21c8863f9fe82d75f38e4522198fa79386ded70b57d68a30880dbabfc91\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("b8ec0e9127af46daac4235efb0b0a296", "res_order_facade", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("78f20a1026494851b566cbac92d17028", "default", "{}", "Default project for domain", "1",
        "b8ec0e9127af46daac4235efb0b0a296");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("b8ec0e9127af46daac4235efb0b0a296", "root", "{}",
        "$6$rounds=40000$b2YvNhUL$1UwJv86/8tN1qHYE8FWqmB9KETxCb.pyy51w7v4k2kcp7ELiwj6LRvCYLbSP9yyiy.zpmnLUyzHpnqRgOLMYu.",
        "1", "b8ec0e9127af46daac4235efb0b0a296", "78f20a1026494851b566cbac92d17028", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "b8ec0e9127af46daac4235efb0b0a296", "78f20a1026494851b566cbac92d17028",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "b8ec0e9127af46daac4235efb0b0a296", "b8ec0e9127af46daac4235efb0b0a296",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("b8ec0e9127af46daac4235efb0b0a296", "b8ec0e9127af46daac4235efb0b0a296",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_order_facade", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "b8ec0e9127af46daac4235efb0b0a296", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("ac3383d16b829cdfa52f3bd34a8bcb9d9da5fc21f8605919913098e633196e2d", "b8ec0e9127af46daac4235efb0b0a296",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"368b919a7f464b40b25d65f406849341\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b6e5fb32f3695e1badb77ccc45237fb21f547a4c0dfb2ca79d37e0f076335e64e2ba4bfe5ce9dcb8e13c4099512c30055\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("08e429a87d3644638066db1e5f23dd88", "res_order_renew", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("e494f161e366481ba5ab6d5264461f3b", "default", "{}", "Default project for domain", "1",
        "08e429a87d3644638066db1e5f23dd88");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("08e429a87d3644638066db1e5f23dd88", "root", "{}",
        "$6$rounds=40000$Mj/voCga$efn8W/EfCbTx5NGDzcclYwSnrDpzUYvhoZ5qpJEkbtAXhNqZr7AaQr9EHt8nvJ08uR/lGUtVnxWvB4PKd7FXv1",
        "1", "08e429a87d3644638066db1e5f23dd88", "e494f161e366481ba5ab6d5264461f3b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "08e429a87d3644638066db1e5f23dd88", "e494f161e366481ba5ab6d5264461f3b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "08e429a87d3644638066db1e5f23dd88", "08e429a87d3644638066db1e5f23dd88",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("08e429a87d3644638066db1e5f23dd88", "08e429a87d3644638066db1e5f23dd88",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_order_renew", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "08e429a87d3644638066db1e5f23dd88", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("6f44a70b46d9055c2ed253a7ac941d04568e44bf8d29b82a7d16e8c399199279", "08e429a87d3644638066db1e5f23dd88",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"d384f79ca700488bb50bfa057c2efb4f\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba99afa72ceb65c249a66dea43c8054222923370386e005e5d235b0e339d42b33aede32d6f9841ee8691fc6fc9b0cfe70\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("5f779a5d168c4df7ac5d722f6410bd96", "res_osp-product", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("09660b609d434e8ebddd1cd4afd1d747", "default", "{}", "Default project for domain", "1",
        "5f779a5d168c4df7ac5d722f6410bd96");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("5f779a5d168c4df7ac5d722f6410bd96", "root", "{}",
        "$6$rounds=40000$TohZoYYE$eXxgUiDTjroo2IGo2GTmaxRWkkmGV2vWlQ4w4b14pQ.OPrV8Q0q/erifzcGqADg8cyi1H2P17DOgCiILlwl.f/",
        "1", "5f779a5d168c4df7ac5d722f6410bd96", "09660b609d434e8ebddd1cd4afd1d747", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "5f779a5d168c4df7ac5d722f6410bd96", "09660b609d434e8ebddd1cd4afd1d747",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "5f779a5d168c4df7ac5d722f6410bd96", "5f779a5d168c4df7ac5d722f6410bd96",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("5f779a5d168c4df7ac5d722f6410bd96", "5f779a5d168c4df7ac5d722f6410bd96",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_osp-product", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "5f779a5d168c4df7ac5d722f6410bd96", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("448ca0e4527e4e8f7178c48ea423706fce2ff675e770356e9c321e5638941b32", "5f779a5d168c4df7ac5d722f6410bd96",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"01a854e850b94fca9b24cac8b8f10a3f\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bf01b39ecfc10e844cbd7d1c7c13cb97a4090f36f1458439cbbf9f09e3b292c984016db1183eadcedbbbdc7f4649e6946\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("f61f6ea7af1342ca8e01f6dcd10e8ff5", "res_osp_dc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("186029c7b7444b8facd925123924084d", "default", "{}", "Default project for domain", "1",
        "f61f6ea7af1342ca8e01f6dcd10e8ff5");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("f61f6ea7af1342ca8e01f6dcd10e8ff5", "root", "{}",
        "$6$rounds=40000$Xycvvky9$7KD9Yhgx1cvaumrfZeQvW2BvtHD4KZAkQRRrBsfmN4GCofVAxvB6qhs6OKpyGcAI/QG/XY3aetkwRaUMKaaGt0",
        "1", "f61f6ea7af1342ca8e01f6dcd10e8ff5", "186029c7b7444b8facd925123924084d", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "f61f6ea7af1342ca8e01f6dcd10e8ff5", "186029c7b7444b8facd925123924084d",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "f61f6ea7af1342ca8e01f6dcd10e8ff5", "f61f6ea7af1342ca8e01f6dcd10e8ff5",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("f61f6ea7af1342ca8e01f6dcd10e8ff5", "f61f6ea7af1342ca8e01f6dcd10e8ff5",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_osp_dc", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "f61f6ea7af1342ca8e01f6dcd10e8ff5", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("6920fde7ea3ee6405e28d63ec176d716805636766b77b07eb3475448a5421b11", "f61f6ea7af1342ca8e01f6dcd10e8ff5",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"12dfb24720864d0c94ed3fae14ee051a\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb57ab3e353fee0623f48148aca810352c82a971a301af8f952a79b3f14451334a114a404c37c61640e0cce5f444d7da2\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("0dd0cfc20bbf4acaa37756f8b9ae72db", "res_palo", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("28549e60af154a418842a25a1933f54d", "default", "{}", "Default project for domain", "1",
        "0dd0cfc20bbf4acaa37756f8b9ae72db");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("0dd0cfc20bbf4acaa37756f8b9ae72db", "root", "{}",
        "$6$rounds=40000$jyfR2xgB$NyGuEXxlYu4k4qXmkHzZDKiSAECcLxRpfi8cKHmiCoBvEaDaI7B41fiHSL/A7XSmsv7BxG9XGXaChOXs7dac2.",
        "1", "0dd0cfc20bbf4acaa37756f8b9ae72db", "28549e60af154a418842a25a1933f54d", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "0dd0cfc20bbf4acaa37756f8b9ae72db", "28549e60af154a418842a25a1933f54d",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "0dd0cfc20bbf4acaa37756f8b9ae72db", "0dd0cfc20bbf4acaa37756f8b9ae72db",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("0dd0cfc20bbf4acaa37756f8b9ae72db", "0dd0cfc20bbf4acaa37756f8b9ae72db",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_palo", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "0dd0cfc20bbf4acaa37756f8b9ae72db", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("bd554d17114bbb9efb4b6a3ed0e05b7a5c5486f62811b252a35f9d713a6056cb", "0dd0cfc20bbf4acaa37756f8b9ae72db",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"9b808ef47d534ad6957ba14a34524591\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b086adad61e1ccbb0650b0a4e188f907c8e2ab4791d0b6cbd733e3cc9b4f7bf88ac86710edc582894f427f3d457900494\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("2e273035f9c64bfa8049a639d0e8842e", "res_parser", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("804ba304a30d4249b1ddf449e596d0e7", "default", "{}", "Default project for domain", "1",
        "2e273035f9c64bfa8049a639d0e8842e");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("2e273035f9c64bfa8049a639d0e8842e", "root", "{}",
        "$6$rounds=40000$GW2ylTHw$Q6VCo63vTo5Rq/Ru4KM19hNidWA4.c7ir7CNJM.qb3JorXLrGyEFoQMYqfSkUDbzOjt.Y42Aw.58TOwvGTOa1/",
        "1", "2e273035f9c64bfa8049a639d0e8842e", "804ba304a30d4249b1ddf449e596d0e7", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "2e273035f9c64bfa8049a639d0e8842e", "804ba304a30d4249b1ddf449e596d0e7",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "2e273035f9c64bfa8049a639d0e8842e", "2e273035f9c64bfa8049a639d0e8842e",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("2e273035f9c64bfa8049a639d0e8842e", "2e273035f9c64bfa8049a639d0e8842e",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_parser", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "2e273035f9c64bfa8049a639d0e8842e", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("f79dd906f23584520a189d261823ba7e3bb243c849879d0339b1b5a4bf11bff0", "2e273035f9c64bfa8049a639d0e8842e",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"cf2153847c904df390703678fc8cefc1\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bef0e1b1f34bba5aed99ceb4d817213dc09039c573d7996d921aca5eabb97be020d337e43ec75c935e5a1e88391fe610a\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("aabb3e75dd5246b19f4caac1a4504d5a", "res_pc-test", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("aca0ccd9dcd6499c97ae62354f118a4a", "default", "{}", "Default project for domain", "1",
        "aabb3e75dd5246b19f4caac1a4504d5a");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("aabb3e75dd5246b19f4caac1a4504d5a", "root", "{}",
        "$6$rounds=40000$MHfNpbBR$ckmLl89a38xzt10KFF4/iX5dqaOkfX3R0QnDMey8dwVg0uCIJ/jYpErsYuJNE9zUvkHkk4.wfi9WKgcWOF9Ir0",
        "1", "aabb3e75dd5246b19f4caac1a4504d5a", "aca0ccd9dcd6499c97ae62354f118a4a", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "aabb3e75dd5246b19f4caac1a4504d5a", "aca0ccd9dcd6499c97ae62354f118a4a",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "aabb3e75dd5246b19f4caac1a4504d5a", "aabb3e75dd5246b19f4caac1a4504d5a",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("aabb3e75dd5246b19f4caac1a4504d5a", "aabb3e75dd5246b19f4caac1a4504d5a",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_pc-test", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "aabb3e75dd5246b19f4caac1a4504d5a", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("44133d3725d5749f2419b174bac829464f7c598e1a87eaf0ef750114cf337e4c", "aabb3e75dd5246b19f4caac1a4504d5a",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"edfee255be354d039374458440a32ff0\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bcef6f2f20f747fa3f9ff9bf8ca8085527502640aafcda9e12778f8203ddc37ad104fbdaa2307586674c7fafa5365fb85\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("261355b34c924d2b8df2fb8268c3e841", "res_pcdn", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("edf40be505f34f6389263ae9ce473d7b", "default", "{}", "Default project for domain", "1",
        "261355b34c924d2b8df2fb8268c3e841");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("261355b34c924d2b8df2fb8268c3e841", "root", "{}",
        "$6$rounds=40000$2zVjgG6G$3i0ERsAAU9ScNit9FoVqr7..wWvfA9k.0rnEXj23ytvD5rKPBfxVRmKmSYBdi.RGdOAfBvZbAR6c4zCwIWTuL1",
        "1", "261355b34c924d2b8df2fb8268c3e841", "edf40be505f34f6389263ae9ce473d7b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "261355b34c924d2b8df2fb8268c3e841", "edf40be505f34f6389263ae9ce473d7b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "261355b34c924d2b8df2fb8268c3e841", "261355b34c924d2b8df2fb8268c3e841",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("261355b34c924d2b8df2fb8268c3e841", "261355b34c924d2b8df2fb8268c3e841",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_pcdn", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "261355b34c924d2b8df2fb8268c3e841", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("d6de8c8f4e24682b1d3380a996d0052eba5ce5af123dbcfbd957230176d65ce0", "261355b34c924d2b8df2fb8268c3e841",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"13bce61d98d4463a8ff4780a86852246\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb74727829e1c8e0f8ecf8983277a82dadd7bf7df19c193b600ade1c628b8d07d07a1a7a216cee714229a389856e855ab\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("bb6ce571bef443d69ca453f6e6afdce2", "res_peerconn", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("e189874ede8344b5901680fc77d4eca1", "default", "{}", "Default project for domain", "1",
        "bb6ce571bef443d69ca453f6e6afdce2");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("bb6ce571bef443d69ca453f6e6afdce2", "root", "{}",
        "$6$rounds=40000$f3AnS9s7$G2iLkpYcqcFCX1SCQ0T/OeTA4nS5FY72acPoAH4zB3LhdbDnbEzmKIVsE6Neg9TVb7NnJh/HzV7wvLaeR/KLI.",
        "1", "bb6ce571bef443d69ca453f6e6afdce2", "e189874ede8344b5901680fc77d4eca1", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "bb6ce571bef443d69ca453f6e6afdce2", "e189874ede8344b5901680fc77d4eca1",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "bb6ce571bef443d69ca453f6e6afdce2", "bb6ce571bef443d69ca453f6e6afdce2",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("bb6ce571bef443d69ca453f6e6afdce2", "bb6ce571bef443d69ca453f6e6afdce2",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_peerconn", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "bb6ce571bef443d69ca453f6e6afdce2", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("d8074e9ad49cd458acb33beb0cb4bae2e2da4ac73df927cfccbaacb13f0b3a30", "bb6ce571bef443d69ca453f6e6afdce2",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"40a50028285641348c7279d6f59e11ac\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b5f64c6f3d5711617dd25f7c78a5b8c6769669287917318f0ee007141cfabe423f538c6d735636d5d4d8d4e9cfd8154e9\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("71ae4ced1f1f4b5ca8b9f976da97f540", "res_pie", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("58fbfb9da52c4a66a3a56ba4d0282c03", "default", "{}", "Default project for domain", "1",
        "71ae4ced1f1f4b5ca8b9f976da97f540");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("71ae4ced1f1f4b5ca8b9f976da97f540", "root", "{}",
        "$6$rounds=40000$QNdbuZaW$TVBqgEIc.0U4XZufzk5dBSpzRH3tdfCJ5zyJHaXnOn2Z6r6OGR/cpGBv05DJC/ntsjymPLVIrp29CEvcrZjC51",
        "1", "71ae4ced1f1f4b5ca8b9f976da97f540", "58fbfb9da52c4a66a3a56ba4d0282c03", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "71ae4ced1f1f4b5ca8b9f976da97f540", "58fbfb9da52c4a66a3a56ba4d0282c03",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "71ae4ced1f1f4b5ca8b9f976da97f540", "71ae4ced1f1f4b5ca8b9f976da97f540",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("71ae4ced1f1f4b5ca8b9f976da97f540", "71ae4ced1f1f4b5ca8b9f976da97f540",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_pie", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "71ae4ced1f1f4b5ca8b9f976da97f540", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("424bcc40601dac88a445f2565cb0f9cb4fed52ebdb2cbe905c47d60757b2d8f2", "71ae4ced1f1f4b5ca8b9f976da97f540",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"c2dd75bba30f44118fd938a17edb15e5\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b3d99b609bc3ed4b983271a67f39c1193ad42ea5c82d81f875f2292b5abe6ff86a2a5e31a511440369069bea66e01d58d\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("87e4071361624e59b940ceba805e375d", "res_pingo", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("33a61636ec1743eaa1dfa8245929e32f", "default", "{}", "Default project for domain", "1",
        "87e4071361624e59b940ceba805e375d");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("87e4071361624e59b940ceba805e375d", "root", "{}",
        "$6$rounds=40000$KZYxlbQY$HpMVWaHKAK7xysZymoxUV.5PERW2pTuG2YrFpUqEDsdk1WBPdrco4pd1ZCKQ6Zng1.1rgNxsD43SesRC5ygAn.",
        "1", "87e4071361624e59b940ceba805e375d", "33a61636ec1743eaa1dfa8245929e32f", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "87e4071361624e59b940ceba805e375d", "33a61636ec1743eaa1dfa8245929e32f",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "87e4071361624e59b940ceba805e375d", "87e4071361624e59b940ceba805e375d",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("87e4071361624e59b940ceba805e375d", "87e4071361624e59b940ceba805e375d",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_pingo", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "87e4071361624e59b940ceba805e375d", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("30610fd6b74f1ff51394e5992f2fe87f6bfcb249d337537ef6a36320dd3901cd", "87e4071361624e59b940ceba805e375d",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"da2fa0a3f48b4af5997c86fc0c7593eb\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b927ae63b46a0690a6b75ec221a74ec65d2c9acea045a3f3895ee7076e976759588ebfc9c72b4d413e355f48e3c37ba75\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("4f53fc9c644e4af388054269501aac16", "res_pns", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("9273fa12b2674695bcd9e0d5b732146a", "default", "{}", "Default project for domain", "1",
        "4f53fc9c644e4af388054269501aac16");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("4f53fc9c644e4af388054269501aac16", "root", "{}",
        "$6$rounds=40000$7brk6wdc$YZwQgNomRB43Tjv1FjTlfeZmrLIs8e./IZoWI47ozuZr.vJSM6.ebJPjJPiwGU4MecjcTxmLXbMjmSmF8mB141",
        "1", "4f53fc9c644e4af388054269501aac16", "9273fa12b2674695bcd9e0d5b732146a", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "4f53fc9c644e4af388054269501aac16", "9273fa12b2674695bcd9e0d5b732146a",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "4f53fc9c644e4af388054269501aac16", "4f53fc9c644e4af388054269501aac16",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("4f53fc9c644e4af388054269501aac16", "4f53fc9c644e4af388054269501aac16",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_pns", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "4f53fc9c644e4af388054269501aac16", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("186bec5b287f769f41738eafff7a9cddc1ab7ef404e7803e5932ec7751ef5c8b", "4f53fc9c644e4af388054269501aac16",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"efc5dd75f5664055bea6d2e4dfcf3fa1\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b2ccbc5aa0d69968f0e301538d98f50bbedd599670ceca3fd507a0637b75b476e40b4c9fc3d4b0e53159cb4028f834703\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("72a5b2c432a04bcfbcf33145f0a2aad8", "res_pnt", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("34441edc040c40889d351ec5b412d18e", "default", "{}", "Default project for domain", "1",
        "72a5b2c432a04bcfbcf33145f0a2aad8");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("72a5b2c432a04bcfbcf33145f0a2aad8", "root", "{}",
        "$6$rounds=40000$6FPvg/Ir$9RQbIk3Vnv.7LQ6oORhBY4sClUAR5ChowjFqAiEIsZilG65xBgkg7Qwsamb5LYOxYT28rs6.cmoNwxon3QO8O1",
        "1", "72a5b2c432a04bcfbcf33145f0a2aad8", "34441edc040c40889d351ec5b412d18e", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "72a5b2c432a04bcfbcf33145f0a2aad8", "34441edc040c40889d351ec5b412d18e",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "72a5b2c432a04bcfbcf33145f0a2aad8", "72a5b2c432a04bcfbcf33145f0a2aad8",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("72a5b2c432a04bcfbcf33145f0a2aad8", "72a5b2c432a04bcfbcf33145f0a2aad8",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_pnt", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "72a5b2c432a04bcfbcf33145f0a2aad8", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("c346e27153dda3fc29eae73eebf70f1323d0813fb6339b7c7864ca4e7df0c889", "72a5b2c432a04bcfbcf33145f0a2aad8",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"8e662429dd3e4afd87bd03e60bba4d74\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bc16a843d22759249e600acaf9f5e4c4677b210fb3229c7e7d1033bf0f58883e92dc48913e96d3cbd3560b61256df8784\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("dc54ad5ddb4c49ab952d179d7d2cebc4", "res_portal", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("a4c36c5bf06c4e02b061149562cd66c0", "default", "{}", "Default project for domain", "1",
        "dc54ad5ddb4c49ab952d179d7d2cebc4");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("dc54ad5ddb4c49ab952d179d7d2cebc4", "root", "{}",
        "$6$rounds=40000$cBS.mPHf$niT0epFjtgwtItlDmFGUBUz1Kz2E/Y3W0jpxwZVsNHOWjx2.fAJgQesgsnn7QZUpYvkGjRayfvMLF1SLIyGRL.",
        "1", "dc54ad5ddb4c49ab952d179d7d2cebc4", "a4c36c5bf06c4e02b061149562cd66c0", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "dc54ad5ddb4c49ab952d179d7d2cebc4", "a4c36c5bf06c4e02b061149562cd66c0",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "dc54ad5ddb4c49ab952d179d7d2cebc4", "dc54ad5ddb4c49ab952d179d7d2cebc4",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("dc54ad5ddb4c49ab952d179d7d2cebc4", "dc54ad5ddb4c49ab952d179d7d2cebc4",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_portal", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "dc54ad5ddb4c49ab952d179d7d2cebc4", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("795279743f7b39ae8a353162b68c08561bc4c9b252ac6807bde4101c002324b1", "dc54ad5ddb4c49ab952d179d7d2cebc4",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"70e65c457ef249a79f8a558c0832a333\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bdc2b8acad9855eefd2c38af7eba61d340f9f6194a8df61b746788ea222e02a5b98bf021e71d2a7e2057068614108a702\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("de1a4c045bd0475c943bd3f9978b425c", "res_projectcenter", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("c02c440a230d4ab39588a89556444e3d", "default", "{}", "Default project for domain", "1",
        "de1a4c045bd0475c943bd3f9978b425c");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("de1a4c045bd0475c943bd3f9978b425c", "root", "{}",
        "$6$rounds=40000$cr3TDZFz$XjhNogI0e9DXlV4yzYlZimBmdGp51nPWJlX.2jU4TPbar5LVvJLNYl3s42u1eo2f.xbEhx5ttFHDA7q6y/aD9.",
        "1", "de1a4c045bd0475c943bd3f9978b425c", "c02c440a230d4ab39588a89556444e3d", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "de1a4c045bd0475c943bd3f9978b425c", "c02c440a230d4ab39588a89556444e3d",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "de1a4c045bd0475c943bd3f9978b425c", "de1a4c045bd0475c943bd3f9978b425c",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("de1a4c045bd0475c943bd3f9978b425c", "de1a4c045bd0475c943bd3f9978b425c",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_projectcenter", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "de1a4c045bd0475c943bd3f9978b425c", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("24d70fe508aadba3b55b6aa72285f687d09680a736464899126498eaa2e98744", "de1a4c045bd0475c943bd3f9978b425c",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"0ea54020a1c74acd864d517a3b563030\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba94405cd56a5836b751043e3b9ab7c07791b9b1a61989e5f574f604c3d40358f0e1d13da2134b58bbf66e6510521dd20\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("4e96509224a14947925b346a0a1ee66a", "res_proxy", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("241910ddb77e49ea91cbb49aa8e79418", "default", "{}", "Default project for domain", "1",
        "4e96509224a14947925b346a0a1ee66a");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("4e96509224a14947925b346a0a1ee66a", "root", "{}",
        "$6$rounds=40000$zYQwCy6H$FTb4jYp5W8m.4bhpvxD7IJugkLI6v0ROqCKiG3dDqfTmhOcRtAb8CrkGkQORehNDUweeo2BZOpMBjmNPztgjK/",
        "1", "4e96509224a14947925b346a0a1ee66a", "241910ddb77e49ea91cbb49aa8e79418", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "4e96509224a14947925b346a0a1ee66a", "241910ddb77e49ea91cbb49aa8e79418",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "4e96509224a14947925b346a0a1ee66a", "4e96509224a14947925b346a0a1ee66a",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("4e96509224a14947925b346a0a1ee66a", "4e96509224a14947925b346a0a1ee66a",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_proxy", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "4e96509224a14947925b346a0a1ee66a", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("c0bb6f2ca4c0c5119390976608a101ff7ee71a8f1ad0dd314c3ef53c93a18e09", "4e96509224a14947925b346a0a1ee66a",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"394440caa041474ba878152c98221409\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57be2fb6473ee0ecffeba12422f2eb7f02ee003862a5048ddef7163bcc1a036a78a8e127ff0c31872f323f2e633004a2c47\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("e196e28f7d0741998f49081b406fdab8", "res_proxy-user", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("70aecfdb4cc94a4c87b1bee533bdce63", "default", "{}", "Default project for domain", "1",
        "e196e28f7d0741998f49081b406fdab8");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("e196e28f7d0741998f49081b406fdab8", "root", "{}",
        "$6$rounds=40000$ZPpHAYzG$rB7EtBQzc8MbPyUzP7u0Cmh7ByOQBztZcOZ/zbZXFbl/LC/YVDyrJICA1y9/KN4Cwnzsts3GOXR3rSm2VQwHC0",
        "1", "e196e28f7d0741998f49081b406fdab8", "70aecfdb4cc94a4c87b1bee533bdce63", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "e196e28f7d0741998f49081b406fdab8", "70aecfdb4cc94a4c87b1bee533bdce63",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "e196e28f7d0741998f49081b406fdab8", "e196e28f7d0741998f49081b406fdab8",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("e196e28f7d0741998f49081b406fdab8", "e196e28f7d0741998f49081b406fdab8",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_proxy-user", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "e196e28f7d0741998f49081b406fdab8", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("a02627afca84d301661471d3e636929588a42f9b8889890f31c6aa755c90e8d9", "e196e28f7d0741998f49081b406fdab8",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"31b24e28aa014939a975096b8d4cdc6c\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b06582fb3729bc0459e59bcfa7761bb8ce503bb4d0ed30eddd40f8ed02beb235c5fc0d676e7d1812a4e87ad19661d4c16\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("5b377ae8500948e0ae448b15b3d201ec", "res_rds", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("01499da05bb24e6ab3ba4e633a21d415", "default", "{}", "Default project for domain", "1",
        "5b377ae8500948e0ae448b15b3d201ec");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("5b377ae8500948e0ae448b15b3d201ec", "root", "{}",
        "$6$rounds=40000$9GS2oSNi$WERARnzjFqim0FokQH0pHMi8fSP2g39LPa.slxy4F7JzmpMplcycNF0sHLtELaMVZOt.bj0t3.SQw.NB35wbq.",
        "1", "5b377ae8500948e0ae448b15b3d201ec", "01499da05bb24e6ab3ba4e633a21d415", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "5b377ae8500948e0ae448b15b3d201ec", "01499da05bb24e6ab3ba4e633a21d415",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "5b377ae8500948e0ae448b15b3d201ec", "5b377ae8500948e0ae448b15b3d201ec",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("5b377ae8500948e0ae448b15b3d201ec", "5b377ae8500948e0ae448b15b3d201ec",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_rds", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "5b377ae8500948e0ae448b15b3d201ec", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("c838dfbf4972fbb61d536b03b858979d5dd56411fb6e3a49acbade17b6a4fb6e", "5b377ae8500948e0ae448b15b3d201ec",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"da878d98736d4b3c9fbe23e65861463f\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b8a7c1e9fbd8412d3301414040da4ee624abbcf013263dd41d073a41871bd3480825cd2faec8eb1389b5e15a385f5d42e\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("7f49a571607842d5a0fcc39474b1ce04", "res_rds_dms_logic", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("a07f6ebebeca497d8cee9b7787e7de80", "default", "{}", "Default project for domain", "1",
        "7f49a571607842d5a0fcc39474b1ce04");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("7f49a571607842d5a0fcc39474b1ce04", "root", "{}",
        "$6$rounds=40000$AjikiloY$22lYejFjBtYpmSqo3lq9PWHuFTtQ9Gz8MwCMNdW9aRi4d14.Fb./RZl/ImlzKCEOAnPuOWcKHszxZ76Wa75Hp.",
        "1", "7f49a571607842d5a0fcc39474b1ce04", "a07f6ebebeca497d8cee9b7787e7de80", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "7f49a571607842d5a0fcc39474b1ce04", "a07f6ebebeca497d8cee9b7787e7de80",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "7f49a571607842d5a0fcc39474b1ce04", "7f49a571607842d5a0fcc39474b1ce04",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("7f49a571607842d5a0fcc39474b1ce04", "7f49a571607842d5a0fcc39474b1ce04",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_rds_dms_logic", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "7f49a571607842d5a0fcc39474b1ce04", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8c4be02b48458190880976912e82c064566114e6a6f38b5140237a5d58d65175", "7f49a571607842d5a0fcc39474b1ce04",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"eaea71f8e12c47ffa590530f7c0b6e35\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b7e87ce287859f06c309877c739aba43e74ae796697a382621b003809b87071201f74cbb76ec1a6bfddc15fc9d8349e99\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("044ad1a3ba604087850f713e1def9fe7", "res_retail", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("2f724fcfcbc44389a7dbdab2686c0d78", "default", "{}", "Default project for domain", "1",
        "044ad1a3ba604087850f713e1def9fe7");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("044ad1a3ba604087850f713e1def9fe7", "root", "{}",
        "$6$rounds=40000$.bXXUDpm$G0XtXPsVLssqWWPA4G9iUig6VVK7.YALPzCqda61zwyreIE2rYTqqm4njioVBOcSUjSFVIBGAO3a6v43FSn6L1",
        "1", "044ad1a3ba604087850f713e1def9fe7", "2f724fcfcbc44389a7dbdab2686c0d78", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "044ad1a3ba604087850f713e1def9fe7", "2f724fcfcbc44389a7dbdab2686c0d78",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "044ad1a3ba604087850f713e1def9fe7", "044ad1a3ba604087850f713e1def9fe7",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("044ad1a3ba604087850f713e1def9fe7", "044ad1a3ba604087850f713e1def9fe7",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_retail", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "044ad1a3ba604087850f713e1def9fe7", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("baa7bbb80519e270021c1ebd6e226ce127438e243411e344e92d6bacd0d572fe", "044ad1a3ba604087850f713e1def9fe7",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"a6990d06b11a4979aafdde8b96d21579\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b994e8b6e60200f72446e6fafb4cf7c56acf37c503a634416caf3a8f3e90a73d3a3954e0f87cac89cd3e7781dcc3ae6c3\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("3c297af290ca4601a65f99b1f400ab6f", "res_risk", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("2bc087eaa92e4f0f80a6da22dcacb52a", "default", "{}", "Default project for domain", "1",
        "3c297af290ca4601a65f99b1f400ab6f");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("3c297af290ca4601a65f99b1f400ab6f", "root", "{}",
        "$6$rounds=40000$3uTcs5Ps$Sla7qGHxrVRjfRzQ21Zfh/D9v7quucG92uJA9UtRauhn1LjllFITTEnxDfESkUXw.TovKqg2cz6qJEUoHSazo/",
        "1", "3c297af290ca4601a65f99b1f400ab6f", "2bc087eaa92e4f0f80a6da22dcacb52a", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "3c297af290ca4601a65f99b1f400ab6f", "2bc087eaa92e4f0f80a6da22dcacb52a",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "3c297af290ca4601a65f99b1f400ab6f", "3c297af290ca4601a65f99b1f400ab6f",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("3c297af290ca4601a65f99b1f400ab6f", "3c297af290ca4601a65f99b1f400ab6f",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_risk", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "3c297af290ca4601a65f99b1f400ab6f", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("94ee9bea86c4ed25e7e23f45c20cf5efcbe70280719754af420b166003527e64", "3c297af290ca4601a65f99b1f400ab6f",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"057ce1929de04528bb6e1988fa835a69\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b389f5286c4801de337abe00f7735ab0664d8d77bb7b7e808d9af627f469dc25c8a0f68770214459432be8fb4c9d24f3f\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("512063aacbf64ab08c84916ace631e37", "res_rtc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("c7e5126b943b49dda23d3169bfc31d69", "default", "{}", "Default project for domain", "1",
        "512063aacbf64ab08c84916ace631e37");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("512063aacbf64ab08c84916ace631e37", "root", "{}",
        "$6$rounds=40000$YVX8J2Dj$0TIMP4Ysn8NIHkEklNvaIL3hZEcEsBuxSagUc5r3ZngWbLQILp3iiJb2ni4dSx6.XiYM0dFtoDpvo0vh2T4Ay/",
        "1", "512063aacbf64ab08c84916ace631e37", "c7e5126b943b49dda23d3169bfc31d69", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "512063aacbf64ab08c84916ace631e37", "c7e5126b943b49dda23d3169bfc31d69",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "512063aacbf64ab08c84916ace631e37", "512063aacbf64ab08c84916ace631e37",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("512063aacbf64ab08c84916ace631e37", "512063aacbf64ab08c84916ace631e37",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_rtc", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "512063aacbf64ab08c84916ace631e37", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("b4b76bae7526750f6f36b09832ccc3d0c1913f8c92979c78c3aa84ed428546cc", "512063aacbf64ab08c84916ace631e37",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"1a72295ba225455ca9f0f4c90ce39411\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b53904f76ad5077a3621fdbf671d087e3c19643a31a13bb00943d27aea040885051a5fcf4309ee3c23565a4feee0800b8\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("eab98ef779e446f79294b6124cb488ec", "res_scs", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("43f058e5daff4666b4388f5467de61ae", "default", "{}", "Default project for domain", "1",
        "eab98ef779e446f79294b6124cb488ec");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("eab98ef779e446f79294b6124cb488ec", "root", "{}",
        "$6$rounds=40000$W7mGu1MH$1Qv9wD237BfSjF7xNda66.gsgj1QI8Otyh2Mg/H8Es.fuwbGLKo9meqG18/a9yogzZRCZ79cFaXK9MbuW0UWm1",
        "1", "eab98ef779e446f79294b6124cb488ec", "43f058e5daff4666b4388f5467de61ae", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "eab98ef779e446f79294b6124cb488ec", "43f058e5daff4666b4388f5467de61ae",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "eab98ef779e446f79294b6124cb488ec", "eab98ef779e446f79294b6124cb488ec",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("eab98ef779e446f79294b6124cb488ec", "eab98ef779e446f79294b6124cb488ec",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_scs", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "eab98ef779e446f79294b6124cb488ec", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("7e6b0c2633c7be2790ab9d0cdf698a738435ef0c468dcc165a0ee8db50aeedf5", "eab98ef779e446f79294b6124cb488ec",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"b4d9eb33791e4cb2a49b7e3ac55aa674\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb9417507f2e9952c4bddae04ad78a8c5c30221ce4fe0a4b08a4607c94246b467124e38e321224ceb7933acc939e155d6\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("d904278a23d746e0a52b6784a0d02354", "res_service_config", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("778ebcecc3b3483596ce30b6edd8c1a2", "default", "{}", "Default project for domain", "1",
        "d904278a23d746e0a52b6784a0d02354");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("d904278a23d746e0a52b6784a0d02354", "root", "{}",
        "$6$rounds=40000$t3KzR4Vc$oRgkH9uyglnld6uWcmQvyVy5/ej9MxPBKbKrApdbSmEL8cCxSfY3XK7yllVg/47Y3uTDzhq/mh4dC2HYM7/jj1",
        "1", "d904278a23d746e0a52b6784a0d02354", "778ebcecc3b3483596ce30b6edd8c1a2", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "d904278a23d746e0a52b6784a0d02354", "778ebcecc3b3483596ce30b6edd8c1a2",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "d904278a23d746e0a52b6784a0d02354", "d904278a23d746e0a52b6784a0d02354",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("d904278a23d746e0a52b6784a0d02354", "d904278a23d746e0a52b6784a0d02354",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_service_config", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "d904278a23d746e0a52b6784a0d02354", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("3f3dcee383011298b368d5cfd48f78c7d8937d99d985afbce018e53e12c4a9b4", "d904278a23d746e0a52b6784a0d02354",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"c6428b36e42648a28387b5d1713ebf38\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb4bdc89547bf6764f85244dbe4c71a537980d9cc2dae28791010b08d45eb5f970c0c38f24ef748860da17ed7833c2f75\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("a345e6924caf4843b87e27d12e382225", "res_service_product", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("0bae278c66294334bc09edea9c4b5976", "default", "{}", "Default project for domain", "1",
        "a345e6924caf4843b87e27d12e382225");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("a345e6924caf4843b87e27d12e382225", "root", "{}",
        "$6$rounds=40000$YGts/reo$gvxitz5EekDx7Ffi5w8QnZNJZ0HApbv76GktUiVQHtPakdc7eiORjk.1jSbSq/c4HXqt7VLIULCj8.X3i7vLR1",
        "1", "a345e6924caf4843b87e27d12e382225", "0bae278c66294334bc09edea9c4b5976", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "a345e6924caf4843b87e27d12e382225", "0bae278c66294334bc09edea9c4b5976",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "a345e6924caf4843b87e27d12e382225", "a345e6924caf4843b87e27d12e382225",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("a345e6924caf4843b87e27d12e382225", "a345e6924caf4843b87e27d12e382225",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_service_product", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "a345e6924caf4843b87e27d12e382225", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("fd4f76ea172e997e5bc6a24cde220ffd40cf44e4b036e43af0a839ae3f29bcbb", "a345e6924caf4843b87e27d12e382225",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"98296489151445e99d2dd18a930d8abd\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b344045e71d2b13911c75764125dbc0681fb8e2de72a94051413f420efa5ee7e5cf8b192e1fcefbc438bf24e33999d1f2\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("b4287b85056b425b92296b8c18af836f", "res_service_register", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("b0a29c1609cd4eb080eb0312058f48d3", "default", "{}", "Default project for domain", "1",
        "b4287b85056b425b92296b8c18af836f");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("b4287b85056b425b92296b8c18af836f", "root", "{}",
        "$6$rounds=40000$buEeVH0m$CAGu.0SF5xaqS7IOG6SR02OYo5FrM9M4IxSFz7hDnrjRVh.ahS3Cg75xdc7d4INBQhtxGTVx8v0QSq//c8hhe/",
        "1", "b4287b85056b425b92296b8c18af836f", "b0a29c1609cd4eb080eb0312058f48d3", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "b4287b85056b425b92296b8c18af836f", "b0a29c1609cd4eb080eb0312058f48d3",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "b4287b85056b425b92296b8c18af836f", "b4287b85056b425b92296b8c18af836f",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("b4287b85056b425b92296b8c18af836f", "b4287b85056b425b92296b8c18af836f",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_service_register", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "b4287b85056b425b92296b8c18af836f", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("a71c10e2a7523230049112d20605673b98c0cb3271194661e1bed9425f496781", "b4287b85056b425b92296b8c18af836f",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"647d5dc5ee4744cdb39c59e66f08afc1\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b1d7051b53f33e37f5709c467276ccfc708546abed35fdfb1f3ef6e971c9e9abd3adc3a56955d646e13472080c73a9f0c\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("0f85d04ad464433b96954f6131490843", "res_service_scheduler", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("383661a0c57f4cf9aaa45e9e10c233cc", "default", "{}", "Default project for domain", "1",
        "0f85d04ad464433b96954f6131490843");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("0f85d04ad464433b96954f6131490843", "root", "{}",
        "$6$rounds=40000$6VpLyBpg$VZBhwfkKbfPkZAvvq6yDM/oKrcDkxnD17I4aRtYUUMWgM13en8fG6PpUIXRwJMzxbtE5Vxx.rXwynHIjyi/Hi/",
        "1", "0f85d04ad464433b96954f6131490843", "383661a0c57f4cf9aaa45e9e10c233cc", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "0f85d04ad464433b96954f6131490843", "383661a0c57f4cf9aaa45e9e10c233cc",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "0f85d04ad464433b96954f6131490843", "0f85d04ad464433b96954f6131490843",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("0f85d04ad464433b96954f6131490843", "0f85d04ad464433b96954f6131490843",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_service_scheduler", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "0f85d04ad464433b96954f6131490843", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("520e96d496383cba5deb3251bfeca6667f6708ff3d94e7c3a7e8ee4f5a257319", "0f85d04ad464433b96954f6131490843",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"0afc09d6d80c46a38d70a09481aef33d\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b845b916ea2fff187142b0851d732644427339139a49df9b3be442271280e51b404716bdd2d9897b8a4da6345e9fd9b60\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("85e52e0805f449d594ca78d47c7e7d69", "res_ses", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("607f6df7cecd47a78c3f6ab3b270be74", "default", "{}", "Default project for domain", "1",
        "85e52e0805f449d594ca78d47c7e7d69");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("85e52e0805f449d594ca78d47c7e7d69", "root", "{}",
        "$6$rounds=40000$5En/hrH3$zhHnbNHAAvg1RHHX60bRZDwVUavphlV3jv7W7B.choZyTs2Q6tjrCWWf/2ARAqAg36o9VL0ZKLJvtS35qzsfS.",
        "1", "85e52e0805f449d594ca78d47c7e7d69", "607f6df7cecd47a78c3f6ab3b270be74", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "85e52e0805f449d594ca78d47c7e7d69", "607f6df7cecd47a78c3f6ab3b270be74",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "85e52e0805f449d594ca78d47c7e7d69", "85e52e0805f449d594ca78d47c7e7d69",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("85e52e0805f449d594ca78d47c7e7d69", "85e52e0805f449d594ca78d47c7e7d69",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_ses", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "85e52e0805f449d594ca78d47c7e7d69", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("324f6ab6cd1b876c219676d4c9a5a30ac3ce2547c5f7aa1200e469254b0ad1b2", "85e52e0805f449d594ca78d47c7e7d69",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"002b12d5c78245c0b2e748a66b51a668\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b69c55718aa6f4ac618818ebedcb2d5faafb0e0e9ef2881317ecf5a11e72c4f5936c0a280113044b2f620654899cc2eab\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("ba4bb243f5d541f6965a56cac5505fa2", "res_showx", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("e0e016b2e6bf4b1aade06e753bdc2d79", "default", "{}", "Default project for domain", "1",
        "ba4bb243f5d541f6965a56cac5505fa2");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("ba4bb243f5d541f6965a56cac5505fa2", "root", "{}",
        "$6$rounds=40000$3H8BE13j$Fk20NokFo5CDz6ChysiM3stBqvUMyWydLpSD7/aJcjCY41EKPkD7XDBrPATK6XBHocOA4WXRKCXcpWtLhDqcJ0",
        "1", "ba4bb243f5d541f6965a56cac5505fa2", "e0e016b2e6bf4b1aade06e753bdc2d79", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "ba4bb243f5d541f6965a56cac5505fa2", "e0e016b2e6bf4b1aade06e753bdc2d79",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "ba4bb243f5d541f6965a56cac5505fa2", "ba4bb243f5d541f6965a56cac5505fa2",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("ba4bb243f5d541f6965a56cac5505fa2", "ba4bb243f5d541f6965a56cac5505fa2",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_showx", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "ba4bb243f5d541f6965a56cac5505fa2", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("db170328b43d11b1316c00b1272ee842d6f5f1169a3bbd1874f7b38f95361c2b", "ba4bb243f5d541f6965a56cac5505fa2",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"bc5533d5243d4a6b9fae685649c60f37\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b8b9a9b77dbe8a7d04a9de3ef39b1222468c5a55520b4c57451bd3da58b860f14194dfac1ffb4291df33aa5545502bea5\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("46dced03907a4f9fad0a9c04863815c2", "res_sms", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("69676ec33861440595d9de526db5d9db", "default", "{}", "Default project for domain", "1",
        "46dced03907a4f9fad0a9c04863815c2");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("46dced03907a4f9fad0a9c04863815c2", "root", "{}",
        "$6$rounds=40000$/L0pbsE7$Y/JUIZ0OxMRZqjdW1u0b7nORTEoXt5ylsiVPozmVz6zQkbduhGHEEyoGHXo3RIMFosl1BUCK5tCaQl2R.caZr1",
        "1", "46dced03907a4f9fad0a9c04863815c2", "69676ec33861440595d9de526db5d9db", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "46dced03907a4f9fad0a9c04863815c2", "69676ec33861440595d9de526db5d9db",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "46dced03907a4f9fad0a9c04863815c2", "46dced03907a4f9fad0a9c04863815c2",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("46dced03907a4f9fad0a9c04863815c2", "46dced03907a4f9fad0a9c04863815c2",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_sms", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "46dced03907a4f9fad0a9c04863815c2", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("299a2865a33d9259f87f869b7dc678a2714422bd20e37824792353513b06123d", "46dced03907a4f9fad0a9c04863815c2",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"82111397052b4aa09d3cdf21b6e5b698\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bc5636b2626277bd86385082bb9490a82db88b0a821b2d7b9eed21a01d34a9ceb5c111685eb0fd9e4ec314c460d859fb3\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("1348e8c930184ba18d0900cb5b5d0212", "res_smw", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("555cf675a7064de9889fc58f2d34f270", "default", "{}", "Default project for domain", "1",
        "1348e8c930184ba18d0900cb5b5d0212");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("1348e8c930184ba18d0900cb5b5d0212", "root", "{}",
        "$6$rounds=40000$64vJpuXq$3QHE1/O.cABvqKijf7b1gtDhTL7dtDe8ryZurbXoewH6P8MUWIOUu.KvhgUjK7QQ.enxXJZjh4LRtYoTS2/.w/",
        "1", "1348e8c930184ba18d0900cb5b5d0212", "555cf675a7064de9889fc58f2d34f270", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "1348e8c930184ba18d0900cb5b5d0212", "555cf675a7064de9889fc58f2d34f270",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "1348e8c930184ba18d0900cb5b5d0212", "1348e8c930184ba18d0900cb5b5d0212",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("1348e8c930184ba18d0900cb5b5d0212", "1348e8c930184ba18d0900cb5b5d0212",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_smw", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "1348e8c930184ba18d0900cb5b5d0212", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("09014664a663562258449641241851c71a3f55a41272c18ead8ffa7d424e13fa", "1348e8c930184ba18d0900cb5b5d0212",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"c4aa1514edc04e3c88619a1d855ab78b\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b2950b43678af4c089c905d4b80bcf58b3521b4bf1686f2619cf91297befdb75d8cfba3a275618f8babbafa5071c3d560\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("7b836d05ed834f58acca72db603b4784", "res_sq", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("64ef1a576350413da5f2b8865aa4da73", "default", "{}", "Default project for domain", "1",
        "7b836d05ed834f58acca72db603b4784");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("7b836d05ed834f58acca72db603b4784", "root", "{}",
        "$6$rounds=40000$ycLbOF/1$gIJtdPH5l9I5jY5qBQRKltAKKoWgnfeqY6pcipH0wEE6yJ0CjtSPma0Ir6qBERU.xxoMOSBKOK4yZq21PWiDc.",
        "1", "7b836d05ed834f58acca72db603b4784", "64ef1a576350413da5f2b8865aa4da73", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "7b836d05ed834f58acca72db603b4784", "64ef1a576350413da5f2b8865aa4da73",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "7b836d05ed834f58acca72db603b4784", "7b836d05ed834f58acca72db603b4784",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("7b836d05ed834f58acca72db603b4784", "7b836d05ed834f58acca72db603b4784",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_sq", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "7b836d05ed834f58acca72db603b4784", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("9665ed7dcc6405b728dc25f0fffbb88718d3719ea2f3f2563790f26f76f08782", "7b836d05ed834f58acca72db603b4784",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"c697c6224f874dffbefbc889ddabe259\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b4224142ed28d3e6f510985d4e7d860c71243b891aee80d079a397730dc20530a750a9a2ae547968bd75a27b4159a3306\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("27078b8db0d14015bf69d757aa20c67b", "res_sqlaudit", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("0def04e76f554ee8b8a84e86844d11cb", "default", "{}", "Default project for domain", "1",
        "27078b8db0d14015bf69d757aa20c67b");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("27078b8db0d14015bf69d757aa20c67b", "root", "{}",
        "$6$rounds=40000$dQ32g.Yw$/TKSMSoAEKDXU.zoC13hc/nT.bNQZbyct04assDb/qIgtIE9H/rBgXTtXhh91AEOdSJrK8JLNR8bQY1agrhLV0",
        "1", "27078b8db0d14015bf69d757aa20c67b", "0def04e76f554ee8b8a84e86844d11cb", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "27078b8db0d14015bf69d757aa20c67b", "0def04e76f554ee8b8a84e86844d11cb",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "27078b8db0d14015bf69d757aa20c67b", "27078b8db0d14015bf69d757aa20c67b",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("27078b8db0d14015bf69d757aa20c67b", "27078b8db0d14015bf69d757aa20c67b",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_sqlaudit", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "27078b8db0d14015bf69d757aa20c67b", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("fc7bba9fffd876bfd391a3320abdca02077cda5156221ac7dbd520d73dfc8bfe", "27078b8db0d14015bf69d757aa20c67b",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"5d839d70098f49c395b51d4419122b85\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b7c54e051230294b0d440e447d775e53d863c0b17070c4e86d25e1cd9c1256e4bcb0c28d835293cda1d4d10dea2ccce4d\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("bb92d26d493a452f94dba02ea52001bc", "res_srd", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("f63bf5813df44a47adeff325f7d00962", "default", "{}", "Default project for domain", "1",
        "bb92d26d493a452f94dba02ea52001bc");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("bb92d26d493a452f94dba02ea52001bc", "root", "{}",
        "$6$rounds=40000$X4dfEP9Y$Wev0Sbgy7073a5LJ5xrdSX6I2SJq8pbHCL/KCqRGrNxOElDvSPQP730Jjy1niNCgCJNeC.Ik91/f4NHkP.ONR0",
        "1", "bb92d26d493a452f94dba02ea52001bc", "f63bf5813df44a47adeff325f7d00962", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "bb92d26d493a452f94dba02ea52001bc", "f63bf5813df44a47adeff325f7d00962",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "bb92d26d493a452f94dba02ea52001bc", "bb92d26d493a452f94dba02ea52001bc",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("bb92d26d493a452f94dba02ea52001bc", "bb92d26d493a452f94dba02ea52001bc",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_srd", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "bb92d26d493a452f94dba02ea52001bc", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8ab9ddc58204ff510ca908d00a059d6dbf779c0d80eb8f29fb30f7184fc5c49a", "bb92d26d493a452f94dba02ea52001bc",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"68185db5e840453baaa67fd602b7d07a\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b644c15e36a100e329d645fee8e85509f9b67d2147bc2198b3401dafcefdbb4c3e175cd1c0143edc2ddd7f9423759fad1\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("4c0ee8f89eb14be1be5c5fdf9c28f5d7", "res_sts", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("5deafb85257b45eb866ca5e55ea53c25", "default", "{}", "Default project for domain", "1",
        "4c0ee8f89eb14be1be5c5fdf9c28f5d7");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("4c0ee8f89eb14be1be5c5fdf9c28f5d7", "root", "{}",
        "$6$rounds=40000$lMailY1e$sLVYNMzxR.RK9zbDldTfiHa5j1OB7BDlrXKfrQxHxvV2BJnkBIMm4.Zjkj.RnbFsixrvepYRsrVF0awum8jb0.",
        "1", "4c0ee8f89eb14be1be5c5fdf9c28f5d7", "5deafb85257b45eb866ca5e55ea53c25", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "4c0ee8f89eb14be1be5c5fdf9c28f5d7", "5deafb85257b45eb866ca5e55ea53c25",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "4c0ee8f89eb14be1be5c5fdf9c28f5d7", "4c0ee8f89eb14be1be5c5fdf9c28f5d7",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("4c0ee8f89eb14be1be5c5fdf9c28f5d7", "4c0ee8f89eb14be1be5c5fdf9c28f5d7",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_sts", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "4c0ee8f89eb14be1be5c5fdf9c28f5d7", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("d4c3fdbbf80324e0238d8ccf6eb4c36ec9dd2434ad5583348f3277196edbd10b", "4c0ee8f89eb14be1be5c5fdf9c28f5d7",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"342b4d1bf6d24297b780f4caf750dbd0\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bcf3b1ed140fb1bbf0fe78a7f49f71bbb73feb1c8ade2b0d74b9e3498433f6ce9e0e5ec427a492bca75fa7c176ac8fd59\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("efc9ae91e3e24680b6e19f1a7203cbb0", "res_sugar", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("d77646ad75d84c5d9d8583bd1b6f9c6c", "default", "{}", "Default project for domain", "1",
        "efc9ae91e3e24680b6e19f1a7203cbb0");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("efc9ae91e3e24680b6e19f1a7203cbb0", "root", "{}",
        "$6$rounds=40000$FX8o4xbl$lR69uVaN540gupUfiMRX9tgW4rV9Zi0KPAWahvcJjCBQ192VpMBjPmsqoHfo8l5i5LGTOaSySdkdv1RZSW3ll/",
        "1", "efc9ae91e3e24680b6e19f1a7203cbb0", "d77646ad75d84c5d9d8583bd1b6f9c6c", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "efc9ae91e3e24680b6e19f1a7203cbb0", "d77646ad75d84c5d9d8583bd1b6f9c6c",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "efc9ae91e3e24680b6e19f1a7203cbb0", "efc9ae91e3e24680b6e19f1a7203cbb0",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("efc9ae91e3e24680b6e19f1a7203cbb0", "efc9ae91e3e24680b6e19f1a7203cbb0",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_sugar", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "efc9ae91e3e24680b6e19f1a7203cbb0", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("a4068e29b9028d79de73d68243ca21b97da4d18e5bc5e9bc67826bd7a878c14e", "efc9ae91e3e24680b6e19f1a7203cbb0",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"46eaeed188d24f45b31f2b2a888137a4\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bd3b6bc589502295b8ca4b905e06df39c9e0bb5c776599131934f26e0856a82854e2a7ceac090d878c18ce025b5b00c29\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("2c9ece624a0d42d58aee78a6f7d637c4", "res_swift", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("5f88691a4fd84a2b97bf426c9b43e40d", "default", "{}", "Default project for domain", "1",
        "2c9ece624a0d42d58aee78a6f7d637c4");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("2c9ece624a0d42d58aee78a6f7d637c4", "root", "{}",
        "$6$rounds=40000$0/5dmIIy$UewdzIz.1Rpg9.5lqqHm7StlRHfqxk5XXUu92aaOQrhwFx6epvi53wsgjRmEc1l.d8y8Ry97j3a93Y/WDseD70",
        "1", "2c9ece624a0d42d58aee78a6f7d637c4", "5f88691a4fd84a2b97bf426c9b43e40d", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "2c9ece624a0d42d58aee78a6f7d637c4", "5f88691a4fd84a2b97bf426c9b43e40d",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "2c9ece624a0d42d58aee78a6f7d637c4", "2c9ece624a0d42d58aee78a6f7d637c4",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("2c9ece624a0d42d58aee78a6f7d637c4", "2c9ece624a0d42d58aee78a6f7d637c4",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_swift", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "2c9ece624a0d42d58aee78a6f7d637c4", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("bb7af3d24e6ae9279e022a8af02571f0f11176e74e1148aebe16e2e1a1ea3d14", "2c9ece624a0d42d58aee78a6f7d637c4",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"24f404dc600947dc96449cd905209eda\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b3246413a80209ad32ec561bdada1d0596579484ec28bb172bccfe15c3884276c4a1ecd20c561f8293d2dc179389b179d\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("1284bbd78d7642f4a0ae7a3449aaba40", "res_tag", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("b7a40a7cf2c74d2382c661fb3111a5f4", "default", "{}", "Default project for domain", "1",
        "1284bbd78d7642f4a0ae7a3449aaba40");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("1284bbd78d7642f4a0ae7a3449aaba40", "root", "{}",
        "$6$rounds=40000$FMDPziJM$Tz1lxXlxS/Rhk1oMXAqLQhsDA8oMpfT5NgYJcAWRRCf6xVFNcKJstiEEqCVLtc85ynS9rzpEpoijuRQRaERgB1",
        "1", "1284bbd78d7642f4a0ae7a3449aaba40", "b7a40a7cf2c74d2382c661fb3111a5f4", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "1284bbd78d7642f4a0ae7a3449aaba40", "b7a40a7cf2c74d2382c661fb3111a5f4",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "1284bbd78d7642f4a0ae7a3449aaba40", "1284bbd78d7642f4a0ae7a3449aaba40",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("1284bbd78d7642f4a0ae7a3449aaba40", "1284bbd78d7642f4a0ae7a3449aaba40",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_tag", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "1284bbd78d7642f4a0ae7a3449aaba40", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("6de9f30d05983f13f46fe5d1b7b58ece330b4221d941a9c475193eef7bcd3938", "1284bbd78d7642f4a0ae7a3449aaba40",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"d5d47888da594dcd9f0cd57826d708f7\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b19cb60e574bf2674531af1f6fd8ef5d16d8803570816aa317e44bdafc212752e4eeb14960d0b378fdc2e493f2982c41c\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("e9b9e5add8cd4f778bb8c728bc33de91", "res_test", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("1666edec53484adbbf399a957f0e2c54", "default", "{}", "Default project for domain", "1",
        "e9b9e5add8cd4f778bb8c728bc33de91");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("e9b9e5add8cd4f778bb8c728bc33de91", "root", "{}",
        "$6$rounds=40000$A.bbYOHe$sRLAawaIr8MjuXDmkN2JeBKpQJTmjSIqgE9heQfmqoMQYg3GeiV95ZFJS5T6kx/OGXgt1ECXQCYFN4j0lF14a1",
        "1", "e9b9e5add8cd4f778bb8c728bc33de91", "1666edec53484adbbf399a957f0e2c54", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "e9b9e5add8cd4f778bb8c728bc33de91", "1666edec53484adbbf399a957f0e2c54",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "e9b9e5add8cd4f778bb8c728bc33de91", "e9b9e5add8cd4f778bb8c728bc33de91",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("e9b9e5add8cd4f778bb8c728bc33de91", "e9b9e5add8cd4f778bb8c728bc33de91",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_test", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "e9b9e5add8cd4f778bb8c728bc33de91", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("66298a4c7ba668b2042e7e57982fe4dc7491123310bb88ebc97a529cbf5112d7", "e9b9e5add8cd4f778bb8c728bc33de91",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"84a09246a6e54887bc77288454571bf7\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b0b8dd32010d329aa1f149a20fb471b0e8769687e84ce5fdf3862044c736ea4befc1df8cedabbbab59bdda638090917b3\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("ba6c853b14424a7a932510b23dd136af", "res_test-create-vms", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("69e61a88c8db4f58a50ee8570bf5e239", "default", "{}", "Default project for domain", "1",
        "ba6c853b14424a7a932510b23dd136af");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("ba6c853b14424a7a932510b23dd136af", "root", "{}",
        "$6$rounds=40000$blbdIC00$Tt2GMxoNDXGeslYoijNrsuzsxiRKn6ROotmivqSCEi9W2ISfPZydHgV18izjHiBff49.pDhRSuI/YUxPoQBA81",
        "1", "ba6c853b14424a7a932510b23dd136af", "69e61a88c8db4f58a50ee8570bf5e239", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "ba6c853b14424a7a932510b23dd136af", "69e61a88c8db4f58a50ee8570bf5e239",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "ba6c853b14424a7a932510b23dd136af", "ba6c853b14424a7a932510b23dd136af",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("ba6c853b14424a7a932510b23dd136af", "ba6c853b14424a7a932510b23dd136af",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_test-create-vms", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "ba6c853b14424a7a932510b23dd136af", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8d20cc45fc3ecb3b8ba3d32372d648efc5f1fdea19694944cf8a5cd07fa94184", "ba6c853b14424a7a932510b23dd136af",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"594e695d631c458fac257c9d92f8db7b\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bed08045945c19d9c96f2d9cdc785fa7295e1df821fdd5a61c95b397e7d18b0c49b400ef7f9a0c89b1285b13778acf08f\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("92434d6513bf4e01ae5ca2bbf4fe70b0", "res_test-ziheng", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("aa00ee98b1dd4448bec91bf8a9384c8a", "default", "{}", "Default project for domain", "1",
        "92434d6513bf4e01ae5ca2bbf4fe70b0");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("92434d6513bf4e01ae5ca2bbf4fe70b0", "root", "{}",
        "$6$rounds=40000$wmEZqPu2$n/Qlw2p8ugUm2Gg1L2iKC6r39CUxiryZEuYV16gFyv.0AMWbn13cgSKInprObPped42btEFHUJUvHcX/UvgM3/",
        "1", "92434d6513bf4e01ae5ca2bbf4fe70b0", "aa00ee98b1dd4448bec91bf8a9384c8a", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "92434d6513bf4e01ae5ca2bbf4fe70b0", "aa00ee98b1dd4448bec91bf8a9384c8a",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "92434d6513bf4e01ae5ca2bbf4fe70b0", "92434d6513bf4e01ae5ca2bbf4fe70b0",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("92434d6513bf4e01ae5ca2bbf4fe70b0", "92434d6513bf4e01ae5ca2bbf4fe70b0",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_test-ziheng", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "92434d6513bf4e01ae5ca2bbf4fe70b0", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("b4037f8abc4a9aff0647ac16bd8f45b8563379a7eac5fd0b9165ee7d5bfc649b", "92434d6513bf4e01ae5ca2bbf4fe70b0",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"f2c309fdd62945ffb66dee6d44d44d4d\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bab094c347996aef579300a100508e24fe4ab4437bd6d52ab2087601e334b3dd16f8fc7721c22825ef64f63b5aa124d36\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("e68def9b104b45b1bb9b863fb8413e4c", "res_test_service", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("1adfb17166724f92b068ea8425a6d21d", "default", "{}", "Default project for domain", "1",
        "e68def9b104b45b1bb9b863fb8413e4c");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("e68def9b104b45b1bb9b863fb8413e4c", "root", "{}",
        "$6$rounds=40000$eP2ELEOd$GUGN0lsJqgKdiVihOFkbfbPuijx3LPlhVxtIOPX7DD6vAerE3hGJ9HT2klftU2UDlrDpgtQb6gz7MSDvFrPGf0",
        "1", "e68def9b104b45b1bb9b863fb8413e4c", "1adfb17166724f92b068ea8425a6d21d", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "e68def9b104b45b1bb9b863fb8413e4c", "1adfb17166724f92b068ea8425a6d21d",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "e68def9b104b45b1bb9b863fb8413e4c", "e68def9b104b45b1bb9b863fb8413e4c",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("e68def9b104b45b1bb9b863fb8413e4c", "e68def9b104b45b1bb9b863fb8413e4c",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_test_service", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "e68def9b104b45b1bb9b863fb8413e4c", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0fe5963b188e4acb156ed48355bec293d52e502106b8a13e1e1a1a4c2265a699", "e68def9b104b45b1bb9b863fb8413e4c",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"2d0ae3dd95864b5f9139474aa5d1ab3e\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b6a3bd38d9ebbd7e4c1499ab6add47284922c3709fd20077d68f690d3c1cb4d3ff0ca976a8ed5ba8baf40e459359e23b3\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("345ccb4d4da04d06977810d6f9d95a34", "res_ticket", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("f429f308b52c49ca828ac9b96f5dc9dc", "default", "{}", "Default project for domain", "1",
        "345ccb4d4da04d06977810d6f9d95a34");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("345ccb4d4da04d06977810d6f9d95a34", "root", "{}",
        "$6$rounds=40000$CPElHDO/$beMa6x7fj1OFgQGf3aPkjoaK24DGa4ZLICump92.aMs1I85EVcf0gKFIhj4NSnMU7873UAQZZRns.9ZsgDMlx.",
        "1", "345ccb4d4da04d06977810d6f9d95a34", "f429f308b52c49ca828ac9b96f5dc9dc", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "345ccb4d4da04d06977810d6f9d95a34", "f429f308b52c49ca828ac9b96f5dc9dc",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "345ccb4d4da04d06977810d6f9d95a34", "345ccb4d4da04d06977810d6f9d95a34",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("345ccb4d4da04d06977810d6f9d95a34", "345ccb4d4da04d06977810d6f9d95a34",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_ticket", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "345ccb4d4da04d06977810d6f9d95a34", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("a5bdac9ddca4d565b4bf7a5b7291b0322b4c204eec8ce721353570e7721424b9", "345ccb4d4da04d06977810d6f9d95a34",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"08c36a9c7aa144d38abc501ac8e8a55f\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bcf13b71ab93cb70f3af5c2119a50c7510e092c9fc3eedb1c542406c84376ce985faad6ef3a80836c4b42f0fefb28567d\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("af25d99b759c44babdacef0e93e27d95", "res_trends", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("24e1c78343cf4bad93f62d4c837243cc", "default", "{}", "Default project for domain", "1",
        "af25d99b759c44babdacef0e93e27d95");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("af25d99b759c44babdacef0e93e27d95", "root", "{}",
        "$6$rounds=40000$Kd9klJIq$6QeyKpP9zp4NFXvp9PiW6VvrMxTCqBZoR35uq6TnyDIrq/4CEYFF6UlOlhQWOUHPgAkH6hz1J7uJpey9T1k.G0",
        "1", "af25d99b759c44babdacef0e93e27d95", "24e1c78343cf4bad93f62d4c837243cc", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "af25d99b759c44babdacef0e93e27d95", "24e1c78343cf4bad93f62d4c837243cc",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "af25d99b759c44babdacef0e93e27d95", "af25d99b759c44babdacef0e93e27d95",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("af25d99b759c44babdacef0e93e27d95", "af25d99b759c44babdacef0e93e27d95",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_trends", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "af25d99b759c44babdacef0e93e27d95", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("6a4d30290dcec91ac0a1281cbca2ef4d131d6794411de002246a374335455f37", "af25d99b759c44babdacef0e93e27d95",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"6416e89348a1402e9f21dc86b2ae2687\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b9a54a554c01eac021f7b2248bb7b58b68d4d1046b8f54d52b6389d03645ada3c6d2375debad43ba246bea560a520a969\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("1cb2dcf92ab04cf7aaa56f2a9113dd8c", "res_tsl2", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("ec580986df2b4e979de61bf627f74951", "default", "{}", "Default project for domain", "1",
        "1cb2dcf92ab04cf7aaa56f2a9113dd8c");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("1cb2dcf92ab04cf7aaa56f2a9113dd8c", "root", "{}",
        "$6$rounds=40000$frJfdRco$GCfnjlCv8HYXFpZUkGQQNFrCZPHOauXVbjV4vMdTkl1TJMMIdKfkdhUyP9KkpXf4f6.W.nNSjM14lim2jQ4pn/",
        "1", "1cb2dcf92ab04cf7aaa56f2a9113dd8c", "ec580986df2b4e979de61bf627f74951", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "1cb2dcf92ab04cf7aaa56f2a9113dd8c", "ec580986df2b4e979de61bf627f74951",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "1cb2dcf92ab04cf7aaa56f2a9113dd8c", "1cb2dcf92ab04cf7aaa56f2a9113dd8c",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("1cb2dcf92ab04cf7aaa56f2a9113dd8c", "1cb2dcf92ab04cf7aaa56f2a9113dd8c",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_tsl2", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "1cb2dcf92ab04cf7aaa56f2a9113dd8c", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("1201de1267b7ea5653764f32df655b10cd857ce463c64b90258798b86f950ff0", "1cb2dcf92ab04cf7aaa56f2a9113dd8c",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"aed1bce06c8840b999965df0664e9c9a\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b000dc248cebacdcc25d837e22da3faaa593a0205d937bf7ddbf6aeb46dabeb6d00d26ee7be8f104d379b7c01d643b63c\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("1d72ac9a18c9411987bf1283e10ea648", "res_user_config", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("25bb6b59d0e941be841fda5621c8965a", "default", "{}", "Default project for domain", "1",
        "1d72ac9a18c9411987bf1283e10ea648");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("1d72ac9a18c9411987bf1283e10ea648", "root", "{}",
        "$6$rounds=40000$WJNFXlTw$LRtfs.fSa4TyQj/i6ipgcbxXqgBtZqWfzUf/6WImcyu2IHfiOYzuVj2LiStDLRmwNi81noUQOb6HsZl4HBQAx1",
        "1", "1d72ac9a18c9411987bf1283e10ea648", "25bb6b59d0e941be841fda5621c8965a", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "1d72ac9a18c9411987bf1283e10ea648", "25bb6b59d0e941be841fda5621c8965a",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "1d72ac9a18c9411987bf1283e10ea648", "1d72ac9a18c9411987bf1283e10ea648",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("1d72ac9a18c9411987bf1283e10ea648", "1d72ac9a18c9411987bf1283e10ea648",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_user_config", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "1d72ac9a18c9411987bf1283e10ea648", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("825cc7142053a4398674c57e3d64a9597be5da489df7212f5b6fbdf225aae728", "1d72ac9a18c9411987bf1283e10ea648",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"53f38a4939f7474087e4f603fc21a037\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b0d8affef6090162f5944f8500619184c72ec35d2c9bcb117360ef6c70c89c28ad584a95ce4a6b96b02dbc3bea4ba030e\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("920a010094764f3dacb9b23bf77f8f5a", "res_vca", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("9bd6cba4f5164be5a93e34ae09d046b9", "default", "{}", "Default project for domain", "1",
        "920a010094764f3dacb9b23bf77f8f5a");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("920a010094764f3dacb9b23bf77f8f5a", "root", "{}",
        "$6$rounds=40000$RIQX4nS.$3QFQq9Z02vg58Rtl1tXVN5iUUReVh8F9cWP1iIVlpX/g.sQqLynIV0.yEC4KhINe96/1ji5AiQg4szmLtZN.I/",
        "1", "920a010094764f3dacb9b23bf77f8f5a", "9bd6cba4f5164be5a93e34ae09d046b9", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "920a010094764f3dacb9b23bf77f8f5a", "9bd6cba4f5164be5a93e34ae09d046b9",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "920a010094764f3dacb9b23bf77f8f5a", "920a010094764f3dacb9b23bf77f8f5a",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("920a010094764f3dacb9b23bf77f8f5a", "920a010094764f3dacb9b23bf77f8f5a",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_vca", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "920a010094764f3dacb9b23bf77f8f5a", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("68377423c55e51682bb81b3952fe9befb0299bc614cc9553e96c7711cb056198", "920a010094764f3dacb9b23bf77f8f5a",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"88de1deaa6714953970e95f8fa2a4b00\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b10f0545753e78cf4cc96f0e598892cb18a62a57b7807b641d4b6655802fa8325c605a7ba3c7175201bf0d9336b75ca5c\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("6d7cb047efda440bad8752b3f6826432", "res_vcr", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("21ca37f44e55485e9aa9caf571d29a70", "default", "{}", "Default project for domain", "1",
        "6d7cb047efda440bad8752b3f6826432");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("6d7cb047efda440bad8752b3f6826432", "root", "{}",
        "$6$rounds=40000$qqz7sFA9$enx7BGeAhDF/SzfoRo9OcqhqSYuINWznOCsI9a.nM5CiNsN6iYfE7jKZk2DMwKX3iI.DgvRzt4UeZYDyvglQf.",
        "1", "6d7cb047efda440bad8752b3f6826432", "21ca37f44e55485e9aa9caf571d29a70", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "6d7cb047efda440bad8752b3f6826432", "21ca37f44e55485e9aa9caf571d29a70",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "6d7cb047efda440bad8752b3f6826432", "6d7cb047efda440bad8752b3f6826432",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("6d7cb047efda440bad8752b3f6826432", "6d7cb047efda440bad8752b3f6826432",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_vcr", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "6d7cb047efda440bad8752b3f6826432", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("9bcc30d5fab9675597fb8892f526f2f7a3a4da1b60a5d4e7e48f2236a855796a", "6d7cb047efda440bad8752b3f6826432",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"ec7b02840b264b1985b030afeede761e\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b3c55af259e7c8e2c5c754758dc877636bdbe7ee1806d81552cbb9405fe4b16e20b3f6cc4c89a3654c9b0e90e15bc240b\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("8cbb73deb6184e79a79454c8b41d0676", "res_vms", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("45d25296ba514cbe8819281b98cc4a3d", "default", "{}", "Default project for domain", "1",
        "8cbb73deb6184e79a79454c8b41d0676");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("8cbb73deb6184e79a79454c8b41d0676", "root", "{}",
        "$6$rounds=40000$bf.WgQ8Q$elOa37gOpLRONkMhxy.bYKv7OGPC2XqDqMa86mnGF/pAwcLlTosUZZn6qN8GDqAg5bV4OdbgNEFbab50bJAxf1",
        "1", "8cbb73deb6184e79a79454c8b41d0676", "45d25296ba514cbe8819281b98cc4a3d", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "8cbb73deb6184e79a79454c8b41d0676", "45d25296ba514cbe8819281b98cc4a3d",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "8cbb73deb6184e79a79454c8b41d0676", "8cbb73deb6184e79a79454c8b41d0676",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("8cbb73deb6184e79a79454c8b41d0676", "8cbb73deb6184e79a79454c8b41d0676",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_vms", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "8cbb73deb6184e79a79454c8b41d0676", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("e43bd27c4bd742d0396697141f40288b9559bce07d5bd64ad71489a43bbfe224", "8cbb73deb6184e79a79454c8b41d0676",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"f3ca15f85a754f03bc8c300d787b3bf9\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b9eb6230c44dda9b6f31f2430db6751d1283c983c305b4e91efaae77315e279dd541bed15fdf9cdda117148a1cd687d9b\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("971428d3edb840d8964e64600c5dbd51", "res_vod", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("b5abcb72d0564153a42abaeb79ba3a50", "default", "{}", "Default project for domain", "1",
        "971428d3edb840d8964e64600c5dbd51");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("971428d3edb840d8964e64600c5dbd51", "root", "{}",
        "$6$rounds=40000$HNyOsUo7$KWlwfIlK1exLUIc2wjjXBn6rBjjmySifMBTu6Tb2qW.g80PccvmM5OGOIPtrCvGB6Ixry5wizQBXhnr4vWD.d0",
        "1", "971428d3edb840d8964e64600c5dbd51", "b5abcb72d0564153a42abaeb79ba3a50", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "971428d3edb840d8964e64600c5dbd51", "b5abcb72d0564153a42abaeb79ba3a50",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "971428d3edb840d8964e64600c5dbd51", "971428d3edb840d8964e64600c5dbd51",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("971428d3edb840d8964e64600c5dbd51", "971428d3edb840d8964e64600c5dbd51",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_vod", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "971428d3edb840d8964e64600c5dbd51", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("caeb38f066766181c8e0fab9d720a2b0798ee4a9cca22e2b1c165944abfb706d", "971428d3edb840d8964e64600c5dbd51",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"7fe039df5bca42698a31be39a74b88a4\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b35b6bf8321ec8535695a25e2f6fe0d989c67331bcfc00afe24d8935ac4e6fbac2fde5dbdd4eaaa4ac5ce39121cdf4a0a\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("3ad059f5676747318ab6fa9657ad100f", "res_vpn", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("0a1799e875334840b7e34000c6b6e1e1", "default", "{}", "Default project for domain", "1",
        "3ad059f5676747318ab6fa9657ad100f");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("3ad059f5676747318ab6fa9657ad100f", "root", "{}",
        "$6$rounds=40000$9c3hfDva$.TT/HBibfyrScQceSufiLaJpy3qzANacBmgssMKHi2bV080zLagnTFu.V7FF40UejRJTYzZxnYUHPDXZKRCU00",
        "1", "3ad059f5676747318ab6fa9657ad100f", "0a1799e875334840b7e34000c6b6e1e1", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "3ad059f5676747318ab6fa9657ad100f", "0a1799e875334840b7e34000c6b6e1e1",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "3ad059f5676747318ab6fa9657ad100f", "3ad059f5676747318ab6fa9657ad100f",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("3ad059f5676747318ab6fa9657ad100f", "3ad059f5676747318ab6fa9657ad100f",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_vpn", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "3ad059f5676747318ab6fa9657ad100f", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("252c4495f370d34adc836a601746c4cdc51e774f15585cf45f85cb59adfcbdbc", "3ad059f5676747318ab6fa9657ad100f",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"42d2f354e2bd4f208da6f605a6fb4f62\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b81d6228f8664138a74d3bf35e2edf2ea26fa3379c597a859396135b96b99a50aea7dd11815c4271e06045b3cb3d3367c\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("0256963ce10a4610b9bcd1157d5dab47", "res_webmaster", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("998b6315e4dc4af4aeb1cc9f4ce7086e", "default", "{}", "Default project for domain", "1",
        "0256963ce10a4610b9bcd1157d5dab47");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("0256963ce10a4610b9bcd1157d5dab47", "root", "{}",
        "$6$rounds=40000$gW2cRIkd$eOhUuVEO4YfSlofXXZUzjuv.naKdYjVCJEzscv2KyiKTaWNzwRWXZsu7tGKmo3QsL/V6uXpxYFCbkVyI.pkgq/",
        "1", "0256963ce10a4610b9bcd1157d5dab47", "998b6315e4dc4af4aeb1cc9f4ce7086e", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "0256963ce10a4610b9bcd1157d5dab47", "998b6315e4dc4af4aeb1cc9f4ce7086e",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "0256963ce10a4610b9bcd1157d5dab47", "0256963ce10a4610b9bcd1157d5dab47",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("0256963ce10a4610b9bcd1157d5dab47", "0256963ce10a4610b9bcd1157d5dab47",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_webmaster", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "0256963ce10a4610b9bcd1157d5dab47", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("357bf97d0621741c0b44a193381c14fa814ab88950f6999ef983d2b6beb350bf", "0256963ce10a4610b9bcd1157d5dab47",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"60178c7fd4af44b0ba4dbcf364b85213\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b17d2d032c35f195e858c4110d0b13ae5f24daa15a90d5fa25f390ab9184691769974a3cf02d4608b20ad474ec222999a\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("2f74979b28314fcaba35b66b01013b0a", "res_webmaster-user", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("fce863f1173f4cf3af8220ae41e13ba3", "default", "{}", "Default project for domain", "1",
        "2f74979b28314fcaba35b66b01013b0a");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("2f74979b28314fcaba35b66b01013b0a", "root", "{}",
        "$6$rounds=40000$GD8w4/Tw$t/Qr2Eut4BzBzdcZgeqI0wrucn60ttupKqHV21GxgOHapLAHqwQZNlvp.sdcAvwAEUMhcE8belV1BGHgiweM8/",
        "1", "2f74979b28314fcaba35b66b01013b0a", "fce863f1173f4cf3af8220ae41e13ba3", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "2f74979b28314fcaba35b66b01013b0a", "fce863f1173f4cf3af8220ae41e13ba3",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "2f74979b28314fcaba35b66b01013b0a", "2f74979b28314fcaba35b66b01013b0a",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("2f74979b28314fcaba35b66b01013b0a", "2f74979b28314fcaba35b66b01013b0a",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_webmaster-user", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "2f74979b28314fcaba35b66b01013b0a", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("add4f356e11387061cfbd0ba2e5c3993538b1ede990348a7a5e87a45a2244803", "2f74979b28314fcaba35b66b01013b0a",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"9a6656149f4b435b9f8b15c4624d05aa\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57be050b8925d3f04f419804b8fa742d07bc95cf34382ead4028a3b1074c3acecf5f481ef440bed029bf1f12b4c53640398\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("44f3747260864f3a9075174637f439ac", "res_WZJJm2MwRbBdtwXKZbuo4If60DhQmGzB", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("65233b8234394f019ff7b2f976218a9a", "default", "{}", "Default project for domain", "1",
        "44f3747260864f3a9075174637f439ac");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("44f3747260864f3a9075174637f439ac", "root", "{}",
        "$6$rounds=40000$Ltwj0gCU$iYun0DiT4aergB0lotHulrowjX.Osl6tBB2Ibdp55U0YU5ogwPkXPtmbMFS7aFRa1iN1WmCuAM8B9loXxCHU90",
        "1", "44f3747260864f3a9075174637f439ac", "65233b8234394f019ff7b2f976218a9a", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "44f3747260864f3a9075174637f439ac", "65233b8234394f019ff7b2f976218a9a",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "44f3747260864f3a9075174637f439ac", "44f3747260864f3a9075174637f439ac",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("44f3747260864f3a9075174637f439ac", "44f3747260864f3a9075174637f439ac",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_WZJJm2MwRbBdtwXKZbuo4If60DhQmGzB", "2019-12-30 15:33:31", "individual",
        "{\"description\":{\"codeChecked\":true}}", "44f3747260864f3a9075174637f439ac", "2019-12-30 15:33:31",
        "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("94d7dd0bb30a105833db3ea34922f6e67fd7d8bfa34de17218e5c5b0f5069189", "44f3747260864f3a9075174637f439ac",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"9b6392f8ed3840f99eeff4234eb7610b\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b56d522a019941ae2658eaf9bb5591ceea004a227aef2a0714b9e27f9c808429e3d679fa6599e13c706b14c4362ae485c\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("c980a47ab18645f8acb7f97c2a4830b3", "res_xiaolvyun", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("0920d375fbc94df48a3aa4dc047b5e7c", "default", "{}", "Default project for domain", "1",
        "c980a47ab18645f8acb7f97c2a4830b3");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("c980a47ab18645f8acb7f97c2a4830b3", "root", "{}",
        "$6$rounds=40000$1tbzAV1i$wsRNMlKlGKzaj3UgJHzN9OCClNTVI64Q0np/jsbtwb8bYOjmB8qQvKYQsxRB3obXTAqlvFe3j7qLFMLx5hlMY/",
        "1", "c980a47ab18645f8acb7f97c2a4830b3", "0920d375fbc94df48a3aa4dc047b5e7c", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "c980a47ab18645f8acb7f97c2a4830b3", "0920d375fbc94df48a3aa4dc047b5e7c",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "c980a47ab18645f8acb7f97c2a4830b3", "c980a47ab18645f8acb7f97c2a4830b3",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("c980a47ab18645f8acb7f97c2a4830b3", "c980a47ab18645f8acb7f97c2a4830b3",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_xiaolvyun", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "c980a47ab18645f8acb7f97c2a4830b3", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("ad6fb4c10f632990a624e44bed58bb5b203498a9150ffec7b71efcea8c44e56c", "c980a47ab18645f8acb7f97c2a4830b3",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"50196c203b1c4b859147ee18045e7e52\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b38a9054e2802c17151af28731a891887e24ffc67ae4a470c6dbdebf8fed3b6f51ef4124e54bb2096f90cb2c026f3e616\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("d6ac3b6146ec46eb9747a8f0838d7c65", "res_xmq", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("5da0f46dbbf44b50a38d4df9f35fa407", "default", "{}", "Default project for domain", "1",
        "d6ac3b6146ec46eb9747a8f0838d7c65");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("d6ac3b6146ec46eb9747a8f0838d7c65", "root", "{}",
        "$6$rounds=40000$D3UpfQT0$GtHximqGduelBzWbb8.3IoRvzHRUtk.N6wq370cF5OVfJA5Oinm6tAwjvGFOw/lupotG3gaHZCh/ClDHY/c8y1",
        "1", "d6ac3b6146ec46eb9747a8f0838d7c65", "5da0f46dbbf44b50a38d4df9f35fa407", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "d6ac3b6146ec46eb9747a8f0838d7c65", "5da0f46dbbf44b50a38d4df9f35fa407",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "d6ac3b6146ec46eb9747a8f0838d7c65", "d6ac3b6146ec46eb9747a8f0838d7c65",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("d6ac3b6146ec46eb9747a8f0838d7c65", "d6ac3b6146ec46eb9747a8f0838d7c65",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_xmq", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "d6ac3b6146ec46eb9747a8f0838d7c65", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("42583c048ab4fdd80a34904240271063e9648976373dde19b24870af5fd24eee", "d6ac3b6146ec46eb9747a8f0838d7c65",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"994aa92436944c309a17b4790477e5e3\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bd26039e4e775816bc96687a5d8166411695b6f70eb3686bb4035af0c3653f9ef69e95357d6caaaedfd6aad85c3ee179f\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("ca3b65f5eb4c4ce3a2760f2c4492c843", "res_yb-demand-createvpc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("e616e34fa9244def98179316e8480394", "default", "{}", "Default project for domain", "1",
        "ca3b65f5eb4c4ce3a2760f2c4492c843");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("ca3b65f5eb4c4ce3a2760f2c4492c843", "root", "{}",
        "$6$rounds=40000$m236NO95$3vgQz2YPBW3xUpNLye4psU1Py0lTNny7gACLPEDJu8BV/sjqbaRf12tzMXrDGG./cd8EHDVtTOAAlDu/jUvGu/",
        "1", "ca3b65f5eb4c4ce3a2760f2c4492c843", "e616e34fa9244def98179316e8480394", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "ca3b65f5eb4c4ce3a2760f2c4492c843", "e616e34fa9244def98179316e8480394",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "ca3b65f5eb4c4ce3a2760f2c4492c843", "ca3b65f5eb4c4ce3a2760f2c4492c843",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("ca3b65f5eb4c4ce3a2760f2c4492c843", "ca3b65f5eb4c4ce3a2760f2c4492c843",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_yb-demand-createvpc", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "ca3b65f5eb4c4ce3a2760f2c4492c843", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("37a159f9411f669f00669b20c5c7141e863f4a3151a6d07ef1812b5b52a67a1b", "ca3b65f5eb4c4ce3a2760f2c4492c843",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"1128266c887b44f3a6c18ce0db5476c4\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b41eb5b5b5d2d903d1869c6108afbfb5fca08515e1239c123f360fec38bc2b6c041d1f81fc64a7d58a085079cbfcdec00\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("66052cda9d5548cba6dd05065a586a92", "res_yb-yinshang-test1", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("5108b847b4e8485889a68ec18defead8", "default", "{}", "Default project for domain", "1",
        "66052cda9d5548cba6dd05065a586a92");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("66052cda9d5548cba6dd05065a586a92", "root", "{}",
        "$6$rounds=40000$gIgmRBE4$Yft.sRpnH5TfVCjSsXeihTmeVzZC/Xquo2k2qxZk5473fb0dnwLzRtG/oNBL14Dssm5JlT8X0agBD1gtFfB3G0",
        "1", "66052cda9d5548cba6dd05065a586a92", "5108b847b4e8485889a68ec18defead8", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "66052cda9d5548cba6dd05065a586a92", "5108b847b4e8485889a68ec18defead8",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "66052cda9d5548cba6dd05065a586a92", "66052cda9d5548cba6dd05065a586a92",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("66052cda9d5548cba6dd05065a586a92", "66052cda9d5548cba6dd05065a586a92",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_yb-yinshang-test1", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "66052cda9d5548cba6dd05065a586a92", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("5e663c3fe72990e86e716c0c53468cef8276f8ea93945ec95e5daec66ba02a81", "66052cda9d5548cba6dd05065a586a92",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"2116f4b0328846cbacbdc3fe44ccb6fe\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb23fe7de6b9cf42c9851b62213fab0739052d4d3261c69f6a37b9fe5d964ab349b09dc6a3b15a8608c3734be1c265edf\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("2de2c660dd3841c9a28df8a91f3fb82c", "res_yb-yinshang-test2", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("9ccc5e14b228434eacb1fa057fea8277", "default", "{}", "Default project for domain", "1",
        "2de2c660dd3841c9a28df8a91f3fb82c");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("2de2c660dd3841c9a28df8a91f3fb82c", "root", "{}",
        "$6$rounds=40000$cwdblZuj$FcyW34Si7rWRjacd/58jACvVxexLyjcEW4gq4fBujLXFD0sn.ecF8u6i9wr3vN/tOLxPUu4qNubOZxwMNOyzG1",
        "1", "2de2c660dd3841c9a28df8a91f3fb82c", "9ccc5e14b228434eacb1fa057fea8277", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "2de2c660dd3841c9a28df8a91f3fb82c", "9ccc5e14b228434eacb1fa057fea8277",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "2de2c660dd3841c9a28df8a91f3fb82c", "2de2c660dd3841c9a28df8a91f3fb82c",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("2de2c660dd3841c9a28df8a91f3fb82c", "2de2c660dd3841c9a28df8a91f3fb82c",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_yb-yinshang-test2", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "2de2c660dd3841c9a28df8a91f3fb82c", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8925842ba121a9971fb8c1d7e34bac245e3be444f941ef90a587a64805c3b842", "2de2c660dd3841c9a28df8a91f3fb82c",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"062e6fd5ec1546458930f65a013d9d44\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b8fa4ec4376add8fbf2b3a9d4b05ac2cfc4429d0cbc19592bfd5ba4aa30ee99361ee66ad7b161f0ce4e2cefb1b56f141a\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("105f78027aa14faa9efe9261cb97a7dd", "res_yunduo", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("ff691041166c4429a07f99f8425b646d", "default", "{}", "Default project for domain", "1",
        "105f78027aa14faa9efe9261cb97a7dd");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("105f78027aa14faa9efe9261cb97a7dd", "root", "{}",
        "$6$rounds=40000$tVZjC9PW$2Aj/mrsS4ebgNwjNICyO82fHjuNwSs87StBCsn5iJlYZkoVmv7p6INCs/aZAi7T82ugb6i9G52uYjSDjDIZG9/",
        "1", "105f78027aa14faa9efe9261cb97a7dd", "ff691041166c4429a07f99f8425b646d", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "105f78027aa14faa9efe9261cb97a7dd", "ff691041166c4429a07f99f8425b646d",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "105f78027aa14faa9efe9261cb97a7dd", "105f78027aa14faa9efe9261cb97a7dd",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("105f78027aa14faa9efe9261cb97a7dd", "105f78027aa14faa9efe9261cb97a7dd",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_yunduo", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "105f78027aa14faa9efe9261cb97a7dd", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("101628b9dc6f894785092aa11372960debf45433845b684d68219fd30b50c8d8", "105f78027aa14faa9efe9261cb97a7dd",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"c173bfc8710542bb81bae4ff77fb8986\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b4cac5001b03d998aa02f332785a4a4ca956899d3ab84f056ef54545ee2be1415573d1ad3770818b9ffe137e160d3822c\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("0f5e0e1a585242edbf4ab30389db587a", "res_yunying", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("c33b708dcd624e40ba1869e32fa42313", "default", "{}", "Default project for domain", "1",
        "0f5e0e1a585242edbf4ab30389db587a");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("0f5e0e1a585242edbf4ab30389db587a", "root", "{}",
        "$6$rounds=40000$QFYjQZI1$mmnlsw2jsLYZCVx31ryTVXwCOlOQa7qmunE.L99QoRvjshukC74D57pYipMCFTNjSWAima3jnPKDzd0aHB2CW0",
        "1", "0f5e0e1a585242edbf4ab30389db587a", "c33b708dcd624e40ba1869e32fa42313", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "0f5e0e1a585242edbf4ab30389db587a", "c33b708dcd624e40ba1869e32fa42313",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "0f5e0e1a585242edbf4ab30389db587a", "0f5e0e1a585242edbf4ab30389db587a",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("0f5e0e1a585242edbf4ab30389db587a", "0f5e0e1a585242edbf4ab30389db587a",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_yunying", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "0f5e0e1a585242edbf4ab30389db587a", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("fa5d89b9f0269c78b337aecf4fbfa80e4b8af1b300e56a71b7f519735e190d81", "0f5e0e1a585242edbf4ab30389db587a",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"caa03c8e62224fc68a9cecfae1afcea3\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba07f177cf342aa8ed19ba6176b98be40370dbf028c16c2538db838cdef757431a35e31ed25c595334638c795811bceac\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("1e1d7e1e76c74e6283ad764d5803101b", "res_yunying-user", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("ed7cba663c2648bf92e59a7903296f5b", "default", "{}", "Default project for domain", "1",
        "1e1d7e1e76c74e6283ad764d5803101b");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("1e1d7e1e76c74e6283ad764d5803101b", "root", "{}",
        "$6$rounds=40000$5S3sBq/9$06RIz8N6hvHMteaUNieRMciMVz7dNCt/DaPZrmWoiH3T9H51xHAM3LZOkvbt24IjBtC45Rx.UUgLctYFDPfy3/",
        "1", "1e1d7e1e76c74e6283ad764d5803101b", "ed7cba663c2648bf92e59a7903296f5b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "1e1d7e1e76c74e6283ad764d5803101b", "ed7cba663c2648bf92e59a7903296f5b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "1e1d7e1e76c74e6283ad764d5803101b", "1e1d7e1e76c74e6283ad764d5803101b",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("1e1d7e1e76c74e6283ad764d5803101b", "1e1d7e1e76c74e6283ad764d5803101b",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_yunying-user", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "1e1d7e1e76c74e6283ad764d5803101b", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("9355094635cdbfb47769c2351ce64e49fae7431cc43969496bbc695f19d1fdaa", "1e1d7e1e76c74e6283ad764d5803101b",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"0bfb2f6f60574c6aa73eeebc708964a1\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb47ec93ee3ac8ceeab0fd5401d1d633f5fca6061b0b780d4a9f496f4e9dd6b22ebf8d962ec9485db78020860d5bbaec9\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("2bb5ff77448c4962b141035d24437f26", "res_zhmdservice", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("b687133ecd4e4249a8b1667e594b648b", "default", "{}", "Default project for domain", "1",
        "2bb5ff77448c4962b141035d24437f26");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("2bb5ff77448c4962b141035d24437f26", "root", "{}",
        "$6$rounds=40000$bubW/qVx$Yc8mR2LldsWxj3WF1EmMg7.Ab8230BtdVNb9FOw7tzil6bwNNRG52Ge6NP.RQrqjoN6eBt7EGiL2Z5QmYlFjA0",
        "1", "2bb5ff77448c4962b141035d24437f26", "b687133ecd4e4249a8b1667e594b648b", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "2bb5ff77448c4962b141035d24437f26", "b687133ecd4e4249a8b1667e594b648b",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "2bb5ff77448c4962b141035d24437f26", "2bb5ff77448c4962b141035d24437f26",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("2bb5ff77448c4962b141035d24437f26", "2bb5ff77448c4962b141035d24437f26",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_zhmdservice", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "2bb5ff77448c4962b141035d24437f26", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("e7628bc58c3fd43865d98972081eca7674369bd2fbf7cecdb8d5a0e0a7f78fd0", "2bb5ff77448c4962b141035d24437f26",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3ee2711a5b194aa290241426293ffdcd\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b60c15d6e30a1deeb6546cf9447670e62ff1747d644b7201342013789f8230da8d55f05402fd15293a89ec8c6b670d900\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("4a2710e7aa544d1f91d11aaaa42282a9", "res_zone", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("0c3cfe4dcca648bcb308bf5525e5197e", "default", "{}", "Default project for domain", "1",
        "4a2710e7aa544d1f91d11aaaa42282a9");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("4a2710e7aa544d1f91d11aaaa42282a9", "root", "{}",
        "$6$rounds=40000$PcPBpm0m$vss5kf27e7fInWIIIKoWhSv3gquz9Jn6Tm5a0VbJDgjYBbsyLR87GLsp5134rWasSvIZtvFRSBrI6TsSR92hz/",
        "1", "4a2710e7aa544d1f91d11aaaa42282a9", "0c3cfe4dcca648bcb308bf5525e5197e", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "4a2710e7aa544d1f91d11aaaa42282a9", "0c3cfe4dcca648bcb308bf5525e5197e",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "4a2710e7aa544d1f91d11aaaa42282a9", "4a2710e7aa544d1f91d11aaaa42282a9",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("4a2710e7aa544d1f91d11aaaa42282a9", "4a2710e7aa544d1f91d11aaaa42282a9",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_zone", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "4a2710e7aa544d1f91d11aaaa42282a9", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("d474dcba2b221af3d4709cd73066125bd4c6417480a093a276a5c8a5e54b920b", "4a2710e7aa544d1f91d11aaaa42282a9",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"960c203d308b4a0e903c95892c39e0b9\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bb01c73f1fc5f8d19d4801b6ba5c69abba10cba6890bd143bce58b6552cd9dc4bf5a3da2af3be094b1a40c0ba76a592e8\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("f88a4114a7a3484ead75e1d5eee62d17", "res_zp-test-1", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("71a2608989c7428484ea0e38a1cb3bf8", "default", "{}", "Default project for domain", "1",
        "f88a4114a7a3484ead75e1d5eee62d17");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("f88a4114a7a3484ead75e1d5eee62d17", "root", "{}",
        "$6$rounds=40000$Tdm.QhXU$3Dapu4lcF9sVFoDDZBE9fJ3Xoejmg8usBk.wOJT8od63r2Obd0UtPFyMeaUUrTvkg7WbH/QX1RpjSCPcwl0t//",
        "1", "f88a4114a7a3484ead75e1d5eee62d17", "71a2608989c7428484ea0e38a1cb3bf8", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "f88a4114a7a3484ead75e1d5eee62d17", "71a2608989c7428484ea0e38a1cb3bf8",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "f88a4114a7a3484ead75e1d5eee62d17", "f88a4114a7a3484ead75e1d5eee62d17",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("f88a4114a7a3484ead75e1d5eee62d17", "f88a4114a7a3484ead75e1d5eee62d17",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_zp-test-1", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "f88a4114a7a3484ead75e1d5eee62d17", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("4ca56a2b10536b2a10896299d6a5d8a2bb48d27db9701c18ac74425e6edd7a4b", "f88a4114a7a3484ead75e1d5eee62d17",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"7bfca748173047c7a3b8d0c3ac381e8b\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b827ed7613cb2406b632380ef28e946029b3df87545f92168525bba1d516abfaecc345e5d0a864b59ef262de2a273dfe5\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("b584022f6f5e425e88c007a618556bec", "res_zp-test-2", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("b89f43abf77340b3a7a78eb91ff33892", "default", "{}", "Default project for domain", "1",
        "b584022f6f5e425e88c007a618556bec");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("b584022f6f5e425e88c007a618556bec", "root", "{}",
        "$6$rounds=40000$wWLDIjCI$cvxcU09UATOU4FvZpCLneGk1NbpjY013uX0kb4/OdRNGPVi9pzfz9.kiP3syT7Wti/gz4ZlDocHhloJtotwSp/",
        "1", "b584022f6f5e425e88c007a618556bec", "b89f43abf77340b3a7a78eb91ff33892", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "b584022f6f5e425e88c007a618556bec", "b89f43abf77340b3a7a78eb91ff33892",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "b584022f6f5e425e88c007a618556bec", "b584022f6f5e425e88c007a618556bec",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("b584022f6f5e425e88c007a618556bec", "b584022f6f5e425e88c007a618556bec",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_zp-test-2", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "b584022f6f5e425e88c007a618556bec", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("485e498e6785555092079cac6bf78b2ab52cb3d1f4ffcdc75449c0a4f68abaf5", "b584022f6f5e425e88c007a618556bec",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"8637fca26f2c4205a890dd134d6def5b\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b7d31dfa39ef68778cbd7e661cde9fa9602020b75cb2b4e048caed1794c8df993112d2e0ed5e907cde4edf1639e49d356\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("a834b4dc71ff442e83fb0e9d9faa799e", "res_zp-test-3", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("78764e8bb1d34abc8bae44d0639ebe53", "default", "{}", "Default project for domain", "1",
        "a834b4dc71ff442e83fb0e9d9faa799e");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("a834b4dc71ff442e83fb0e9d9faa799e", "root", "{}",
        "$6$rounds=40000$uyu0SmN2$HWhXFaSHbWv3fNeetJerpTR/kdD21yV/mbTQY1c5JIOcp/chTVi.M4Irpd4FJP8eudGCmPd3TVBHsglHR.nBe1",
        "1", "a834b4dc71ff442e83fb0e9d9faa799e", "78764e8bb1d34abc8bae44d0639ebe53", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "a834b4dc71ff442e83fb0e9d9faa799e", "78764e8bb1d34abc8bae44d0639ebe53",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "a834b4dc71ff442e83fb0e9d9faa799e", "a834b4dc71ff442e83fb0e9d9faa799e",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("a834b4dc71ff442e83fb0e9d9faa799e", "a834b4dc71ff442e83fb0e9d9faa799e",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_zp-test-3", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "a834b4dc71ff442e83fb0e9d9faa799e", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("7dc441a8a39fb9b4ee9060d86eddae127c43bf023cdd7572adbc4b1e76ccc969", "a834b4dc71ff442e83fb0e9d9faa799e",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"1ea2b3f7d6684b1384d961915a9e170e\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b646ae5c313c6d72d9661c2c5687f9292b676aab31a8748a92eb1bdc7dbb09a4b9c45fc822cee8e4098439d26c7c9ce43\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("cb418ef0af1243bd8c76e5ca9836f234", "res_zp-test-4", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("5249d92ad5774d91a0b11b9a4c67193d", "default", "{}", "Default project for domain", "1",
        "cb418ef0af1243bd8c76e5ca9836f234");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("cb418ef0af1243bd8c76e5ca9836f234", "root", "{}",
        "$6$rounds=40000$G0.zE1Og$RD4rNdwRZdYx2xFXHtCECpWXHSSCi1Ub6tyNuDTtvDCqD1oIGqByI3Mk00GO3jssJrY2t88b59.jQGPnpyu9K0",
        "1", "cb418ef0af1243bd8c76e5ca9836f234", "5249d92ad5774d91a0b11b9a4c67193d", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "cb418ef0af1243bd8c76e5ca9836f234", "5249d92ad5774d91a0b11b9a4c67193d",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "cb418ef0af1243bd8c76e5ca9836f234", "cb418ef0af1243bd8c76e5ca9836f234",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("cb418ef0af1243bd8c76e5ca9836f234", "cb418ef0af1243bd8c76e5ca9836f234",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_zp-test-4", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "cb418ef0af1243bd8c76e5ca9836f234", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("518fad484a7fea693f298046f039367acd4432358dc17e8bc6858089a7e561b3", "cb418ef0af1243bd8c76e5ca9836f234",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"6c4381f48eec4f52b97a6050bfdd4941\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b3e07c1d9b8a989eb81640ede0c1f43dbefcf2c5460768cb15adc1e817eb4cea43c0c553235285697d5d822db1c9bb385\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("b66cfd99924642aca01f03d89a8a641a", "res_console_rabbitmq", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("5c1d6b6d1a554a4a96b8a0fbcdd49711", "default", "{}", "Default project for domain", "1",
        "b66cfd99924642aca01f03d89a8a641a");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("b66cfd99924642aca01f03d89a8a641a", "root", "{}",
        "$6$rounds=40000$3mZ8ORNp$BxvXEi396jre4Y/Qshcfi/nRtcRWRsGNN/Fp6WDrvSddqXrVJnyY.lzkin4S92qNSlUQGcCMcctmw54fm3JJw1",
        "1", "b66cfd99924642aca01f03d89a8a641a", "5c1d6b6d1a554a4a96b8a0fbcdd49711", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "b66cfd99924642aca01f03d89a8a641a", "5c1d6b6d1a554a4a96b8a0fbcdd49711",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "b66cfd99924642aca01f03d89a8a641a", "b66cfd99924642aca01f03d89a8a641a",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("b66cfd99924642aca01f03d89a8a641a", "b66cfd99924642aca01f03d89a8a641a",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_console_rabbitmq", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "b66cfd99924642aca01f03d89a8a641a", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("38bdfb993bc4257b7cea715537263f087254362904b16ca6820261f209b87523", "b66cfd99924642aca01f03d89a8a641a",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"64921e5c9f0a4b3fbf366fc9312d1060\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b5a2d8b48ca3e3c7dd1c9241fb7b56db68294f5c7f32b63613b06411d6752c8f47080b4c6747638001cf93d597b6268f7\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("8c83b51e295f437781c6f98af92d7ed1", "res_logic_rabbitmq", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("68fe9c9b395d4ee8a9c73e7497dd3a40", "default", "{}", "Default project for domain", "1",
        "8c83b51e295f437781c6f98af92d7ed1");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("8c83b51e295f437781c6f98af92d7ed1", "root", "{}",
        "$6$rounds=40000$G6gu6BPX$qBGRvnkEkGhFdX7gfTxCsWN/SnQrqRG6dszK1nb8uYl4ZJM2xeyTJFQLG5O/rVSqUxs4IfpHUO3rZhDGxD.H1.",
        "1", "8c83b51e295f437781c6f98af92d7ed1", "68fe9c9b395d4ee8a9c73e7497dd3a40", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "8c83b51e295f437781c6f98af92d7ed1", "68fe9c9b395d4ee8a9c73e7497dd3a40",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "8c83b51e295f437781c6f98af92d7ed1", "8c83b51e295f437781c6f98af92d7ed1",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("8c83b51e295f437781c6f98af92d7ed1", "8c83b51e295f437781c6f98af92d7ed1",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_logic_rabbitmq", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "8c83b51e295f437781c6f98af92d7ed1", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("dcc458e4109d97412d6bcf5697a552ee675970391ecea5244bd57dd02e8bc9f9", "8c83b51e295f437781c6f98af92d7ed1",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"e2bc899da96b4384bca43108d584085b\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bc7e42de65bc3337c4d268c2bf681674f0f1d33a3f04f3caede303dc0da96a40a5564f42a257dc7c345349d5c86f2710a\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");
INSERT INTO domain (id, name, enabled, extra)
VALUES ("cb4c0eca8c54415d8163af799b8a9fcf", "res_rabbitmq", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("8055347e6e204cd08053c203139257c6", "default", "{}", "Default project for domain", "1",
        "cb4c0eca8c54415d8163af799b8a9fcf");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("cb4c0eca8c54415d8163af799b8a9fcf", "root", "{}",
        "$6$rounds=40000$v0d2zTTp$NRSE4yEf0zbq43MMiwA.xC5I2cNjLiqTZa2SaNX8E8NMbC6PlWHGW5XoWSOSXVDTKAdDa0JdPiCIN/vjk5h8a.",
        "1", "cb4c0eca8c54415d8163af799b8a9fcf", "8055347e6e204cd08053c203139257c6", "2019-12-30 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "cb4c0eca8c54415d8163af799b8a9fcf", "8055347e6e204cd08053c203139257c6",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "cb4c0eca8c54415d8163af799b8a9fcf", "cb4c0eca8c54415d8163af799b8a9fcf",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("cb4c0eca8c54415d8163af799b8a9fcf", "cb4c0eca8c54415d8163af799b8a9fcf",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2019-12-30 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_rabbitmq", "2019-12-30 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "cb4c0eca8c54415d8163af799b8a9fcf", "2019-12-30 15:33:31", "2019-12-30 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("dcd5b215e34dd0de7fb2e03daaf5eb41467285d01e857d204ebdb1abed7ac059", "cb4c0eca8c54415d8163af799b8a9fcf",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"dc28a1f159364da0a37a0b9c0898f273\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b8c2ff4701cf892a4f2e4fd264889eedc630042fa8a36e5ac2cd39712cf19c457c0e85825d21a4279437727b509cdf338\\\"}\"",
        "ec2", "{}", "2019-12-30 15:33:31");

INSERT INTO domain (id, name, enabled, extra)
VALUES ("fb49f0420c3140b9a6bccfb9640d8c5b", "res_ccr", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("74a616eb5d1c458fa09478654b1dc6c6", "default", "{}", "Default project for domain", "1",
        "fb49f0420c3140b9a6bccfb9640d8c5b");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("fb49f0420c3140b9a6bccfb9640d8c5b", "root", "{}",
        "$6$rounds=40000$qYmBetLd$pgbk8zvybXs0TDeOtzxsOr7ptkEgJx9C3AuRY0MlwgwkwgL.WBHh8LSNpmYkdDiV4NGmYKrYBoOBWRzBS0lvW1",
        "1", "fb49f0420c3140b9a6bccfb9640d8c5b", "74a616eb5d1c458fa09478654b1dc6c6", "2020-02-26 17:53:41", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "fb49f0420c3140b9a6bccfb9640d8c5b", "74a616eb5d1c458fa09478654b1dc6c6",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "fb49f0420c3140b9a6bccfb9640d8c5b", "fb49f0420c3140b9a6bccfb9640d8c5b",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("fb49f0420c3140b9a6bccfb9640d8c5b", "fb49f0420c3140b9a6bccfb9640d8c5b",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2020-02-26 17:53:41");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_ccr", "2020-02-26 17:53:41", "individual", "{\"description\":{\"codeChecked\":true}}",
        "fb49f0420c3140b9a6bccfb9640d8c5b", "2020-02-26 17:53:41", "2020-02-26 17:53:41");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("abf97afb481a8fed5fc179ef2d707d05a81e3a3f6ff0c37e616ac704ae1e5afc", "fb49f0420c3140b9a6bccfb9640d8c5b",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"c306598db1104308b85044e1ef319562\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bf7a45dc95c8389cf1a308ecf9918c58dfe10f1131b40ca3db69e1b2617ce2e1ec1b1daf170cd874ab1b2113c6aee818e\\\"}\"",
        "ec2", "{}", "2020-02-26 17:53:41");
        
INSERT INTO domain (id, name, enabled, extra)
VALUES ("618d10ddffe144c8874fe6315d206fa6", "res_chsm", "1", "{}");

INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("50104cb66c444ecd987a4eaf520f0a54", "default", "{}", "Default project for domain", "1",
        "618d10ddffe144c8874fe6315d206fa6");

INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("618d10ddffe144c8874fe6315d206fa6", "root", "{}",
        "$6$rounds=40000$rN324rxEYAX5bLh2$hrCIigAnr76F1YC6IrxeRuiOJvLjxsHseQbjWCsLq/EChe69FxViGEVrvl76Hag.3Vgp5bLKXB811b83wIloA0",
        "1", "618d10ddffe144c8874fe6315d206fa6", "50104cb66c444ecd987a4eaf520f0a54", "2021-10-14 15:33:31", "0");

INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "618d10ddffe144c8874fe6315d206fa6", "50104cb66c444ecd987a4eaf520f0a54",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");

INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "618d10ddffe144c8874fe6315d206fa6", "618d10ddffe144c8874fe6315d206fa6",
        "2a083c03d6ee4d348da41362cb6c7381", "1");

INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("618d10ddffe144c8874fe6315d206fa6", "618d10ddffe144c8874fe6315d206fa6",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2021-10-14 15:33:31");

INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_chsm", "2021-10-14 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "618d10ddffe144c8874fe6315d206fa6", "2021-10-14 15:33:31", "2021-10-14 15:33:31");


INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("be74b77b04cbe41b81f1f86d0ad7088608489e1bf9ddedfd28467a0626b04505", "618d10ddffe144c8874fe6315d206fa6",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"07a6dbbaa50349c69c9c5f0241d89e25\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bdd0da49af3385dda8a229b06544fa1752c2b94d8b9d6e05959b345f594e18e8ba2a5cda2ac1a9df14ffc6de36da6c62b\\\"}\"",
        "ec2", "{}", "2021-10-14 15:33:31");



INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,
                  `public_id`, `create_time`, `subuser`)
VALUES ("6a36d47383d9410085402d5cd8d877d0", "ddc", "{\"enabled\":true, \"status\":\"ACTIVE\"}",
        "$6$rounds=40000$TI6HycvJ$WwAhlUFs3wwhxCMyC8fqpmpbf3ssIOOqoJhkjUepxZswHkqwMzWU5Ya2Iru3H0M4LVmUUVNDfQakDyJK02fHF.",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");
INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "6a36d47383d9410085402d5cd8d877d0", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");
INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "6a36d47383d9410085402d5cd8d877d0", "default", "7151762f590a47f89213d5d8677a9b63", "0");
INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("632187a4f7dd2e95b0db307660b1b77bf9d0b15b664894c8caad7811df9c1fa4", "6a36d47383d9410085402d5cd8d877d0", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"09c97231165b42a397b6a26b11ac49a3\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b731f3fe51b9a58f2f80bf4d41d48c5b4adcaf1053a935ed46c17d0371765d7b70cd84b3e643c0465988c693f378fe55f\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");
INSERT INTO `active_service`
VALUES ('6a36d47383d9410085402d5cd8d877d0', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO domain (id, name, enabled, extra)
VALUES ("ff1c998bd58e4ad3a863c22072788374", "res_ddc", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("1177117332f04b3da712bd51870d09d3", "default", "{}", "Default project for domain", "1",
        "ff1c998bd58e4ad3a863c22072788374");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("ff1c998bd58e4ad3a863c22072788374", "root", "{}",
        "$6$rounds=40000$w.D9Cer2c5t/jVEX$CyCwSqM0w3ZoUoki1Vrfr0/.dL6TO84Rx8fvyJ5QQWc/RDtgMFtjtOwP3f/0kUBHf2NorbCvhraLhzajrJZxq0",
        "1", "ff1c998bd58e4ad3a863c22072788374", "1177117332f04b3da712bd51870d09d3", "2022-01-12 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "ff1c998bd58e4ad3a863c22072788374", "1177117332f04b3da712bd51870d09d3",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "ff1c998bd58e4ad3a863c22072788374", "ff1c998bd58e4ad3a863c22072788374",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("ff1c998bd58e4ad3a863c22072788374", "ff1c998bd58e4ad3a863c22072788374",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2022-01-12 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_ddc", "2022-01-12 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "ff1c998bd58e4ad3a863c22072788374", "2022-01-12 15:33:31", "2022-01-12 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0b02684266ea1567440113a69a5fd2ae868f42b30b04082b0f3d1c4f834beaa6", "ff1c998bd58e4ad3a863c22072788374",
        "0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"f840a81e9eb643ca8427e2c7a9f0c986\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bad3ffc73e3f2b73ce0efea421ba22354f45094491741717a5f82d943fab872bd64993d6983e3567d2cfefc1a87430d8e\\\"}\"",
        "ec2", "{}", "2022-01-12 15:33:31");

INSERT INTO assignment  (`type`, `actor_id`, `target_id`, `role_id`, `inherited`) VALUES ("UserProject", "52695dc9627b4076bae50cd065257d4d", "0557fd0d0d244cf7801dba4cadf214c5", "30b39c9eb92e47c5b9d77c20e0fc5599", "0");
INSERT INTO assignment  (`type`, `actor_id`, `target_id`, `role_id`, `inherited`) VALUES ("UserDomain", "52695dc9627b4076bae50cd065257d4d", "default", "30b39c9eb92e47c5b9d77c20e0fc5599", "0");

INSERT INTO assignment  (`type`, `actor_id`, `target_id`, `role_id`, `inherited`) VALUES ("UserProject", "938b5b980a5345a4beaf958df47add39", "0557fd0d0d244cf7801dba4cadf214c5", "30b39c9eb92e47c5b9d77c20e0fc5599", "0");
INSERT INTO assignment  (`type`, `actor_id`, `target_id`, `role_id`, `inherited`) VALUES ("UserDomain", "938b5b980a5345a4beaf958df47add39", "default", "30b39c9eb92e47c5b9d77c20e0fc5599", "0");


INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,
                  `public_id`, `create_time`, `subuser`)
VALUES ("e05a4ca93a6d4e42ac50942db7af3c90", "pfs", "{\"enabled\":true, \"status\":\"ACTIVE\"}",
        "$6$rounds=40000$LXCaYyie$79n8vS4Y9hBfvJQiaoQUS.MG0BxxT3yRZGoNRQrBfFDsbcyw4H.KcI5hJ.oyFnnUueRiGjZM6ztxDNBlmGQun1",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");
INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "e05a4ca93a6d4e42ac50942db7af3c90", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");
INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "e05a4ca93a6d4e42ac50942db7af3c90", "default", "7151762f590a47f89213d5d8677a9b63", "0");
INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("f215deb8f7a9b2090307a0c337be0c30f7db70133c58aeefed0fc91699b6ef49", "e05a4ca93a6d4e42ac50942db7af3c90", "0557fd0d0d244cf7801dba4cadf214c5",
 "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"85e30caa44ae413ba9cd511e67bdbe48\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b64dbcda48037f2e8026a9baf43a2ce1a268a1a9658cb3e2e07ec85b520e29a8e80a59ae997a12c8b0a93c526497e7fe0\\\"}\"",
  "ec2", "{}", "2022-01-25 17:38:53");
INSERT INTO `active_service`
VALUES ('e05a4ca93a6d4e42ac50942db7af3c90', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO domain (id, name, enabled, extra)
VALUES ("954b2191f8164e5ab0d7dece83540ed5", "res_pfs", "1", "{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("7b8e28a69df34552bb8ac483b2f402b2", "default", "{}", "Default project for domain", "1",
        "954b2191f8164e5ab0d7dece83540ed5");
INSERT INTO user (id, name, extra, password, enabled, domain_id, default_project_id, create_time, subuser)
VALUES ("954b2191f8164e5ab0d7dece83540ed5", "root", "{}",
        "$6$rounds=40000$cuKSR.rzRKLb/yeR$nPaMQzOk5X9uffwsuJcwrYeJf8d/2a9bVivG3zk8.G559mUuVlTePV/w9.ZcFD5.OHE14uRLv2ABU2dgGNYOc1",
        "1", "954b2191f8164e5ab0d7dece83540ed5", "7b8e28a69df34552bb8ac483b2f402b2", "2022-01-12 15:33:31", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserProject", "954b2191f8164e5ab0d7dece83540ed5", "7b8e28a69df34552bb8ac483b2f402b2",
        "8a8a9c5f9ff94391b0e39ee339d2a92d", "0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES ("UserDomain", "954b2191f8164e5ab0d7dece83540ed5", "954b2191f8164e5ab0d7dece83540ed5",
        "2a083c03d6ee4d348da41362cb6c7381", "1");
INSERT INTO contact_info (domain_id, user_id, mobile_phone, mobile_verified, create_time)
VALUES ("954b2191f8164e5ab0d7dece83540ed5", "954b2191f8164e5ab0d7dece83540ed5",
        "noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6", "1",
        "2022-01-12 15:33:31");
INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time)
VALUES ("BCIA:res_pfs", "2022-01-12 15:33:31", "individual", "{\"description\":{\"codeChecked\":true}}",
        "954b2191f8164e5ab0d7dece83540ed5", "2022-01-12 15:33:31", "2022-01-12 15:33:31");
INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("4104aced8b421ef04a650faca183240279d05dbe5b634d085224cd804033addf", "954b2191f8164e5ab0d7dece83540ed5","0557fd0d0d244cf7801dba4cadf214c5",
        "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"5f9d6f8f81a74a42985674f04f736bc4\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bbd4b5afd870303703b0f23f633ac0ebd553f1d516c1aecb3eb462b470fba9a331cd4dcf73ba9f68dc7dc5aa7af02a142\\\"}\"",
        "ec2", "{}", "2022-01-12 15:33:31");

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,
                  `public_id`, `create_time`, `subuser`)
VALUES ("353ef2f871c84b3d9014f081ca679dfc", "pfs_logic", "{\"enabled\":true, \"status\":\"ACTIVE\"}",
        "$6$rounds=40000$QZE8WrmR$DVE67Y8OTsLLgVT70S4nZjS5ZwkxhvTaOPpcFS1HEU.e.g.bkp1Z4GsVqSECMcKD/7hORpFJbNVIW1KdQ/4To.",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-26 17:18:11", "0");
INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "353ef2f871c84b3d9014f081ca679dfc", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");
INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "353ef2f871c84b3d9014f081ca679dfc", "default", "7151762f590a47f89213d5d8677a9b63", "0");
INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("b7fe8990b4883430f8d3f1e42bffa0df6c63966fed9b0fa7710bc68f8c3263f8", "353ef2f871c84b3d9014f081ca679dfc", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"6596db62561c4bc6ac467d31b8523e74\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57bd9885aab3037f949dc86293777ec68cfd7c53716943d4c0fe7e7dafc1423f34dd27a771fa2636eec991d13f20ec299da\\\"}\"",
     "ec2", "{}", "2022-01-26 17:27:10");
INSERT INTO `active_service`
VALUES ('353ef2f871c84b3d9014f081ca679dfc', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,
                  `public_id`, `create_time`, `subuser`)
VALUES ("964d6abe8ca240ee82a69d5f51740602", "console_ddc", "{\"enabled\":true, \"status\":\"ACTIVE\"}",
        "$6$rounds=40000$u1i61xAY$Y9hNXxXUgKOuVxw9Amqx4Qmv4hP4vrA3VYIhTIxTAlv8PZWnWNgd7sUa6uN9MxaRevApFfFZ.swVMDwU.0x.b/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-02-25 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "964d6abe8ca240ee82a69d5f51740602", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");
INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "964d6abe8ca240ee82a69d5f51740602", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("9949e5f8ccf3b0cc1773384e513f16a948460e6dedbc9ffa5f7077835c26041c", "964d6abe8ca240ee82a69d5f51740602", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"29e1f7f46bc746d89f541ea83cfcb6b0\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba1f5b08fc20ec0e8d138283b12001012d1e208aa12787b564c9fb5252fa3868ff53d4c96813880dcb2d64abbf0211e9f\\\"}\"",
     "ec2", "{}", "2022-02-25 17:27:04");


INSERT INTO `active_service`
VALUES ('964d6abe8ca240ee82a69d5f51740602', 'e274118227b24725b57a3d089a39c3f8', NULL);

-- 服务号
INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("42f2a9f1d3df4d21b8dc15b47758b94e", "hbase", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "42f2a9f1d3df4d21b8dc15b47758b94e", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "42f2a9f1d3df4d21b8dc15b47758b94e", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("eba10c9c5ce40619c169e0c14fae7034d526b3d5e588f862f64ed768fd352823", "42f2a9f1d3df4d21b8dc15b47758b94e", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('42f2a9f1d3df4d21b8dc15b47758b94e', 'e274118227b24725b57a3d089a39c3f8', NULL);

-- 资源账号
set names utf8;
INSERT INTO domain (id, name, enabled, extra)
VALUES ("7cce9262dab14d5d9294d7157af364d6","res_hbase","1","{}");
INSERT INTO project (id, name, extra, description, enabled, domain_id)
VALUES ("cc50075ceb9641959ae24c9540c6ed38","default","{}","Default project for domain","1","7cce9262dab14d5d9294d7157af364d6");

INSERT INTO user (id,name,extra,password,enabled,domain_id,default_project_id,create_time,subuser)
VALUES("7cce9262dab14d5d9294d7157af364d6","root","{}","$6$rounds=40000$w.D9Cer2c5t/jVEX$CyCwSqM0w3ZoUoki1Vrfr0/.dL6TO84Rx8fvyJ5QQWc/RDtgMFtjtOwP3f/0kUBHf2NorbCvhraLhzajrJZxq0","1","7cce9262dab14d5d9294d7157af364d6","cc50075ceb9641959ae24c9540c6ed38",now(),"0");

INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES("UserProject","7cce9262dab14d5d9294d7157af364d6","cc50075ceb9641959ae24c9540c6ed38","8a8a9c5f9ff94391b0e39ee339d2a92d","0");
INSERT INTO assignment (type, actor_id, target_id, role_id, inherited)
VALUES("UserDomain","7cce9262dab14d5d9294d7157af364d6","7cce9262dab14d5d9294d7157af364d6","2a083c03d6ee4d348da41362cb6c7381","1");

INSERT INTO contact_info (domain_id,user_id,mobile_phone,mobile_verified,create_time)
VALUES("7cce9262dab14d5d9294d7157af364d6","7cce9262dab14d5d9294d7157af364d6","noencryptedprefix@0fe7a268b7479d19858252c2e59ea57bb45055ec69a85749c4c39e5203ccf9e6","1",now());

INSERT INTO account (name, register_time, account_type, extra, domain_id, activate_time, modify_time )
VALUES ("BCIA:res_hbase", now(), "individual", "{\"description\":{\"codeChecked\":true}}", "7cce9262dab14d5d9294d7157af364d6", now(), now() );

INSERT INTO credential (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0eda6ca364570d6668da0b34e9f77f1efaa015facc59929aa3a14ec176112669", "7cce9262dab14d5d9294d7157af364d6", "cc50075ceb9641959ae24c9540c6ed38",
"\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"d76745e06bc74e6881488bbc7630713b\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57b9eeb5bc3e5f13f285e1ba2698aaadca09725986ebc34d514fa65a130160f65c888d66fcf9a56d3b5401fa9b0ffbba948\\\"}\"",
"ec2", "{}", "2022-03-07 11:29:06" );

set names utf8;
INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("d8e919e678b74e758c7480f70e535b8e", "abcce", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "d8e919e678b74e758c7480f70e535b8e", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "d8e919e678b74e758c7480f70e535b8e", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("07c8c5f50069e5f8f1b0f8ac7d6455a345554b850d73361eaf6f8576809f1259", "d8e919e678b74e758c7480f70e535b8e", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('d8e919e678b74e758c7480f70e535b8e', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("b5490aa323434fedbf1429dd20824f2f", "abc_store", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "b5490aa323434fedbf1429dd20824f2f", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "b5490aa323434fedbf1429dd20824f2f", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("a528670edd7c0d4b15f5751fbb6f9c582da0bab7bc53dada57d50f8522986dd0", "b5490aa323434fedbf1429dd20824f2f", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('b5490aa323434fedbf1429dd20824f2f', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("efb1b3d9965347ea9d50a625f3d92ce4", "abc_store_bec", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "efb1b3d9965347ea9d50a625f3d92ce4", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "efb1b3d9965347ea9d50a625f3d92ce4", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("1cca2ae0eccb3676ae6a68306a6fedd8ef6666ad2384d4af80f5ce88664c41b9", "efb1b3d9965347ea9d50a625f3d92ce4", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('efb1b3d9965347ea9d50a625f3d92ce4', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("aaaee359bd4f416580067879f65b0159", "adas", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "aaaee359bd4f416580067879f65b0159", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "aaaee359bd4f416580067879f65b0159", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("3d870438975fd6a629d21c67ccdaec8a39005f23d9e22a6af822560c5784b708", "aaaee359bd4f416580067879f65b0159", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('aaaee359bd4f416580067879f65b0159', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("bdc8d5b468324af9a8c21df7a9386aa7", "ai_biar", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "bdc8d5b468324af9a8c21df7a9386aa7", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "bdc8d5b468324af9a8c21df7a9386aa7", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("ff4bd97c4f6c634da7a0fc95e2ad8b7ec5e840d5401e4ea827e3b3dc3220cb32", "bdc8d5b468324af9a8c21df7a9386aa7", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('bdc8d5b468324af9a8c21df7a9386aa7', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("b58ea5732b6d4fee8dcc9afb0f85b694", "ai_bml_public", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "b58ea5732b6d4fee8dcc9afb0f85b694", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "b58ea5732b6d4fee8dcc9afb0f85b694", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("4f3230a73bff87fceb2bd888db97f15f808d2c6fb8c36696ad3617d65d220902", "b58ea5732b6d4fee8dcc9afb0f85b694", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('b58ea5732b6d4fee8dcc9afb0f85b694', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("6cb0f4398c874471bea514dde1ce03e8", "ai_cdfp", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "6cb0f4398c874471bea514dde1ce03e8", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "6cb0f4398c874471bea514dde1ce03e8", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("96d8ee8d7c42a1639dc9490359771c6a4dbb72ebc3dcc81f7203ddc7958a7b07", "6cb0f4398c874471bea514dde1ce03e8", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('6cb0f4398c874471bea514dde1ce03e8', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("f0147224413d48cea0baddc01aa057bc", "ai_cuist", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "f0147224413d48cea0baddc01aa057bc", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "f0147224413d48cea0baddc01aa057bc", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0c27059ea7dcaa3443d25547111ed67dc41c32e108dd013da20e7f47a4f769ec", "f0147224413d48cea0baddc01aa057bc", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('f0147224413d48cea0baddc01aa057bc', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("989ed75cf4134255a66c796386667543", "ai_dh", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "989ed75cf4134255a66c796386667543", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "989ed75cf4134255a66c796386667543", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("f50381402d7f8e18b499c77219b38c11926d71c056c1bde85ffd0453fc31974e", "989ed75cf4134255a66c796386667543", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('989ed75cf4134255a66c796386667543', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("3f9e611049bb49dc8f6abbc9b4dc7bb0", "ai_follow_up_public", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "3f9e611049bb49dc8f6abbc9b4dc7bb0", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "3f9e611049bb49dc8f6abbc9b4dc7bb0", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("ce750f267b1c445d1e701c817b8e4c9a0cc4fa5ff9e6f7e96f56fdf6486b58ec", "3f9e611049bb49dc8f6abbc9b4dc7bb0", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('3f9e611049bb49dc8f6abbc9b4dc7bb0', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("5dfee8ccdecc430d826815b22effe991", "ai_ieip", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "5dfee8ccdecc430d826815b22effe991", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "5dfee8ccdecc430d826815b22effe991", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("1f4f5651ed5f726b03612c0b029a8f4bb48972aa0bb1e0b9f03106ff724c91a5", "5dfee8ccdecc430d826815b22effe991", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('5dfee8ccdecc430d826815b22effe991', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("2574a7d376584c5caae009c2749d1bb7", "ai_mms", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "2574a7d376584c5caae009c2749d1bb7", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "2574a7d376584c5caae009c2749d1bb7", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("2fb8df19801ea0ead32a03b79615808ea58dcd8e48a691f67fa04b5c0b55602c", "2574a7d376584c5caae009c2749d1bb7", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('2574a7d376584c5caae009c2749d1bb7', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("0aee931667714f5d860c3b954c1e2ddf", "ai_vcs", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "0aee931667714f5d860c3b954c1e2ddf", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "0aee931667714f5d860c3b954c1e2ddf", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("568825348317aec26579b334c740a8865f0693d80b4f5161ac506319d27cfddb", "0aee931667714f5d860c3b954c1e2ddf", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('0aee931667714f5d860c3b954c1e2ddf', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("7daaebe736a546e5a52af30214019b42", "antibot", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "7daaebe736a546e5a52af30214019b42", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "7daaebe736a546e5a52af30214019b42", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("1b9aed9bd2ebb4a605cdcbf3e31b9b93e9605936f9e755d6d2b7768ed9a43cea", "7daaebe736a546e5a52af30214019b42", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('7daaebe736a546e5a52af30214019b42', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("ac56aa242b6346fcac164898a9cae68a", "armcm", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "ac56aa242b6346fcac164898a9cae68a", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "ac56aa242b6346fcac164898a9cae68a", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("84a9b398db76d493e91fa709e7a3031c6d8989bba966fdd489c8083d0e2d587a", "ac56aa242b6346fcac164898a9cae68a", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('ac56aa242b6346fcac164898a9cae68a', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("53b74b7e8913410b8a229abd33f194c9", "企业版ccr", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "53b74b7e8913410b8a229abd33f194c9", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "53b74b7e8913410b8a229abd33f194c9", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("01e7ecdda5c7531313a94c9055816c656067040d2509afcc7d185027f48f4b31", "53b74b7e8913410b8a229abd33f194c9", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('53b74b7e8913410b8a229abd33f194c9', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("4571db28948e470e86625f5d33409266", "bce_op", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "4571db28948e470e86625f5d33409266", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "4571db28948e470e86625f5d33409266", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("e1ae2f0c893d8c75ddd881d7fdacabf666ae272c455f28e1db7b6257632600d6", "4571db28948e470e86625f5d33409266", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('4571db28948e470e86625f5d33409266', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("70d964f9a0f140d68bb0e255a8d397f7", "bnd_e", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "70d964f9a0f140d68bb0e255a8d397f7", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "70d964f9a0f140d68bb0e255a8d397f7", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("968dbc87d157832b6cb678f8fb6c9a32f6815a776fbdcd8b8a4630750701ebe4", "70d964f9a0f140d68bb0e255a8d397f7", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('70d964f9a0f140d68bb0e255a8d397f7', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("109f39871a82465fa70cbe45bbe09262", "bsg_new", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "109f39871a82465fa70cbe45bbe09262", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "109f39871a82465fa70cbe45bbe09262", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0c421c0ce5c1701898d931d994700b8ba3081ec42742243211664a6c533b9024", "109f39871a82465fa70cbe45bbe09262", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('109f39871a82465fa70cbe45bbe09262', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("baadc72aee0b4c97aac945b776c03795", "btn", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "baadc72aee0b4c97aac945b776c03795", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "baadc72aee0b4c97aac945b776c03795", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("d359fff0c676c2d2370467da0592f07fbca4018a227c0baa7d821537140271e8", "baadc72aee0b4c97aac945b776c03795", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('baadc72aee0b4c97aac945b776c03795', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("2cf8016dfcb347b690759687cdf7df44", "cag", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "2cf8016dfcb347b690759687cdf7df44", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "2cf8016dfcb347b690759687cdf7df44", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("336079c8950f12e28cf61f802613e6bec823a5ef2a9d40806d34317b4155e353", "2cf8016dfcb347b690759687cdf7df44", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('2cf8016dfcb347b690759687cdf7df44', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("8f77e8e49b2449a9a28b5c190ea10e78", "cbe", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "8f77e8e49b2449a9a28b5c190ea10e78", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "8f77e8e49b2449a9a28b5c190ea10e78", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8aa89dd7998e49d299833c552ad9382a8177226e3895bcd05e53060102a4b86a", "8f77e8e49b2449a9a28b5c190ea10e78", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('8f77e8e49b2449a9a28b5c190ea10e78', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("9fb243cc70114a83a9a0e9f0d3b7d492", "cbs", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "9fb243cc70114a83a9a0e9f0d3b7d492", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "9fb243cc70114a83a9a0e9f0d3b7d492", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("60e455e1ee7743f39bf567ab5581ea2791534bf38156709056b9290c105e5676", "9fb243cc70114a83a9a0e9f0d3b7d492", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('9fb243cc70114a83a9a0e9f0d3b7d492', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("1203d698c351467bb5185fe055cfcc91", "cdm", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "1203d698c351467bb5185fe055cfcc91", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "1203d698c351467bb5185fe055cfcc91", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("1e3c94924f87c34908025b51189797e623f28f7a77864b7821306dc477663b01", "1203d698c351467bb5185fe055cfcc91", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('1203d698c351467bb5185fe055cfcc91', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("7c4848b1df3c45188891a836da6ba52a", "cdss", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "7c4848b1df3c45188891a836da6ba52a", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "7c4848b1df3c45188891a836da6ba52a", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("ab9601a03c61d108e1aabeaa4347bd33f85580de939e77800d480d50a7c00435", "7c4848b1df3c45188891a836da6ba52a", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('7c4848b1df3c45188891a836da6ba52a', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("58d3c97b6d2c4a6f8017a49c504ac0f4", "cdss_irqp", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "58d3c97b6d2c4a6f8017a49c504ac0f4", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "58d3c97b6d2c4a6f8017a49c504ac0f4", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("15240668137616e7283c13c46baf8dd3509d4d6eef799cf7debf0292eb318158", "58d3c97b6d2c4a6f8017a49c504ac0f4", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('58d3c97b6d2c4a6f8017a49c504ac0f4', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("5a92e3f794b9427bbe72cddeabbafc05", "ceih", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "5a92e3f794b9427bbe72cddeabbafc05", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "5a92e3f794b9427bbe72cddeabbafc05", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("d5456a16c5eddb1f4af28da0e6161915ee036a273086f531f4467ccef859e458", "5a92e3f794b9427bbe72cddeabbafc05", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('5a92e3f794b9427bbe72cddeabbafc05', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("22b6b0c381a845fb9ef78b8fe7467bb0", "cfw", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "22b6b0c381a845fb9ef78b8fe7467bb0", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "22b6b0c381a845fb9ef78b8fe7467bb0", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("35c987726b6edc95e801bdeddb60faa979a4b2200840627f64bee5912fe0e696", "22b6b0c381a845fb9ef78b8fe7467bb0", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('22b6b0c381a845fb9ef78b8fe7467bb0', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("8279537a6f824b90822a2bf446db9b0b", "chpc", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "8279537a6f824b90822a2bf446db9b0b", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "8279537a6f824b90822a2bf446db9b0b", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("52d316e83e38233b46e7c9108b5c61448d4f5f0743768d7e7108843d9841de3d", "8279537a6f824b90822a2bf446db9b0b", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('8279537a6f824b90822a2bf446db9b0b', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("df650b41bc244ca4a6cffcb85dab2d3a", "Cloud assistant", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "df650b41bc244ca4a6cffcb85dab2d3a", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "df650b41bc244ca4a6cffcb85dab2d3a", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("fcd94bd43b911bc6de152395e264c257a04df1a969473407480edc9df68a51f3", "df650b41bc244ca4a6cffcb85dab2d3a", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('df650b41bc244ca4a6cffcb85dab2d3a', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("137e3e4dc46e44ea805c173835ad6347", "cprom", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "137e3e4dc46e44ea805c173835ad6347", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "137e3e4dc46e44ea805c173835ad6347", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8de38fb60be790939dadb18d79047b888574c3297e299ab614562839867f06c0", "137e3e4dc46e44ea805c173835ad6347", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('137e3e4dc46e44ea805c173835ad6347', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("ca77d08f2a8642a8b67182b0db92288a", "cpts", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "ca77d08f2a8642a8b67182b0db92288a", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "ca77d08f2a8642a8b67182b0db92288a", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8496926d8c558b16c9c374f3b506f32d44b45e034649cb9b346cdec862735ba9", "ca77d08f2a8642a8b67182b0db92288a", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('ca77d08f2a8642a8b67182b0db92288a', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("9818d9331e15425494b0069f487b9469", "crm-financial-report", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "9818d9331e15425494b0069f487b9469", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "9818d9331e15425494b0069f487b9469", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("1787ed57649fb57d7c42fd3b4cb32d80b6e8e5ae11f0653d662eb29fca430773", "9818d9331e15425494b0069f487b9469", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('9818d9331e15425494b0069f487b9469', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("769ff70be233409da8d43178d5c6258c", "CRM_to_CMS", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "769ff70be233409da8d43178d5c6258c", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "769ff70be233409da8d43178d5c6258c", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("7992acd30bd5b58483d58cc97805380b8ee510f0691753a3019e171d8e2c0715", "769ff70be233409da8d43178d5c6258c", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('769ff70be233409da8d43178d5c6258c', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("2bf77fcc60c0446b8d45abc3bd789ce0", "csm", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "2bf77fcc60c0446b8d45abc3bd789ce0", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "2bf77fcc60c0446b8d45abc3bd789ce0", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("374d740fc60c4e8013f486971054b94a9dd906b0ff2e5b261988ff6375329ad3", "2bf77fcc60c0446b8d45abc3bd789ce0", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('2bf77fcc60c0446b8d45abc3bd789ce0', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("b512ee9a463e44bd98b01f2b3d6906ba", "dcell", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "b512ee9a463e44bd98b01f2b3d6906ba", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "b512ee9a463e44bd98b01f2b3d6906ba", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("5f191dcbdb9d164343ec740592f6362ef592e5dea8b8c2eb45873b5c66c94724", "b512ee9a463e44bd98b01f2b3d6906ba", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('b512ee9a463e44bd98b01f2b3d6906ba', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("4138cc525fbe424684d6796b856597a4", "dmi", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "4138cc525fbe424684d6796b856597a4", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "4138cc525fbe424684d6796b856597a4", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("78ac47c0de524a9a5207ec8d6c7dedba971c3094152090ec0d863a52056a52c4", "4138cc525fbe424684d6796b856597a4", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('4138cc525fbe424684d6796b856597a4', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("050c1ccc67984829bdd5b90980b1efcf", "drcdn", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "050c1ccc67984829bdd5b90980b1efcf", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "050c1ccc67984829bdd5b90980b1efcf", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("137092ef11cf57596cb3f5cfe1cfbed339b6539954af6312c66d02a304e93064", "050c1ccc67984829bdd5b90980b1efcf", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('050c1ccc67984829bdd5b90980b1efcf', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("36e2be3dc2d942c893946805be0b5c62", "eccr", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "36e2be3dc2d942c893946805be0b5c62", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "36e2be3dc2d942c893946805be0b5c62", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("9d42d1a751d361ad6b9a0593f5b90130783a71c04e968e3212f53f61f8e4aaba", "36e2be3dc2d942c893946805be0b5c62", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('36e2be3dc2d942c893946805be0b5c62', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("e3d6ed92abc643ae81e2c9aa6d14ed2c", "emrss_public", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "e3d6ed92abc643ae81e2c9aa6d14ed2c", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "e3d6ed92abc643ae81e2c9aa6d14ed2c", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("dcfc4d2542ffa88587aabda19e739b9ff2ad58297d7b75d4221b485447609d59", "e3d6ed92abc643ae81e2c9aa6d14ed2c", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('e3d6ed92abc643ae81e2c9aa6d14ed2c', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("d14b88a5c7c34324b10b22182fbd6f3e", "es", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "d14b88a5c7c34324b10b22182fbd6f3e", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "d14b88a5c7c34324b10b22182fbd6f3e", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("79efc4085d1e54a4aed0a7cbeba5e13c72193c857aa6947ab81b6a18b02fb93e", "d14b88a5c7c34324b10b22182fbd6f3e", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('d14b88a5c7c34324b10b22182fbd6f3e', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("abfbaae27aaf41fe82becc2325c221d4", "es_private", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "abfbaae27aaf41fe82becc2325c221d4", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "abfbaae27aaf41fe82becc2325c221d4", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("7234be2bc1696a7232e3eecb46e3982066dd72b232c9e45dc24084d9118b9a6d", "abfbaae27aaf41fe82becc2325c221d4", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('abfbaae27aaf41fe82becc2325c221d4', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("c17e461a454a4fbf8ef1143ab3137092", "evs", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "c17e461a454a4fbf8ef1143ab3137092", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "c17e461a454a4fbf8ef1143ab3137092", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("7889856bb51981c19bcdee2da91b51cc2cf5dc866285b11f4e873cbd3e505a90", "c17e461a454a4fbf8ef1143ab3137092", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('c17e461a454a4fbf8ef1143ab3137092', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("9422ac7cde6940f381cfd62ff0bc009e", "financial_report", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "9422ac7cde6940f381cfd62ff0bc009e", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "9422ac7cde6940f381cfd62ff0bc009e", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("abfb3dfd2d36bb3b775b875597a7380be2ce527d8f555a7600bfeccd76b7226c", "9422ac7cde6940f381cfd62ff0bc009e", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('9422ac7cde6940f381cfd62ff0bc009e', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("27348e2c972b48148ee0e85ea96890f6", "fsi_fmp", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "27348e2c972b48148ee0e85ea96890f6", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "27348e2c972b48148ee0e85ea96890f6", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("a63e5398543519766702e1d1130fb509df82fb9443cc603d7fd9b4c3ed963ca0", "27348e2c972b48148ee0e85ea96890f6", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('27348e2c972b48148ee0e85ea96890f6', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("f36cfbb04f0f4ddbb42620079252ddec", "fsu_dvsh", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "f36cfbb04f0f4ddbb42620079252ddec", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "f36cfbb04f0f4ddbb42620079252ddec", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("4dc43379c5f17d25de44f5208e97b132879482745aa5ac89b0319b4d252fff61", "f36cfbb04f0f4ddbb42620079252ddec", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('f36cfbb04f0f4ddbb42620079252ddec', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("6f342a6863f34d4da0abfa48ae16078b", "gaiadb", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "6f342a6863f34d4da0abfa48ae16078b", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "6f342a6863f34d4da0abfa48ae16078b", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("46413056243f95c22e2760ca2e3d61a2dd5a759be8d2434adec27050ca98a370", "6f342a6863f34d4da0abfa48ae16078b", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('6f342a6863f34d4da0abfa48ae16078b', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("a05fe2fedb424655b4486ca6415804a6", "gtt", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "a05fe2fedb424655b4486ca6415804a6", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "a05fe2fedb424655b4486ca6415804a6", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("bb5ebc0b9e452fefd43d9d82e7023c1fc2f6b37101af2ef301249c36a450e2bb", "a05fe2fedb424655b4486ca6415804a6", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('a05fe2fedb424655b4486ca6415804a6', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("c1bf3850348b4c24a130b084d589431e", "hisk_private", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "c1bf3850348b4c24a130b084d589431e", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "c1bf3850348b4c24a130b084d589431e", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("46619d31406171267548e538ba355dcc3904b722360daa61ec88f4f66163ce32", "c1bf3850348b4c24a130b084d589431e", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('c1bf3850348b4c24a130b084d589431e', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("566f87148b214190970ea35a9ba52f65", "iam_waf", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "566f87148b214190970ea35a9ba52f65", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "566f87148b214190970ea35a9ba52f65", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("3a7b2b5722f02300f1e2dd08c4189a9a8f4a05540da513a350a1c90e495db93e", "566f87148b214190970ea35a9ba52f65", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('566f87148b214190970ea35a9ba52f65', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("fe33cec31fe94584a61c20da7d474111", "iap", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "fe33cec31fe94584a61c20da7d474111", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "fe33cec31fe94584a61c20da7d474111", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("2cb5f21310769b483173a8ae60665f6bb67b1c5c43f045c4e8648260cafaf9f2", "fe33cec31fe94584a61c20da7d474111", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('fe33cec31fe94584a61c20da7d474111', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("b2b1a4806e4c432aaaaab4256a47dddf", "icafe", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "b2b1a4806e4c432aaaaab4256a47dddf", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "b2b1a4806e4c432aaaaab4256a47dddf", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("2c0a37fe8aac191a5ff87433754ca155d99b85a81e7f193e1f99b3c30845f354", "b2b1a4806e4c432aaaaab4256a47dddf", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('b2b1a4806e4c432aaaaab4256a47dddf', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("0db4515cb04a428787194e5cfd8c21e2", "intelligentwriting", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "0db4515cb04a428787194e5cfd8c21e2", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "0db4515cb04a428787194e5cfd8c21e2", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("74416f54653cb6a80ab4a24a3e2245f23ae27d55041fbd19177b4d16198e6a8d", "0db4515cb04a428787194e5cfd8c21e2", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('0db4515cb04a428787194e5cfd8c21e2', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("a66805e8cab24f2e9fddc366869900a7", "iotviz", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "a66805e8cab24f2e9fddc366869900a7", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "a66805e8cab24f2e9fddc366869900a7", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("f46c26e146f2c90be48fa7d4a88a939ddf6c50e16d90af42a31ccea53c69a33c", "a66805e8cab24f2e9fddc366869900a7", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('a66805e8cab24f2e9fddc366869900a7', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("c9a17dd151564bfd82b7eeeab21349ed", "iprojectinvoke", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "c9a17dd151564bfd82b7eeeab21349ed", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "c9a17dd151564bfd82b7eeeab21349ed", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("2aba4eb710e027d962ad120d136faf45d95d6c8e2aeb0ae8fc282cb803cec2da", "c9a17dd151564bfd82b7eeeab21349ed", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('c9a17dd151564bfd82b7eeeab21349ed', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("826729390e154def9a6b5a3e394c7869", "lcc", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "826729390e154def9a6b5a3e394c7869", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "826729390e154def9a6b5a3e394c7869", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("2caf4ce7c1c6b044eb07d9bee744ee4a1dfe43f7ec982ac227b7f0772801c99f", "826729390e154def9a6b5a3e394c7869", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('826729390e154def9a6b5a3e394c7869', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("5eedf465b319480f90602c2576d1b5a0", "lcps", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "5eedf465b319480f90602c2576d1b5a0", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "5eedf465b319480f90602c2576d1b5a0", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("9a0d43b5fa1f8d366eb79cb76b68d177e31104ee39177253c2046a20e5c0b972", "5eedf465b319480f90602c2576d1b5a0", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('5eedf465b319480f90602c2576d1b5a0', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("6d0101bc6da44674a60f808276fa4bc8", "ld", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "6d0101bc6da44674a60f808276fa4bc8", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "6d0101bc6da44674a60f808276fa4bc8", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("cdfa95e042f30077783d6a4843b80f886783a7b8873bc1a5f782a6730a72e50f", "6d0101bc6da44674a60f808276fa4bc8", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('6d0101bc6da44674a60f808276fa4bc8', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("c05f998781794fcc8ae1552da15adc4a", "lightsever", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "c05f998781794fcc8ae1552da15adc4a", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "c05f998781794fcc8ae1552da15adc4a", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("bc8c957f6e06b11e9b4ef9348768efb477e50977cd6825f5e74e2d5a362f2bec", "c05f998781794fcc8ae1552da15adc4a", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('c05f998781794fcc8ae1552da15adc4a', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("5fcfcf6d52d041149e088264aae89049", "logic_gaiadb", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "5fcfcf6d52d041149e088264aae89049", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "5fcfcf6d52d041149e088264aae89049", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("d31d58785c0ee5d53e9d2f95556f6e5d03d881a52042b3474d0404c7e4c9f610", "5fcfcf6d52d041149e088264aae89049", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('5fcfcf6d52d041149e088264aae89049', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("67c55baa711347d593cfcf70d2fc6485", "machinecost", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "67c55baa711347d593cfcf70d2fc6485", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "67c55baa711347d593cfcf70d2fc6485", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("3f3dfb2f55e6533d4023a0cffb9ab57b8a196222e411cf9e47c8aacdd3b87f0f", "67c55baa711347d593cfcf70d2fc6485", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('67c55baa711347d593cfcf70d2fc6485', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("911d4822bb37475e98bc4b7d0ede0831", "mkt_bd", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "911d4822bb37475e98bc4b7d0ede0831", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "911d4822bb37475e98bc4b7d0ede0831", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("0c96b6dbb2db5a7410d7b8e6610814fc9c14f75c2c0b357beb6a5f6b90e32bf8", "911d4822bb37475e98bc4b7d0ede0831", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('911d4822bb37475e98bc4b7d0ede0831', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("ed767c6cf4f04c209607123f7ff7a8a6", "mlp", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "ed767c6cf4f04c209607123f7ff7a8a6", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "ed767c6cf4f04c209607123f7ff7a8a6", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("401d335fea7ba035916525eda54d4b708045cc04c8d5bfe1ea08129fcfbd4494", "ed767c6cf4f04c209607123f7ff7a8a6", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('ed767c6cf4f04c209607123f7ff7a8a6', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("82196be86f3844e7b16df76105e099d5", "nfv", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "82196be86f3844e7b16df76105e099d5", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "82196be86f3844e7b16df76105e099d5", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("5df88f995167bcda345f5cb76c69205d2fb55fba8940fbe6e293b618c7f08da7", "82196be86f3844e7b16df76105e099d5", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('82196be86f3844e7b16df76105e099d5', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("e8831cefcf204afc8f506d65e82e7b74", "ocr_deleted", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "e8831cefcf204afc8f506d65e82e7b74", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "e8831cefcf204afc8f506d65e82e7b74", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("af23373eb74bb4d808bb45d1dc0015c763ada4924a87d08775329c95f6679819", "e8831cefcf204afc8f506d65e82e7b74", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('e8831cefcf204afc8f506d65e82e7b74', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("558cb8e17ca34777a3b615d65c000d93", "oos", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "558cb8e17ca34777a3b615d65c000d93", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "558cb8e17ca34777a3b615d65c000d93", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("a7cb89b793ef6ba49d25535836fa5a5f4d17ed961eb7a69b568891047830341b", "558cb8e17ca34777a3b615d65c000d93", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('558cb8e17ca34777a3b615d65c000d93', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("90c71e1d43e34f21b25790d585e725cd", "ota", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "90c71e1d43e34f21b25790d585e725cd", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "90c71e1d43e34f21b25790d585e725cd", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("67a511709aad47ce8a924217428cec3f94f3c6d5904c048a83b961eb5e863d02", "90c71e1d43e34f21b25790d585e725cd", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('90c71e1d43e34f21b25790d585e725cd', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("e9a459c8250f4ec6b458c9b6eea4871b", "otc", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "e9a459c8250f4ec6b458c9b6eea4871b", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "e9a459c8250f4ec6b458c9b6eea4871b", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("87ace9e13424a130f966532a1c2c168505d8b369f3c8a523cd3e5a7894fe9554", "e9a459c8250f4ec6b458c9b6eea4871b", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('e9a459c8250f4ec6b458c9b6eea4871b', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("7af44137f719458ab259701391d4b6a3", "paloprivate", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "7af44137f719458ab259701391d4b6a3", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "7af44137f719458ab259701391d4b6a3", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("eaaad079dd2fc89ab4c25c3d1264a3f892041304f053ffd0438aa1f04170b48e", "7af44137f719458ab259701391d4b6a3", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('7af44137f719458ab259701391d4b6a3', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("4af3bd677bf24d678e28376b6e9c01c9", "pase", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "4af3bd677bf24d678e28376b6e9c01c9", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "4af3bd677bf24d678e28376b6e9c01c9", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("8a3d1e22040f096c845c0c0a98e21d535c342ffc208d77ba4c23d7d166f27ec3", "4af3bd677bf24d678e28376b6e9c01c9", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('4af3bd677bf24d678e28376b6e9c01c9', 'e274118227b24725b57a3d089a39c3f8', NULL);

INSERT INTO user (`id`, `name`, `extra`, `password`, `enabled`, `domain_id`, `default_project_id`, `provider`,`public_id`, `create_time`, `subuser`)
VALUES ("a906e8305164402297bb847b2a94cb18", "quota_center", "{\"enabled\":true,\"status\":\"ACTIVE\",\"password_last_reset_time\":\"2022-10-26T09:02:11.275Z\"}",
        "$6$rounds=40000$3N.lp2Cw$W83kthUThQZytSt6VFecmppyNCs.i4uJnpi75pCVum61RMKTOig/urRES0RP2OqiN.ZKzIkpQpjO5mGV/MM8F/",
        "1", "default", "0557fd0d0d244cf7801dba4cadf214c5", "", "", "2022-01-12 17:18:11", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserProject", "a906e8305164402297bb847b2a94cb18", "0557fd0d0d244cf7801dba4cadf214c5",
        "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO assignment (`type`, `actor_id`, `target_id`, `role_id`, `inherited`)
VALUES ("UserDomain", "a906e8305164402297bb847b2a94cb18", "default", "7151762f590a47f89213d5d8677a9b63", "0");

INSERT INTO credential  (id, user_id, project_id, `blob`, `type`, `extra`, modify_time)
VALUES ("28f48f75af92e9bb47b2d4130ca54b9868b1c99941cfddeb95610619e1b8fa77", "a906e8305164402297bb847b2a94cb18", "0557fd0d0d244cf7801dba4cadf214c5",
    "\"{\\\"trust_id\\\":null,\\\"access\\\":\\\"3467ce3f22cc479ea031b85adf1c6d28\\\",\\\"secret_encrypted\\\":\\\"0fe7a268b7479d19858252c2e59ea57ba8d5ad68e25afb807ca77ddf7af109651a88ca1fd9e3fcbc2eab24735d20947e288461ad7c9dbd911229e7db2f2bf3be\\\"}\"",
     "ec2", "{}", "2022-01-12 17:45:47");

INSERT INTO `active_service`
VALUES ('a906e8305164402297bb847b2a94cb18', 'e274118227b24725b57a3d089a39c3f8', NULL);