SET names utf8;

DELETE FROM `product_mapping`;

INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'proxy', '2018-03-16 09:16:27', '2018-03-16 09:16:27');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'admin', '2018-03-16 09:16:35', '2018-03-16 09:16:35');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_iam', '2018-03-16 09:23:28', '2018-03-16 09:23:28');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'agent', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'apigateway', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'billing', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'crm', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'finance', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'messages', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'order_execute', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'order_executor', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'order_facade', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'order_renew', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'osp-product', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'portal', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'service_config', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'service_product', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'service_register', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'service_scheduler', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'sts', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'tag', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'ticket', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'user_config', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'yunying', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'zone', '2018-03-16 09:31:37', '2018-03-16 09:31:37');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_agent', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_billing', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_finance', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_frontend', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_home', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_log', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_login', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_qualify', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_tag', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_ticket', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_zone', '2018-03-16 09:35:14', '2018-03-16 09:35:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'mc', '2018-03-16 09:36:49', '2018-03-16 09:36:49');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'risk', '2018-03-16 09:37:20', '2018-03-16 09:37:20');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('AR', 'ar_sdk', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('BATCH', 'batchcompute', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'beian', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('KAFKA', 'bpsadminserver', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('KAFKA', 'bpsproxy', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'cinder', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_beian', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_mc', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'glance', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'logical', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('BLB', 'logical_blb', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('EIP', 'logical_eip', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('BBC', 'logic_bbc', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('BCC', 'logic_bcc', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'logic_image', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('PCDN', 'logic_pcdn', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('RDS', 'logic_rds', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('SCS', 'logic_scs', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'logic_snapshot', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'logic_volume', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'log_trail', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'neutron', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'nova', '2018-03-16 10:07:50', '2018-03-16 10:07:50');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'BSS', '2018-03-16 10:10:10', '2018-03-16 18:15:02');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_bss', '2018-03-16 10:13:18', '2018-03-16 10:13:18');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('NAT', 'network_gateways', '2018-11-08 09:14:53', '2018-11-08 09:14:53');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'bcc-proxy', '2018-11-09 08:02:35', '2018-11-09 08:02:35');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'logic_home', '2018-11-14 07:15:26', '2018-11-14 07:15:26');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'bch', '2018-11-16 05:42:33', '2018-11-16 05:42:33');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('MONGODB', 'logic_mongodb', '2018-11-20 12:53:00', '2018-11-20 12:53:00');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('NETWORK', 'logical_vpc', '2018-11-21 10:10:33', '2018-11-21 10:10:33');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('BCC', 'console_bcc', '2018-12-04 02:08:44', '2018-12-04 02:08:44');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_bcm', '2018-12-06 08:57:52', '2018-12-06 08:57:52');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_waf', '2018-12-10 07:50:44', '2018-12-10 07:50:44');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'logic_recycle', '2019-01-04 03:49:52', '2019-01-04 03:49:52');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_cdn', '2019-02-20 06:14:42', '2019-02-20 06:14:42');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('CDN', 'logic-cdn', '2019-02-20 06:32:22', '2019-02-20 06:32:22');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_scs', '2019-03-15 08:46:52', '2019-03-15 08:46:52');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('BTS', 'bts_bench', '2019-03-27 02:43:36', '2019-03-27 02:43:36');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('CNAP', 'bap', '2019-05-16 09:37:21', '2019-05-16 09:37:21');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_bos', '2019-05-20 08:12:21', '2019-05-20 08:12:21');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('BOS', 'console_bos', '2019-05-21 05:25:09', '2019-05-21 05:25:09');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('BMR', 'console_bmr', '2019-05-30 02:20:16', '2019-05-30 02:20:16');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'console_iot', '2019-07-24 09:21:11', '2019-07-24 09:21:11');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('BCC', 'bcc_overview', '2019-12-26 05:09:35', '2019-12-26 05:09:35');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'log_trace', '2020-01-16 09:04:29', '2020-01-16 09:04:29');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('INTERNAL', 'test', '2020-01-17 09:37:14', '2020-01-17 09:37:14');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('BCT', 'cloud_trail', '2020-02-19 05:39:05', '2020-02-19 05:39:05');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('DNS', 'console_dns', '2020-02-27 08:08:08', '2020-02-27 08:08:08');
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('RABBITMQ', 'logic_rabbitmq', now(), now());
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('MQ', 'logical-mq', now(), now());
INSERT INTO `product_mapping` (`product`, `service`, `create_time`, `update_time`) VALUES ('DRDS', 'logic_drds', now(), now());