SET names utf8;

DELETE FROM `quota_rule`;

INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('edd3c288-b0da-48eb-926f-1a6b2f6b2e45', 'cloud_trail', 86400, 100, 'FIXED', '2019-07-25 06:33:58', '2019-07-25 06:36:07', 'ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('cf85e6a8-ebf1-4e56-b97a-595e31c458df', 'cloud_trail', 86400, 10, 'FIXED', '2019-07-25 06:35:12','2019-07-25 06:35:12', 'ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('fb9bba60c7a0487852e4e2a6eddf470a', 'cloud_trail', 86400, 1000, 'FIXED', '2019-07-29 09:11:35','2019-07-29 09:11:35', 'ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('4cc49aa525e06cc244b95ae658f33f59', 'iam', 0, 1000, 'FIXED', '2019-08-14 00:43:43', '2019-08-14 08:43:43','ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('b3642b9e713d0c3b75bf5ded21dd6ab8', 'iam', 0, 500, 'FIXED', '2019-08-14 00:45:24', '2019-08-14 08:45:24', 'ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('926f94df68a04a33d4b5361decbc1340', 'iam', 1111, 100, 'FIXED', '2019-09-06 05:53:49', '2019-09-06 13:53:49', 'ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('16b687fd400634a547e8405d9a8775c8', 'iam', 1111, 50, 'FIXED', '2019-09-06 05:54:13', '2019-09-06 13:54:13', 'ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('68e7e347461fe5af6fa7804c2984c7cc', 'risk', 3600, 4, 'SLIDING', '2019-11-21 03:24:04', '2019-11-21 14:38:56','ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('1c9cad0629104864b8c81d43f06c6c8f', 'risk', 3600, 4, 'SLIDING', '2019-11-21 03:24:22', '2019-11-21 14:38:59', 'ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('13bbdcd75489dc5a6ce9c4b6e0f40efd', 'risk', 3600, 4, 'SLIDING', '2019-11-21 03:24:27', '2019-11-21 14:39:02', 'ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('2d901341d0be38826256b5ce991c47f8', 'proxy', 0, 0, 'PERMANENT', '2020-09-17 07:39:49', '2020-09-17 15:39:49', 'ACTIVE');
INSERT INTO `quota_rule` (`id`, `service`, `duration`, `quota`, `type`, `create_time`, `update_time`, `status`) VALUES ('e5e76f2d0e2aa16894dc8a8bddbef750', 'iam', 0, 0, 'PERMANENT', '2020-09-17 12:39:04', '2020-09-17 20:39:04', 'ACTIVE');


DELETE FROM `quota_rule_term`;

INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('edd3c288-b0da-48eb-926f-1a6b2f6b2e45', 'accountId', '-', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('edd3c288-b0da-48eb-926f-1a6b2f6b2e45', 'success', '*', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('edd3c288-b0da-48eb-926f-1a6b2f6b2e45', 'errmsg', '*', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('cf85e6a8-ebf1-4e56-b97a-595e31c458df', 'accountId', '-', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('cf85e6a8-ebf1-4e56-b97a-595e31c458df', 'success', 'false', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('cf85e6a8-ebf1-4e56-b97a-595e31c458df', 'errmsg', '-', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('94816613d58e9c0a5816cec4d31094b8', 'accountId', '-', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('94816613d58e9c0a5816cec4d31094b8', 'success', '*', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('94816613d58e9c0a5816cec4d31094b8', 'errmsg', '*', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('fb9bba60c7a0487852e4e2a6eddf470a', 'accountId', 'f1f62daf2ea34a7ba67b8aae9e398ef3', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('fb9bba60c7a0487852e4e2a6eddf470a', 'success', '*', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('fb9bba60c7a0487852e4e2a6eddf470a', 'errmsg', '*', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('4cc49aa525e06cc244b95ae658f33f59', 'service', 'bch', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('b3642b9e713d0c3b75bf5ded21dd6ab8', 'service', 'bms', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('926f94df68a04a33d4b5361decbc1340', 'service', 'hardRejectCnt', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('16b687fd400634a547e8405d9a8775c8', 'service', 'softRejectCnt', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('68e7e347461fe5af6fa7804c2984c7cc', 'accountId', '-', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('68e7e347461fe5af6fa7804c2984c7cc', 'passwordRetry', 'false', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('68e7e347461fe5af6fa7804c2984c7cc', 'userId', '-', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('1c9cad0629104864b8c81d43f06c6c8f', 'device', '-', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('1c9cad0629104864b8c81d43f06c6c8f', 'loginRetry', 'false', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('1c9cad0629104864b8c81d43f06c6c8f', 'userId', '-', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('13bbdcd75489dc5a6ce9c4b6e0f40efd', 'ip', '-', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('13bbdcd75489dc5a6ce9c4b6e0f40efd', 'loginRetry', 'false', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('13bbdcd75489dc5a6ce9c4b6e0f40efd', 'userId', '-', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('2d901341d0be38826256b5ce991c47f8', 'globalMfaConfig', 'globalMfaConfig', '0', '0');
INSERT INTO `quota_rule_term` (`rule_id`, `term_key`, `term_value`, `term_min`, `term_max`) VALUES ('e5e76f2d0e2aa16894dc8a8bddbef750', 'globalMfaConfig', 'globalMfaConfig', '0', '0');


DELETE FROM `quota_term_info`;

INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('iam2', 'account', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('iam2', 'tag', 'Int', 1);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('cloud_trail', 'ip', 'Ip', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('cloud_trail', 'ipRange', 'Ip', 1);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('cloud_trail', 'age', 'Int', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('cloud_trail', 'account', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('cloud_trail', 'tag', 'Int', 1);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('cloud_trail', 'accountId', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('cloud_trail', 'errmsg', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('cloud_trail', 'success', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('iam', 'service', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('risk', 'accountId', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('risk', 'passwordRetry', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('risk', 'ip', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('risk', 'loginRetry', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('risk', 'device', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('risk', 'userId', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('proxy', 'globalMfaConfig', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('iam', 'globalMfaConfig', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('iam', 'accountId', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('iam', 'userManagementRule', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('console_iam', 'userManagementRule', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('cloud_trail', 'akskTag', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('iam', 'organizationId', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('iam', 'organizationRule', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('console_iam', 'accountId', 'String', 0);
INSERT INTO `quota_term_info` (`service`, `term_key`, `term_type`, `support_range`) VALUES ('console_iam', 'accountId', 'String', 0);