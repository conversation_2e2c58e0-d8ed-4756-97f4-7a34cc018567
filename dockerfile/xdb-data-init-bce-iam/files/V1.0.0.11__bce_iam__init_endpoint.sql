set names utf8;

DELETE FROM `endpoint`;

{{- range $i, $region := $.Values.appspace.charts.global.regions }}
{{- $curRegion :=$region.region }}
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '48b949bbcebf4417a2785917fe099068', 'admin', '{{ $region.region}}',
        '6749bb1641094f928ab6590f37ad1e17', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyCinder.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyCinder.port }}/v1/%(tenant_id)s', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), 'f18d4bbe68174fb2a1acac95bd6e2bb5', 'admin', '{{ $region.region}}',
        '4628515070d147b999ced100171e2366', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyCinder.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyCinder.port }}/v2/%(tenant_id)s', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), 'f18d4bbe68174fb2a1acac95bd6e2bb5', 'internal', '{{ $region.region}}',
        '4628515070d147b999ced100171e2366', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyCinder.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyCinder.port }}/v2/%(tenant_id)s', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '48b949bbcebf4417a2785917fe099068', 'internal', '{{ $region.region}}',
        '6749bb1641094f928ab6590f37ad1e17', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyCinder.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyCinder.port }}/v1/%(tenant_id)s', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), 'f18d4bbe68174fb2a1acac95bd6e2bb5', 'public', '{{ $region.region}}',
        '4628515070d147b999ced100171e2366', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyCinder.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyCinder.port }}/v2/%(tenant_id)s', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '48b949bbcebf4417a2785917fe099068', 'public', '{{ $region.region}}',
        '6749bb1641094f928ab6590f37ad1e17', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyCinder.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyCinder.port }}/v1/%(tenant_id)s', '{}', 1);

INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '680f4cc2a48b4442ac1a6100e557dedb', 'admin', '{{ $region.region}}',
        '6506eef2b99e40bdbbd2b52bd4470a95', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyLogical.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyLogical.port }}/v1', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '680f4cc2a48b4442ac1a6100e557dedb', 'internal', '{{ $region.region}}',
        '6506eef2b99e40bdbbd2b52bd4470a95', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyLogical.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyLogical.port }}/v1', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '680f4cc2a48b4442ac1a6100e557dedb', 'public', '{{ $region.region}}',
        '6506eef2b99e40bdbbd2b52bd4470a95', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyLogical.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyLogical.port }}/v1', '{}', 1);

INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '304a0905e6614b73989d8f903bb4f64b', 'admin', '{{ $region.region}}',
        'e6fb534810204c089116a5636856d152', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyNeutron.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyNeutron.port }}', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '304a0905e6614b73989d8f903bb4f64b', 'internal', '{{ $region.region}}',
        'e6fb534810204c089116a5636856d152', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyNeutron.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyNeutron.port }}', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '304a0905e6614b73989d8f903bb4f64b', 'public', '{{ $region.region}}',
        'e6fb534810204c089116a5636856d152', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyNeutron.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyNeutron.port }}', '{}', 1);

INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '695aa85fe662457f9760c75f80c00b6c', 'admin', '{{ $region.region}}',
        '9466864faeb84b47ba6f7264019f787f', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxy.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxy.port }}', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '695aa85fe662457f9760c75f80c00b6c', 'internal', '{{ $region.region}}',
        '9466864faeb84b47ba6f7264019f787f', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxy.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxy.port }}', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '695aa85fe662457f9760c75f80c00b6c', 'public', '{{ $region.region}}',
        '9466864faeb84b47ba6f7264019f787f', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxy.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxy.port }}', '{}', 1);

INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '1be6a7979d8a4717af0390510525754d', 'admin', '{{ $region.region}}',
        '7eab524f47d54845992a20254c5353cb', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyNova.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyNova.port }}/v2/%(tenant_id)s', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '1be6a7979d8a4717af0390510525754d', 'internal', '{{ $region.region}}',
        '7eab524f47d54845992a20254c5353cb', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyNova.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyNova.port }}/v2/%(tenant_id)s', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '1be6a7979d8a4717af0390510525754d', 'public', '{{ $region.region}}',
        '7eab524f47d54845992a20254c5353cb', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bccproxyNova.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bccproxyNova.port }}/v2/%(tenant_id)s', '{}', 1);

INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '7dbe1e20863b4af2bf9d507f425225f4', 'admin', '{{ $region.region}}',
        'cb24bcb3cc1740af9ac41361c2843e0d', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.glanceApi.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.glanceApi.port }}', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '7dbe1e20863b4af2bf9d507f425225f4', 'internal', '{{ $region.region}}',
        'cb24bcb3cc1740af9ac41361c2843e0d', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.glanceApi.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.glanceApi.port }}', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '7dbe1e20863b4af2bf9d507f425225f4', 'public', '{{ $region.region}}',
        'cb24bcb3cc1740af9ac41361c2843e0d', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.glanceApi.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.glanceApi.port }}', '{}', 1);

INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), 'c4892f10a2b6464db09c07a7ce5cd715', 'admin', '{{ $region.region}}',
        '743aee2b567b41a39a0bfdd5af5f56af', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v2.0', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), 'c4892f10a2b6464db09c07a7ce5cd715', 'internal', '{{ $region.region}}',
        '743aee2b567b41a39a0bfdd5af5f56af', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), 'c4892f10a2b6464db09c07a7ce5cd715', 'public', '{{ $region.region}}',
        '743aee2b567b41a39a0bfdd5af5f56af', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.iam.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.iam.port }}/v3', '{}', 1);

INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '56436e5fb4f744a5843669caa7ff74b2', 'admin', '{{ $region.region}}',
        'c3bfb4e7848c4b6b9ad4b54f613667cc', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.blbMeta.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.blbMeta.port }}/json-api/v2', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '56436e5fb4f744a5843669caa7ff74b2', 'internal', '{{ $region.region}}',
        'c3bfb4e7848c4b6b9ad4b54f613667cc', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.blbMeta.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.blbMeta.port }}/json-api/v2', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), '56436e5fb4f744a5843669caa7ff74b2', 'public', '{{ $region.region}}',
        'c3bfb4e7848c4b6b9ad4b54f613667cc', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.blbMeta.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.blbMeta.port }}/json-api/v2', '{}', 1);

INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), 'cdee6c79aa354daeb959a727a19b301f', 'admin', '{{ $region.region}}',
        'bec28fb4e98f40aba4471e45a29d6bbb', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.hosteye.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.hosteye.port }}', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), 'cdee6c79aa354daeb959a727a19b301f', 'internal', '{{ $region.region}}',
        'bec28fb4e98f40aba4471e45a29d6bbb', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.hosteye.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.hosteye.port }}', '{}', 1);
INSERT INTO `endpoint` (`id`,`legacy_endpoint_id`,`interface`,`region`,`service_id`,`url`,`extra`,`enabled`)
VALUES (REPLACE(UUID(), '-', ''), 'cdee6c79aa354daeb959a727a19b301f', 'public', '{{ $region.region}}',
        'bec28fb4e98f40aba4471e45a29d6bbb', 'http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.hosteye.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.hosteye.port }}', '{}', 1);

{{- end }}