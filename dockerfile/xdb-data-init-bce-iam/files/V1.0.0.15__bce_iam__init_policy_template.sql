DELETE FROM `policy_template`;

SET names utf8;
-- region service
INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('077fd5757f7f4ada9cd8bf743a92a7d3', 'default', 'eip_bp', 'EIP_BP', 'generator',
'{"regions": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], "resources": [{"documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/EIP/index.html", "resourceInstanceType": "eipBp", "permissionItems": [{"permissionName": "READ", "irrelevantWithResource": false, "permissionNameZh": "只读操作", "permissionMandatory": true}, {"permissionName": "OPERATE", "irrelevantWithResource": false, "permissionNameZh": "运维操作", "permissionMandatory": false}, {"permissionName": "FULL_CONTROL", "irrelevantWithResource": false, "permissionNameZh": "管理操作", "permissionMandatory": false}], "returnFields": [{"identifier": true, "displayName": "实例ID", "supportFilter": false, "key": "shortId", "display": true}, {"identifier": false, "displayName": "实例名称°", "supportFilter": false, "key": "name", "display": true},{"identifier": false, "displayName": "地域", "supportFilter": false, "key": "eip", "display": true}, {"identifier": false, "displayName": "地域", "supportFilter": false, "key": "region", "display": false}],
"endpoints":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri": "/api/eipbp/listeipBp", "region": "{{ $region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.eiplogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.eiplogicInternal.port }}", "listParameters": {}}{{- if $i -}},{{- end }}{{- end }}], "permissionSelection": "checkbox", "resourceLevel": 1, "permissionType": "EIP带宽包EIP_BP"}]}',
'null', '2018-11-23 07:42:37', '2019-08-21 01:01:36', '2019-08-21 01:01:36', '{}');

INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('0f21ae362bc24b6083f7e33d629da99c', 'default', 'waf', 'WAF', 'generator',
'{"regions": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], "resources": [{"documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/WAF/index.html", "resourceInstanceType": "instance", "permissionItems": [{"permissionName": "READ", "irrelevantWithResource": false, "permissionNameZh": "只读操作", "permissionMandatory": false}, {"permissionName": "OPERATE", "irrelevantWithResource": false, "permissionNameZh": "运维操作", "permissionMandatory": false}], "returnFields": [{"identifier": true, "displayName": "实例id", "supportFilter": false, "key": "id", "display": true}, {"identifier": false, "displayName": "实例名称", "supportFilter": false, "key": "name", "display": false}, {"identifier": false, "displayName": "地域", "supportFilter": false, "key": "region", "display": true}, {"identifier": false, "displayName": "实例类型", "supportFilter": false, "key": "instanceType", "display": true}], 
"endpoints": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri": "/json-api/v2/waf/waf_instance_list", "region": "{{ $region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.hosteye.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.hosteye.port }}", "listParameters": {}}{{- end }}],
"permissionSelection": "checkbox", "resourceLevel": 1, "permissionType": "实例类型"}]}',
'null', '2018-11-27 04:25:47', '2019-08-21 01:12:52', '2019-08-21 01:12:52', '{}');

INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('197f0624201a4fc49307d0ab75240d08', 'default', 'mongodb', 'MONGODB', 'generator',
'{"regions": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], "resources": [{"documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/MONGODB/index.html", "resourceInstanceType": "instance", "permissionItems": [{"permissionName": "READ", "irrelevantWithResource": false, "permissionNameZh": "u53eau8bfb", "permissionMandatory": true}, {"permissionName": "OPERATE", "irrelevantWithResource": false, "permissionNameZh": "u8fd0u7ef4", "permissionMandatory": false}, {"permissionName": "FULL_CONTROL", "irrelevantWithResource": false, "permissionNameZh": "u7ba1u7406", "permissionMandatory": false}], "returnFields": [{"identifier": true, "displayName": "u5b9eu4f8bID", "supportFilter": false, "key": "dbInstanceId", "display": true}, {"identifier": false, "displayName": "u5b9eu4f8bu540du79f0", "supportFilter": false, "key": "dbInstanceName", "display": true}, {"identifier": false, "displayName": "u5b58u50a8u5f15u64ce", "supportFilter": false, "key": "storageEngine", "display": true}],
"endpoints": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri": "/v1/instance", "region": "{{ $region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicMongodbInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicMongodbInternal.port }}", "listParameters": {"manner": "page"}}{{- end }}], "permissionSelection": "checkbox", "resourceLevel": 1, "permissionType": "u5b9eu4f8b"}]}',
'null', '2018-09-05 05:12:32', '2019-08-21 01:22:43', '2019-08-21 01:22:43', '{}');

INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('1f1d2de1808a4db8b1090b633ffa037b', 'default', 'vpn', 'VPN', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/EIP/index.html\", \"resourceInstanceType\": \"vpn\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \" 只读管理Vpn的权限\", \"permissionMandatory\": true}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \" 运维管理Vpn的权限\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \" 完全控制管理Vpn的权限\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": false, \"displayName\": \"实例名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": true, \"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"vpnId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"地域\", \"supportFilter\": false, \"key\": \"region\", \"display\": false}],\"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/list_vpns_for_iam\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.vpclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.vpclogicInternal.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"vpn\"}]}',
'null', '2018-11-28 03:18:56', '2019-08-21 01:10:33', '2019-08-21 01:10:33', '{}');

INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('2516dbe6228b4194982afcdb4630d37d', 'default', 'network', 'NETWORK', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}],\"resources\":[{\"resourceLevel\":1,\"permissionSelection\":\"checkbox\",\"permissionType\":\"vpc\",\"documentLink\":\"http://portal{{.Values.appspace.charts.global.domain}}/doc/VPC/index.html\",\"resourceInstanceType\":\"vpc\",\"permissionItems\":[{\"permissionName\":\"VPC_READ\",\"permissionNameZh\":\"只读VPC权限\",\"permissionMandatory\":true,\"irrelevantWithResource\":false},{\"permissionName\":\"VPC_OPERATE\",\"permissionNameZh\":\"运维VPC权限\",\"permissionMandatory\":false,\"irrelevantWithResource\":false},{\"permissionName\":\"FULL_CONTROL\",\"permissionNameZh\":\"管理VPC权限\",\"permissionMandatory\":false,\"irrelevantWithResource\":false}],\"endpoints\":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/listVpcForIam\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.vpclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.vpclogicInternal.port }}\", \"listParameters\": {}}{{- end }}],\"returnFields\":[{\"key\":\"name\",\"displayName\":\"实例名称\",\"supportFilter\":false,\"identifier\":false,\"display\":true},{\"key\":\"shortId\",\"displayName\":\"实例ID\",\"supportFilter\":false,\"identifier\":true,\"display\":true},{\"key\":\"desc\",\"displayName\":\"描述\",\"supportFilter\":false,\"identifier\":false,\"display\":true},{\"key\":\"region\",\"displayName\":\"地域\",\"supportFilter\":false,\"identifier\":false,\"display\":false}]},{\"resourceLevel\":1,\"permissionSelection\":\"checkbox\",\"permissionType\":\"subnet\",\"documentLink\":\"http://portal{{.Values.appspace.charts.global.domain}}/doc/VPC/index.html\",\"resourceInstanceType\":\"subnet\",\"permissionItems\":[{\"permissionName\":\"SUBNET_READ\",\"permissionNameZh\":\"只读SUBNET权限\",\"permissionMandatory\":true,\"irrelevantWithResource\":false},{\"permissionName\":\"SUBNET_OPERATE\",\"permissionNameZh\":\"运维SUBNET权限\",\"permissionMandatory\":false,\"irrelevantWithResource\":false},{\"permissionName\":\"FULL_CONTROL\",\"permissionNameZh\":\"管理SUBNET权限\",\"permissionMandatory\":false,\"irrelevantWithResource\":false}],\"endpoints\":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/listSubnetForIam\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.vpclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.vpclogicInternal.port }}\", \"listParameters\": {}}{{- end }}],\"returnFields\":[{\"key\":\"name\",\"displayName\":\"实例名称\",\"supportFilter\":false,\"identifier\":false,\"display\":true},{\"key\":\"shortId\",\"displayName\":\"实例ID\",\"supportFilter\":false,\"identifier\":true,\"display\":true},{\"key\":\"desc\",\"displayName\":\"描述\",\"supportFilter\":false,\"identifier\":false,\"display\":true},{\"key\":\"region\",\"displayName\":\"地域\",\"supportFilter\":false,\"identifier\":false,\"display\":false}]},{\"resourceLevel\":1,\"permissionSelection\":\"checkbox\",\"permissionType\":\"securityGroup\",\"documentLink\":\"http://portal{{.Values.appspace.charts.global.domain}}/doc/VPC/index.html\",\"resourceInstanceType\":\"securityGroup\",\"permissionItems\":[{\"permissionName\":\"SECURITYGROUP_READ\",\"permissionNameZh\":\"只读SECURITYGROUP权限\",\"permissionMandatory\":true,\"irrelevantWithResource\":false},{\"permissionName\":\"SECURITYGROUP_OPERATE\",\"permissionNameZh\":\"运维SECURITYGROUP权限\",\"permissionMandatory\":false,\"irrelevantWithResource\":false},{\"permissionName\":\"FULL_CONTROL\",\"permissionNameZh\":\"管理SECURITYGROUP权限\",\"permissionMandatory\":false,\"irrelevantWithResource\":false}],\"endpoints\":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/api/logical/network/v1/security/listSecurityGroupForIam\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.vpclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.vpclogicInternal.port }}\", \"listParameters\": {}}{{- end }}],\"returnFields\":[{\"key\":\"name\",\"displayName\":\"实例名称\",\"supportFilter\":false,\"identifier\":false,\"display\":true},{\"key\":\"shortId\",\"displayName\":\"实例ID\",\"supportFilter\":false,\"identifier\":true,\"display\":true},{\"key\":\"desc\",\"displayName\":\"描述\",\"supportFilter\":false,\"identifier\":false,\"display\":true},{\"key\":\"region\",\"displayName\":\"地域\",\"supportFilter\":false,\"identifier\":false,\"display\":false}]},{\"resourceLevel\":1,\"permissionSelection\":\"checkbox\",\"permissionType\":\"route\",\"documentLink\":\"http://portal{{.Values.appspace.charts.global.domain}}/doc/VPC/index.html\",\"resourceInstanceType\":\"route\",\"permissionItems\":[{\"permissionName\":\"ROUTE_READ\",\"permissionNameZh\":\"只读ROUTE权限\",\"permissionMandatory\":true,\"irrelevantWithResource\":false},{\"permissionName\":\"ROUTE_OPERATE\",\"permissionNameZh\":\"运维ROUTE权限\",\"permissionMandatory\":false,\"irrelevantWithResource\":false},{\"permissionName\":\"FULL_CONTROL\",\"permissionNameZh\":\"管理ROUTE权限\",\"permissionMandatory\":false,\"irrelevantWithResource\":false}],\"endpoints\":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/listRouteForIam\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.vpclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.vpclogicInternal.port }}\", \"listParameters\": {}}{{- end }}],\"returnFields\":[{\"key\":\"name\",\"displayName\":\"实例名称\",\"supportFilter\":false,\"identifier\":false,\"display\":true},{\"key\":\"shortId\",\"displayName\":\"实例ID\",\"supportFilter\":false,\"identifier\":true,\"display\":true},{\"key\":\"desc\",\"displayName\":\"VPC ID\",\"supportFilter\":false,\"identifier\":false,\"display\":true},{\"key\":\"region\",\"displayName\":\"地域\",\"supportFilter\":false,\"identifier\":false,\"display\":false}]},{\"resourceLevel\":1,\"permissionSelection\":\"checkbox\",\"permissionType\":\"acl\",\"documentLink\":\"http://portal{{.Values.appspace.charts.global.domain}}/doc/VPC/index.html\",\"resourceInstanceType\":\"acl\",\"permissionItems\":[{\"permissionName\":\"ACL_READ\",\"permissionNameZh\":\"只读ACL权限\",\"permissionMandatory\":true,\"irrelevantWithResource\":false},{\"permissionName\":\"ACL_OPERATE\",\"permissionNameZh\":\"运维ACL权限\",\"permissionMandatory\":false,\"irrelevantWithResource\":false},{\"permissionName\":\"FULL_CONTROL\",\"permissionNameZh\":\"管理ACL权限\",\"permissionMandatory\":false,\"irrelevantWithResource\":false}],\"endpoints\":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/acls\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.vpclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.vpclogicInternal.port }}\", \"listParameters\": {}}{{- end }}],\"returnFields\":[{\"key\":\"vpcId\",\"displayName\":\"实例ID\",\"supportFilter\":false,\"identifier\":true,\"display\":true},{\"key\":\"vpcName\",\"displayName\":\"实例名称\",\"supportFilter\":false,\"identifier\":false,\"display\":true},{\"key\":\"region\",\"displayName\":\"区域\",\"supportFilter\":false,\"identifier\":false,\"display\":false}]},{\"resourceLevel\":1,\"permissionSelection\":\"checkbox\",\"permissionType\":\"专线网关\",\"documentLink\":\"http://portal{{.Values.appspace.charts.global.domain}}/doc/VPC/index.html\",\"resourceInstanceType\":\"dedicatedconn\",\"permissionItems\":[{\"permissionName\":\"DEDICATEDCONN_READ\",\"permissionNameZh\":\"只读专线网关\",\"permissionMandatory\":true,\"irrelevantWithResource\":false},{\"permissionName\":\"DEDICATEDCONN_OPERATE\",\"permissionNameZh\":\"运维专线网关\",\"permissionMandatory\":false,\"irrelevantWithResource\":false},{\"permissionName\":\"FULL_CONTROL\",\"permissionNameZh\":\"管理专线网关\",\"permissionMandatory\":false,\"irrelevantWithResource\":false}],\"endpoints\":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/dedicatedconns\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.vpclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.vpclogicInternal.port }}\", \"listParameters\": {}}{{- end }}],\"returnFields\":[{\"key\":\"id\",\"displayName\":\"实例ID\",\"supportFilter\":false,\"identifier\":true,\"display\":true},{\"key\":\"name\",\"displayName\":\"实例名称\",\"supportFilter\":false,\"identifier\":false,\"display\":true},{\"key\":\"region\",\"displayName\":\"区域\",\"supportFilter\":false,\"identifier\":false,\"display\":false}]}],\"globalConditionKeys\":[\"time\"]}',
'null', '2018-07-10 06:30:07', '2019-08-21 00:59:28', '2019-08-21 00:59:28', '{}');

INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('27b0b2635734401c8c053752f87c5754', 'default', 'rds', 'RDS', 'jsonEditor', 'null',
'[{\"name\": \"final_test\", \"value\": {\"accessControlList\": [{\"region\": \"*\", \"resource\": [\"*\"], \"effect\": \"Allow\", \"service\": \"bce:rds\", \"permission\": [\"*\"]}]}, \"description\": \"minutel_final_test\"}]',
'2017-08-02 09:44:27', '2017-09-18 09:29:41', '2017-09-18 09:29:41', '{}');
INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('28888eb2e4b34d599b4cf6908070c2fe', 'default', 'rds', 'RDS', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"portal{{.Values.appspace.charts.global.domain}}/doc/RDS/index.html\", \"resourceInstanceType\": \"instance\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读操作\", \"permissionMandatory\": true}, {\"permissionName\": \"WRITE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维操作\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"管理操作\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"instanceShortId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"实例名称\", \"supportFilter\": false, \"key\": \"instanceName\", \"display\": true}, {\"identifier\": false, \"displayName\": \"引擎类型\", \"supportFilter\": false, \"key\": \"engine\", \"display\": true}, {\"identifier\": false, \"displayName\": \"实例名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": false}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/rds/instances/forPolicy\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicRds.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicRds.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"实例\"}], \"globalConditionKeys\": [\"time\"]}',
'null', '2018-06-08 03:13:27', '2019-08-29 06:03:59', '2019-08-29 06:03:59', '{}');

INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('4c26614e9c684276b47c0dd568bfd80a', 'default', 'rds', 'RDS', 'jsonEditor', 'null',
'[{\"name\": \"rds_test1\", \"value\": {\"accessControlList\": [{\"region\": \"*\", \"resource\": [\"*\"], \"effect\": \"Allow\", \"service\": \"bce:rds\", \"permission\": [\"READ\"]}]}, \"description\": \"minutel1\"}]',
'2017-08-02 06:40:41', '2017-08-02 09:42:52', '2017-08-02 09:42:52', '{}');
INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('c546cae549e54667ba8fd43488807213', 'default', 'rds', 'RDS', 'jsonEditor', 'null',
'[{\"name\": \"rds_read_sample\", \"value\": {\"accessControlList\": [{\"region\": \"*\", \"resource\": [\"*\"], \"effect\": \"Allow\", \"service\": \"bce:rds\", \"permission\": [\"READ\"]}]}, \"description\": \"rds read sample\"}, {\"name\": \"rds_full_control_sample\", \"value\": {\"accessControlList\": [{\"region\": \"*\", \"resource\": [\"*\"], \"effect\": \"Allow\", \"service\": \"bce:rds\", \"permission\": [\"FULL_CONTROL\"]}]}, \"description\": \"rds_full_control_sample\"}, {\"name\": \"rds_operate_sample\", \"value\": {\"accessControlList\": [{\"region\": \"*\", \"resource\": [\"*\"], \"effect\": \"Allow\", \"service\": \"bce:rds\", \"permission\": [\"OPERATE\"]}]}, \"description\": \"rds_operate_sample\"}]',
'2017-07-06 03:27:05', '2017-07-06 04:15:23', '2017-07-06 04:15:23', '{}');

INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('3a356979b2214915aa953d4364b45023', 'default', 'blb', 'BLB', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/BLB/index.html\", \"resourceInstanceType\": \"blb\", \"permissionItems\": [{\"permissionName\": \"BLB_READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读权限\", \"permissionMandatory\": true}, {\"permissionName\": \"BLB_OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维权限\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"管理权限\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"id\", \"display\": true}, {\"identifier\": false, \"displayName\": \"实例名称\",\"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"区域\", \"supportFilter\": false, \"key\": \"region\", \"display\": false}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/api/logical/blb/v1/list_blb_for_iam\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.blblogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.blblogicInternal.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"负载均衡BLB\"}]}',
'null', '2018-07-05 07:07:38', '2019-08-21 01:18:22', '2019-08-21 01:18:22', '{}');
INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('8a3b0ce50c9f4688ac988795698f1524', 'default', 'scs', 'SCS', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/SCS/index.html\", \"resourceInstanceType\": \"instance\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读操作\", \"permissionMandatory\": true}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维操作\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"管理权限\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"cacheClusterShowId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"实例名称\", \"supportFilter\": true, \"key\": \"cacheClusterName\", \"display\": true}, {\"identifier\": false, \"displayName\": \"引擎类型\", \"supportFilter\": true, \"key\": \"engine\", \"display\": true}, {\"identifier\": false, \"displayName\": \"实例名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": false}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/scs\", \"region\":\"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.scslogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.scslogicInternal.port }}\", \"listParameters\": {\"manner\": \"page\"}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"应用权限\"}], \"globalConditionKeys\": [\"time\"]}',
'null', '2017-07-19 09:56:28', '2019-08-29 06:04:29', '2019-08-29 06:04:29', '{}');
INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('8a7e4d520cce4ddfafb83d0d8b7cfa27', 'default', 'bos', 'BOS', 'jsonEditor', 'null',
'[{\"name\": \"bos-read-sample\", \"value\": {\"accessControlList\": [{\"region\": \"*\", \"resource\": [\"*\"], \"effect\": \"Allow\", \"service\": \"bce:bos\", \"permission\": [\"READ\"]}]}, \"description\": \"bos-read-sample\"}, {\"name\": \"bos-sample\", \"value\": {\"accessControlList\": [{\"region\": \"*\", \"resource\": [\"*\"], \"effect\": \"Allow\", \"service\": \"bce:bos\", \"permission\": [\"FULL_CONTROL\"]}]}, \"description\": \"bos-sample\"}]',
'2017-06-29 05:11:58', '2018-06-25 07:21:41', '2018-06-25 07:21:41', '{}');
INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('8f717244e45c4e86966f111f41fe2115', 'default', 'cfs', 'CFS', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/CFS/index.html\", \"resourceInstanceType\": \"fs\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读\", \"permissionMandatory\": false}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"系统ID\", \"supportFilter\": false, \"key\": \"fs_uuid\", \"display\": true}, {\"identifier\": false, \"displayName\": \"系统名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"系统类型\", \"supportFilter\": false, \"key\": \"type\", \"display\": true}, {\"identifier\": false, \"displayName\": \"系统协议\", \"supportFilter\": false, \"key\": \"protocol\", \"display\": false}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/FileMasterService/get_fs\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.cfsFilemaster.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.cfsFilemaster.port }}\", \"listParameters\": {\"maxKeys\": \"2000000\", \"pageNo\": \"1\"}}{{- end }}], \"permissionSelection\": \"radiobox\", \"resourceLevel\": 1, \"permissionType\": \"实例\"}]}',
'null', '2019-01-02 13:20:33', '2019-08-21 01:27:04', '2019-08-21 01:27:04', '{}');
INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('a0662fa5b1e541e78ff082d70d8d2b4a', 'default', 'hosteye', 'HOSTEYE', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}],\"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/HOSTEYE/index.html\", \"resourceInstanceType\": \"instance\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读操作\", \"permissionMandatory\": false}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维操作\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"id\", \"display\": true}, {\"identifier\": false, \"displayName\": \"实例名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"地域\", \"supportFilter\": false, \"key\": \"region\", \"display\": false}, {\"identifier\": false, \"displayName\": \"实例类型\", \"supportFilter\": false, \"key\": \"instanceType\", \"display\": true}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/json-api/v1/hosteye/bcc_instance_list\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.hosteye.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.hosteye.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"实例类型\"}]}',
'null', '2018-07-09 07:43:47', '2019-08-21 01:07:33', '2019-08-21 01:07:33', '{}');








INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('d4605fb8df3e4450b419be1005bdd9bb', 'default', 'peerconn', 'PEERCONN', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/EIP/index.html\", \"resourceInstanceType\": \"peerconn\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读对等连接的权限\", \"permissionMandatory\": true}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维对等连接的权限\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"完全控制对等连接的权限\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"peerConnId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"区域\", \"supportFilter\": false,\"key\": \"region\", \"display\": true}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/peerconns\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.vpclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.vpclogicInternal.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"对等连接\"}]}',
'null', '2018-12-06 13:09:07', '2019-08-21 01:26:06', '2019-08-21 01:26:06', '{}');

INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('f9fac39eef5a4d7e8cb589655d779bdb', 'default', 'nat', 'NAT', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/EIP/index.html\", \"resourceInstanceType\": \"nat\", \"permissionItems\": [{\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维权限\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"管理权限\", \"permissionMandatory\": false}, {\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读权限\", \"permissionMandatory\": true}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"ID\", \"supportFilter\": false, \"key\": \"shortId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"区域\", \"supportFilter\": false, \"key\": \"region\", \"display\": false}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/nats\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.vpclogicInternal.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.vpclogicInternal.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"NAT网关\"}], \"globalConditionKeys\": [\"time\", \"ipAddress\"]}',
'null', '2018-06-14 07:49:15', '2019-08-28 11:02:18', '2019-08-28 11:02:18', '{}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('0b54182d772b4afea91689619683014f', 'default', 'bcc', 'BCC', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/BCC/index.html#.E5.A4.9A.E7.94.A8.E6.88.B7.E8.AE.BF.E9.97.AE.E6.8E.A7.E5.88.B6\", \"resourceInstanceType\": \"instance\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读操作\", \"permissionMandatory\": false}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维操作\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": false, \"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"instanceId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"实例名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"地域\", \"supportFilter\": false, \"key\": \"region\", \"display\": false}, {\"identifier\": false, \"displayName\": \"实例类型\", \"supportFilter\": false, \"key\": \"type\", \"display\": true}, {\"identifier\": true, \"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"id\", \"display\":false}], \"endpoints\":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/instances\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicBcc.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicBcc.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"操作类型\"}], \"globalConditionKeys\": [\"time\", \"ipAddress\", \"referer\"]}',
'null', '2017-09-04 07:49:35', '2019-09-09 06:49:24', '2019-09-09 06:49:24', '{}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ( '5649b317ef6f438a904cf1cac8d8d802', 'default', 'bmr', 'BMR', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/BMR/index.html#.E6.A6.82.E8.BF.B0\", \"resourceInstanceType\": \"cluster\", \"permissionItems\": [{\"permissionName\": \"ReadCluster\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"查看集群\", \"permissionMandatory\": true}, {\"permissionName\": \"RenameCluster\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"更改集群名称\", \"permissionMandatory\": false}, {\"permissionName\": \"ScaleCluster\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"变更集群配置\", \"permissionMandatory\": false}, {\"permissionName\": \"ReadStep\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"查看作业详情\", \"permissionMandatory\": false}, {\"permissionName\": \"CreateStep\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"创建作业\", \"permissionMandatory\": false}, {\"permissionName\": \"CancelStep\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"终止作业\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"集群ID\", \"supportFilter\": false, \"key\": \"id\", \"display\": true}, {\"identifier\": false, \"displayName\": \"集群名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"状态\", \"supportFilter\": false, \"key\": \"state\", \"display\": true}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/clusterSummaryForIAM\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bmrProxy.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bmrProxy.port }}\", \"listParameters\": {\"source\": \"bmr\"}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"集群\"}, {\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/BMR/index.html#.E6.A6.82.E8.BF.B0\", \"resourceInstanceType\": \"executionPlan\", \"permissionItems\": [{\"permissionName\": \"ReadExecutionPlan\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"查看定时任务\", \"permissionMandatory\": true}, {\"permissionName\": \"UpdateExecutionPlan\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"更新定时任务\", \"permissionMandatory\": false}, {\"permissionName\": \"SwitchExecutionPlan\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"启停定时任务\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"定时任务对象ID\", \"supportFilter\": false, \"key\": \"id\", \"display\": true}, {\"identifier\": false, \"displayName\": \"定时任务名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/executionPlan/list\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bmrProxy.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bmrProxy.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"定时任务\"}]}',
'null', '2018-09-28 04:09:46', '2019-08-21 01:10:01', '2019-08-21 01:10:01', '{}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('e21fa37cc0fe4827acd2c5e1cde7834a', 'default', 'bmr', 'BMR', 'jsonEditor', 'null',
'[{\"name\": \"BMR\\u8bbf\\u95ee\\u63a7\\u5236\\u6743\\u9650\", \"value\": {\"accessControlList\": [{\"region\": \"*\", \"resource\": [\"*\"], \"effect\": \"Allow\", \"service\": \"bce:bmr\", \"permission\": [\"READ\", \"LIST_ALL_STEP\", \"OPERATE\", \"CREATE_DELETE\", \"FULL_CONTROL\", \"ReadCluster\", \"RenameCluster\", \"ScaleCluster\", \"CreateStep\", \"ReadStep\", \"CancelStep\", \"ReadExecutionPlan\", \"UpdateExecutionPlan\", \"SwitchExecutionPlan\"]}]}, \"description\": \"BMR\\u8bbf\\u95ee\\u63a7\\u5236\\u6743\\u9650\\u6a21\\u677f\"}]',
'2018-10-27 11:22:49', '2018-12-13 09:41:35', '2018-12-13 09:41:35', '{\"helpUrl\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/BMR/GettingStarted.html#.E6.A6.82.E8.BF.B0\"}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('08bf64864cc0479db6856ef9e6027070', 'default', 'edap', 'EDAP', 'jsonEditor', 'null',
'[{\"name\": \"edapu9879u76eeu5185u8d44u6e90u6743u9650\", \"value\": {\"accessControlList\": [{\"region\": \"*\", \"resource\": [\"${projectName}/pipeline\", \"${projectName}/canvas\", \"${projectName}/jobGroup\", \"${projectName}/note\"], \"effect\": \"Allow\", \"service\": \"bce:edap\", \"permission\": [\"LIST\", \"CREATE\"]}, {\"region\": \"*\", \"resource\": [\"${projectName}/tablequery\"], \"effect\": \"Allow\", \"service\": \"bce:edap\", \"permission\": [\"LIST\", \"OPERATE\"]}, {\"region\": \"*\", \"resource\": [\"${projectName}/pipeline/*\", \"${projectName}/canvas/*\", \"${projectName}/jobGroup/*\", \"${projectName}/note/*\"], \"effect\": \"Allow\", \"service\": \"bce:edap\", \"permission\": [\"READ\", \"OPERATE\"]}]}, \"description\": \"u9879u76eeu5185u8d44u6e90u6743u9650u6a21u677f\"}]',
'2020-05-25 11:49:49', '2020-06-02 04:35:41', '2020-06-02 04:35:41', '{\"helpUrl\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/EDAP/index.html\"}');




INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('34b8a9c4f3f149d7ad23da8b99e7bd69', 'default', 'eip', 'EIP', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/EIP/index.html\", \"resourceInstanceType\": \"eip\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读操作\", \"permissionMandatory\": true}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维操作\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\":false, \"permissionNameZh\": \"管理操作\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true,\"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"shortId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"实例名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"公网IP\", \"supportFilter\": false, \"key\": \"eip\", \"display\": true}, {\"identifier\": false, \"displayName\": \"地域\", \"supportFilter\": false, \"key\": \"region\", \"display\": false}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/api/eip/listeip\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceLogicalEip.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceLogicalEip.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"弹性公网IP\"}]}',
'null', '2018-07-04 09:12:03', '2019-08-19 06:50:52', '2019-08-19 06:50:52', '{}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('fc830bdd142d4d93b23730434bfd4aab', 'default', 'eipgroup', 'EIPGROUP', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/EIP/index.html\", \"resourceInstanceType\": \"eipgroup\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读操作\", \"permissionMandatory\": true}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维操作\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"管理操作\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"shortId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"实例名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"地域\", \"supportFilter\": false, \"key\": \"region\", \"display\": true}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/api/eipgroup/listeipgroup\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceLogicalEip.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceLogicalEip.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"共享带宽\"}]}',
'null', '2018-06-19 13:08:38', '2019-08-21 01:26:42', '2019-08-21 01:26:42', '{}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('3a20c72b1f6e4decb8d9d34cd4901128', 'default', 'ipvsixgw', 'IPVSIXGW', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/EIP/index.html\", \"resourceInstanceType\": \"ipv6\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读IPv6网关的权限\", \"permissionMandatory\": true}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"运维IPv6网关的权限\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"完全控制IPv6网关的权限\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"实例ID\", \"supportFilter\": false, \"key\": \"gatewayId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"私有网络ID\", \"supportFilter\": false, \"key\": \"vpcShortId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"区域\", \"supportFilter\": false, \"key\": \"region\", \"display\": false}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/listIpv6Gateway\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceLogicalVpc.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceLogicalVpc.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"IPv6网关\"}]}',
'null', '2018-12-07 08:05:59', '2019-08-21 01:21:33', '2019-08-21 01:21:33', '{}');
INSERT INTO `policy_template` (`id`,`name`,`service_name`,`product_name`,`template_type`,`generator_config`,`json_editor_templates`,`create_time`,`update_time`,`effective_time`,`extra`)
VALUES ('beb523ba14824745b99b0f86a0e5628c','default','kms','KMS','generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/KMS/index.html\", \"resourceInstanceType\": \"instance\", \"permissionItems\": [{\"permissionName\": \"ENCRYPT\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u4ec5\\u52a0\\u5bc6\", \"permissionMandatory\": false}, {\"permissionName\": \"DECRYPT\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u4ec5\\u89e3\\u5bc6\", \"permissionMandatory\": false}, {\"permissionName\": \"ENCRYPT_DECRYPT\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u52a0\\u89e3\\u5bc6\", \"permissionMandatory\": false}, {\"permissionName\": \"VIEW\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u67e5\\u770b\", \"permissionMandatory\": false}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u7ba1\\u7406\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"\\u5bc6\\u94a5ID\", \"supportFilter\": false, \"key\": \"keyId\", \"display\": true}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/?action=ListKeys\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.kmsService.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.kmsService.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"\\u5bc6\\u94a5\\u5b9e\\u4f8b\"}], \"globalConditionKeys\": [\"time\"]}',
'null','2020-03-23 12:07:26','2020-07-01 04:39:04','2020-07-01 04:39:04','{}');
INSERT INTO `policy_template` (`id`,`name`,`service_name`,`product_name`,`template_type`,`generator_config`,`json_editor_templates`,`create_time`,`update_time`,`effective_time`,`extra`)
VALUES ('6618c3590e8a45349c168d6e114e1876','default','snic','SNIC','generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/EIP/index.html\", \"resourceInstanceType\": \"SNIC\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u53ea\\u8bfbSNIC\\u6743\\u9650\", \"permissionMandatory\": true}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u8fd0\\u7ef4SNIC\\u6743\\u9650\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u7ba1\\u7406SNIC\\u6743\\u9650\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": false, \"displayName\": \"\\u5b9e\\u4f8b\\u540d\\u79f0\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": true, \"displayName\": \"\\u5b9e\\u4f8bId\", \"supportFilter\": false, \"key\": \"shortId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"\\u63cf\\u8ff0\", \"supportFilter\": false, \"key\": \"desc\", \"display\": true}, {\"identifier\": false, \"displayName\": \"\\u5730\\u57df\", \"supportFilter\": false, \"key\": \"region\", \"display\": false}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/listEndpointForIam\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceLogicalVpc.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceLogicalVpc.port }}\", \"listParameters\": {}}{{- end }}], \"permissionSelection\": \"radiobox\", \"resourceLevel\": 1, \"permissionType\": \"\\u670d\\u52a1\\u7f51\\u5361SNIC\"}]}',
'null','2019-04-29 03:21:40','2019-08-21 01:02:45','2019-08-21 01:02:45','{}');
INSERT INTO `policy_template` (`id`,`name`,`service_name`,`product_name`,`template_type`,`generator_config`,`json_editor_templates`,`create_time`,`update_time`,`effective_time`,`extra`)
VALUES ('d099f0ed623841f496c2f8e121c26b61','default','ld','LD','generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/DNS/index.html\", \"resourceInstanceType\": \"ld\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u53ea\\u8bfb\\u672c\\u5730dns\\u7684\\u6743\\u9650\", \"permissionMandatory\": true}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u8fd0\\u7ef4\\u672c\\u5730dns\\u7684\\u6743\\u9650\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u5b8c\\u5168\\u63a7\\u5236\\u672c\\u5730dns\\u7684\\u6743\\u9650\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"\\u79c1\\u6709\\u57dfId\", \"supportFilter\": false, \"key\": \"shortId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"\\u79c1\\u6709\\u57df\\u540d\\u79f0\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"\\u533a\\u57df\", \"supportFilter\": false, \"key\": \"region\", \"display\": true}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/v1/api/logical/network/zone/listForIAM\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceLogicalVpc.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceLogicalVpc.port }}\", \"listParameters\": {}}{{- end }}], \"permissionType\": \"ld\"}]}',
'null','2019-05-16 12:16:51','2020-03-04 04:00:56','2020-03-04 04:00:56','{}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('021cb2a986794635ad76c167978d07d8', 'default', 'dts', 'DTS', 'jsonEditor', 'null', '[{\"name\":\"dtsacltest\",\"description\":\"dtsacltest\",\"value\":{\"id\":\"policy_38adbc043eab4f8a8846ad8b5136d3d8\",\"accessControlList\":[{\"service\":\"bce:dts\",\"region\":\"*\",\"resource\":[\"*\"],\"effect\":\"Allow\",\"permission\":[\"READ\",\"OPERATE\"]}]}}]', '2020-11-11 03:42:11', '2020-11-11 03:42:11', '2020-11-11 03:42:11', '{\"helpUrl\":\"fff\"}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('5c1badfbfda940a189fb8521b9d509de', 'default', 'bbc', 'BBC', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/BBC/index.html\", \"resourceInstanceType\": \"instance\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u53ea\\u8bfb\\u64cd\\u4f5c\", \"permissionMandatory\": false}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u8fd0\\u7ef4\\u64cd\\u4f5c\", \"permissionMandatory\": false}, {\"permissionName\": \"FULL_CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u7ba1\\u7406\\u64cd\\u4f5c\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": false, \"displayName\": \"\\u5b9e\\u4f8bID\", \"supportFilter\": false, \"key\": \"instanceId\", \"display\": true}, {\"identifier\": true, \"displayName\": \"\\u5b9e\\u4f8bUUID\", \"supportFilter\": false, \"key\": \"id\", \"display\": true}, {\"identifier\": false, \"displayName\": \"\\u5b9e\\u4f8b\\u540d\\u79f0\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"\\u5730\\u57df\", \"supportFilter\": false, \"key\": \"region\", \"display\": false}, {\"identifier\": false, \"displayName\": \"\\u63cf\\u8ff0\", \"supportFilter\": false, \"key\": \"description\", \"display\": false}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/api/logical/bcc/v1/bbc/instance\", \"region\": \"{{ $region.region}}\",
\"endpoint\": \"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicBbc.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicBbc.port }}\", \"listParameters\": {\"instanceType\": \"2\", \"manner\": \"page\"}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"\\u5b9e\\u4f8b\"}], \"globalConditionKeys\": [\"time\"]}',
'null', '2018-03-12 08:43:39', '2020-03-19 12:07:42', '2020-03-19 12:07:42', '{}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('357f354a009142138e3b233faf4b47ca', 'default', 'bes', 'BES', 'generator',
'{"regions":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}],"resources":[{"resourceLevel":1,"permissionSelection":"checkbox","permissionType":"任务","documentLink":"http://portal{{.Values.appspace.charts.global.domain}}/doc/BES/index.html","resourceInstanceType":"deploy","permissionItems":[{"permissionName":"FULL_CONTROL","permissionNameZh":"管理","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"OPERATE","permissionNameZh":"运维","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"READ","permissionNameZh":"只读","permissionMandatory":true,"irrelevantWithResource":false}],"endpoints":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"region":"{{ $region.region}}",
"endpoint":"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apigateway.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apigateway.port }}","listUri":"/{{ $region.region}}/v1/permission/deploys/bes","listParameters":{}}{{- end }}],"returnFields":[{"key":"id","displayName":"集群Id","supportFilter":false,"identifier":true,"display":true},{"key":"name","displayName":"集群名称","supportFilter":false,"identifier":false,"display":true},{"key":"description","displayName":"描述","supportFilter":false,"identifier":false,"display":true}]}],"globalConditionKeys":["time"]}', 'null', '2020-10-21 06:55:16', '2020-11-12 09:00:00', '2020-10-21 06:55:16', '{}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('de1dcfb8e70e4eb4a3a6cf21f672b387', 'default', 'dcc', 'DCC', 'generator',
'{"regions":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}],"resources":[ { "resourceLevel":1, "permissionSelection":"radiobox", "permissionType": "专属服务器", "documentLink":"http://portal{{.Values.appspace.charts.global.domain}}/doc/DCC/index.html", "resourceInstanceType":"instance", "permissionItems":[ { "permissionName":"READ", "irrelevantWithResource":false, "permissionNameZh":"只读权限", "permissionMandatory":false }, { "permissionName":"OPERATE", "irrelevantWithResource":false, "permissionNameZh":"运维权限", "permissionMandatory":false }, { "permissionName":"FULL_CONTROL", "irrelevantWithResource":false, "permissionNameZh":"管理权限", "permissionMandatory":false } ], "endpoints":[ {{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri": "/api/logical/bcc/v1/hypervisor/host/list", "region": "{{$region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicBcc.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicBcc.port }}", "listParameters": { "pageSize":"214783647","daysToExpiration":"-1","pageNo":"1","manner":"page"}}{{- end }} ], "returnFields":[ { "identifier":false, "displayName":"专属服务器ID", "supportFilter":false, "key":"dedicatedHostId", "display":true }, { "identifier":true, "displayName":"专属服务器UUID", "supportFilter":true, "key":"dedicatedHostUuid", "display":true }, { "identifier":false, "displayName":"专属服务器名称", "supportFilter":false, "key":"name", "display":true }, { "identifier":false, "displayName":"地域", "supportFilter":false, "key":"region", "display":false }, { "identifier":false, "displayName":"描述", "supportFilter":false, "key":"desc", "display":false } ] } ], "globalConditionKeys":[ "time" ] }', 'null', '2018-07-16 09:56:25', '2019-08-22 06:40:33', '2019-08-22 06:40:33', '{}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('24f01b5b14074d45819f1cf8c6e2e84f', 'default', 'adas', 'ADAS', 'generator',
'{"regions":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}],"resources":[ { "resourceLevel":1, "permissionSelection":"checkbox", "permissionType": "弹性网卡ENIC", "documentLink":"http://portal{{.Values.appspace.charts.global.domain}}/doc/ENIC/index.html", "resourceInstanceType":"enic", "permissionItems":[{"permissionName":"READ","permissionNameZh":"只读ENIC权限","permissionMandatory":true,"irrelevantWithResource":false},{"permissionName":"OPERATE","permissionNameZh":"运维ENIC权限","permissionMandatory":false,"irrelevantWithResource":false},{
"permissionName":"FULL_CONTROL","permissionNameZh":"管理ENIC权限","permissionMandatory":false,"irrelevantWithResource":false}], "endpoints":[ {{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri": "/v1/api/logical/network/listEnicForIam", "region": "{{$region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceLogicalVpc.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceLogicalVpc.port }}", "listParameters": {}}{{- end }} ],"returnFields":[{"key":"name","displayName":"实例名称","supportFilter":false,"identifier":false,"display":true},{"key":"eniId","displayName":"实例ID","supportFilter":false,"identifier":true,"display":true},{"key":"description","displayName":"描述","supportFilter":false,"identifier":false,"display":true},{"key":"region","displayName":"地域","supportFilter":false,"identifier":false,"display":false},{"key":"subnetId","displayName":"子网ID","supportFilter":false,"identifier":false,"display":false}]} ],"globalConditionKeys":["time","ipAddress","sourceVpc" ] }', 'null', '2021-01-14 12:27:55', '2022-06-08 14:20:51', '2021-01-14 12:27:55', '{}');
INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('f354af53589448ab87bb63fc5ed23634', 'default', 'cds', 'CDS', 'generator',
'{\"regions\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }} {{- if $i -}},{{- end }}"{{ $region.region}}"{{- end }}], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/CDS/index.html\", \"resourceInstanceType\": \"volume\", \"permissionItems\": [{\"permissionName\": \"READ\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"只读权限\", \"permissionMandatory\": true}, {\"permissionName\": \"OPERATE\", \"irrelevantWithResource\": false,\"permissionNameZh\": \"运维权限\", \"permissionMandatory\": false}, {\"permissionName\": \"CONTROL\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"管理权限\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": false, \"displayName\": \"磁盘UUID\", \"supportFilter\": false, \"key\": \"volumeUuid\", \"display\": true}, {\"identifier\": true, \"displayName\": \"磁盘ID\", \"supportFilter\": false, \"key\": \"volumeId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"磁盘名称\", \"supportFilter\": false, \"key\": \"name\", \"display\": true}, {\"identifier\": false, \"displayName\": \"地域\", \"supportFilter\": false, \"key\": \"region\", \"display\": false}], \"endpoints\": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{\"listUri\": \"/api/logical/bcc/v1/cinder\", \"region\": \"{{ $region.region}}\",
\"endpoint\":\"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicVolume.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicVolume.port }}\", \"listParameters\": {\"pageSize\": \"2000000\", \"pageNo\": \"1\", \"manner\": \"page\"}}{{- end }}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"云磁盘\"}]}',
'null', '2018-08-20 03:55:56', '2019-08-21 01:26:22', '2019-08-21 01:26:22', '{}');

INSERT INTO policy_template (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('6e8b8c3dae104d9daff253d58c3cd39a', 'default', 'rabbitmq', 'RABBITMQ', 'generator',
'{"regions": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}], "resources": [{"documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/RabbitMQ/index.html", "resourceInstanceType": "instance", "permissionItems": [{"permissionName": "READ", "irrelevantWithResource": false, "permissionNameZh": "\u53ea\u8bfb", "permissionMandatory": true}, {"permissionName": "OPERATE", "irrelevantWithResource": false, "permissionNameZh": "\u8fd0\u7ef4", "permissionMandatory": false}, {"permissionName": "FULL_CONTROL", "irrelevantWithResource": false, "permissionNameZh": "\u7ba1\u7406", "permissionMandatory": false}], "returnFields": [{"identifier": true, "displayName": "\u5b9e\u4f8bID", "supportFilter": false, "key": "instanceId", "display": true}, {"identifier": false, "displayName": "\u5b9e\u4f8b\u540d\u79f0", "supportFilter": false, "key": "instanceName", "display": true}], "endpoints": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri": "/v1/instance", "region": "{{$region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicRabbitmq.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicRabbitmq.port }}", "listParameters": {"manner": ""}}{{- end }}], "permissionSelection": "checkbox", "resourceLevel": 1, "permissionType": "\u5b9e\u4f8b"}], "globalConditionKeys": ["time"]}','null', '2022-09-20 14:34:00', '2022-09-20 14:34:00', '2022-09-20 14:34:00', '{}');

INSERT INTO policy_template (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`,  `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('17641252851a4ab88e433da86f400131', 'default','dbaudit','DBAUDIT','generator',
'{"regions":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}],"resources":[
{"resourceLevel":1,"permissionSelection":"checkbox","permissionType":"审计集群","documentLink":"http://portal.icpc.changan.com/doc/DBAUDIT/index.html","resourceInstanceType":"cluster","permissionItems":[{"permissionName":"FULL_CONTROL","permissionNameZh":"管理","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"OPERATE","permissionNameZh":"运维","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"READ","permissionNameZh":"只读","permissionMandatory":true,"irrelevantWithResource":false}],"endpoints":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"region":"{{$region.region}}","endpoint":"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.dbauditApi.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.dbauditApi.port }}","listUri":"/api/sqlaudit/pub/v1/cluster/permission","listParameters":{}}{{- end }}],"returnFields":[{"key":"clusterId","displayName":"审计集群ID","supportFilter":false,"identifier":true,"display":true},{"key":"clusterName","displayName":"审计集群名称","supportFilter":false,"identifier":false,"display":true},{"key":"createTime","displayName":"创建时间","supportFilter":false,"identifier":false,"display":true}]},
{"resourceLevel":1,"permissionSelection":"checkbox","permissionType":"审计策略","documentLink":"http://portal.{{.Values.appspace.charts.global.domain}}/doc/DBAUDIT/index.html","resourceInstanceType":"policy","permissionItems":[{"permissionName":"FULL_CONTROL","permissionNameZh":"管理","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"OPERATE","permissionNameZh":"运维","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"READ","permissionNameZh":"只读","permissionMandatory":true,"irrelevantWithResource":false}],"endpoints":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"region":"{{$region.region}}","endpoint":"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.dbauditApi.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.dbauditApi.port }}","listUri":"/api/sqlaudit/pub/v1/policy/permission","listParameters":{}}{{- end }}],"returnFields":[{"key":"policyId","displayName":"审计策略ID","supportFilter":false,"identifier":true,"display":true},{"key":"policyName","displayName":"审计策略名称","supportFilter":false,"identifier":false,"display":true},{"key":"createTime","displayName":"创建时间","supportFilter":false,"identifier":false,"display":true}]}],"globalConditionKeys":["time"]}',
'null', '2022-09-19 14:34:00', '2022-09-19 14:34:00', '2022-09-19 14:34:00', '{}');

INSERT INTO policy_template (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('733bdd45454b4709bb7a3bb96219908b', 'default', 'tbsp', 'TBSP', 'generator',
'{"regions": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}], "resources": [{"documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/EIP/index.html", "resourceInstanceType": "instance", "permissionItems":[{"permissionName":"READ","permissionNameZh":"只读权限","permissionMandatory":true,"irrelevantWithResource":false},{"permissionName":"OPERATE","permissionNameZh":"运维权限","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"FULL_CONTROL","permissionNameZh":"管理权限","permissionMandatory":false,"irrelevantWithResource":false}], "returnFields":[{"key":"instanceId","displayName":"实例ID","supportFilter":false,"identifier":true,"display":true},{"key":"name","displayName":"实例名称","supportFilter":false,"identifier":false,"display":true},{"key":"region","displayName":"地域","supportFilter":false,"identifier":false,"display":false}], "endpoints": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri": "/api/tbsp/listtbsp", "region": "{{$region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceLogicalEip.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceLogicalEip.port }}", "listParameters": {}}{{- end }}], "permissionSelection": "checkbox", "resourceLevel": 1, "permissionType": "流量突发服务包TBSP"}], "globalConditionKeys": ["time","ipAddress","sourceVpc"]}','null', '2022-09-20 14:34:00', '2022-09-20 14:34:00', '2022-09-20 14:34:00', '{}');

INSERT INTO policy_template (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('c8427c5b92a04a5da561950327b26629', 'default', 'logical_csn', 'CSN', 'generator',
'{"regions":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}],"resources":[{"resourceLevel":1,"permissionSelection":"checkbox","permissionType":"云智能网","documentLink":"http://portal{{.Values.appspace.charts.global.domain}}/doc/CSN/index.html","resourceInstanceType":"csn","permissionItems":[{"permissionName":"READ","permissionNameZh":"只读权限","permissionMandatory":true,"irrelevantWithResource":false},{"permissionName":"OPERATE","permissionNameZh":"运维权限","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"FULL_CONTROL","permissionNameZh":"管理权限","permissionMandatory":false,"irrelevantWithResource":false}],"endpoints":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri": "/v1/csn/listForIam", "region": "{{$region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicalCsn.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicalCsn.port }}", "listParameters": {}}{{- end }}],"returnFields":[{"key":"csnId","displayName":"实例ID","supportFilter":false,"identifier":true,"display":true},{"key":"name","displayName":"名称","supportFilter":false,"identifier":false,"display":true},{"key":"description","displayName":"描述","supportFilter":false,"identifier":false,"display":true}]}, {"resourceLevel":1,"permissionSelection":"checkbox","permissionType":"带宽包","documentLink":"http://{{.Values.appspace.charts.global.domain}}/doc/CSN/index.html","resourceInstanceType":"csnBp","permissionItems":[{"permissionName":"READ","permissionNameZh":"只读权限","permissionMandatory":true,"irrelevantWithResource":false},{"permissionName":"OPERATE","permissionNameZh":"运维权限","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"FULL_CONTROL","permissionNameZh":"管理权限","permissionMandatory":false,"irrelevantWithResource":false}],"endpoints":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri": "/v1/csn/bp/listForIam", "region": "{{$region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicalCsn.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicalCsn.port }}", "listParameters": {}}{{- end }}],"returnFields":[{"key":"csnBpId","displayName":"带宽包ID","supportFilter":false,"identifier":true,"display":true},{"key":"name","displayName":"名称","supportFilter":false,"identifier":false,"display":true},{"key":"bandwidth","displayName":"带宽","supportFilter":false,"identifier":false,"display":true}]}],"globalConditionKeys":["time"]}','null', '2022-10-31 03:02:48', '2022-10-31 03:02:48', '2022-10-31 03:02:48', '{}');







INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('c5cb5fa94d9b48c594f774bad4000b1b', 'default', 'kafka', 'KAFKA', 'generator',
'{"regions": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}],"resources": [{"resourceLevel":1, "permissionSelection": "checkbox", "permissionType": "专享版_集群", "documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/Kafka/index.html", "resourceInstanceType": "cluster", "permissionItems": [ { "permissionName":"updateCluster", "permissionNameZh":"变更集群", "permissionMandatory":false, "irrelevantWithResource":false }, { "permissionName":"reassignPartition", "permissionNameZh":"重分区", "permissionMandatory":false, "irrelevantWithResource":false }, { "permissionName":"restartBroker", "permissionNameZh":"重启Kafka Broker", "permissionMandatory":false, "irrelevantWithResource":false } ],
"endpoints": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{ "listUri": "/v2/iam/clusters", "region": "{{$region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceKafkaBms.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceKafkaBms.port }}", "listParameters": {} }{{- end }}],"returnFields":[{"key":"name", "displayName":"集群名称", "supportFilter":false, "identifier":false, "display":true }, { "key":"createTime", "displayName":"创建时间", "supportFilter":false, "identifier":false, "display":true }, { "key":"resourceId", "displayName":"集群唯一标识", "supportFilter":false, "identifier":true, "display":false } ] }, { "resourceLevel": 1, "permissionSelection": "checkbox", "permissionType": "专享版_主题", "documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/Kafka/index.html", "resourceInstanceType": "topic", "permissionItems": [ { "permissionName": "updateTopic", "permissionNameZh": "更改主题", "permissionMandatory": false, "irrelevantWithResource": false } ],
"endpoints": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{ "listUri": "/v2/iam/topics", "region": "{{$region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceKafkaBms.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceKafkaBms.port }}", "listParameters": {} }{{- end }}], "returnFields": [{ "key":"clusterTopicName", "displayName":"集群-主题名称", "supportFilter":false, "identifier":false, "display":true }, { "key":"createTime", "displayName":"创建时间", "supportFilter":false, "identifier":false, "display":true }, { "key":"resourceId", "displayName":"主题唯一标识", "supportFilter":false, "identifier":true, "display":false } ] }, { "resourceLevel": 1, "permissionSelection": "checkbox", "permissionType": "专享版_消费组", "documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/Kafka/index.html", "resourceInstanceType": "consumerGroup", "permissionItems": [ { "permissionName": "resetOffset", "permissionNameZh": "重置消费offset", "permissionMandatory": false, "irrelevantWithResource": false } ],
"endpoints": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{ "listUri": "/v2/iam/consumer-groups", "region": "{{$region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceKafkaBms.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceKafkaBms.port }}", "listParameters": {} }{{- end }}], "returnFields": [{ "key": "clusterGroupName", "displayName": "集群-消费组名称", "supportFilter": false, "identifier": false, "display": true }, { "key": "updateTime", "displayName": "消费组最后更新时间", "supportFilter": false, "identifier": false, "display": true }, { "key": "resourceId", "displayName": "资源唯一标识", "supportFilter": false, "identifier": true, "display": false } ] }, { "resourceLevel": 1, "permissionSelection": "checkbox", "permissionType": "专享版_用户", "documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/Kafka/index.html", "resourceInstanceType": "user", "permissionItems": [ { "permissionName": "updateUser", "permissionNameZh": "更改用户", "permissionMandatory": false, "irrelevantWithResource": false } ],
"endpoints": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{ "listUri": "/v2/iam/users", "region": "{{$region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.bceKafkaBms.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.bceKafkaBms.port }}", "listParameters": {} }{{- end }}], "returnFields": [ { "key": "clusterUserName", "displayName": "集群-用户名称", "supportFilter": false, "identifier": false, "display": true }, { "key": "createTime", "displayName": "创建时间", "supportFilter": false, "identifier": false, "display": true }, { "key": "resourceId", "displayName": "资源唯一标识", "supportFilter": false, "identifier": true, "display": false } ] }], "globalConditionKeys": [ "time" ] }',
'null', '2018-11-08 05:52:01', '2019-11-29 02:28:27', '2019-11-29 02:28:27', '{}');

INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('d51a082049fb4955bd01c2c688f30712', 'default', 'kafka', 'KAFKA', 'jsonEditor', 'null', '[{"name":"kafka_acl","value":{"accessControlList":[{"region":"*","resource":["*"],"effect":"Allow","service":"bce:kafka","permission":["FULL_CONTROL","AdminTopic","AdminCertificate","TopicReadOper","TopicWriteOper","CertificateReadOper","CertificateWriteOper","ListTopic","DescTopic","DescTopicCertificate","CreateTopic","AlterTopic","DeleteTopic","AddTopicPartition","UpdateTopicCertificate","ListCertificate","DescCertificate","DownloadCertificate","DescCertificateTopic","CreateCertificate","AlterCertificate","DeleteCertificate","RebuildCertificate","UpdateCertificateTopic"]}]},"description":"kafka_acl\u6a21\u677f"}]',
now(),now(),now(), '{"helpUrl": "http://portal{{.Values.appspace.charts.global.domain}}/doc/Kafka/index.html"}');

INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('B46368BF081A43A2A0A1774DA6F134A2', 'default', 'logical-mq', 'MQ', 'generator',
'{"regions":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}],"resources":[{"documentLink":"http://portal{{.Values.appspace.charts.global.domain}}m/doc/MQ/index.html","resourceInstanceType":"instance","permissionItems":[{"permissionName":"READ","irrelevantWithResource":false,"permissionNameZh":"只读操作","permissionMandatory":false},{"permissionName":"OPERATE","irrelevantWithResource":false,"permissionNameZh":"运维操作","permissionMandatory":false}],"returnFields":[{"identifier":true,"displayName":"实例ID","supportFilter":false,"key":"clusterId","display":true},{"identifier":false,"displayName":"实例名称","supportFilter":false,"key":"name","display":true},{"identifier":false,"displayName":"地域","supportFilter":false,"key":"region","display":false}],
"endpoints":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri":"/api/logical/mq/v1/cluster","region":"{{$region.region}}",
"endpoint":"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicMq.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicMq.port }}","listParameters":{"pageSize":"214783647","pageNo":"1","manner":"page"} }{{- end }}],"permissionSelection":"checkbox","resourceLevel":1,"permissionType":"instance"}],"globalConditionKeys":[]}',
'null', '2020-10-21 06:55:16', '2020-11-12 09:00:00', '2020-10-21 06:55:16', '{}');

INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('7767748111854e1b89f622b529eb7347', 'default', 'drds', 'DRDS', 'generator',
'{"regions":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}],"resources":[{"documentLink":"http://portal{{.Values.appspace.charts.global.domain}}/doc/DRDS/index.html","resourceInstanceType":"instance","permissionItems":[{"permissionName":"READ","irrelevantWithResource":false,"permissionNameZh":"只读操作","permissionMandatory":false},{"permissionName":"OPERATE","irrelevantWithResource":false,"permissionNameZh":"运维操作","permissionMandatory":false},{"permissionName":"FULL_CONTROL","permissionNameZh":"管理","permissionMandatory":false,"irrelevantWithResource":false}],"returnFields":[{"identifier":true,"displayName":"集群ID","supportFilter":false,"key":"clusterId","display":true},{"identifier":false,"displayName":"集群名称","supportFilter":false,"key":"clusterName","display":false},{"identifier":false,"displayName":"实例名称","supportFilter":false,"key":"name","display":true}],
"endpoints":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri":"/api/drds/cluster/list","region":"{{$region.region}}",
"endpoint":"http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.apiLogicDrds.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.apiLogicDrds.port }}","listParameters":{"pageSize":"1000","pageNo":"1"}}{{- end }}],"permissionSelection":"checkbox","resourceLevel":1,"permissionType":"操作类型"}],"globalConditionKeys":[]}',
'null', now(), now(), now(), '{}');

INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('fd2d89a9ac75409e8c494700091c3993', 'default', 'palo', 'PALO', 'generator',
'{"regions": [{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}"{{ $region.region}}"{{- end }}], "resources": [{"documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/PALO/index.html", "resourceInstanceType": "deploy", "permissionItems": [{"permissionName": "READ", "irrelevantWithResource": false, "permissionNameZh": "只读操作", "permissionMandatory": true}, {"permissionName": "OPERATE", "irrelevantWithResource": false, "permissionNameZh": "运维操作", "permissionMandatory": false}], "returnFields": [{"identifier": true, "displayName": "集群ID", "supportFilter": false, "key": "id", "display": true}, {"identifier": false, "displayName": "集群名称", "supportFilter": false, "key": "name", "display": true}],
"endpoints":[{{- range $i, $region := $.Values.appspace.charts.global.regions }}{{- if $i -}},{{- end }}{{- $curRegion :=$region.region }}{"listUri": "/api/palo/iam/resources", "region": "{{ $region.region}}",
"endpoint": "http://{{ include "getDomain" (dict "domains" $.Values.appspace.charts.requires.paloconsole.domains "region" $curRegion) }}:{{ $.Values.appspace.charts.requires.paloconsole.port }}", "listParameters": { "region": "{{ $region.region}}"}}{{- end }}], "permissionSelection": "checkbox", "resourceLevel": 1, "permissionType": "操作类型"}],"globalConditionKeys": ["time"]}',
'null', '2022-09-21 11:38:59', '2022-09-23 06:29:52', '2022-09-21 11:38:59', '{}');


-- global service
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('5c292be4a2fe4fae8e50a867f165e0db', 'default', 'dts', 'DTS', 'generator',
'{"regions":["global"],"resources":[{"resourceLevel":1,"permissionSelection":"checkbox","permissionType":"任务","documentLink":"http://portal{{.Values.appspace.charts.global.domain}}/doc/DTS/index.html","resourceInstanceType":"task","permissionItems":[{"permissionName":"FULL_CONTROL","permissionNameZh":"管理","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"OPERATE","permissionNameZh":"运维","permissionMandatory":false,"irrelevantWithResource":false},{"permissionName":"READ","permissionNameZh":"只读","permissionMandatory":true,"irrelevantWithResource":false}],
"endpoints":[{"region":"global","endpoint":"http://{{.Values.appspace.charts.requires.apiLogicDts.domain}}:{{.Values.appspace.charts.requires.apiLogicDts.port}}","listUri":"/api/dts/taskForPermission","listParameters":{}}],"returnFields":[{"key":"id","displayName":"实例ID","supportFilter":false,"identifier":true,"display":true},{"key":"name","displayName":"实例名称","supportFilter":false,"identifier":false,"display":true},{"key":"description","displayName":"描述","supportFilter":false,"identifier":false,"display":true}]}],"globalConditionKeys":["time"]}',
'null', '2020-10-21 06:55:16', '2020-11-12 09:00:00', '2020-10-21 06:55:16', '{}');
INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('e787d47c251f4463b9c776dbae1ea549', 'default', 'dns', 'DNS', 'generator',
'{"regions": ["global"], "resources": [{"documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/DNS/index.html", "resourceInstanceType": "zoneId", "permissionItems": [{"permissionName": "READ", "irrelevantWithResource": false, "permissionNameZh": "只读操作", "permissionMandatory": true}, {"permissionName": "OPERATE", "irrelevantWithResource": false, "permissionNameZh": "运维操作", "permissionMandatory": false}, {"permissionName": "FULL_CONTROL", "irrelevantWithResource": false, "permissionNameZh": "管理权限", "permissionMandatory": false}], "returnFields": [{"identifier": true, "displayName": "域名id", "supportFilter": false, "key": "resourceId", "display": true}, {"identifier": false, "displayName": "域名", "supportFilter": false, "key": "zone", "display": true}, {"identifier": false, "displayName": "域名状态", "supportFilter": false, "key": "flag", "display": true}],
"endpoints": [{"region":"global","endpoint": "http://{{.Values.appspace.charts.requires.bceConsoleDns.domain}}:{{.Values.appspace.charts.requires.bceConsoleDns.port}}", "listParameters": {}}], "permissionSelection": "checkbox", "resourceLevel": 1, "permissionType": "域名"}], "globalConditionKeys": ["time"]}',
'null', '2019-04-01 09:04:32', '2019-08-21 01:16:37', '2019-08-21 01:16:37', '{}');
INSERT INTO `policy_template` (id, name, service_name, product_name, template_type, generator_config, json_editor_templates, create_time, update_time, effective_time, extra)
VALUES ('f08c2829b6d3459685098ec78d2311eb', 'default', 'bos', 'BOS', 'generator',
'{"regions": ["global"], "resources": [{"documentLink": "http://portal{{.Values.appspace.charts.global.domain}}/doc/BOS/index.html", "resourceInstanceType": "bucket", "permissionItems": [{"permissionName": "READ", "irrelevantWithResource": false, "permissionNameZh": "只读权限", "permissionMandatory": false}, {"permissionName": "FULL_CONTROL", "irrelevantWithResource": false, "permissionNameZh": "管理权限", "permissionMandatory": false}],"returnFields": [{"identifier": true, "displayName": "存储桶", "supportFilter": false, "key": "name", "display":true}, {"identifier": false, "displayName": "地域", "supportFilter": false, "key": "location", "display": true}],
"endpoints": [{"listUri": "/", "region": "global", "endpoint": "http://{{.Values.appspace.charts.requires.bosNginx.domain}}:{{.Values.appspace.charts.requires.bosNginx.port}}", "listParameters": {}}], "permissionSelection": "checkbox", "resourceLevel": 1, "permissionType": "权限"}], "globalConditionKeys": ["time", "ipAddress", "referer"]}',
'null', '2018-09-13 06:42:20', '2019-09-05 01:29:07', '2019-09-05 01:29:07', '{}');
INSERT INTO `policy_template` (`id`, `name`, `service_name`, `product_name`, `template_type`, `generator_config`, `json_editor_templates`, `create_time`, `update_time`, `effective_time`, `extra`)
VALUES ('5126259609094858bf75502ff2bff259', 'default', 'edap', 'EDAP', 'generator',
'{\"regions\": [\"global\"], \"resources\": [{\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/EDAP/index.html\", \"resourceInstanceType\": \"METADATA\", \"permissionItems\": [{\"permissionName\": \"CREATE_LOCATION\", \"irrelevantWithResource\": true, \"permissionNameZh\": \"\\u521b\\u5efa\\u6570\\u636e\\u6e56\\u5730\\u5740\", \"permissionMandatory\": false}, {\"permissionName\": \"CREATE_CONNECTION\", \"irrelevantWithResource\": true, \"permissionNameZh\": \"\\u521b\\u5efa\\u6e90\\u8fde\\u63a5\", \"permissionMandatory\": false}, {\"permissionName\": \"CREATE_TOPIC\", \"irrelevantWithResource\": true, \"permissionNameZh\": \"\\u521b\\u5efa\\u5143\\u6570\\u636e\\u4e3b\\u4f53\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"ID\", \"supportFilter\": false, \"key\": \"locationId\", \"display\": true}, {\"identifier\": false, \"displayName\": \"\\u7c7b\\u578b\", \"supportFilter\": false, \"key\": \"type\", \"display\": true}, {\"identifier\": false, \"displayName\": \"\\u63cf\\u8ff0\", \"supportFilter\": false, \"key\": \"location2\", \"display\": true}], \"endpoints\": [{\"listUri\": \"/v1/location\", \"region\": \"global\",
\"endpoint\": \"http://{{.Values.appspace.charts.requires.edapGateway.domain}}:{{.Values.appspace.charts.requires.edapGateway.port}}\", \"listParameters\": {}}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"\\u6570\\u636e\\u6743\\u9650\"}, {\"documentLink\": \"http://portal{{.Values.appspace.charts.global.domain}}/doc/EDAP/index.html\", \"resourceInstanceType\": \"note\", \"permissionItems\": [{\"permissionName\": \"CREATE_NOTE\", \"irrelevantWithResource\": false, \"permissionNameZh\": \"\\u521b\\u5efa\\u7b14\\u8bb0\", \"permissionMandatory\": false}], \"returnFields\": [{\"identifier\": true, \"displayName\": \"\\u9879\\u76ee\\u540d\\u79f0\", \"supportFilter\": false, \"key\": \"projectName\", \"display\": true}], \"endpoints\": [{\"listUri\": \"/v1/iamproject\", \"region\": \"global\",
\"endpoint\": \"http://{{.Values.appspace.charts.requires.edapGateway.domain}}:{{.Values.appspace.charts.requires.edapGateway.port}}\", \"listParameters\": {}}], \"permissionSelection\": \"checkbox\", \"resourceLevel\": 1, \"permissionType\": \"\\u7b14\\u8bb0\\u6743\\u9650\"}], \"globalConditionKeys\": [\"time\"]}',
'null', '2020-02-24 08:55:19', '2020-05-07 11:24:26', '2020-05-07 11:24:26', '{}');