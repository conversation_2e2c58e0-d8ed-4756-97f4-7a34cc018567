#!/bin/bash

# 将 keys.json 对应的 SQL 文件拷贝到 flyway 对应的目录，并将 SQL 文件重命名为 flyway 要求的格式：
# V1.0.0.19__bce_iam__init_ak_sk.sql，然后由 flyway 去注入

set -ex

# aksk SQL 原始文件所在目录(通过 configmap 挂载)
readonly AKSK_SQL_FILE_PATH=/home/<USER>/aksk-sql/

# aksk SQL 原始文件要复制到的 flyway 目录
readonly NEW_AKSK_SQL_FILE_PATH=/home/<USER>/xdb-data-init/bce_iam/
# aksk SQL 原始文件对应 flyway 目录中的文件名
readonly NEW_AKSK_SQL_FILE=V1.0.0.19__bce_iam__init_ak_sk.sql

# 预计只有一个 SQL 文件
readonly AKSK_SQL_FILE_COUNT=$(find "${AKSK_SQL_FILE_PATH}" -type f -name "*sql*" | wc -l)
if [ "${AKSK_SQL_FILE_COUNT}" -ne 1 ]; then
    echo "Error: Only one SQL file should be found, but found ${AKSK_SQL_FILE_COUNT}"
    exit 1
fi

readonly AKSK_SQL_FILE=$(find "${AKSK_SQL_FILE_PATH}" -type f -name "*sql*" | head -1)

echo "Start to copy ${AKSK_SQL_FILE} to ${NEW_AKSK_SQL_FILE_PATH}${NEW_AKSK_SQL_FILE}"
cp "${AKSK_SQL_FILE}" "${NEW_AKSK_SQL_FILE_PATH}${NEW_AKSK_SQL_FILE}"
if [ $? -eq 0 ]; then
    echo "Success: Copied ${AKSK_SQL_FILE} to ${NEW_AKSK_SQL_FILE_PATH}${NEW_AKSK_SQL_FILE}"
else
    echo "Error: Failed to copy the SQL file."
    exit 1
fi