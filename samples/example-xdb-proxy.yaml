apiVersion: cloudbed.abcstack.com/v1
kind: XDBProxy      #proxy 独立模式，pod中只有 xagent、proxy 两个服务，实现单服务扩容
metadata:
  name: lxz-xdb-extension-dbproxy        #对现有xdb集群 增加proxy节点
  namespace: base
spec:
  instances: 3
  version: "1.4.38.1"
  # 新增image字段，可选，不设置时使用默认镜像
  image: "registry.example.com/xdb-proxy:v1.4.38.1"
  region: "cn-beijing"
  clusterRef:
    name: xdb-global-test
    namespace: base
  # 自定义 affinity 配置
  # affinity:
  #   # Pod反亲和性配置 - 尽量将Pod分散到不同节点
  #   podAntiAffinity:
  #     preferredDuringSchedulingIgnoredDuringExecution:
  #     - weight: 100
  #       podAffinityTerm:
  #         labelSelector:
  #           matchLabels:
  #             component: xdbproxy
  #             cloudbed.abcstack.com/cluster: {spec.name}
  #         topologyKey: kubernetes.io/hostname
  #     # 如果资源充足，强制分散到不同节点
  #     requiredDuringSchedulingIgnoredDuringExecution:
  #     - labelSelector:
  #         matchLabels:
  #           component: xdbproxy
  #           cloudbed.abcstack.com/cluster: {spec.name}
  #       topologyKey: kubernetes.io/hostname
  #       # 设置软反亲和性，当资源充足时可以调到同一节点
  #       whenUnsatisfiable: ScheduleAnyway
  #   # 节点亲和性配置 - 选择特定标签的节点
  #   nodeAffinity:
  #     requiredDuringSchedulingIgnoredDuringExecution:
  #       nodeSelectorTerms:
  #       - matchExpressions:
  #         - key: kubernetes.io/role
  #           operator: In
  #           values:
  #           - worker
  # 自定义资源配置    
  resources:
    requests:
      cpu: "2"
      memory: "2Gi"
    limits:
      cpu: "2"
      memory: "2Gi"
  # 存储配置
  storage:
    xdbproxy:
      storageType: "persistentVolume"  # 可选值: emptyDir, persistentVolume
      storageClass: "local-path"         # 当storageType为persistentVolume时必填
      size: "50Gi"                     # 当storageType为persistentVolume时必填
    xagent:
      storageType: "persistentVolume"
      storageClass: "local-path" 
      size: "20Gi"

