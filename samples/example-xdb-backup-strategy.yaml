apiVersion: v1
kind: Secret
metadata:
  name: my-cluster-backup-strategy-s3-secret
type: Opaque
data:
  endpoint: s3.bj-bos-sandbox.baidu-int.com 
  region: bj
  accessKey: ********************************************  # 原始值需要进行base64编码
  secretKey: NjY1MzgzZGFlYjRhNDFjNmE5MGY2MWJjMGNiMGFkMGQ=  # 原始值需要进行base64编码
  bucket: mybackup-test  
---
apiVersion: cloudbed.abcstack.com/v1
kind: XDBBackupStrategy
metadata:
  name: my-cluster-backup-strategy-s3-secret
  namespace: base
spec:
  clusterName: xdb-cluster
  clusterNamespace: base
  storageType: "fs"  # s3/fs/
  secretName: my-cluster-backup-strategy-secret     #如果s3存储，需要配置secret
  kind: "schedule"    #schedule / single
  expireDuration: "72h"
  schedule: "0 1 * * *"           # schedule: minute(0-59) hour(0-23) day(1-31) month(1-12) day_of_week(0-6, mon,tue,wed,thu,fri,sat,sun)
##                                                execute once a day at 23:00: 0 23 * * *
##                                                1:00 every weekend: 0 1 * * sun
##                                                executed at 1:00 AM on the first day of each month : 0 1 1 * *
##                                                execute once every day at 0:00, 13:00: 0 0,13 * * *
##                                                executed at 1:00 AM every Wednesday and Saturday: 0 1 * * wed,sat
##                                            For more information see https://apscheduler.readthedocs.io/en/3.x/modules/triggers/cron.html?highlight=cron
