apiVersion: v1
kind: Secret
metadata:
  name: my-user-password
  namespace: xdb-operator
data:
  password: bXlzcWwtcGFzc3dvcmQtZm9yLXVzZXI=
---
apiVersion: cloudbed.abcstack.com/v1
kind: XDBUser
metadata:
  name: xdbadmin
  namespace: xdb-operator
  labels:
    cloudbed.abcstack.com/xdbcluster-name: xdbglobaltest      #XDB集群名称,方便其他资源根据labels关联过滤
    cloudbed.abcstack.com/xdbcluster-ns: base                #XDB集群名称空间
  annotations:
    xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy: retain # When the MysqlUser is deleted, the MySQL user will be preserved
spec:
  user: xdb_admin
  password:
    secretName: my-user-password
    key: password
  clusterRef:
    name: xdbglobaltest
    namespace: base
  allowedHosts: 
    - localhost             # 访问dbproxy的默认为所有(*),针对特殊应用，需要直接访问mysql地址，需要配置localhost
  dbproxyUserMaxConnections: 500  # DBProxy最大连接数
  mysqlUserMaxConnections: 3  # MySQL最大连接数
  wrFlag: 2        #读写分离的开关，0:开启读写分离，1：只从 从库读，2：只从主库读写
  permissions:              #默认 数据库权限，支持读、写、all
    - schema: db1
      permissions: WRITE      # dml 权限：只能增删改查
    - schema: db2
      permissions: READ
    - schema: '*'
      permissions: MONITOR
    - schema: '*'
      permissions: DBA
