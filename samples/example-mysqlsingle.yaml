apiVersion: cloudbed.abcstack.com/v1
kind: MysqlSingle      #proxy 独立模式，pod中只有 xagent、proxy 两个服务，实现单服务扩容
metadata:
  name: xdbglobaldefault-recovery-20250508    #对现有xdb集群 增加proxy节点
  namespace: base
spec:
  instances: 1
  mysqlVersion: "5.7"
  mysqlDataStorageSize: "20Gi"
  image: "registry.example.com/xdb-mysql:v1.4.38.1"
  snapshotRef:
    name: auto-snapshot-xdbglobaltest-20250508-150200
    namespace: base

