apiVersion: cloudbed.abcstack.com/v1
kind: XDBBackup
metadata:
  name: xdbcluster-auto-2023-82-2123     #("%s-auto-%s"), j.<PERSON><PERSON><PERSON><PERSON>, time.Now().Format("2006-01-02t15-04-05"))
  namespace: base
spec:
  clusterName: xdb-cluster
  clusterNamespace: base
  backupStrategyName: default-backup
  backupStrategyNamespace: base
    ## specify a secret where to find credentials to access the
    ## bucket
    # backupSecretName: backup-secret
    ## specify the remote deletion policy. It can be on of ["retain", "delete"]
  # remoteDeletePolicy: retain
