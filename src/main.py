"""
IAM-Operator 主入口
"""
import asyncio
import logging
import kopf
from kubernetes import client, config as k8s_config

# 导入处理器
from .handlers import configmap_handler, crd_handler
from .utils.config import config
from .utils.metrics import metrics
from .clients.iam_client import IAMClient

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


@kopf.on.startup()
async def startup_handler(**kwargs):
    """启动时的初始化处理"""
    
    logger.info("Starting IAM-Operator...")
    
    try:
        # 加载 Kubernetes 配置
        try:
            k8s_config.load_incluster_config()
            logger.info("Loaded in-cluster Kubernetes config")
        except k8s_config.ConfigException:
            k8s_config.load_kube_config()
            logger.info("Loaded local Kubernetes config")
        
        # 启动指标服务器
        metrics.start_server()
        
        # 健康检查
        await perform_startup_health_checks()
        
        logger.info("IAM-Operator started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start IAM-Operator: {e}")
        raise


@kopf.on.cleanup()
async def cleanup_handler(**kwargs):
    """清理处理"""
    logger.info("Shutting down IAM-Operator...")


async def perform_startup_health_checks():
    """启动时健康检查"""
    
    logger.info("Performing startup health checks...")
    
    # 检查 IAM API 连通性
    try:
        iam_client = IAMClient()
        if await asyncio.get_event_loop().run_in_executor(None, iam_client.health_check):
            logger.info("✅ IAM API health check passed")
        else:
            logger.warning("⚠️ IAM API health check failed, but continuing...")
    except Exception as e:
        logger.warning(f"⚠️ IAM API health check error: {e}, but continuing...")
    
    # 检查必要的权限
    try:
        await check_rbac_permissions()
        logger.info("✅ RBAC permissions check passed")
    except Exception as e:
        logger.error(f"❌ RBAC permissions check failed: {e}")
        raise


async def check_rbac_permissions():
    """检查必要的 RBAC 权限"""
    
    v1 = client.CoreV1Api()
    
    try:
        # 测试 ConfigMap 读权限
        await asyncio.get_event_loop().run_in_executor(
            None, 
            lambda: v1.list_config_map_for_all_namespaces(limit=1)
        )
        logger.debug("ConfigMap read permission OK")
        
        # 测试 ConfigMap 写权限（尝试读取状态 ConfigMap）
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: v1.read_namespaced_config_map(
                    name=config.get('state_configmap_name'),
                    namespace=config.get('state_configmap_namespace')
                )
            )
        except client.exceptions.ApiException as e:
            if e.status != 404:  # 404 是正常的，表示 ConfigMap 不存在
                raise
        
        logger.debug("ConfigMap write permission OK")
        
    except Exception as e:
        raise PermissionError(f"Insufficient RBAC permissions: {e}")


@kopf.on.probe(id='liveness')
def liveness_probe(**kwargs):
    """存活探针"""
    return {'status': 'alive'}


@kopf.on.probe(id='readiness')
def readiness_probe(**kwargs):
    """就绪探针"""
    try:
        # 检查 IAM API 连通性
        iam_client = IAMClient()
        if iam_client.health_check():
            return {'status': 'ready'}
        else:
            return {'status': 'not ready', 'reason': 'IAM API not available'}
    except Exception as e:
        return {'status': 'not ready', 'reason': str(e)}


def main():
    """主函数"""
    
    logger.info("Initializing IAM-Operator...")
    
    # 配置 kopf
    kopf.configure(
        verbose=config.log_level.upper() == 'DEBUG',
        log_format=kopf.LogFormat.FULL,
    )
    
    # 运行 operator
    kopf.run(
        standalone=True,
    )


if __name__ == '__main__':
    main()
