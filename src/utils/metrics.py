"""
监控指标模块
"""
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import logging

logger = logging.getLogger(__name__)

# 定义指标
credentials_processed = Counter(
    'iam_credentials_processed_total', 
    'Total processed credentials', 
    ['source_type', 'product', 'status']
)

processing_duration = Histogram(
    'iam_processing_duration_seconds',
    'Time spent processing credentials',
    ['source_type', 'product']
)

active_credentials = Gauge(
    'iam_active_credentials',
    'Number of active credentials',
    ['product']
)

api_requests = Counter(
    'iam_api_requests_total',
    'Total IAM API requests',
    ['method', 'endpoint', 'status_code']
)

api_request_duration = Histogram(
    'iam_api_request_duration_seconds',
    'IAM API request duration',
    ['method', 'endpoint']
)


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, port: int = 8080):
        self.port = port
        self._server_started = False
    
    def start_server(self):
        """启动指标服务器"""
        if not self._server_started:
            try:
                start_http_server(self.port)
                self._server_started = True
                logger.info(f"Metrics server started on port {self.port}")
            except Exception as e:
                logger.error(f"Failed to start metrics server: {e}")
    
    def record_credential_processed(self, source_type: str, product: str, status: str):
        """记录凭据处理指标"""
        credentials_processed.labels(
            source_type=source_type,
            product=product,
            status=status
        ).inc()
    
    def record_processing_duration(self, source_type: str, product: str, duration: float):
        """记录处理耗时"""
        processing_duration.labels(
            source_type=source_type,
            product=product
        ).observe(duration)
    
    def update_active_credentials(self, product: str, count: int):
        """更新活跃凭据数量"""
        active_credentials.labels(product=product).set(count)
    
    def record_api_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """记录 API 请求指标"""
        api_requests.labels(
            method=method,
            endpoint=endpoint,
            status_code=status_code
        ).inc()
        
        api_request_duration.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)


# 全局指标收集器实例
metrics = MetricsCollector()
