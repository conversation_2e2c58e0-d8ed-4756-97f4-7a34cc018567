"""
ConfigMap 事件处理器
"""
import kopf
import logging
from ..processors.parser import parser
from ..processors.credential_processor import processor
from ..utils.config import config
from ..utils.exceptions import ConfigParseError, IAMAPIError

logger = logging.getLogger(__name__)


def is_iam_configmap(body: dict, name: str, namespace: str) -> bool:
    """边界判断：是否为 IAM 相关的 ConfigMap"""
    
    # 检查命名空间
    if namespace not in config.target_namespaces:
        return False
    
    # 检查 ConfigMap 数据
    data = body.get('data', {})
    if not data:
        return False
    
    # 检查是否包含 IAM 凭据模式
    for file_name, content in data.items():
        if isinstance(content, str):
            # 检查 PHP 格式的凭据
            if ('$' in content and '_ak' in content and '_sk' in content):
                return True
            
            # 检查 YAML/JSON 格式的凭据
            if ('iam:' in content or '"iam"' in content) and ('ak:' in content or '"ak"' in content):
                return True
    
    return False


@kopf.on.create('v1', 'configmaps')
@kopf.on.update('v1', 'configmaps')
def handle_configmap_input(body, name, namespace, **kwargs):
    """处理从 values.yaml 转换来的 ConfigMap"""
    
    try:
        # 边界检查：只处理包含 IAM 配置的 ConfigMap
        if not is_iam_configmap(body, name, namespace):
            return
        
        logger.info(f"Processing IAM ConfigMap: {namespace}/{name}")
        
        # 解析并标准化
        credentials = parser.parse_configmap_credentials(body.get('data', {}))
        
        if not credentials:
            logger.info(f"No valid credentials found in ConfigMap: {namespace}/{name}")
            return
        
        logger.info(f"Found credentials for products: {list(credentials.keys())}")
        
        # 统一处理
        processor.process_credentials(
            source_type='configmap',
            source_name=f"{namespace}/{name}",
            credentials=credentials
        )
        
        logger.info(f"Successfully processed ConfigMap: {namespace}/{name}")
        
    except ConfigParseError as e:
        kopf.warn(f"配置解析失败: {e}")
        # 不重试，记录警告
        
    except IAMAPIError as e:
        kopf.error(f"IAM API 调用失败: {e}")
        # 重试机制
        raise kopf.TemporaryError(f"IAM API 暂时不可用: {e}", delay=60)
        
    except Exception as e:
        kopf.error(f"处理 ConfigMap 时发生未知错误: {e}")
        raise


@kopf.on.delete('v1', 'configmaps')
def handle_configmap_delete(body, name, namespace, **kwargs):
    """处理 ConfigMap 删除事件"""
    
    try:
        # 边界检查
        if not is_iam_configmap(body, name, namespace):
            return
        
        logger.info(f"ConfigMap deleted: {namespace}/{name}")
        
        # 解析凭据以确定需要清理的产品
        credentials = parser.parse_configmap_credentials(body.get('data', {}))
        
        if not credentials:
            return
        
        # 清理 IAM 凭据（可选，根据业务需求决定）
        # 这里可以选择删除 IAM 中的凭据，或者只是记录日志
        for product_name in credentials.keys():
            logger.info(f"ConfigMap deleted, product credentials may need cleanup: {namespace}/{name}/{product_name}")
            
            # 如果需要自动清理，可以调用：
            # processor.iam_client.delete_service_credentials(name, product_name)
        
    except Exception as e:
        logger.error(f"处理 ConfigMap 删除事件时发生错误: {e}")
        # 删除事件的错误不需要重试
