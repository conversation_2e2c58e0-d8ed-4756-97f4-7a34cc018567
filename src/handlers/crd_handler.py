"""
CRD 事件处理器
"""
import kopf
import logging
from ..processors.credential_processor import processor
from ..utils.exceptions import ValidationError, IAMAPIError

logger = logging.getLogger(__name__)


@kopf.on.create('iam.example.com', 'v1', 'iamserviceaccounts')
@kopf.on.update('iam.example.com', 'v1', 'iamserviceaccounts')
def handle_crd_input(spec, name, namespace, **kwargs):
    """处理 CRD 输入"""
    
    try:
        logger.info(f"Processing IAMServiceAccount: {namespace}/{name}")
        
        # 验证 CRD 规格
        if not _validate_crd_spec(spec):
            raise ValidationError(f"Invalid CRD spec: {namespace}/{name}")
        
        # 提取服务名和产品凭据
        service_name = spec.get('serviceName', name)
        products = spec.get('products', {})
        
        if not products:
            logger.warning(f"No products defined in CRD: {namespace}/{name}")
            return
        
        logger.info(f"Found credentials for products: {list(products.keys())}")
        
        # 直接处理结构化数据，无需解析
        processor.process_credentials(
            source_type='crd',
            source_name=f"{namespace}/{name}",
            credentials=products
        )
        
        # 更新 CRD 状态
        _update_crd_status(name, namespace, 'success', f"Processed {len(products)} products")
        
        logger.info(f"Successfully processed IAMServiceAccount: {namespace}/{name}")
        
    except ValidationError as e:
        kopf.warn(f"CRD 验证失败: {e}")
        _update_crd_status(name, namespace, 'failed', str(e))
        # 不重试验证错误
        
    except IAMAPIError as e:
        kopf.error(f"IAM API 调用失败: {e}")
        _update_crd_status(name, namespace, 'failed', str(e))
        # 重试机制
        raise kopf.TemporaryError(f"IAM API 暂时不可用: {e}", delay=60)
        
    except Exception as e:
        kopf.error(f"处理 IAMServiceAccount 时发生未知错误: {e}")
        _update_crd_status(name, namespace, 'failed', str(e))
        raise


@kopf.on.delete('iam.example.com', 'v1', 'iamserviceaccounts')
def handle_crd_delete(spec, name, namespace, **kwargs):
    """处理 CRD 删除事件"""
    
    try:
        logger.info(f"IAMServiceAccount deleted: {namespace}/{name}")
        
        # 提取产品信息
        products = spec.get('products', {})
        service_name = spec.get('serviceName', name)
        
        if not products:
            return
        
        # 清理 IAM 凭据（根据业务需求决定是否自动清理）
        for product_name in products.keys():
            logger.info(f"CRD deleted, cleaning up credentials: {service_name}/{product_name}")
            
            try:
                # 删除 IAM 凭据
                processor.iam_client.delete_service_credentials(service_name, product_name)
                logger.info(f"Successfully deleted credentials: {service_name}/{product_name}")
                
            except IAMAPIError as e:
                logger.warning(f"Failed to delete credentials {service_name}/{product_name}: {e}")
                # 删除失败不阻塞 CRD 删除
        
    except Exception as e:
        logger.error(f"处理 CRD 删除事件时发生错误: {e}")
        # 删除事件的错误不需要重试


def _validate_crd_spec(spec: dict) -> bool:
    """验证 CRD 规格"""
    
    # 检查必需字段
    if not isinstance(spec, dict):
        return False
    
    service_name = spec.get('serviceName')
    if not service_name or not isinstance(service_name, str):
        logger.error("Missing or invalid serviceName")
        return False
    
    products = spec.get('products')
    if not products or not isinstance(products, dict):
        logger.error("Missing or invalid products")
        return False
    
    # 验证每个产品的凭据
    for product_name, credentials in products.items():
        if not isinstance(credentials, dict):
            logger.error(f"Invalid credentials for product {product_name}")
            return False
        
        # 检查必需的凭据字段
        if not credentials.get('ak') or not credentials.get('sk'):
            logger.error(f"Missing ak or sk for product {product_name}")
            return False
    
    return True


def _update_crd_status(name: str, namespace: str, status: str, message: str):
    """更新 CRD 状态"""
    
    try:
        from kubernetes import client
        from datetime import datetime
        
        # 构造状态更新
        status_patch = {
            'status': {
                'lastUpdated': datetime.utcnow().isoformat() + 'Z',
                'conditions': [
                    {
                        'type': 'Ready',
                        'status': 'True' if status == 'success' else 'False',
                        'lastTransitionTime': datetime.utcnow().isoformat() + 'Z',
                        'reason': status.capitalize(),
                        'message': message
                    }
                ]
            }
        }
        
        # 更新 CRD 状态
        custom_api = client.CustomObjectsApi()
        custom_api.patch_namespaced_custom_object_status(
            group='iam.example.com',
            version='v1',
            namespace=namespace,
            plural='iamserviceaccounts',
            name=name,
            body=status_patch
        )
        
        logger.debug(f"Updated CRD status: {namespace}/{name} - {status}")
        
    except Exception as e:
        logger.warning(f"Failed to update CRD status: {e}")


@kopf.on.field('iam.example.com', 'v1', 'iamserviceaccounts', field='spec.products')
def handle_products_change(old, new, name, namespace, **kwargs):
    """处理产品凭据字段变化"""
    
    try:
        logger.info(f"Products changed in IAMServiceAccount: {namespace}/{name}")
        
        # 比较变化
        old_products = set(old.keys()) if old else set()
        new_products = set(new.keys()) if new else set()
        
        added_products = new_products - old_products
        removed_products = old_products - new_products
        updated_products = old_products & new_products
        
        if added_products:
            logger.info(f"Added products: {added_products}")
        
        if removed_products:
            logger.info(f"Removed products: {removed_products}")
            # 可以选择清理已删除的产品凭据
        
        if updated_products:
            logger.info(f"Updated products: {updated_products}")
        
        # 处理变化后的凭据
        if new:
            processor.process_credentials(
                source_type='crd',
                source_name=f"{namespace}/{name}",
                credentials=new
            )
        
    except Exception as e:
        logger.error(f"处理产品变化时发生错误: {e}")
        raise
