"""
Mock IAM API 客户端 - 用于测试
"""
import time
import logging
from typing import Dict, Any
from ..utils.exceptions import IAMAPIError

logger = logging.getLogger(__name__)


class MockIAMClient:
    """Mock IAM API 客户端 - 用于本地测试"""
    
    def __init__(self):
        self.base_url = "mock://iam-api"
        self.timeout = 30
        self._credentials_store = {}
        
        logger.info("使用 Mock IAM 客户端")
    
    def health_check(self) -> bool:
        """健康检查 - 总是返回成功"""
        logger.info("Mock IAM API health check - OK")
        return True
    
    def create_service_credentials(self, service_name: str, product_name: str, credentials: Dict[str, str]) -> Dict[str, Any]:
        """创建服务凭据 - Mock 实现"""
        key = f"{service_name}/{product_name}"
        
        logger.info(f"Mock: 创建凭据 {key}")
        logger.debug(f"Mock: 凭据内容 - ak: {credentials.get('ak', '')[:8]}...")
        
        # 模拟 API 延迟
        time.sleep(0.1)
        
        # 存储凭据
        self._credentials_store[key] = {
            "service_name": service_name,
            "product_name": product_name,
            "credentials": credentials,
            "created_at": time.time(),
            "updated_at": time.time()
        }
        
        return {
            "status": "success",
            "message": f"Credentials created for {service_name}/{product_name}",
            "credential_id": f"mock-{hash(key) % 10000}"
        }
    
    def update_service_credentials(self, service_name: str, product_name: str, credentials: Dict[str, str]) -> Dict[str, Any]:
        """更新服务凭据 - Mock 实现"""
        key = f"{service_name}/{product_name}"
        
        logger.info(f"Mock: 更新凭据 {key}")
        logger.debug(f"Mock: 凭据内容 - ak: {credentials.get('ak', '')[:8]}...")
        
        # 模拟 API 延迟
        time.sleep(0.1)
        
        if key not in self._credentials_store:
            # 如果不存在，抛出 404 错误，触发创建逻辑
            raise IAMAPIError(f"404 - Credentials not found: {key}")
        
        # 更新凭据
        self._credentials_store[key]["credentials"] = credentials
        self._credentials_store[key]["updated_at"] = time.time()
        
        return {
            "status": "success",
            "message": f"Credentials updated for {service_name}/{product_name}",
            "credential_id": f"mock-{hash(key) % 10000}"
        }
    
    def delete_service_credentials(self, service_name: str, product_name: str) -> Dict[str, Any]:
        """删除服务凭据 - Mock 实现"""
        key = f"{service_name}/{product_name}"
        
        logger.info(f"Mock: 删除凭据 {key}")
        
        # 模拟 API 延迟
        time.sleep(0.1)
        
        if key in self._credentials_store:
            del self._credentials_store[key]
            return {
                "status": "success",
                "message": f"Credentials deleted for {service_name}/{product_name}"
            }
        else:
            raise IAMAPIError(f"404 - Credentials not found: {key}")
    
    def get_service_credentials(self, service_name: str, product_name: str) -> Dict[str, Any]:
        """获取服务凭据 - Mock 实现"""
        key = f"{service_name}/{product_name}"
        
        logger.info(f"Mock: 获取凭据 {key}")
        
        # 模拟 API 延迟
        time.sleep(0.1)
        
        if key in self._credentials_store:
            return self._credentials_store[key]
        else:
            raise IAMAPIError(f"404 - Credentials not found: {key}")
    
    def list_all_credentials(self) -> Dict[str, Any]:
        """列出所有凭据 - Mock 实现"""
        logger.info("Mock: 列出所有凭据")
        
        return {
            "status": "success",
            "count": len(self._credentials_store),
            "credentials": list(self._credentials_store.keys())
        }
