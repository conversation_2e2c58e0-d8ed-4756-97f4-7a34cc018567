"""
IAM API 客户端 - 基于 iam-manager API 规范
"""
import requests
import time
import logging
import json
import base64
from typing import Dict, Any, Optional
from ..utils.config import config
from ..utils.exceptions import IAMAPIError
from ..utils.metrics import metrics

logger = logging.getLogger(__name__)


class IAMClient:
    """IAM API 客户端 - 基于 iam-manager v3 API"""

    def __init__(self):
        # 基于 iam-manager 的实际配置
        self.base_url = config.iam_api_url.rstrip('/') + '/v3'  # 添加 v3 路径
        self.timeout = config.iam_api_timeout
        self.session = requests.Session()
        self._auth_token = None
        self._token_expires_at = 0

        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'IAM-Operator/1.0'
        })

        logger.info(f"IAM Client initialized with endpoint: {self.base_url}")
    
    def _get_auth_token(self) -> str:
        """获取认证 Token - 基于 iam-manager 认证流程"""

        # 检查 token 是否还有效
        if self._auth_token and time.time() < self._token_expires_at:
            return self._auth_token

        try:
            # 基于 iam-manager 的认证请求格式
            auth_request = {
                "auth": {
                    "identity": {
                        "methods": ["password"],
                        "password": {
                            "user": {
                                "name": config.get('iam_username', 'iam-operator'),
                                "password": config.get('iam_password', 'default-password'),
                                "domain": {"name": config.get('iam_domain', 'default')}
                            }
                        }
                    }
                }
            }

            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/auth/tokens",
                json=auth_request,
                timeout=self.timeout
            )
            duration = time.time() - start_time

            metrics.record_api_request('POST', '/auth/tokens', response.status_code, duration)

            if response.status_code == 201:  # iam-manager 返回 201 Created
                # 从响应头获取 token
                token = response.headers.get('X-Subject-Token')
                if token:
                    self._auth_token = token
                    # 设置 token 过期时间（默认 1 小时）
                    self._token_expires_at = time.time() + 3600
                    logger.info("IAM authentication successful")
                    return token
                else:
                    raise IAMAPIError("No X-Subject-Token in response")
            else:
                raise IAMAPIError(f"Authentication failed: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"IAM authentication error: {e}")
            raise IAMAPIError(f"Authentication failed: {e}")

    def health_check(self) -> bool:
        """健康检查 - 使用 iam-manager 的健康检查端点"""
        try:
            start_time = time.time()
            # iam-manager 的健康检查端点
            response = self.session.get(
                f"{self.base_url.replace('/v3', '')}/health",  # 移除 v3 路径
                timeout=self.timeout
            )
            duration = time.time() - start_time

            metrics.record_api_request('GET', '/health', response.status_code, duration)

            if response.status_code == 200:
                logger.info("IAM API health check passed")
                return True
            else:
                logger.warning(f"IAM API health check failed: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"IAM API health check error: {e}")
            return False
    
    def get_user_by_name(self, username: str, domain_id: str = None) -> Optional[Dict[str, Any]]:
        """根据用户名查找用户 - 基于 iam-manager IdentityController"""
        try:
            token = self._get_auth_token()
            headers = {'X-Auth-Token': token}

            # 查询用户列表，按名称过滤
            params = {'name': username}
            if domain_id:
                params['domain_id'] = domain_id

            response = self._call_api_with_auth(
                method='GET',
                endpoint='/users',
                headers=headers,
                params=params
            )

            users = response.get('users', [])
            for user in users:
                if user.get('name') == username:
                    return user
            return None

        except Exception as e:
            logger.warning(f"Failed to get user {username}: {e}")
            return None

    def create_user(self, username: str, domain_id: str, enabled: bool = True) -> Dict[str, Any]:
        """创建用户 - 基于 iam-manager IdentityController"""
        token = self._get_auth_token()
        headers = {'X-Auth-Token': token}

        user_data = {
            "user": {
                "name": username,
                "domain_id": domain_id,
                "enabled": enabled,
                "description": f"Created by IAM-Operator for service credentials"
            }
        }

        return self._call_api_with_auth(
            method='POST',
            endpoint='/users',
            headers=headers,
            data=user_data
        )

    def create_access_key(self, user_id: str) -> Dict[str, Any]:
        """为用户创建 AccessKey - 基于 iam-manager AccessKeyController"""
        token = self._get_auth_token()
        headers = {'X-Auth-Token': token}

        return self._call_api_with_auth(
            method='POST',
            endpoint=f'/users/{user_id}/accesskeys',
            headers=headers
        )

    def list_user_access_keys(self, user_id: str) -> Dict[str, Any]:
        """列出用户的 AccessKey - 基于 iam-manager AccessKeyController"""
        token = self._get_auth_token()
        headers = {'X-Auth-Token': token}

        return self._call_api_with_auth(
            method='GET',
            endpoint=f'/users/{user_id}/accesskeys',
            headers=headers
        )

    def create_service_credentials(self, service_name: str, product_name: str, credentials: Dict[str, str]) -> Dict[str, Any]:
        """创建服务凭据 - 基于 iam-manager API 的高级封装"""
        try:
            # 1. 构造用户名（服务名 + 产品名）
            username = f"{service_name}-{product_name}"
            domain_id = config.get('iam_domain_id', 'default')

            # 2. 查找或创建用户
            user = self.get_user_by_name(username, domain_id)
            if not user:
                logger.info(f"Creating user: {username}")
                user_response = self.create_user(username, domain_id)
                user = user_response.get('user', {})

            user_id = user.get('id')
            if not user_id:
                raise IAMAPIError(f"Failed to get user ID for {username}")

            # 3. 创建 AccessKey
            logger.info(f"Creating AccessKey for user: {username}")
            ak_response = self.create_access_key(user_id)

            return {
                "status": "success",
                "message": f"Credentials created for {service_name}/{product_name}",
                "user_id": user_id,
                "username": username,
                "access_key_response": ak_response
            }

        except Exception as e:
            logger.error(f"Failed to create service credentials: {e}")
            raise IAMAPIError(f"Create credentials failed: {e}")
    
    def update_service_credentials(self, service_name: str, product_name: str, credentials: Dict[str, str]) -> Dict[str, Any]:
        """更新服务凭据 - 基于 iam-manager API"""
        # 对于 iam-manager，更新通常意味着重新创建 AccessKey
        # 先尝试获取现有用户，如果不存在则创建
        try:
            username = f"{service_name}-{product_name}"
            domain_id = config.get('iam_domain_id', 'default')

            user = self.get_user_by_name(username, domain_id)
            if not user:
                # 用户不存在，调用创建方法
                return self.create_service_credentials(service_name, product_name, credentials)

            # 用户存在，为其创建新的 AccessKey
            user_id = user.get('id')
            token = self._get_auth_token()
            headers = {'X-Auth-Token': token}

            ak_response = self._call_api_with_auth(
                method='POST',
                endpoint=f'/users/{user_id}/accesskeys',
                headers=headers
            )

            return {
                "status": "success",
                "message": f"Credentials updated for {service_name}/{product_name}",
                "user_id": user_id,
                "username": username,
                "access_key": ak_response
            }

        except Exception as e:
            logger.error(f"Failed to update service credentials: {e}")
            raise IAMAPIError(f"Update credentials failed: {e}")
    
    def delete_service_credentials(self, service_name: str, product_name: str) -> Dict[str, Any]:
        """删除服务凭据"""
        return self._call_api(
            method='DELETE',
            endpoint=f'/api/v1/service-credentials/{service_name}/{product_name}'
        )
    
    def get_service_credentials(self, service_name: str, product_name: str) -> Dict[str, Any]:
        """获取服务凭据"""
        return self._call_api(
            method='GET',
            endpoint=f'/api/v1/service-credentials/{service_name}/{product_name}'
        )
    
    def _call_api_with_auth(self, method: str, endpoint: str, headers: Dict[str, str] = None,
                           data: Dict[str, Any] = None, params: Dict[str, str] = None) -> Dict[str, Any]:
        """带认证的 API 调用方法 - 用于 iam-manager API"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()

        # 合并请求头
        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)

        try:
            if method.upper() == 'GET':
                response = self.session.get(url, headers=request_headers, params=params, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = self.session.post(url, headers=request_headers, json=data, timeout=self.timeout)
            elif method.upper() == 'PUT':
                response = self.session.put(url, headers=request_headers, json=data, timeout=self.timeout)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, headers=request_headers, timeout=self.timeout)
            else:
                raise IAMAPIError(f"Unsupported HTTP method: {method}")

            duration = time.time() - start_time
            metrics.record_api_request(method.upper(), endpoint, response.status_code, duration)

            # 检查响应状态
            if response.status_code >= 400:
                error_msg = f"IAM API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise IAMAPIError(error_msg)

            # 解析响应
            try:
                result = response.json()
                logger.debug(f"IAM API response: {result}")
                return result
            except ValueError:
                # 如果响应不是 JSON，返回状态码
                return {"status": "success", "status_code": response.status_code}

        except requests.exceptions.Timeout:
            error_msg = f"IAM API timeout: {url}"
            logger.error(error_msg)
            raise IAMAPIError(error_msg)
        except requests.exceptions.ConnectionError:
            error_msg = f"IAM API connection error: {url}"
            logger.error(error_msg)
            raise IAMAPIError(error_msg)
        except Exception as e:
            error_msg = f"IAM API unexpected error: {e}"
            logger.error(error_msg)
            raise IAMAPIError(error_msg)

    def _call_api(self, method: str, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """统一的 API 调用方法"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, timeout=self.timeout)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data, timeout=self.timeout)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, timeout=self.timeout)
            else:
                raise IAMAPIError(f"Unsupported HTTP method: {method}")
            
            duration = time.time() - start_time
            metrics.record_api_request(method.upper(), endpoint, response.status_code, duration)
            
            # 检查响应状态
            if response.status_code >= 400:
                error_msg = f"IAM API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise IAMAPIError(error_msg)
            
            # 解析响应
            try:
                result = response.json()
                logger.debug(f"IAM API response: {result}")
                return result
            except ValueError:
                # 如果响应不是 JSON，返回状态码
                return {"status": "success", "status_code": response.status_code}
                
        except requests.exceptions.Timeout:
            error_msg = f"IAM API timeout: {url}"
            logger.error(error_msg)
            raise IAMAPIError(error_msg)
        except requests.exceptions.ConnectionError:
            error_msg = f"IAM API connection error: {url}"
            logger.error(error_msg)
            raise IAMAPIError(error_msg)
        except Exception as e:
            error_msg = f"IAM API unexpected error: {e}"
            logger.error(error_msg)
            raise IAMAPIError(error_msg)
