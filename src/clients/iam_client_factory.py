"""
IAM 客户端工厂 - 根据配置选择合适的客户端实现
"""
import logging
from typing import Union
from ..utils.config import config
from .iam_client import IAMClient
from .iam_service_discovery import IAMServiceDiscoveryClient

logger = logging.getLogger(__name__)


class IAMClientFactory:
    """IAM 客户端工厂类"""
    
    @staticmethod
    def create_client() -> Union[IAMClient, IAMServiceDiscoveryClient]:
        """
        根据配置创建合适的 IAM 客户端
        
        Returns:
            Union[IAMClient, IAMServiceDiscoveryClient]: IAM 客户端实例
        """
        if config.iam_service_discovery_enabled:
            logger.info("Using IAM Service Discovery Client")
            return IAMServiceDiscoveryClient(
                iam_namespace=config.iam_service_namespace,
                service_name=config.iam_service_name
            )
        else:
            logger.info("Using static IAM Client")
            return IAMClient()
    
    @staticmethod
    def test_both_clients():
        """
        测试两种客户端实现
        
        Returns:
            dict: 测试结果
        """
        results = {
            'static_client': {'available': False, 'error': None},
            'service_discovery_client': {'available': False, 'error': None}
        }
        
        # 测试静态客户端
        try:
            static_client = IAMClient()
            if static_client.health_check():
                results['static_client']['available'] = True
                logger.info("Static IAM client is available")
            else:
                results['static_client']['error'] = "Health check failed"
        except Exception as e:
            results['static_client']['error'] = str(e)
            logger.warning(f"Static IAM client failed: {e}")
        
        # 测试服务发现客户端
        try:
            discovery_client = IAMServiceDiscoveryClient(
                iam_namespace=config.iam_service_namespace,
                service_name=config.iam_service_name
            )
            test_result = discovery_client.test_service_discovery()
            if test_result['service_discovery']:
                results['service_discovery_client']['available'] = True
                results['service_discovery_client']['endpoint'] = test_result['endpoint']
                results['service_discovery_client']['service_info'] = test_result['service_info']
                logger.info("Service discovery IAM client is available")
            else:
                results['service_discovery_client']['error'] = test_result.get('error', 'Unknown error')
        except Exception as e:
            results['service_discovery_client']['error'] = str(e)
            logger.warning(f"Service discovery IAM client failed: {e}")
        
        return results


# 全局客户端实例（延迟初始化）
_global_iam_client = None


def get_iam_client() -> Union[IAMClient, IAMServiceDiscoveryClient]:
    """
    获取全局 IAM 客户端实例（单例模式）
    
    Returns:
        Union[IAMClient, IAMServiceDiscoveryClient]: IAM 客户端实例
    """
    global _global_iam_client
    
    if _global_iam_client is None:
        _global_iam_client = IAMClientFactory.create_client()
    
    return _global_iam_client


def reset_iam_client():
    """重置全局 IAM 客户端实例（用于测试或配置更改后）"""
    global _global_iam_client
    _global_iam_client = None
    logger.info("Global IAM client instance reset")
