"""
配置解析器
"""
import re
import logging
from typing import Dict, Any
from ..utils.exceptions import ConfigParseError

logger = logging.getLogger(__name__)


class ConfigParser:
    """配置解析器"""
    
    def parse_configmap_credentials(self, configmap_data: Dict[str, str]) -> Dict[str, Dict[str, str]]:
        """解析 ConfigMap 中的 IAM 凭据"""
        credentials = {}
        
        for file_name, content in configmap_data.items():
            try:
                if self._is_php_config(file_name, content):
                    parsed = self._parse_php_credentials(content)
                    credentials.update(parsed)
                elif self._is_yaml_config(file_name, content):
                    parsed = self._parse_yaml_credentials(content)
                    credentials.update(parsed)
                elif self._is_json_config(file_name, content):
                    parsed = self._parse_json_credentials(content)
                    credentials.update(parsed)
            except Exception as e:
                logger.warning(f"Failed to parse config file {file_name}: {e}")
                continue
        
        return credentials
    
    def _is_php_config(self, file_name: str, content: str) -> bool:
        """判断是否为 PHP 配置文件"""
        return (
            file_name.endswith('.php') or 
            'conf.php' in file_name or
            ('<?php' in content and '$' in content)
        )
    
    def _is_yaml_config(self, file_name: str, content: str) -> bool:
        """判断是否为 YAML 配置文件"""
        return (
            file_name.endswith('.yaml') or 
            file_name.endswith('.yml') or
            ('iam:' in content and 'ak:' in content)
        )
    
    def _is_json_config(self, file_name: str, content: str) -> bool:
        """判断是否为 JSON 配置文件"""
        return (
            file_name.endswith('.json') or
            (content.strip().startswith('{') and '"iam"' in content)
        )
    
    def _parse_php_credentials(self, php_content: str) -> Dict[str, Dict[str, str]]:
        """解析 PHP 配置文件中的 IAM 凭据"""
        credentials = {}
        
        # 定义匹配模式
        patterns = {
            'ak': r'\$(\w+)_ak\s*=\s*[\'"]([^\'"]+)[\'"]',
            'sk': r'\$(\w+)_sk\s*=\s*[\'"]([^\'"]+)[\'"]',
            'password': r'\$(\w+)_passwd\s*=\s*[\'"]([^\'"]+)[\'"]',
            'userId': r'\$(\w+)_uid\s*=\s*[\'"]([^\'"]+)[\'"]'
        }
        
        # 收集所有产品的凭据
        products = {}
        for field, pattern in patterns.items():
            matches = re.findall(pattern, php_content, re.IGNORECASE)
            for product, value in matches:
                if product not in products:
                    products[product] = {}
                products[product][field] = value
        
        # 只返回包含 ak 和 sk 的完整凭据
        for product, creds in products.items():
            if 'ak' in creds and 'sk' in creds:
                credentials[product] = creds
                logger.debug(f"Parsed PHP credentials for product: {product}")
        
        return credentials
    
    def _parse_yaml_credentials(self, yaml_content: str) -> Dict[str, Dict[str, str]]:
        """解析 YAML 配置文件中的 IAM 凭据"""
        try:
            import yaml
            data = yaml.safe_load(yaml_content)
            
            credentials = {}
            
            # 查找 iam 配置
            iam_config = self._find_iam_config(data)
            if iam_config:
                for product, creds in iam_config.items():
                    if isinstance(creds, dict) and 'ak' in creds and 'sk' in creds:
                        credentials[product] = {
                            'ak': str(creds.get('ak', '')),
                            'sk': str(creds.get('sk', '')),
                            'password': str(creds.get('password', '')),
                            'userId': str(creds.get('userId', ''))
                        }
                        logger.debug(f"Parsed YAML credentials for product: {product}")
            
            return credentials
            
        except Exception as e:
            raise ConfigParseError(f"Failed to parse YAML config: {e}")
    
    def _parse_json_credentials(self, json_content: str) -> Dict[str, Dict[str, str]]:
        """解析 JSON 配置文件中的 IAM 凭据"""
        try:
            import json
            data = json.loads(json_content)
            
            credentials = {}
            
            # 查找 iam 配置
            iam_config = self._find_iam_config(data)
            if iam_config:
                for product, creds in iam_config.items():
                    if isinstance(creds, dict) and 'ak' in creds and 'sk' in creds:
                        credentials[product] = {
                            'ak': str(creds.get('ak', '')),
                            'sk': str(creds.get('sk', '')),
                            'password': str(creds.get('password', '')),
                            'userId': str(creds.get('userId', ''))
                        }
                        logger.debug(f"Parsed JSON credentials for product: {product}")
            
            return credentials
            
        except Exception as e:
            raise ConfigParseError(f"Failed to parse JSON config: {e}")
    
    def _find_iam_config(self, data: Any) -> Dict[str, Any]:
        """在嵌套数据结构中查找 IAM 配置"""
        if isinstance(data, dict):
            # 直接查找 iam 键
            if 'iam' in data:
                return data['iam']
            
            # 查找嵌套路径
            paths_to_check = [
                ['appspace', 'charts', 'requires', 'iam'],
                ['charts', 'iam'],
                ['requires', 'iam']
            ]
            
            for path in paths_to_check:
                current = data
                for key in path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        current = None
                        break
                
                if current and isinstance(current, dict):
                    return current
        
        return {}


# 全局解析器实例
parser = ConfigParser()
