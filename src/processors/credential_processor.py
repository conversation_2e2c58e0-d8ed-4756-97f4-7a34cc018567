"""
凭据处理引擎
"""
import hashlib
import json
import time
import logging
import os
from typing import Dict, Any, Optional
from kubernetes import client, config as k8s_config
from ..clients.iam_client import IAMClient
from ..clients.mock_iam_client import MockIAMClient
from ..clients.iam_client_factory import get_iam_client
from ..utils.config import config
from ..utils.exceptions import IAMAPIError, ValidationError
from ..utils.metrics import metrics

logger = logging.getLogger(__name__)


class CredentialProcessor:
    """统一的凭据处理引擎"""
    
    def __init__(self):
        # 根据环境变量选择 IAM 客户端
        use_mock = config.get('use_mock_iam', False) or os.getenv('USE_MOCK_IAM', 'false').lower() == 'true'

        if use_mock:
            self.iam_client = MockIAMClient()
            logger.info("使用 Mock IAM 客户端")
        else:
            # 使用客户端工厂，支持服务发现
            self.iam_client = get_iam_client()
            client_type = "服务发现" if config.iam_service_discovery_enabled else "静态配置"
            logger.info(f"使用 {client_type} IAM 客户端")

        self.k8s_client = client.CoreV1Api()
        self._state_cache = {}
    
    def process_credentials(self, source_type: str, source_name: str, credentials: Dict[str, Dict[str, str]]):
        """统一的凭据处理逻辑 - 避免重复代码"""
        
        logger.info(f"Processing credentials from {source_type}: {source_name}")
        
        for product_name, creds in credentials.items():
            start_time = time.time()
            
            try:
                # 边界检查：验证凭据完整性
                if not self._is_valid_credentials(creds):
                    logger.warning(f"跳过无效凭据: {source_name}/{product_name}")
                    metrics.record_credential_processed(source_type, product_name, 'invalid')
                    continue
                
                # 幂等性检查：避免重复处理
                if not self._needs_update(source_name, product_name, creds):
                    logger.info(f"凭据无变化，跳过: {source_name}/{product_name}")
                    metrics.record_credential_processed(source_type, product_name, 'skipped')
                    continue
                
                # 调用 IAM API
                self._update_iam_credentials(source_name, product_name, creds)
                
                # 记录成功状态
                self._record_success(source_name, product_name, creds)
                metrics.record_credential_processed(source_type, product_name, 'success')
                
                logger.info(f"Successfully processed credentials: {source_name}/{product_name}")
                
            except Exception as e:
                # 记录失败状态
                self._record_failure(source_name, product_name, str(e))
                metrics.record_credential_processed(source_type, product_name, 'failed')
                
                logger.error(f"Failed to process credentials {source_name}/{product_name}: {e}")
                
                # 对于 IAM API 错误，抛出异常以触发重试
                if isinstance(e, IAMAPIError):
                    raise
            
            finally:
                # 记录处理耗时
                duration = time.time() - start_time
                metrics.record_processing_duration(source_type, product_name, duration)
    
    def _is_valid_credentials(self, creds: Dict[str, str]) -> bool:
        """最小验证：只检查必需字段"""
        if not isinstance(creds, dict):
            return False
        
        ak = creds.get('ak', '').strip()
        sk = creds.get('sk', '').strip()
        
        # 检查必需字段
        if not ak or not sk:
            return False
        
        # 检查字段长度（基本合理性检查）
        if len(ak) < 10 or len(sk) < 10:
            logger.warning(f"Credentials seem too short: ak={len(ak)}, sk={len(sk)}")
            return False
        
        return True
    
    def _needs_update(self, source_name: str, product_name: str, creds: Dict[str, str]) -> bool:
        """幂等性检查：避免重复处理"""
        try:
            current_hash = self._hash_credentials(creds)
            last_processed_hash = self._get_last_processed_hash(source_name, product_name)
            
            return current_hash != last_processed_hash
            
        except Exception as e:
            logger.warning(f"Failed to check update status: {e}")
            # 如果无法检查状态，默认需要更新
            return True
    
    def _hash_credentials(self, creds: Dict[str, str]) -> str:
        """计算凭据哈希值"""
        # 只对关键字段计算哈希，忽略顺序
        key_fields = ['ak', 'sk', 'password', 'userId']
        hash_data = {}
        
        for field in key_fields:
            if field in creds:
                hash_data[field] = creds[field]
        
        hash_string = json.dumps(hash_data, sort_keys=True)
        return hashlib.md5(hash_string.encode()).hexdigest()
    
    def _get_last_processed_hash(self, source_name: str, product_name: str) -> Optional[str]:
        """获取上次处理的哈希值"""
        try:
            state_key = f"{source_name}/{product_name}"
            
            # 先从缓存获取
            if state_key in self._state_cache:
                return self._state_cache[state_key].get('hash')
            
            # 从 ConfigMap 获取
            try:
                cm = self.k8s_client.read_namespaced_config_map(
                    name=config.get('state_configmap_name'),
                    namespace=config.get('state_configmap_namespace')
                )
                
                if cm.data and state_key in cm.data:
                    state_data = json.loads(cm.data[state_key])
                    self._state_cache[state_key] = state_data
                    return state_data.get('hash')
                    
            except client.exceptions.ApiException as e:
                if e.status == 404:
                    # ConfigMap 不存在，创建一个
                    self._create_state_configmap()
                else:
                    logger.warning(f"Failed to read state ConfigMap: {e}")
            
            return None
            
        except Exception as e:
            logger.warning(f"Failed to get last processed hash: {e}")
            return None
    
    def _update_iam_credentials(self, service_name: str, product_name: str, creds: Dict[str, str]):
        """调用 IAM API 更新凭据"""
        try:
            # 首先尝试更新
            result = self.iam_client.update_service_credentials(service_name, product_name, creds)
            logger.debug(f"Updated credentials via IAM API: {result}")
            
        except IAMAPIError as e:
            # 如果更新失败（可能是不存在），尝试创建
            if '404' in str(e) or 'not found' in str(e).lower():
                logger.info(f"Credentials not found, creating new: {service_name}/{product_name}")
                result = self.iam_client.create_service_credentials(service_name, product_name, creds)
                logger.debug(f"Created credentials via IAM API: {result}")
            else:
                raise
    
    def _record_success(self, source_name: str, product_name: str, creds: Dict[str, str]):
        """记录成功状态"""
        state_data = {
            'timestamp': time.time(),
            'status': 'success',
            'hash': self._hash_credentials(creds),
            'error': None
        }
        
        self._update_state(source_name, product_name, state_data)
    
    def _record_failure(self, source_name: str, product_name: str, error: str):
        """记录失败状态"""
        state_data = {
            'timestamp': time.time(),
            'status': 'failed',
            'hash': None,
            'error': error
        }
        
        self._update_state(source_name, product_name, state_data)
    
    def _update_state(self, source_name: str, product_name: str, state_data: Dict[str, Any]):
        """更新状态存储"""
        try:
            state_key = f"{source_name}/{product_name}"
            
            # 更新缓存
            self._state_cache[state_key] = state_data
            
            # 更新 ConfigMap
            try:
                cm = self.k8s_client.read_namespaced_config_map(
                    name=config.get('state_configmap_name'),
                    namespace=config.get('state_configmap_namespace')
                )
                
                if not cm.data:
                    cm.data = {}
                
                cm.data[state_key] = json.dumps(state_data)
                
                self.k8s_client.patch_namespaced_config_map(
                    name=config.get('state_configmap_name'),
                    namespace=config.get('state_configmap_namespace'),
                    body=cm
                )
                
            except client.exceptions.ApiException as e:
                if e.status == 404:
                    self._create_state_configmap({state_key: json.dumps(state_data)})
                else:
                    logger.warning(f"Failed to update state ConfigMap: {e}")
                    
        except Exception as e:
            logger.warning(f"Failed to update state: {e}")
    
    def _create_state_configmap(self, initial_data: Dict[str, str] = None):
        """创建状态存储 ConfigMap"""
        try:
            cm = client.V1ConfigMap(
                metadata=client.V1ObjectMeta(
                    name=config.get('state_configmap_name'),
                    namespace=config.get('state_configmap_namespace')
                ),
                data=initial_data or {}
            )
            
            self.k8s_client.create_namespaced_config_map(
                namespace=config.get('state_configmap_namespace'),
                body=cm
            )
            
            logger.info("Created state ConfigMap")
            
        except Exception as e:
            logger.error(f"Failed to create state ConfigMap: {e}")


# 全局处理器实例
processor = CredentialProcessor()
