appspace:
  charts:
    images:
      xdb-operator:
        repository: abc-stack/xdb-operator
        imageTag: ${xdb-operator.image.tag}
    provides: {}
    configVals:
      pullPolicy: IfNotPresent
      globalResourceDeletePolicy: retain
      DefaultResourceDeletePolicy: retain
      defaultProxyImageTag: 1.0.0.7
      defaultMysqlImageTag: 1.0.0.5
    ingress:
      dns:
        enable: false
      apptree:
        enable: true
    monitor:
      noaheePro:
        enable: true
    namespace: cloudbed-system
    replica: 3
    platformConfigVals:
      appName: xdb-operator
    # global:
    #   AbcstackGlobalUseHttps: false
    #   registry: registry.dev7.abcstackint.com:5000      #需要替换成具体环境的地址

fullnameOverride: ""
namespaceOverride: cloudbed-system
resources:
  limits:
    cpu: 4000m
    memory: 8Gi
  requests:
    cpu: 500m
    memory: 300Mi

storage:
  className: "local-path"  # 存储类名称
  size: "10Gi"  
  # 存储大小