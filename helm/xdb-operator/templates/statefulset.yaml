{{- $imagePullPolicies := list "ifnotpresent" "always" "never" }}
{{- if (((.Values).image).pullPolicy) }}
  {{- if not (has (lower (((.Values).image).pullPolicy)) ($imagePullPolicies)) }}
    {{- $err := printf "Unknown image pull policy %s. Must be one of %v" (((.Values).image).pullPolicy) $imagePullPolicies }}
    {{- fail $err }}
  {{- end }}
{{- end }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ template "xdboperator.fullname" . }}
  namespace: {{ template "xdboperator.namespace" . }}
  labels:
    chartversion: "{{ default .Chart.AppVersion }}"
    app.kubernetes.io/name: xdb-operator
    operatorversion: "{{ index .Values.appspace.images "xdb-operator" "imageTag"}}"
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: "{{ default .Chart.AppVersion }}"
    app.kubernetes.io/component: xdb-operator
    app.kubernetes.io/managed-by: helm
    app.kubernetes.io/created-by: helm
spec:
  serviceName: "xdb-operator"
  replicas: 1
  selector:
    matchLabels:
      name: xdb-operator
  template:
    metadata:
      labels:
        name: xdb-operator
        chartversion: "{{ default .Chart.AppVersion }}"
        app.kubernetes.io/name: xdb-operator
        operatorversion: "{{ index .Values.appspace.images "xdb-operator" "imageTag"}}"
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/version: "{{ default .Chart.AppVersion }}"
        app.kubernetes.io/component: xdb-operator
        app.kubernetes.io/managed-by: helm
        app.kubernetes.io/created-by: helm
    spec:
      serviceAccountName: xdb-operator-sa
      containers:
        - name: xdb-operator
          image: {{ template "xdboperator.image" . }}
          imagePullPolicy: IfNotPresent
          args: ["/usr/local/bin/kopf", "run","/xdb/xdboperator/controller/operator.py", "-A", "--verbose", "--liveness=http://0.0.0.0:8080/healthz"]
          env:
          - name: XDB_OPERATOR_DEFAULT_REPOSITORY
            value: {{ index .Values.appspace.images "xdb-operator" "repository"}} 
          - name: GLOBAL_RESOURCE_DELETE_POLICY
            value: {{ .Values.appspace.charts.configVals.globalResourceDeletePolicy }}
          - name: DEFAULT_RESOURCE_DELETE_POLICY
            value: {{.Values.appspace.charts.configVals.DefaultResourceDeletePolicy }}
          - name: XDB_OPERATOR_DEFAULT_REGISTRY
            value: {{ template "default.registry" . }}
          - name: MYSQL_OPERATOR_IMAGE_PULL_POLICY
            value: {{ .Values.appspace.charts.configVals.pullPolicy }}
          - name: DEFAULT_OPERATOR_VERSION_TAG
            value: {{ index .Values.appspace.images "xdb-operator" "imageTag"}} 
          - name: DEFAULT_PROXY_VERSION_TAG
            value: {{ .Values.appspace.charts.configVals.defaultProxyImageTag }} 
          - name: DEFAULT_MYSQL_VERSION_TAG
            value: {{ .Values.appspace.charts.configVals.defaultMysqlImageTag }}  
          - name: XDB_BACKUP_BASE_DIR
            value: "/var/lib/xdb/backups"
          readinessProbe:
            exec:
              command:
              - cat
              - /tmp/xdb-operator-ready
            initialDelaySeconds: 1
            periodSeconds: 3
          livenessProbe:
            timeoutSeconds: 10
            initialDelaySeconds: 60
            periodSeconds: 60
            failureThreshold: 10
            successThreshold: 1
            httpGet:
              path: /healthz
              port: 8080
          volumeMounts:
            - mountPath: /opt
              name: opt
            - mountPath: /var/lib/xdb/backups
              name: backup-storage
          securityContext:
            runAsUser: 0
            allowPrivilegeEscalation: true
            privileged: true
            readOnlyRootFilesystem: false
          {{- if .Values.resources }}
          resources: {{ toYaml .Values.resources | nindent 12 }}
          {{- end }}
      volumes:
        - hostPath:
           path: /opt
           type: ""
          name: opt
  volumeClaimTemplates:
  - metadata:
      name: backup-storage
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: {{ .Values.storage.className | quote }}
      resources:
        requests:
          storage: {{ .Values.storage.size }}
      serviceAccountName: xdb-operator-sa