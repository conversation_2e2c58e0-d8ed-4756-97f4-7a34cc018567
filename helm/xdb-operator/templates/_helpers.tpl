{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}

{{- define "xdboperator.fullname" -}}
{{- if .Values.fullnameOverride -}}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" -}}
{{- else -}}
{{- $name := default  .Values.appspace.charts.platformConfigVals.appName .Values.nameOverride -}}
{{- if contains $name .Release.Name -}}
{{- .Release.Name | trunc 63 | trimSuffix "-" -}}
{{- else -}}
{{- printf "%s" $name | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}
{{- end -}}



{{- define "xdboperator.namespace" -}}
{{- if .Values.namespaceOverride -}}
    {{- .Values.namespaceOverride -}}
{{- else -}}
    {{- .Release.Namespace -}}
{{- end -}}
{{- end -}}



{{- define "xdboperator.image" -}}
{{- $registry := .Values.appspace.charts.global.registry -}}
{{- $repository :=  (index $.Values.appspace.images "xdb-operator" "repository") -}}
{{- $tag := (index $.Values.appspace.images "xdb-operator" "imageTag")  -}}
{{- printf "%s/%s:%s" $registry $repository $tag -}}
{{- end -}}

{{- define "default.registry" -}}
{{- $registry := .Values.appspace.charts.global.registry -}}
{{- printf "%s/abc-stack" $registry  -}}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "appspace.labels.selector" -}}
karrier.abcstack.com/app: {{.Values.appspace.charts.platformConfigVals.appName }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "xdboperator.labels" -}}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
helm.sh/chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
{{- end }}