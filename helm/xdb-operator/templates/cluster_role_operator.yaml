# The main role for the operator
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: xdb-operator
rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch", "patch"]
  - apiGroups: [""]
    resources: ["pods/status"]
    verbs: ["get", "patch", "update", "watch"]
    # <PERSON><PERSON> needs patch on secrets or the sidecar will throw
    # The operator needs this verb to be able to pass it to the sidecar
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "create", "list", "watch", "patch"]
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "create", "update", "list", "watch", "patch", "delete"]
  - apiGroups: [""]
    resources: ["services"]
    verbs: ["get", "create", "list", "update", "delete", "patch"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get", "create", "list", "update", "delete", "patch"]
  - apiGroups: [""]
    resources: ["serviceaccounts"]
    verbs: ["get", "create", "patch"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch", "update"]
  - apiGroups: ["rbac.authorization.k8s.io"]
    resources: ["rolebindings"]
    verbs: ["get", "create"]
  - apiGroups: ["policy"]
    resources: ["poddisruptionbudgets"]
    verbs: ["get", "create"]
  - apiGroups: ["batch"]
    resources: ["jobs"]
    verbs: ["create"]
  - apiGroups: ["batch"]
    resources: ["cronjobs"]
    verbs: ["get", "create", "update", "delete"]
  - apiGroups: ["apps"]
    resources: ["deployments", "statefulsets"]
    verbs: ["get", "create", "patch", "update", "watch", "delete"]
  - apiGroups: ["cloudbed.abcstack.com"]
    resources: ["*"]
    verbs: ["*"]
  # Framework: knowing which other operators are running (i.e. peering).
  - apiGroups: ["kopf.dev"]
    resources: ["*"]
    verbs: ["get", "patch", "list", "watch"]
  # Kopf: runtime observation of namespaces & CRDs (addition/deletion).
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["list", "watch"]
  - apiGroups: [""]
    resources: ["namespaces"]
    verbs: ["list", "watch"]
  - apiGroups: [ "monitoring.coreos.com" ]
    resources: [ "servicemonitors" ]
    verbs: [ "get", "create", "patch", "update", "delete" ]
  - apiGroups: [""]
    resources: ["pods/exec"]
    verbs: ["*"]
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["get", "list", "watch"]
