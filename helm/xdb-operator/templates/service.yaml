apiVersion: v1
kind: Service
metadata:
  name: {{ template "xdboperator.fullname" . }}
  namespace: {{ template "xdboperator.namespace" . }}
  labels:
    app.kubernetes.io/name: xdb-operator
    app.kubernetes.io/instance: {{ .Release.Name }}
    {{- include "xdboperator.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  ports:
  - port: 8080
    name: healthz
  clusterIP: None
  selector:
    name: xdb-operator