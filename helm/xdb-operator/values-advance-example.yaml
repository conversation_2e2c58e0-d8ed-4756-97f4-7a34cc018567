appspace:
  affinity: null
  charts:
    configVals:
      DefaultResourceDeletePolicy: retain
      globalResourceDeletePolicy: retain
      pullPolicy: IfNotPresent
      defaultProxyImageTag: 1.0.0.7
      defaultMysqlImageTag: 1.0.0.5
    global:
      regions:
        - region_zh: 西安
          azones:
            - name: 康定
              az: kangding
          subdomain: ''
          region: xian
          region_type: central
          k8sPodCIDR: 10.6.80.0/21
          k8sSvcCIDR: 100.69.0.0/16
          clusters: []
      azones:
        - name: 康定
          az: kangding
      AbcstackGlobalUseHttps: false
      registry: registry.dev7.abcstackint.com:5000
      domain: .dev7.abcstackint.com
    middleware:
      xdb: {}
      zookeeper: null
      etcd: null
      redis: null
      es: {}
      kafka: {}
    platformConfigVals:
      appName: xdb-operator
      azone: kangding
      level: global
      region: xian
      componentName: xdb-operator
      componentVersion: 3.3.1-2
      deployMode: k8s
      category: 基础
      subsystem: BaseXDBOperator
      subsystemCn: 公共基础服务xdb-operator
      integrationTest: {}
      autoInstall: false
      deployStage: 基础服务阶段
      configVersion: 1
    provides: {}
    requires: {}
  namespace: cloudbed-system
  replica: 3
  workload:
    kind: ''
  ssl:
    enableSSL: false
    certSecretRefs: []
    certMountPath: ''
  images:
    xdb-operator:
      imageTag: 1.0.0.24
      repository: abc-stack/xdb-operator
  ingress:
    apptree:
      enable: true
      subsystem: 公共基础服务xdb-operator
      subsystemEn: BaseXDBOperator
    dns:
      enable: false
  monitor:
    noaheePro:
      enable: true
  provides: {}

resources:
  limits:
    cpu: 4000m
    memory: 8Gi
  requests:
    cpu: 500m
    memory: 300Mi
fullnameOverride: ''
namespaceOverride: cloudbed-system


