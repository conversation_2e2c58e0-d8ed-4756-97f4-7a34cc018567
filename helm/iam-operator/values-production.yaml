# IAM-Operator 生产环境配置

# 镜像配置
image:
  repository: registry.prod.abcstackint.com:5000/abc-stack/iam-operator
  tag: "1.0.0"
  pullPolicy: IfNotPresent

# 副本数（生产环境建议多副本）
replicaCount: 2

# 命名空间配置
namespaceOverride: "base"

# 资源配置（生产环境）
resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 200m
    memory: 256Mi

# 节点选择器（选择稳定的节点）
nodeSelector:
  node-role.kubernetes.io/worker: "true"

# 容忍度（容忍一些污点）
tolerations:
- key: "node.kubernetes.io/disk-pressure"
  operator: "Exists"
  effect: "NoSchedule"
- key: "node.kubernetes.io/memory-pressure"
  operator: "Exists"
  effect: "NoSchedule"

# IAM API 配置（生产环境启用服务发现）
iamApi:
  url: "http://iam-manage-xian.console.svc.cluster.local:8468"  # 作为fallback
  timeout: 30

  # 启用服务发现
  serviceDiscovery:
    enabled: true
    namespace: "console"
    serviceName: "iam-manage-xian"

# 亲和性（避免单点故障）
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - iam-operator
        topologyKey: kubernetes.io/hostname

# IAM API 配置（生产环境）
iamApi:
  url: "http://iam-openapi.console.svc.cluster.local:8480"
  timeout: 60

# 处理配置（生产环境调优）
processing:
  timeout: 60
  retryInterval: 120
  maxRetries: 5

# 监控配置
metrics:
  enabled: true
  port: 8080
  path: /metrics

# 日志配置（生产环境使用 INFO 级别）
logging:
  level: INFO

# 目标命名空间配置
targetNamespaces:
  - security
  - console
  - billing
  - payment
  - order
  - storage

# 探针配置（生产环境调优）
probes:
  liveness:
    enabled: true
    initialDelaySeconds: 60
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3
  readiness:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

# 自动扩缩容（生产环境启用）
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Pod 中断预算（生产环境启用）
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# 监控集成（生产环境启用）
monitoring:
  serviceMonitor:
    enabled: true
    namespace: monitoring
    interval: 30s
    scrapeTimeout: 10s
    labels:
      team: platform
      component: iam-operator

# 网络策略（生产环境启用）
networkPolicy:
  enabled: true

# 更新策略（生产环境保守策略）
updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 1
    maxSurge: 0

# 终止宽限期（生产环境延长）
terminationGracePeriodSeconds: 60

# Pod 注解（生产环境监控）
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8080"
  prometheus.io/path: "/metrics"
  cluster-autoscaler.kubernetes.io/safe-to-evict: "true"

# Pod 标签（生产环境标识）
podLabels:
  environment: production
  team: platform
  component: iam-operator
