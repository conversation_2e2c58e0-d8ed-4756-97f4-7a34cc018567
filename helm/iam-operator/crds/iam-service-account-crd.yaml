apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: iamserviceaccounts.iam.example.com
  labels:
    app: iam-operator
spec:
  group: iam.example.com
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              serviceName:
                type: string
                description: "服务名称，如 waf-meta"
                minLength: 1
                maxLength: 63
                pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
              products:
                type: object
                description: "产品凭据映射"
                additionalProperties:
                  type: object
                  properties:
                    ak:
                      type: string
                      description: "Access Key"
                      minLength: 10
                    sk:
                      type: string
                      description: "Secret Key"
                      minLength: 10
                    password:
                      type: string
                      description: "密码"
                    userId:
                      type: string
                      description: "用户ID"
                  required: ["ak", "sk"]
              autoRotate:
                type: boolean
                description: "是否启用自动轮换"
                default: false
              rotationInterval:
                type: string
                description: "轮换间隔，如 30d, 7d"
                default: "30d"
                pattern: '^[0-9]+[dwmy]$'
            required: ["serviceName", "products"]
          status:
            type: object
            properties:
              lastUpdated:
                type: string
                format: date-time
                description: "最后更新时间"
              conditions:
                type: array
                description: "状态条件"
                items:
                  type: object
                  properties:
                    type:
                      type: string
                      description: "条件类型"
                    status:
                      type: string
                      enum: ["True", "False", "Unknown"]
                      description: "条件状态"
                    lastTransitionTime:
                      type: string
                      format: date-time
                      description: "最后转换时间"
                    reason:
                      type: string
                      description: "原因"
                    message:
                      type: string
                      description: "详细消息"
                  required: ["type", "status"]
              processedProducts:
                type: integer
                description: "已处理的产品数量"
                minimum: 0
    additionalPrinterColumns:
    - name: Service
      type: string
      description: "服务名称"
      jsonPath: .spec.serviceName
    - name: Products
      type: integer
      description: "产品数量"
      jsonPath: .status.processedProducts
    - name: Status
      type: string
      description: "状态"
      jsonPath: .status.conditions[?(@.type=="Ready")].status
    - name: Age
      type: date
      jsonPath: .metadata.creationTimestamp
    subresources:
      status: {}
  scope: Namespaced
  names:
    plural: iamserviceaccounts
    singular: iamserviceaccount
    kind: IAMServiceAccount
    shortNames:
    - iamsa
