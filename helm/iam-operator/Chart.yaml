apiVersion: v2
name: iam-operator
description: IAM Operator Helm Chart for managing IAM credentials in Kubernetes
type: application
version: "1.0.0"
appVersion: "1.0.0"

keywords:
  - iam
  - operator
  - credentials
  - authentication

home: https://github.com/your-org/iam-operator
sources:
  - https://github.com/your-org/iam-operator

maintainers:
  - name: IAM Team
    email: <EMAIL>

annotations:
  arch: x86_64
  buildInfo: 'IAM Operator for managing service credentials'
  category: 基础
  chartMode: native
  dependencies: ''
  deployStage: 基础服务阶段
  grade: 关键组件
  impactRange: 影响服务 IAM 凭据管理，不影响已有凭据
  isStatus: true
  level: global
  licenses: '- Apache-2.0'
  maxReplica: 3
  minReplica: 1
  os: 'linux'
  statefulProtocol: '-'
  stepReplica: 1
  subsystem: 云底座插件
  subsystemEn: KarrierPlugins
