apiVersion: v1
kind: Service
metadata:
  name: {{ include "iam-operator.fullname" . }}
  namespace: {{ include "iam-operator.namespace" . }}
  labels:
    {{- include "iam-operator.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
  - name: metrics
    port: {{ .Values.service.metricsPort }}
    targetPort: metrics
    protocol: TCP
  - name: health
    port: {{ .Values.service.healthPort }}
    targetPort: health
    protocol: TCP
  selector:
    {{- include "iam-operator.selectorLabels" . | nindent 4 }}
