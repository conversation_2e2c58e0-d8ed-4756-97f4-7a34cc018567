{{- if .Values.podDisruptionBudget.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "iam-operator.fullname" . }}
  namespace: {{ include "iam-operator.namespace" . }}
  labels:
    {{- include "iam-operator.labels" . | nindent 4 }}
spec:
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  selector:
    matchLabels:
      {{- include "iam-operator.selectorLabels" . | nindent 6 }}
{{- end }}
