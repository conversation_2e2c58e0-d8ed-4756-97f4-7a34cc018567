apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "iam-operator.fullname" . }}
  namespace: {{ include "iam-operator.namespace" . }}
  labels:
    {{- include "iam-operator.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "iam-operator.selectorLabels" . | nindent 6 }}
  {{- with .Values.updateStrategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  template:
    metadata:
      labels:
        {{- include "iam-operator.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      annotations:
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ include "iam-operator.serviceAccountName" . }}
      {{- with .Values.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.initContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: iam-operator
        image: {{ include "iam-operator.image" . }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        command:
        - python
        - -m
        - src.main
        env:
        {{- include "iam-operator.iamApiEnv" . | nindent 8 }}
        {{- include "iam-operator.processingEnv" . | nindent 8 }}
        {{- include "iam-operator.monitoringEnv" . | nindent 8 }}
        {{- include "iam-operator.loggingEnv" . | nindent 8 }}
        {{- include "iam-operator.namespacesEnv" . | nindent 8 }}
        {{- include "iam-operator.stateStorageEnv" . | nindent 8 }}
        {{- with .Values.env }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.envFrom }}
        envFrom:
        {{- toYaml . | nindent 8 }}
        {{- end }}
        ports:
        - name: metrics
          containerPort: {{ .Values.metrics.port }}
          protocol: TCP
        - name: health
          containerPort: {{ .Values.health.port }}
          protocol: TCP
        {{- if .Values.probes.liveness.enabled }}
        livenessProbe:
          httpGet:
            path: {{ .Values.health.livenessPath }}
            port: health
          initialDelaySeconds: {{ .Values.probes.liveness.initialDelaySeconds }}
          periodSeconds: {{ .Values.probes.liveness.periodSeconds }}
          timeoutSeconds: {{ .Values.probes.liveness.timeoutSeconds }}
          failureThreshold: {{ .Values.probes.liveness.failureThreshold }}
        {{- end }}
        {{- if .Values.probes.readiness.enabled }}
        readinessProbe:
          httpGet:
            path: {{ .Values.health.readinessPath }}
            port: health
          initialDelaySeconds: {{ .Values.probes.readiness.initialDelaySeconds }}
          periodSeconds: {{ .Values.probes.readiness.periodSeconds }}
          timeoutSeconds: {{ .Values.probes.readiness.timeoutSeconds }}
          failureThreshold: {{ .Values.probes.readiness.failureThreshold }}
        {{- end }}
        {{- with .Values.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.securityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.volumeMounts }}
        volumeMounts:
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.sidecars }}
      {{- toYaml . | nindent 6 }}
      {{- end }}
      {{- with .Values.volumes }}
      volumes:
      {{- toYaml . | nindent 6 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
