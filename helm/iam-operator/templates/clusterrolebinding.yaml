{{- if .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "iam-operator.fullname" . }}
  labels:
    {{- include "iam-operator.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "iam-operator.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "iam-operator.serviceAccountName" . }}
  namespace: {{ include "iam-operator.namespace" . }}
{{- end }}
