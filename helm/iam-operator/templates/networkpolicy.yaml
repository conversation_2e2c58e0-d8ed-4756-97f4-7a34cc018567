{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "iam-operator.fullname" . }}
  namespace: {{ include "iam-operator.namespace" . }}
  labels:
    {{- include "iam-operator.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "iam-operator.selectorLabels" . | nindent 6 }}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # 允许 Prometheus 抓取指标
  - from: []
    ports:
    - protocol: TCP
      port: {{ .Values.metrics.port }}
  # 允许健康检查
  - from: []
    ports:
    - protocol: TCP
      port: {{ .Values.health.port }}
  egress:
  # 允许访问 Kubernetes API
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 6443
  # 允许访问 IAM API
  - to: []
    ports:
    - protocol: TCP
      port: 8480
  # 允许 DNS 解析
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
{{- end }}
