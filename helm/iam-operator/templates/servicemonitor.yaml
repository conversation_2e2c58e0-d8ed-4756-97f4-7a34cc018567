{{- if .Values.monitoring.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "iam-operator.fullname" . }}
  namespace: {{ .Values.monitoring.serviceMonitor.namespace }}
  labels:
    {{- include "iam-operator.labels" . | nindent 4 }}
    {{- with .Values.monitoring.serviceMonitor.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "iam-operator.selectorLabels" . | nindent 6 }}
  namespaceSelector:
    matchNames:
    - {{ include "iam-operator.namespace" . }}
  endpoints:
  - port: metrics
    path: {{ .Values.metrics.path }}
    interval: {{ .Values.monitoring.serviceMonitor.interval }}
    scrapeTimeout: {{ .Values.monitoring.serviceMonitor.scrapeTimeout }}
{{- end }}
