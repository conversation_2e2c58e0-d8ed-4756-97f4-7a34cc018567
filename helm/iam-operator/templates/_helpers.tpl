{{/*
Expand the name of the chart.
*/}}
{{- define "iam-operator.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "iam-operator.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "iam-operator.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "iam-operator.labels" -}}
helm.sh/chart: {{ include "iam-operator.chart" . }}
{{ include "iam-operator.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "iam-operator.selectorLabels" -}}
app.kubernetes.io/name: {{ include "iam-operator.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "iam-operator.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "iam-operator.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the namespace to use
*/}}
{{- define "iam-operator.namespace" -}}
{{- if .Values.namespaceOverride }}
{{- .Values.namespaceOverride }}
{{- else }}
{{- .Release.Namespace }}
{{- end }}
{{- end }}

{{/*
Create the image name
*/}}
{{- define "iam-operator.image" -}}
{{- printf "%s:%s" .Values.image.repository .Values.image.tag }}
{{- end }}

{{/*
Create environment variables for IAM API configuration
*/}}
{{- define "iam-operator.iamApiEnv" -}}
- name: IAM_API_URL
  value: {{ .Values.iamApi.url | quote }}
- name: IAM_API_TIMEOUT
  value: {{ .Values.iamApi.timeout | quote }}
{{- end }}

{{/*
Create environment variables for processing configuration
*/}}
{{- define "iam-operator.processingEnv" -}}
- name: PROCESSING_TIMEOUT
  value: {{ .Values.processing.timeout | quote }}
- name: RETRY_INTERVAL
  value: {{ .Values.processing.retryInterval | quote }}
- name: MAX_RETRIES
  value: {{ .Values.processing.maxRetries | quote }}
{{- end }}

{{/*
Create environment variables for monitoring configuration
*/}}
{{- define "iam-operator.monitoringEnv" -}}
- name: METRICS_PORT
  value: {{ .Values.metrics.port | quote }}
- name: HEALTH_CHECK_PORT
  value: {{ .Values.health.port | quote }}
{{- end }}

{{/*
Create environment variables for logging configuration
*/}}
{{- define "iam-operator.loggingEnv" -}}
- name: LOG_LEVEL
  value: {{ .Values.logging.level | quote }}
{{- end }}

{{/*
Create environment variables for target namespaces
*/}}
{{- define "iam-operator.namespacesEnv" -}}
- name: TARGET_NAMESPACES
  value: {{ join "," .Values.targetNamespaces | quote }}
{{- end }}

{{/*
Create environment variables for state storage
*/}}
{{- define "iam-operator.stateStorageEnv" -}}
- name: STATE_CONFIGMAP_NAME
  value: {{ .Values.stateStorage.configMapName | quote }}
- name: STATE_CONFIGMAP_NAMESPACE
  value: {{ .Values.stateStorage.configMapNamespace | quote }}
{{- end }}
