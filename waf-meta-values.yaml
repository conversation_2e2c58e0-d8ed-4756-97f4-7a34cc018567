appspace:
  affinity: {}
  charts:
    configVals:
      cag_cdsdisk_type: premium_ssd
      cag_image_uuid: 9fd8fdfa-2679-4032-bba9-644097f5ea23
      cag_lcc_endpoint: https://**************
      cag_lcc_token: akAm4hs9rX5jpvWpkGzbtNbsO53viAsD
      cag_rootdisk_type: premium_ssd
      cag_skip_auto_license: 0
      cag_skip_eip: 0
      es_check_keep: 372
      es_clean_date: 365
      iwafop_app_key: 300067
      iwafop_sec_key: 414e66f24796035a1ddf618c9ca35087
      nvwa_callback: ''
      vul_online: false
    global:
      azones:
      - az: xakd
        name: 西安康定
      domain: .dev6.abcstackint.com
      regions:
      - azones:
        - az: xakd
          name: 西安康定
        k8sPodCIDR: **********/20
        k8sSvcCIDR: **********/16
        region: xa
        region_type: central
        region_zh: 西安
      registry: registry.dev6.abcstackint.com:5000
    middleware:
      es:
        index:
          clusterName: elasticsearch-global-default
          domain: elasticsearch-global-default.dev6.abcstackint.com
          port: 8200
          svcDomain: elasticsearch-global-default.base.svc.cluster.local
      kafka:
        topic:
          clusterName: kafka-global-default
          domain: kafka-global-default.dev6.abcstackint.com
          partitions: 30
          port: 8959
          replication: 3
          svcDomain: kafka-global-default.base.svc.cluster.local
          topicName: waflog
      redis:
        clusterName: redis-global-security
        domain: redis-global-security.dev6.abcstackint.com
        password: redis@instance-vp4jqc0xbrqw
        port: 6379
        svcDomain: redis-global-security-master.base.svc.cluster.local
        version: 5
      xdb:
        bss_hosteye_master:
          clusterName: xdb-xa-default
          dbType: xdb
          domain: xdb-xa-default.dev6.abcstackint.com
          domains:
          - domain: xdb-xa-default.dev6.abcstackint.com
            region: xa
          password: xdb@instance-vp4jqc0xbrqw
          permission: all
          port: 6203
          svcDomain: xdb-xa-default.base.svc.cluster.local
          user: waf_meta
        bss_service:
          clusterName: xdb-global-default
          dbType: xdb
          domain: xdb-global-default.dev6.abcstackint.com
          password: xdb@instance-vp4jqc0xbrqw
          permission: all
          port: 6203
          svcDomain: xdb-global-default.base.svc.cluster.local
          user: waf_meta
    platformConfigVals:
      appName: waf-meta
      azone: all
      category: 安全
      componentName: waf-meta
      componentVersion: 3.2.4-120
      deployMode: k8s
      deployStage: 待定
      integrationTest: {}
      level: region
      region: xa
      subsystem: WAF
      subsystemCn: 应用防火墙WAF
    provides:
      wafMeta:
        dnsBindType: vip
        domain: waf-meta.xa.dev6.abcstackint.com
        interface: waf-meta
        port: 8303
        portType: TCP
        views:
        - xakd
        vipType: underlay
    requires:
      ac:
        appName: unknown
        componentName: unknown
        domain: unknown.dev6.abcstackint.com
        domains: []
        interface: ac
        optional: true
        port: 40004
      bccWh:
        appName: api-gateway-xa
        componentName: api-gateway
        domain: bcc.internal.xa.dev6.abcstackint.com
        domains:
        - domain: bcc.internal.xa.dev6.abcstackint.com
          region: xa
        interface: bcc-wh
        optional: false
        port: 8680
      bcclogicInternal:
        appName: api-gateway-xa
        componentName: api-gateway
        domain: bcclogic.internal.xa.dev6.abcstackint.com
        domains:
        - domain: bcclogic.internal.xa.dev6.abcstackint.com
          region: xa
        interface: bcclogic-internal
        optional: false
        port: 8680
      bceMessages:
        appName: bce-messages
        componentName: bce-messages
        domain: messages.dev6.abcstackint.com
        domains:
        - domain: messages.dev6.abcstackint.com
          region: xa
        interface: bce-messages
        optional: false
        port: 8593
      billingOrder:
        appName: sp-order
        componentName: sp-order
        domain: order.dev6.abcstackint.com
        domains:
        - domain: order.dev6.abcstackint.com
          region: xa
        interface: billing-order
        optional: false
        port: 8003
      billingOrderPricing:
        appName: sp-pricing
        componentName: sp-pricing
        domain: pricing.dev6.abcstackint.com
        domains:
        - domain: pricing.dev6.abcstackint.com
          region: xa
        interface: billing-order-pricing
        optional: false
        port: 8006
      blbMeta:
        appName: blb-meta-xa
        componentName: blb-meta
        domain: blb-meta.xa.dev6.abcstackint.com
        domains:
        - domain: blb-meta.xa.dev6.abcstackint.com
          region: xa
        interface: blb-meta
        optional: false
        port: 8885
      bosNginx:
        appName: bos-nginx-xa
        componentName: bos-nginx
        domain: bcebos.xa.dev6.abcstackint.com
        domains:
        - domain: bcebos.xa.dev6.abcstackint.com
          region: xa
        interface: bos-nginx
        optional: false
        port: 8080
      bpResourceManager:
        appName: bp-resource-manager
        componentName: bp-resource-manager
        domain: bp-resource-manager.dev6.abcstackint.com
        domains:
        - domain: bp-resource-manager.dev6.abcstackint.com
          region: xa
        interface: bp-resource-manager
        optional: false
        port: 8600
      eipWh:
        appName: api-gateway-xa
        componentName: api-gateway
        domain: eip.internal.xa.dev6.abcstackint.com
        domains:
        - domain: eip.internal.xa.dev6.abcstackint.com
          region: xa
        interface: eip-wh
        optional: false
        port: 8680
      iam:
        appName: iam-nginx-xa
        bss:
          ak: f90daa8e8af94f6bb2da26c380e0a9cb
          password: TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5
          sk: d90b6603d4414151a0dac95409dcd815
          userId: 1ee85f66f3f84121ba055126e0a6a3e6
        componentName: iam-nginx
        domain: iam.xa.dev6.abcstackint.com
        domains:
        - domain: iam.xa.dev6.abcstackint.com
          region: xa
        interface: iam-nginx
        optional: false
        port: 35357
      iamSts:
        appName: iam-sts-xa
        componentName: iam-sts
        domain: sts.xa.dev6.abcstackint.com
        domains:
        - domain: sts.xa.dev6.abcstackint.com
          region: xa
        interface: iam-sts
        optional: false
        port: 8586
      iwafOp:
        appName: iwaf-op-x86
        componentName: iwaf-op
        domain: iwaf-op.dev6.abcstackint.com
        domains:
        - domain: iwaf-op.dev6.abcstackint.com
          region: xa
        interface: iwaf-op
        optional: true
        port: 8389
      nginxMonitor:
        appName: nginx-monitor-xa
        componentName: nginx-monitor
        domain: nginx-monitor.xa.dev6.abcstackint.com
        domains:
        - domain: nginx-monitor.xa.dev6.abcstackint.com
          region: xa
        interface: nginx-monitor
        optional: true
        port: 8089
      platOrderFacade:
        appName: plat-order-facade
        componentName: plat-order-facade
        domain: orderfacade.dev6.abcstackint.com
        domains:
        - domain: orderfacade.dev6.abcstackint.com
          region: xa
        interface: plat-order-facade
        optional: false
        port: 8993
      platOrderRenew:
        appName: plat-order-renew
        componentName: plat-order-renew
        domain: autorenew.dev6.abcstackint.com
        domains:
        - domain: autorenew.dev6.abcstackint.com
          region: xa
        interface: plat-order-renew
        optional: false
        port: 8986
      platServiceRegister:
        appName: plat-service-register
        componentName: plat-service-register
        domain: serviceregister.dev6.abcstackint.com
        domains:
        - domain: serviceregister.dev6.abcstackint.com
          region: xa
        interface: plat-service-register
        optional: false
        port: 8985
  commonAnnotations: []
  commonLabels: []
  containers:
  - enable: true
    image: abc-stack/waf-meta
    imagePullPolicy: IfNotPresent
    imageTag: *********
    livenessProbe:
      enable: false
    log:
    - filePaths:
      - /home/<USER>/bss/log/BSS_SERVICE
      - /home/<USER>/bss/log/BSS_SERVICE.wf
      - /home/<USER>/bss/log/bss_service
      - /home/<USER>/bss/log/bss_service.wf
      logType: local
      logVolume: waf-meta-log
      rule:
        rotate: 30
        rotatePeriod: daily
    name: waf-meta
    ports:
    - containerPort: 8303
      name: 8303-tcp
      nodePort: ''
      protocol: TCP
      servicePort: 8303
      targetPort: 8303
    postStart:
      enable: false
    preStop:
      enable: false
    readinessProbe:
      enable: false
    resources:
      limits:
        cpu: 1000m
        memory: 6000Mi
      requests:
        cpu: 750m
        memory: 6000Mi
    startupProbe:
      enable: false
    volumeMounts:
    - mountPath: /home/<USER>/bss/log/
      name: waf-meta-log
      readOnly: false
    - mountPath: /home/<USER>/conf/tools/init_user.py
      name: waf-meta
      readOnly: false
      subPath: init_user.py.template
    - mountPath: /home/<USER>/bss/inventory_es_clean.sh
      name: waf-meta
      readOnly: false
      subPath: inventory_es_clean.sh
    - mountPath: /home/<USER>/bss/inventory_es_init.sh
      name: waf-meta
      readOnly: false
      subPath: inventory_es_init.sh
    - mountPath: /home/<USER>/bss/third/nginx/conf/nginx.conf
      name: waf-meta
      readOnly: false
      subPath: nginx.conf
    - mountPath: /home/<USER>/bss/bss_service/protected/config/common_conf.php
      name: waf-meta
      readOnly: false
      subPath: common_conf.php.template
    - mountPath: /home/<USER>/bss/bss_service/protected/config/conf.php
      name: waf-meta
      readOnly: false
      subPath: conf.php.template
    - mountPath: /home/<USER>/bss/bss_service/protected/commands/cronTask/task.conf
      name: waf-meta
      readOnly: false
      subPath: task.conf
    - mountPath: /etc/localtime
      name: appspace-localtime
    - mountPath: /usr/share/zoneinfo/Asia/Shanghai
      name: appspace-shanghai
    - mountPath: /home/<USER>/tools
      name: appspace-tools
    - mountPath: /home/<USER>/script
      name: appspace-scripts
  hookContainers: []
  hostAliases: []
  images:
    waf-meta:
      imageTag: *********
      repository: abc-stack/waf-meta
  ingress:
    apptree:
      enable: true
      subsystem: 应用防火墙WAF
      subsystemEn: WAF
    dns:
      enable: true
    hostNetwork:
      enable: false
    service:
      allocateLoadBalancerNodePorts: false
      enable: true
      ports:
      - name: 8303-tcp
        nodePort: ''
        port: 8303
        protocol: TCP
        targetPort: 8303
      type: LoadBalancer
      vipType: underlay
  initContainers: []
  log:
    local:
      enable: true
  monitor:
    noaheePro:
      enable: true
  namespace: security
  nodeSelector: {}
  podAnnotations: []
  podLabels: []
  replica: 2
  restartPolicy: Always
  security:
    enable: false
  securityContext: {}
  ssl: {}
  terminationGracePeriodSeconds: 15
  tolerations:
  - effect: NoSchedule
    key: node.kubernetes.io/disk-pressure
    operator: Exists
  - effect: NoSchedule
    key: node.kubernetes.io/memory-pressure
    operator: Exists
  - effect: NoSchedule
    key: node.kubernetes.io/pid-pressure
    operator: Exists
  volumeClaimTemplate:
  - backup: false
    backupCronExp: 0 0 * * *
    backupTTL: 7d
    name: waf-meta-log
    size: 30Gi
    storageClassName: local-path
    storageType: local-path
    type: LOCAL_VOLUME
  volumes:
  - hostFileType: ''
    hostPath: /etc/localtime
    name: appspace-localtime
    type: HOST_PATH
  - hostFileType: ''
    hostPath: /usr/share/zoneinfo/Asia/Shanghai
    name: appspace-shanghai
    type: HOST_PATH
  - hostFileType: ''
    hostPath: /home/<USER>/tools
    name: appspace-tools
    type: HOST_PATH
  - hostFileType: ''
    hostPath: /home/<USER>/scripts
    name: appspace-scripts
    type: HOST_PATH
  workload:
    automountServiceAccountToken: true
    fixedIp: false
    kind: STATEFUL
    maxUnavailable: 25%
    partition: 0
    podAvailableEnable: true
    podManagementPolicy: OrderedReady
    publishNotReadyAddresses: false
    setHostnameAsFQDN: false
    updateType: RollingUpdate
  workloadAnnotations: []
  workloadLabels: []
