{"kubernetes_connection": true, "discovered_services": [{"name": "iam-manage-xian", "namespace": "console", "cluster_ip": "**************", "port": 8468, "endpoint": "http://**************:8468/v3", "type": "LoadBalancer", "ports": [{"name": "8468-tcp", "port": 8468, "protocol": "TCP"}]}, {"name": "iam-openapi", "namespace": "console", "cluster_ip": "**************", "port": 8480, "endpoint": "http://**************:8480/v3", "type": "LoadBalancer", "ports": [{"name": "8480-tcp", "port": 8480, "protocol": "TCP"}]}], "endpoint_construction": true, "feasible": true, "recommendation": "✅ 方案2可行，推荐使用 iam-manage-xian 服务"}