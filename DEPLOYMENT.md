# IAM-Operator 部署指南

## 概述

IAM-Operator 是一个 Kubernetes Operator，用于自动管理服务的 IAM 认证凭据。它支持从 ConfigMap 和 CRD 两种输入源处理 IAM 凭据，并通过 IAM API 进行统一管理。

## 部署阶段

IAM-Operator 属于 **Day 1 - 基础服务阶段** 部署，需要在业务服务部署之前完成。

```
Day 0: 基础设施 (Kubernetes 集群)
    ↓
Day 1: 基础服务 (IAM-Operator, XDB-Operator 等)
    ↓
Day 2: 业务服务 (waf-meta, billing-service 等)
```

## 前置条件

### 环境要求
- Kubernetes 1.19+
- Helm 3.0+
- Docker (用于构建镜像)
- kubectl (配置好集群访问)

### 依赖服务
- IAM OpenAPI 服务 (http://iam-openapi.console.svc.cluster.local:8480)
- Kubernetes API Server

## 部署方式

### 方式 1：Helm 部署（推荐）

#### 1. 克隆项目
```bash
git clone <iam-operator-repo>
cd iam-operator
```

#### 2. 构建和部署
```bash
# 开发环境部署
./scripts/helm-deploy.sh deploy

# 生产环境部署
./scripts/helm-deploy.sh deploy iam-operator base 1.0.0 helm/iam-operator/values-production.yaml

# 自定义配置部署
./scripts/helm-deploy.sh deploy my-iam-operator base 1.1.0 my-values.yaml
```

#### 3. 验证部署
```bash
# 查看状态
./scripts/helm-deploy.sh status

# 运行测试
./scripts/helm-deploy.sh test

# 查看日志
./scripts/helm-deploy.sh logs
```

### 方式 2：原生 Kubernetes 部署

#### 1. 部署 CRD
```bash
kubectl apply -f deploy/crds/
```

#### 2. 部署 RBAC
```bash
kubectl apply -f deploy/rbac/
```

#### 3. 部署配置和 Operator
```bash
kubectl apply -f deploy/configmaps/
kubectl apply -f deploy/deployment.yaml
```

## 配置说明

### 核心配置项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `image.repository` | `abc-stack/iam-operator` | 镜像仓库 |
| `image.tag` | `1.0.0` | 镜像标签 |
| `replicaCount` | `1` | 副本数 |
| `iamApi.url` | `http://iam-openapi.console.svc.cluster.local:8480` | IAM API 地址 |
| `targetNamespaces` | `[security, console, billing]` | 目标命名空间 |

### 环境配置

#### 开发环境
```yaml
# values.yaml (默认)
replicaCount: 1
resources:
  requests:
    cpu: 100m
    memory: 128Mi
logging:
  level: INFO
```

#### 生产环境
```yaml
# values-production.yaml
replicaCount: 2
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
resources:
  requests:
    cpu: 200m
    memory: 256Mi
  limits:
    cpu: 1000m
    memory: 1Gi
podDisruptionBudget:
  enabled: true
monitoring:
  serviceMonitor:
    enabled: true
```

## 使用方式

### 1. CRD 方式（推荐）

创建 IAMServiceAccount 资源：

```yaml
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: waf-meta-iam
  namespace: security
spec:
  serviceName: waf-meta
  products:
    bss:
      ak: f90daa8e8af94f6bb2da26c380e0a9cb
      sk: d90b6603d4414151a0dac95409dcd815
      password: TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5
      userId: 1ee85f66f3f84121ba055126e0a6a3e6
```

### 2. ConfigMap 方式（兼容现有）

Operator 会自动检测包含 IAM 凭据的 ConfigMap：

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: waf-meta-config
  namespace: security
data:
  config.php: |
    <?php
    $bss_ak = 'f90daa8e8af94f6bb2da26c380e0a9cb';
    $bss_sk = 'd90b6603d4414151a0dac95409dcd815';
    ?>
```

## 监控和运维

### 健康检查
```bash
# 存活检查
curl http://<pod-ip>:8081/healthz

# 就绪检查
curl http://<pod-ip>:8081/ready
```

### 指标监控
```bash
# 查看指标
curl http://<pod-ip>:8080/metrics | grep iam_
```

### 日志查看
```bash
# Helm 方式
./scripts/helm-deploy.sh logs

# 原生方式
kubectl logs -f deployment/iam-operator -n base
```

## 故障排查

### 常见问题

#### 1. Pod 启动失败
```bash
# 查看 Pod 状态
kubectl describe pod <pod-name> -n base

# 查看日志
kubectl logs <pod-name> -n base
```

#### 2. IAM API 连接失败
```bash
# 检查 IAM API 服务
kubectl get svc -n console | grep iam-openapi

# 测试连通性
kubectl exec -it <pod-name> -n base -- curl http://iam-openapi.console.svc.cluster.local:8480/health
```

#### 3. 权限问题
```bash
# 检查 RBAC
kubectl auth can-i get configmaps --as=system:serviceaccount:base:iam-operator

# 查看角色绑定
kubectl describe clusterrolebinding iam-operator
```

### 调试模式

启用调试日志：

```yaml
# values-debug.yaml
logging:
  level: DEBUG

env:
- name: KOPF_LOG_LEVEL
  value: DEBUG
```

## 升级和回滚

### 升级
```bash
# Helm 升级
./scripts/helm-deploy.sh upgrade iam-operator base 1.1.0

# 检查升级状态
kubectl rollout status deployment/iam-operator -n base
```

### 回滚
```bash
# Helm 回滚
helm rollback iam-operator -n base

# 或者指定版本
helm rollback iam-operator 1 -n base
```

## 卸载

### Helm 卸载
```bash
./scripts/helm-deploy.sh uninstall
```

### 原生卸载
```bash
kubectl delete -f deploy/
```

### 清理 CRD（可选）
```bash
kubectl delete crd iamserviceaccounts.iam.example.com
```

## 安全考虑

### 1. RBAC 最小权限
Operator 只拥有必要的权限，不能访问 Secret 等敏感资源。

### 2. 网络策略
生产环境建议启用网络策略限制网络访问。

### 3. 镜像安全
- 使用非 root 用户运行
- 只读根文件系统
- 禁用特权提升

## 性能调优

### 资源配置
```yaml
resources:
  requests:
    cpu: 200m      # 根据实际负载调整
    memory: 256Mi
  limits:
    cpu: 1000m
    memory: 1Gi
```

### 并发配置
```yaml
env:
- name: KOPF_WORKERS
  value: "4"       # 并发处理数
```

## 备份和恢复

### 状态备份
```bash
# 备份状态 ConfigMap
kubectl get configmap iam-operator-state -n base -o yaml > iam-operator-state-backup.yaml
```

### 配置备份
```bash
# 备份 Helm values
helm get values iam-operator -n base > iam-operator-values-backup.yaml
```
