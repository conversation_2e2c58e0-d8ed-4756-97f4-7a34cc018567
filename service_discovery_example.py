
# 方案2实现示例：通过K8s Service发现调用iam-manage API

from kubernetes import client, config as k8s_config
from src.clients.iam_service_discovery import IAMServiceDiscoveryClient

# 1. 初始化服务发现客户端
iam_client = IAMServiceDiscoveryClient(
    iam_namespace="console",
    service_name="iam-manage-xian"  # 根据实际部署调整
)

# 2. 自动发现服务端点
try:
    endpoint = iam_client._discover_iam_endpoint()
    print(f"发现的IAM端点: {endpoint}")
    
    # 3. 进行健康检查
    if iam_client.health_check():
        print("IAM服务可用")
        
        # 4. 使用服务（示例）
        # token = iam_client._get_auth_token()
        # ... 其他API调用
    else:
        print("IAM服务不可用")
        
except Exception as e:
    print(f"服务发现失败: {e}")
