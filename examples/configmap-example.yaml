# ConfigMap 示例 - 模拟从 values.yaml 转换来的配置

# 示例 1: PHP 格式的配置（类似 waf-meta）
apiVersion: v1
kind: ConfigMap
metadata:
  name: waf-meta-config
  namespace: security
  labels:
    app: waf-meta
    source: helm-values
data:
  config.php: |
    <?php
    // BSS 配置
    $bss_ak = 'f90daa8e8af94f6bb2da26c380e0a9cb'; //bss access key
    $bss_sk = 'd90b6603d4414151a0dac95409dcd815'; //bss secret key
    $bss_uid = '1ee85f66f3f84121ba055126e0a6a3e6';//bss sandbox
    $bss_username = 'BSS'; //bss username
    $bss_passwd = 'TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5'; //bss password
    
    // 其他配置
    $app_name = 'waf-meta';
    $debug = false;
    ?>
  
  common_conf.php: |
    <?php
    // 通用配置
    define('BSS_AK', 'f90daa8e8af94f6bb2da26c380e0a9cb');
    define('BSS_SK', 'd90b6603d4414151a0dac95409dcd815');
    define('BSS_PASSWORD', 'TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5');
    ?>

---
# 示例 2: YAML 格式的配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: billing-service-config
  namespace: billing
  labels:
    app: billing-service
    source: helm-values
data:
  application.yaml: |
    app:
      name: billing-service
    
    iam:
      payment:
        ak: a1b2c3d4e5f6789012345678901234567
        sk: x9y8z7w6v5u4321098765432109876543
        password: PaymentPassword123
        userId: 2ff95f77g4g95232cb166237f1b7b4f7
      order:
        ak: m5n4o3p2q1r0987654321098765432109
        sk: q1w2e3r4t5y6u7i8o9p0a1s2d3f4g5h6
        password: OrderPassword456
        userId: 3gg06g88h5h06343dc277348g2c8c5g8

---
# 示例 3: JSON 格式的配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: storage-service-config
  namespace: storage
  labels:
    app: storage-service
    source: helm-values
data:
  config.json: |
    {
      "app": {
        "name": "storage-service",
        "version": "1.0.0"
      },
      "iam": {
        "storage": {
          "ak": "storage-access-key-12345",
          "sk": "storage-secret-key-67890",
          "password": "StoragePassword789",
          "userId": "4hh17h99i6i17454ed388459h3d9d6h9"
        }
      }
    }
