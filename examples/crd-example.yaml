# IAMServiceAccount CRD 使用示例

# 示例 1: waf-meta 服务的 IAM 凭据
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: waf-meta-iam
  namespace: security
  labels:
    app: waf-meta
    managed-by: iam-operator
spec:
  serviceName: waf-meta
  products:
    bss:
      ak: f90daa8e8af94f6bb2da26c380e0a9cb
      sk: d90b6603d4414151a0dac95409dcd815
      password: TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5
      userId: 1ee85f66f3f84121ba055126e0a6a3e6
  autoRotate: false
  rotationInterval: "30d"

---
# 示例 2: billing 服务的多产品凭据
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: billing-service-iam
  namespace: billing
  labels:
    app: billing-service
    managed-by: iam-operator
spec:
  serviceName: billing-service
  products:
    payment:
      ak: a1b2c3d4e5f6789012345678901234567
      sk: x9y8z7w6v5u4321098765432109876543
      password: PaymentPassword123
      userId: 2ff95f77g4g95232cb166237f1b7b4f7
    order:
      ak: m5n4o3p2q1r0987654321098765432109
      sk: q1w2e3r4t5y6u7i8o9p0a1s2d3f4g5h6
      password: OrderPassword456
      userId: 3gg06g88h5h06343dc277348g2c8c5g8
  autoRotate: true
  rotationInterval: "7d"

---
# 示例 3: 最小配置
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: simple-service-iam
  namespace: default
spec:
  serviceName: simple-service
  products:
    storage:
      ak: simple-access-key-12345
      sk: simple-secret-key-67890
