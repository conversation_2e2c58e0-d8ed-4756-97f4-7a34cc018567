#!/usr/bin/env python3
"""
测试 ConfigMap 解析 aksk 功能
"""

import sys
import os
import json

# 添加项目路径
sys.path.insert(0, 'iam-operator/src')

try:
    from processors.parser import parser
    from handlers.configmap_handler import is_iam_configmap
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

def test_php_config():
    """测试 PHP 格式配置解析"""
    print("=== 测试 PHP 格式配置解析 ===")
    
    php_content = """<?php
    // BSS 配置
    $bss_ak = 'f90daa8e8af94f6bb2da26c380e0a9cb'; //bss access key
    $bss_sk = 'd90b6603d4414151a0dac95409dcd815'; //bss secret key
    $bss_uid = '1ee85f66f3f84121ba055126e0a6a3e6';//bss sandbox
    $bss_username = 'BSS'; //bss username
    $bss_passwd = 'TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5'; //bss password
    
    // 支付配置
    $payment_ak = 'payment-access-key-12345';
    $payment_sk = 'payment-secret-key-67890';
    $payment_passwd = 'payment-password-123';
    ?>"""
    
    configmap_data = {
        'config.php': php_content
    }
    
    try:
        credentials = parser.parse_configmap_credentials(configmap_data)
        print(f"解析结果: {json.dumps(credentials, indent=2, ensure_ascii=False)}")
        
        if credentials:
            print(f"✅ 成功解析 {len(credentials)} 个产品的凭据")
            for product, creds in credentials.items():
                print(f"  产品: {product}")
                print(f"    AK: {creds.get('ak', 'N/A')}")
                print(f"    SK: {creds.get('sk', 'N/A')}")
                print(f"    Password: {creds.get('password', 'N/A')}")
        else:
            print("❌ 未解析到任何凭据")
            
    except Exception as e:
        print(f"❌ 解析失败: {e}")

def test_yaml_config():
    """测试 YAML 格式配置解析"""
    print("\n=== 测试 YAML 格式配置解析 ===")
    
    yaml_content = """app:
  name: billing-service

iam:
  payment:
    ak: a1b2c3d4e5f6789012345678901234567
    sk: x9y8z7w6v5u4321098765432109876543
    password: PaymentPassword123
    userId: 2ff95f77g4g95232cb166237f1b7b4f7
  order:
    ak: m5n4o3p2q1r0987654321098765432109
    sk: q1w2e3r4t5y6u7i8o9p0a1s2d3f4g5h6
    password: OrderPassword456
    userId: 3gg06g88h5h06343dc277348g2c8c5g8"""
    
    configmap_data = {
        'application.yaml': yaml_content
    }
    
    try:
        credentials = parser.parse_configmap_credentials(configmap_data)
        print(f"解析结果: {json.dumps(credentials, indent=2, ensure_ascii=False)}")
        
        if credentials:
            print(f"✅ 成功解析 {len(credentials)} 个产品的凭据")
            for product, creds in credentials.items():
                print(f"  产品: {product}")
                print(f"    AK: {creds.get('ak', 'N/A')}")
                print(f"    SK: {creds.get('sk', 'N/A')}")
                print(f"    Password: {creds.get('password', 'N/A')}")
                print(f"    UserId: {creds.get('userId', 'N/A')}")
        else:
            print("❌ 未解析到任何凭据")
            
    except Exception as e:
        print(f"❌ 解析失败: {e}")

def test_json_config():
    """测试 JSON 格式配置解析"""
    print("\n=== 测试 JSON 格式配置解析 ===")
    
    json_content = """{
  "app": {
    "name": "storage-service",
    "version": "1.0.0"
  },
  "iam": {
    "storage": {
      "ak": "storage-access-key-12345",
      "sk": "storage-secret-key-67890",
      "password": "StoragePassword789",
      "userId": "4hh17h99i6i17454ed388459h3d9d6h9"
    },
    "backup": {
      "ak": "backup-access-key-54321",
      "sk": "backup-secret-key-09876"
    }
  }
}"""
    
    configmap_data = {
        'config.json': json_content
    }
    
    try:
        credentials = parser.parse_configmap_credentials(configmap_data)
        print(f"解析结果: {json.dumps(credentials, indent=2, ensure_ascii=False)}")
        
        if credentials:
            print(f"✅ 成功解析 {len(credentials)} 个产品的凭据")
            for product, creds in credentials.items():
                print(f"  产品: {product}")
                print(f"    AK: {creds.get('ak', 'N/A')}")
                print(f"    SK: {creds.get('sk', 'N/A')}")
                print(f"    Password: {creds.get('password', 'N/A')}")
                print(f"    UserId: {creds.get('userId', 'N/A')}")
        else:
            print("❌ 未解析到任何凭据")
            
    except Exception as e:
        print(f"❌ 解析失败: {e}")

def test_configmap_detection():
    """测试 ConfigMap 检测功能"""
    print("\n=== 测试 ConfigMap 检测功能 ===")
    
    # 模拟 ConfigMap body
    test_cases = [
        {
            'name': 'waf-meta-config',
            'namespace': 'security',
            'body': {
                'data': {
                    'config.php': '<?php $bss_ak = "test"; $bss_sk = "test"; ?>'
                }
            },
            'expected': True
        },
        {
            'name': 'normal-config',
            'namespace': 'default',
            'body': {
                'data': {
                    'app.conf': 'debug=true\nport=8080'
                }
            },
            'expected': False
        },
        {
            'name': 'yaml-iam-config',
            'namespace': 'security',
            'body': {
                'data': {
                    'config.yaml': 'iam:\n  test:\n    ak: test\n    sk: test'
                }
            },
            'expected': True
        }
    ]
    
    for case in test_cases:
        try:
            # 临时设置目标命名空间
            import iam_operator.src.utils.config as config_module
            original_namespaces = config_module.config._config.get('target_namespaces', [])
            config_module.config._config['target_namespaces'] = ['security', 'console', 'billing']
            
            result = is_iam_configmap(case['body'], case['name'], case['namespace'])
            status = "✅" if result == case['expected'] else "❌"
            print(f"{status} {case['name']} (namespace: {case['namespace']}) -> {result} (期望: {case['expected']})")
            
            # 恢复原始配置
            config_module.config._config['target_namespaces'] = original_namespaces
            
        except Exception as e:
            print(f"❌ 测试 {case['name']} 失败: {e}")

def main():
    """主函数"""
    print("ConfigMap 解析测试工具")
    print("=" * 50)
    
    # 测试各种格式的配置解析
    test_php_config()
    test_yaml_config()
    test_json_config()
    test_configmap_detection()
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("\n使用说明:")
    print("1. 这个测试验证了 ConfigMap 中 aksk 的解析功能")
    print("2. 支持 PHP、YAML、JSON 三种格式")
    print("3. 可以识别多个产品的凭据")
    print("4. 只有包含 ak 和 sk 的完整凭据才会被解析")

if __name__ == "__main__":
    main()
