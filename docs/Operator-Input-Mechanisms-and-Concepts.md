# Operator 输入机制与核心概念详解

## 背景

在云计算项目的 Day 2 持续运营阶段，IAM-Operator 需要从外部获取各种输入来执行自动化任务。本文档详细解释 Operator 的输入机制、核心概念，以及在智算中心场景中的应用。

## Kubernetes 核心概念

### 1. Deployment

**定义**：Kubernetes 中部署应用的标准方式，管理应用的生命周期。

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: billing-service
  namespace: billing
spec:
  replicas: 3  # 运行3个副本
  selector:
    matchLabels:
      app: billing-service
  template:
    metadata:
      labels:
        app: billing-service
    spec:
      containers:
      - name: billing
        image: billing:v1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: billing-service-credentials
              key: access_key
```

**Deployment 的作用**：
- 管理应用的多个副本（Pod）
- 处理应用的滚动更新
- 确保指定数量的副本始终运行
- 提供回滚功能

### 2. 自定义资源定义（CRD）

**定义**：扩展 Kubernetes API，创建自定义资源类型。

```yaml
# 定义新的资源类型
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: iamserviceaccounts.iam.example.com
spec:
  group: iam.example.com
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              serviceName:
                type: string
                description: "服务名称"
              serviceNumber:
                type: string
                description: "服务号标识"
              permissions:
                type: array
                description: "权限配置列表"
```

**CRD 的作用**：
- 让你可以像管理 Pod、Service 一样管理业务对象
- 提供类型安全和验证
- 支持版本控制和升级
- 例如：IAMServiceAccount、XDBCluster、BackupPolicy 等

### 3. 概念关系图

```mermaid
graph TD
    A[用户] --> B[创建 CRD 资源]
    A --> C[创建 Deployment]
    
    B --> D[Operator 监听 CRD]
    C --> E[Operator 监听 Deployment]
    
    D --> F[执行业务逻辑]
    E --> F
    
    F --> G[调用 IAM API]
    F --> H[创建 Secret]
    F --> I[更新 Deployment]
    
    H --> C
```

## 服务号概念澄清

### 服务号 = 服务 + 账号

**本质**：各产品/服务在 IAM 系统中的身份标识

**组成部分**：
- **服务标识**：如 "bce-billing"、"bce-payment"
- **账号凭据**：AK/SK、STS Token、数据库密码等
- **权限配置**：该服务可以访问哪些资源

**典型服务号配置**：
```yaml
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: billing-service-account
  namespace: billing
spec:
  serviceName: "bce-billing"           # 产品服务名
  serviceNumber: "SN-BILLING-001"      # 服务号标识
  credentialTypes:
    - "aksk"      # Access Key / Secret Key
    - "sts"       # Security Token Service
    - "password"  # 数据库密码
  permissions:
    - resource: "database:bce_billing"
      actions: ["read", "write"]
    - resource: "storage:billing-bucket"
      actions: ["read", "write", "delete"]
  rotationPolicy:
    enabled: true
    interval: "30d"  # 30天轮换一次
```

## Operator 外部输入机制

### 1. 通过自定义资源（CRD）输入（主要方式）

**流程**：
1. 用户创建 YAML 文件定义需求
2. 通过 `kubectl apply` 提交到 Kubernetes
3. Operator 监听资源变化，获取输入数据

**用户输入示例**：
```yaml
# 文件：iam-service-account.yaml
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: billing-service
  namespace: billing
spec:
  serviceName: "bce-billing"           # 输入：服务名
  serviceNumber: "SN-BILLING-001"      # 输入：服务号
  permissions:                         # 输入：权限配置
    - resource: "database:bce_billing"
      actions: ["read", "write"]
    - resource: "storage:billing-bucket"
      actions: ["read", "write"]
  rotationPolicy:                      # 输入：轮换策略
    enabled: true
    interval: "30d"
```

**Operator 接收处理**：
```python
@kopf.on.create('iam.example.com', 'v1', 'iamserviceaccounts')
def handle_iam_service_account(spec, name, namespace, **kwargs):
    # spec 就是用户输入的数据
    service_name = spec.get('serviceName')        # 获取输入
    service_number = spec.get('serviceNumber')    # 获取输入
    permissions = spec.get('permissions', [])     # 获取输入
    
    # 基于输入执行业务逻辑
    create_iam_credentials(service_name, service_number, permissions)
```

### 2. 通过 ConfigMap 输入全局配置

```yaml
# 配置文件：iam-operator-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: iam-operator-config
  namespace: base
data:
  iam-api-url: "http://iam-openapi.console.svc.cluster.local:8480"
  default-rotation-interval: "30d"
  default-permissions: |
    - resource: "database:*"
      actions: ["read"]
  service-mapping: |
    billing: SN-BILLING-001
    payment: SN-PAYMENT-001
    user: SN-USER-001
```

### 3. 通过 Annotation 输入轻量级配置

```yaml
# 在现有 Deployment 上添加注解
apiVersion: apps/v1
kind: Deployment
metadata:
  name: billing-service
  namespace: billing
  annotations:
    iam.example.com/service-account: "true"           # 输入：启用 IAM
    iam.example.com/service-number: "SN-BILLING-001"  # 输入：服务号
    iam.example.com/permissions: |                    # 输入：权限
      - resource: "database:bce_billing"
        actions: ["read", "write"]
spec:
  # ... deployment spec
```

### 4. 通过环境变量输入运行时配置

```yaml
# Operator Deployment 中的环境变量
apiVersion: apps/v1
kind: Deployment
metadata:
  name: iam-operator
spec:
  template:
    spec:
      containers:
      - name: iam-operator
        image: iam-operator:v1.0.0
        env:
        - name: IAM_API_URL                    # 输入：IAM API 地址
          value: "http://iam-openapi.console.svc.cluster.local:8480"
        - name: DEFAULT_ROTATION_INTERVAL      # 输入：默认轮换间隔
          value: "30d"
        - name: LOG_LEVEL                      # 输入：日志级别
          value: "INFO"
        - name: DRY_RUN                        # 输入：是否为演练模式
          value: "false"
```

### 5. 通过 Secret 输入敏感信息

```yaml
# 敏感配置：iam-operator-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: iam-operator-secrets
  namespace: base
type: Opaque
data:
  iam-api-token: <base64-encoded-token>     # 输入：API 认证 Token
  database-password: <base64-encoded-pwd>   # 输入：数据库密码
```

## 云计算项目生命周期定位

### 项目阶段划分

**Day 0 - Facility & Rack-Stack Build**
```
物理基础设施建设
├── 机房建设
├── 网络布线
├── 服务器上架
└── 基础硬件配置
```

**Day 1 - Cloud Foundation Go-Live**
```
云平台基础服务部署
├── Kubernetes 集群部署
├── 基础组件安装
│   ├── XDB-Operator
│   ├── IAM 服务
│   ├── 网络组件
│   └── 存储组件
├── 基础配置
└── 平台验证
```

**Day 2 - Continuous Ops & Change** ⭐️ **（IAM-Operator 的定位）**
```
持续运营和变更管理
├── 自动化运维工具
│   ├── IAM-Operator (我们的项目)
│   ├── 监控告警
│   ├── 日志收集
│   └── 备份恢复
├── 服务生命周期管理
│   ├── 服务部署自动化
│   ├── 凭据自动轮换
│   ├── 配置变更管理
│   └── 故障自愈
└── 持续优化
    ├── 性能调优
    ├── 成本优化
    └── 安全加固
```

**Day N - Tech Refresh / De-commission**
```
技术刷新和退役
├── 硬件升级
├── 软件版本升级
├── 服务迁移
└── 资源回收
```

### 在智算中心的应用场景

**典型运营场景**：
```
智算中心 Day 2 运营
├── AI 训练任务管理
│   ├── 自动分配存储凭据
│   ├── 自动分配计算资源凭据
│   └── 任务完成后自动回收凭据
├── 多租户管理
│   ├── 租户服务自动隔离
│   ├── 权限自动分配和回收
│   └── 资源使用监控
└── 运维自动化
    ├── 服务健康检查
    ├── 故障自动恢复
    └── 性能自动调优
```

## 完整输入流程示例

### 用户操作流程

```bash
# 1. 创建服务账号配置
cat > my-service-iam.yaml << EOF
apiVersion: iam.example.com/v1
kind: IAMServiceAccount
metadata:
  name: my-service
  namespace: default
spec:
  serviceName: "my-service"
  serviceNumber: "SN-MYSERVICE-001"
  permissions:
    - resource: "database:my_service_db"
      actions: ["read", "write"]
    - resource: "storage:my-service-bucket"
      actions: ["read", "write"]
  rotationPolicy:
    enabled: true
    interval: "7d"
EOF

# 2. 提交到 Kubernetes
kubectl apply -f my-service-iam.yaml

# 3. 查看结果
kubectl get iamserviceaccounts
kubectl get secrets | grep my-service
```

### Operator 处理流程

```python
@kopf.on.create('iam.example.com', 'v1', 'iamserviceaccounts')
def create_iam_service_account(spec, name, namespace, **kwargs):
    # 1. 获取用户输入
    service_name = spec.get('serviceName')
    service_number = spec.get('serviceNumber') 
    permissions = spec.get('permissions', [])
    rotation_policy = spec.get('rotationPolicy', {})
    
    # 2. 调用 IAM API（使用输入数据）
    iam_client = IAMClient()
    credentials = iam_client.create_service_credentials(
        service_name=service_name,
        service_number=service_number,
        permissions=permissions
    )
    
    # 3. 创建 Kubernetes Secret（输出结果）
    create_credential_secret(
        name=f"{name}-credentials",
        namespace=namespace,
        credentials=credentials
    )
    
    # 4. 设置轮换定时器（基于输入的策略）
    if rotation_policy.get('enabled'):
        schedule_rotation(name, namespace, rotation_policy.get('interval'))
```

## 输入机制总结

### 输入方式对比

| 输入方式 | 适用场景 | 优点 | 缺点 |
|---------|---------|------|------|
| CRD 资源 | 业务数据和配置 | 类型安全、版本控制、声明式 | 需要定义 CRD |
| ConfigMap | 全局配置 | 简单、可热更新 | 无类型检查 |
| Annotation | 轻量级配置 | 与现有资源集成 | 功能有限 |
| 环境变量 | 运行时配置 | 简单、容器原生 | 重启才能更新 |
| Secret | 敏感信息 | 安全、加密存储 | 管理复杂 |

### 设计原则

1. **声明式**：用户只需要描述想要什么，不需要关心怎么实现
2. **版本控制**：所有配置都可以用 Git 管理
3. **审计追踪**：所有变更都有记录
4. **权限控制**：可以通过 RBAC 控制谁能修改什么配置
5. **类型安全**：通过 CRD Schema 验证输入数据的正确性

### 实际价值

在智算中心的 Day 2 运营中，这种输入机制带来：

1. **降低运维成本**：减少人工操作，降低出错率
2. **提高安全性**：自动凭据轮换，及时权限回收
3. **提升效率**：新服务快速上线，无需等待人工配置
4. **合规保障**：自动化合规检查，生成审计报告
5. **标准化管理**：统一的配置格式和流程
