# IAM Operator 测试指南

## 概述

本文档描述了 IAM Operator 的测试方法，包括 ConfigMap 解析功能测试和 Operator 部署测试。

## 1. ConfigMap 解析功能测试

### 1.1 简化测试（无依赖）

创建独立的测试脚本，不依赖项目环境：

```python
#!/usr/bin/env python3
"""
简化的 ConfigMap 解析测试 - 不依赖 IAM 连接
"""

import re
import json
import yaml

def parse_php_credentials(php_content):
    """解析 PHP 配置文件中的 IAM 凭据"""
    credentials = {}
    
    # 定义匹配模式
    patterns = {
        'ak': r'\$(\w+)_ak\s*=\s*[\'"]([^\'"]+)[\'"]',
        'sk': r'\$(\w+)_sk\s*=\s*[\'"]([^\'"]+)[\'"]',
        'password': r'\$(\w+)_passwd\s*=\s*[\'"]([^\'"]+)[\'"]',
        'userId': r'\$(\w+)_uid\s*=\s*[\'"]([^\'"]+)[\'"]'
    }
    
    # 收集所有产品的凭据
    products = {}
    for field, pattern in patterns.items():
        matches = re.findall(pattern, php_content, re.IGNORECASE)
        for product, value in matches:
            if product not in products:
                products[product] = {}
            products[product][field] = value
    
    # 只返回包含 ak 和 sk 的完整凭据
    for product, creds in products.items():
        if 'ak' in creds and 'sk' in creds:
            credentials[product] = creds
    
    return credentials

# 测试用例
def test_real_configmap():
    """测试真实的 ConfigMap 数据"""
    real_php_content = """<?php
    // BSS 配置
    $bss_ak = 'f90daa8e8af94f6bb2da26c380e0a9cb';
    $bss_sk = 'd90b6603d4414151a0dac95409dcd815';
    $bss_passwd = 'TIOc1Hahb2WrSrwtjGEVb13VyhRLTAa5';
    ?>"""
    
    credentials = parse_php_credentials(real_php_content)
    print(f"解析结果: {json.dumps(credentials, indent=2)}")
```

**运行测试：**
```bash
python test_configmap_simple.py
```

### 1.2 完整项目测试

使用项目中的解析器进行测试：

```bash
# 进入项目目录
cd iam-operator

# 运行完整测试
python test_configmap_parser.py
```

## 2. Operator 部署测试

### 2.1 安全边界

IAM Operator 的监听边界：

- **命名空间限制**: 只监听配置的目标命名空间（默认：`security`, `console`, `billing`）
- **内容过滤**: 只处理包含 IAM 凭据模式的 ConfigMap：
  - PHP 格式: 包含 `$xxx_ak` 和 `$xxx_sk` 的内容
  - YAML/JSON 格式: 包含 `iam:` 和 `ak:` 的内容
- **只读操作**: 只读取 ConfigMap，不修改原始内容

### 2.2 影响范围评估

**会创建的资源：**
- CRD: `iamserviceaccounts.iam.example.com`
- RBAC: ClusterRole, ClusterRoleBinding, ServiceAccount
- ConfigMap: `iam-operator-config`, `iam-operator-state`
- Deployment: `iam-operator` (在 base 命名空间)

**不会影响：**
- 现有业务服务
- 现有 ConfigMap 内容
- 其他命名空间的资源

### 2.3 安全测试部署

为了避免影响生产环境，建议使用独立的测试命名空间：

#### 步骤 1: 创建测试环境

```bash
# 创建测试命名空间
kubectl create namespace iam-test

# 修改配置只监听测试命名空间
# 编辑 iam-operator/deploy/configmaps/operator-config.yaml
```

修改配置文件：
```yaml
data:
  # 目标命名空间（只监听测试命名空间）
  target-namespaces: "iam-test"
```

#### 步骤 2: 部署 Operator

```bash
cd iam-operator

# 方式 1: 使用部署脚本
./scripts/deploy.sh deploy

# 方式 2: 使用 Helm
./scripts/helm-deploy.sh deploy

# 方式 3: 手动部署
kubectl apply -f deploy/crds/
kubectl apply -f deploy/rbac/
kubectl apply -f deploy/configmaps/
kubectl apply -f deploy/deployment.yaml
```

#### 步骤 3: 创建测试 ConfigMap

```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-iam-config
  namespace: iam-test
data:
  config.php: |
    <?php
    \$test_ak = 'test-access-key-12345';
    \$test_sk = 'test-secret-key-67890';
    \$test_passwd = 'test-password-123';
    \$test_uid = 'test-user-id-456';
    ?>
  application.yaml: |
    iam:
      payment:
        ak: payment-ak-12345
        sk: payment-sk-67890
        password: PaymentPassword123
EOF
```

#### 步骤 4: 验证功能

```bash
# 查看 Operator 日志
kubectl logs -f deployment/iam-operator -n base

# 查看状态 ConfigMap
kubectl get configmap iam-operator-state -n base -o yaml

# 检查 Operator 状态
kubectl get pods -n base -l app=iam-operator
```

预期日志输出：
```
Processing IAM ConfigMap: iam-test/test-iam-config
Found credentials for products: ['test', 'payment']
Successfully processed ConfigMap: iam-test/test-iam-config
```

### 2.4 完全清理

测试完成后，完全清理所有资源：

```bash
# 使用脚本清理
cd iam-operator
./scripts/deploy.sh cleanup

# 或手动清理
kubectl delete deployment iam-operator -n base --ignore-not-found=true
kubectl delete configmap iam-operator-config -n base --ignore-not-found=true
kubectl delete configmap iam-operator-state -n base --ignore-not-found=true
kubectl delete clusterrolebinding iam-operator --ignore-not-found=true
kubectl delete clusterrole iam-operator --ignore-not-found=true
kubectl delete serviceaccount iam-operator -n base --ignore-not-found=true
kubectl delete crd iamserviceaccounts.iam.example.com --ignore-not-found=true

# 删除测试命名空间
kubectl delete namespace iam-test --ignore-not-found=true

# 删除测试 Pod
kubectl delete pod test-iam --ignore-not-found=true
```

## 3. IAM API 连接测试

### 3.1 当前 IAM 服务信息

基于环境调研，IAM 服务配置：
- **主要服务**: `iam-manage-xian.console.svc.cluster.local:8468`
- **API 版本**: `/v3`
- **健康检查**: `/v3/health`
- **认证端点**: `/v3/auth/tokens`

### 3.2 连通性测试

```bash
# 创建测试 Pod
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-iam-api
spec:
  restartPolicy: Never
  containers:
  - name: curl
    image: curlimages/curl
    command: ["sleep", "300"]
    resources:
      limits:
        cpu: "100m"
        memory: "128Mi"
      requests:
        cpu: "50m"
        memory: "64Mi"
EOF

# 进入 Pod 测试
kubectl exec -it test-iam-api -- sh

# 在 Pod 内执行
curl -v http://iam-manage-xian.console.svc.cluster.local:8468/v3/health
```

### 3.3 API 测试脚本

```bash
cd iam-operator
python scripts/test-iam-api.py
```

## 4. 故障排查

### 4.1 常见问题

1. **Pod 无法启动**
   ```bash
   kubectl describe pod -n base -l app=iam-operator
   kubectl logs -n base -l app=iam-operator
   ```

2. **ConfigMap 未被处理**
   - 检查命名空间是否在 target-namespaces 中
   - 检查 ConfigMap 内容是否包含 IAM 凭据模式
   - 查看 Operator 日志

3. **权限问题**
   ```bash
   kubectl auth can-i get configmaps --as=system:serviceaccount:base:iam-operator
   ```

### 4.2 调试模式

修改日志级别为 DEBUG：
```yaml
# 在 iam-operator-config ConfigMap 中
data:
  log-level: "DEBUG"
```

## 5. 测试检查清单

- [ ] ConfigMap 解析功能正常
- [ ] Operator 部署成功
- [ ] 只监听指定命名空间
- [ ] 日志输出正常
- [ ] 状态 ConfigMap 创建
- [ ] IAM API 连通性测试
- [ ] 完全清理验证

## 6. 注意事项

1. **生产环境**: 不要在生产环境直接测试，使用独立的测试命名空间
2. **权限控制**: 确保 RBAC 配置正确，避免权限过大
3. **资源清理**: 测试完成后及时清理所有资源
4. **日志监控**: 密切关注 Operator 日志，及时发现问题
5. **版本控制**: 记录测试的代码版本和配置变更
