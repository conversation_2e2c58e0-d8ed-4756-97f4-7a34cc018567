# IAM-Operator 服务发现功能

## 概述

IAM-Operator 支持两种方式调用 IAM API：

1. **静态配置**：使用固定的 URL 地址
2. **服务发现**：通过 Kubernetes Service 动态发现 IAM 服务端点

## 方案2：服务发现实现

### 技术原理

```go
// 服务发现的核心逻辑（Go伪代码）
iamService, err := clientset.CoreV1().Services("iam-namespace").Get(ctx, "iam-nginx", metav1.GetOptions{})
iamEndpoint := fmt.Sprintf("http://%s:%d/v3", 
    iamService.Spec.ClusterIP, 
    iamService.Spec.Ports[0].Port)
```

### Python 实现

```python
from src.clients.iam_service_discovery import IAMServiceDiscoveryClient

# 初始化服务发现客户端
client = IAMServiceDiscoveryClient(
    iam_namespace="console",
    service_name="iam-manage-xian"
)

# 自动发现服务端点
endpoint = client._discover_iam_endpoint()
print(f"发现的IAM端点: {endpoint}")
```

## 配置方式

### 1. Helm Chart 配置

在 `values.yaml` 中启用服务发现：

```yaml
iamApi:
  url: "http://iam-manage-xian.console.svc.cluster.local:8468"  # fallback
  timeout: 30
  
  serviceDiscovery:
    enabled: true           # 启用服务发现
    namespace: "console"    # IAM 服务所在命名空间
    serviceName: "iam-manage-xian"  # IAM 服务名称
```

### 2. 环境变量配置

```bash
export IAM_SERVICE_DISCOVERY_ENABLED=true
export IAM_SERVICE_NAMESPACE=console
export IAM_SERVICE_NAME=iam-manage-xian
```

### 3. ConfigMap 配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: iam-operator-config
data:
  iam-service-discovery-enabled: "true"
  iam-service-namespace: "console"
  iam-service-name: "iam-manage-xian"
```

## 测试和验证

### 1. 基础测试

```bash
# 运行服务发现测试
python scripts/test-service-discovery.py

# 验证方案可行性
python scripts/validate-service-discovery-approach.py
```

### 2. 最小测试

```python
from src.clients.iam_service_discovery import IAMServiceDiscoveryClient

client = IAMServiceDiscoveryClient()

# 测试服务发现
result = client.test_service_discovery()
print(f"服务发现结果: {result}")

# 健康检查
health = client.health_check()
print(f"健康状态: {health}")
```

### 3. 健康检查

服务发现客户端提供内置的健康检查功能：

```python
# 在 Kubernetes 探针中使用
@kopf.on.probe(id='readiness')
def readiness_probe(**kwargs):
    try:
        client = get_iam_client()
        if hasattr(client, 'health_check') and client.health_check():
            return {'status': 'ready'}
        else:
            return {'status': 'not ready', 'reason': 'IAM service unavailable'}
    except Exception as e:
        return {'status': 'not ready', 'reason': str(e)}
```

## 优势和特点

### ✅ 优势

1. **动态发现**：自动适应服务 IP 变化
2. **高可用**：支持服务故障转移
3. **缓存机制**：减少 Kubernetes API 调用
4. **错误处理**：完善的异常处理机制
5. **向后兼容**：可与静态配置共存

### 🔧 特点

1. **缓存TTL**：服务端点缓存5分钟
2. **超时控制**：支持配置超时时间
3. **端口选择**：智能选择合适的服务端口
4. **日志记录**：详细的操作日志

## 部署指南

### 1. 开发环境

```bash
# 使用默认配置（静态）
helm install iam-operator ./helm/iam-operator

# 启用服务发现
helm install iam-operator ./helm/iam-operator \
  --set iamApi.serviceDiscovery.enabled=true
```

### 2. 生产环境

```bash
# 使用生产配置（已启用服务发现）
helm install iam-operator ./helm/iam-operator \
  -f helm/iam-operator/values-production.yaml
```

### 3. 升级现有部署

```bash
# 启用服务发现
helm upgrade iam-operator ./helm/iam-operator \
  --set iamApi.serviceDiscovery.enabled=true \
  --set iamApi.serviceDiscovery.namespace=console \
  --set iamApi.serviceDiscovery.serviceName=iam-manage-xian
```

## 监控和故障排除

### 监控指标

- `iam_service_discovery_success_total`：服务发现成功次数
- `iam_service_discovery_duration_seconds`：服务发现耗时
- `iam_service_discovery_cache_hits_total`：缓存命中次数

### 常见问题

1. **服务不存在**
   ```
   错误：Service console/iam-manage-xian not found
   解决：检查服务名称和命名空间配置
   ```

2. **权限不足**
   ```
   错误：Forbidden: services is forbidden
   解决：确保 ServiceAccount 有读取 Service 的权限
   ```

3. **网络不通**
   ```
   错误：Connection refused
   解决：检查网络策略和服务端口配置
   ```

### 调试命令

```bash
# 检查服务状态
kubectl get svc -n console iam-manage-xian

# 查看服务详情
kubectl describe svc -n console iam-manage-xian

# 检查 operator 日志
kubectl logs -n base deployment/iam-operator
```

## 最佳实践

1. **渐进式部署**：先在测试环境验证，再部署到生产环境
2. **监控告警**：设置服务发现失败的告警
3. **故障转移**：保留静态配置作为备用方案
4. **定期测试**：定期执行端到端测试
5. **文档更新**：及时更新服务名称和配置文档
