自动注入 keys.json 对应 SQL

[[eam-ui-15273] 【公共服务】目前需要手动操作导入 蓝图 dev6的 bce_iam-append-02.sql对应的 SQL文件注入，需要自动化](https://console.cloud.baidu-int.com/devops/icafe/issue/eam-ui-15273/show?source=copy-shortcut?t=mention&mt=doc&dt=sdk)

# 背景

[产品使用ak、sk需要动态注入iam数据库问题](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/ZIeThHHOUA/pt0u1Vr4UAUQ7i?t=mention&mt=doc&dt=doc)

每套环境服务号的 ak/sk 都是不一样的，保存在 keys.json，新云底座：由蓝图负责动态生成并维护，在部署/升级 chart 时自动注入，而 keys.json 对应的 SQL 在 0-1 部署时，也需要自动化注入

# 方案

暂用方案二实现 0-1 部署时 keys.json 对应 SQL 的自动化注入，前置条件是：提前为 keys.json 对应的 SQL 文件（bce-iam-append-02.sql） 创建 configmap，名称为 bce-iam-aksk，命名空间为 xdb-data-init   **@蓝图提供 SQL、@沧竹创建 cm**

然后，xdb-data-init-bce-iam 执行如下动作：[评审：eam-ui-15273 add ak/sk SQL](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/product-chart/iam/reviews/*********/files/base...latest/README.md?t=mention&mt=doc&dt=sdk)

- 保存`ak/sk`的`keys.json`对应的`SQL文件`通过`chart`包`xdb-data-init-bce-iam`挂载`configmap`的方式获取并执行（详见 `values.yaml` 中的 `bce-iam-aksk` 变量）
- `keys.json`对应`SQL文件`执行时机是: 所有服务号 `ak/sk` 的`模版数据`对应的`SQL`已经执行，比如 `V1.0.0.4__bce_iam__init_service_resource_account.sql` 等文件已由 `flyway` 执行
- 所以，定义如下规则：
    - 后续追加服务号、资源账号`ak/sk`统一在`V1.0.0.17`和`V1.0.0.18`文件中
    - `keys.json`对应的`SQL`文件名固定为`V1.0.0.19__bce_iam__init_ak_sk.sql`

此时会保证所有服务号的`ak/sk`都会刷新成`keys.json`中的（详见`dockerfile`目录中的`preinstall.sh`），可以实现 0-1 的自动化部署，并且`keys.json`的`SQL`只会执行一次（基于`flyway`的版本控制）可以满足 det 部署要求。但针对后续存量项目新增服务号，需要和 `charm` 包一样手动操作，但此时 det 已无需关注，所以暂时先这样弄。最终方案可以采用 `operator`：https://ku.baidu-int.com/d/pt0u1Vr4UAUQ7i

# 效果

[](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=bd2eb2cffba1489e9cade0218e6e6215&docGuid=h-BSEqiwYzoOQM)