# IAM Operator dev7 快速参考

## 🚀 快速状态检查

### 检查Pod状态
```bash
kubectl get pods -n iam-operator-system -o wide
```

### 查看实时日志
```bash
kubectl logs -n iam-operator-system -l control-plane=controller-manager -f
```

### 检查服务发现结果
```bash
kubectl logs -n iam-operator-system -l control-plane=controller-manager | grep "Discovered IAM endpoint"
```

## 🔧 常用运维命令

### 重启Operator
```bash
kubectl delete pod -n iam-operator-system -l control-plane=controller-manager
```

### 更新配置
```bash
kubectl edit configmap iam-operator-config -n iam-operator-system
# 修改后需要重启Pod生效
kubectl delete pod -n iam-operator-system -l control-plane=controller-manager
```

### 检查RBAC权限
```bash
kubectl describe clusterrole iam-operator-manager-role
kubectl describe clusterrolebinding iam-operator-manager-rolebinding
```

## 📊 监控指标

### 健康检查
```bash
kubectl get pods -n iam-operator-system
# 期望状态: 1/1 Running

# 健康检查端点
kubectl port-forward -n iam-operator-system svc/iam-operator-controller-manager-metrics-service 8081:8081
curl http://localhost:8081/healthz
curl http://localhost:8081/readyz
```

### 资源使用
```bash
kubectl top pod -n iam-operator-system
kubectl describe pod -n iam-operator-system -l control-plane=controller-manager
```

## 🐛 故障排查

### 常见问题及解决方案

#### 1. Pod CrashLoopBackOff
```bash
# 查看详细错误
kubectl describe pod -n iam-operator-system -l control-plane=controller-manager
kubectl logs -n iam-operator-system -l control-plane=controller-manager --previous
```

#### 2. 服务发现失败
```bash
# 检查目标服务是否存在
kubectl get svc iam-manage-xian -n console
kubectl describe svc iam-manage-xian -n console

# 检查网络连通性
kubectl exec -n iam-operator-system -it deployment/iam-operator-controller-manager -- nslookup iam-manage-xian.console.svc.cluster.local
```

#### 3. ConfigMap处理异常
```bash
# 创建测试ConfigMap
kubectl create configmap test-iam-credentials \
  --from-literal=access_key=test-ak \
  --from-literal=secret_key=test-sk \
  -n default

# 检查处理日志
kubectl logs -n iam-operator-system -l control-plane=controller-manager | grep test-iam-credentials
```

#### 4. 权限问题
```bash
# 检查ServiceAccount权限
kubectl auth can-i get configmaps --as=system:serviceaccount:iam-operator-system:iam-operator-controller-manager
kubectl auth can-i list services --as=system:serviceaccount:iam-operator-system:iam-operator-controller-manager
```

## 🔄 镜像更新流程

### 1. 本地构建新镜像
```bash
# 编译新版本
GOOS=linux GOARCH=amd64 go build -o bin/manager-linux-amd64 cmd/main.go

# 构建Docker镜像
docker build --platform linux/amd64 -t iam-operator:local .

# 导出镜像
docker save -o iam-operator-local-new.tar iam-operator:local
```

### 2. 上传到集群节点
```bash
scp -o ProxyJump=跳板机 iam-operator-local-new.tar <EMAIL>:/tmp/iam-operator-image/
```

### 3. 在节点更新镜像
```bash
# SSH到节点
ssh -o ProxyJump=跳板机 <EMAIL>

# 删除旧镜像
ctr -n k8s.io images remove docker.io/library/iam-operator:local

# 导入新镜像
ctr -n k8s.io images import /tmp/iam-operator-image/iam-operator-local-new.tar

# 验证
ctr -n k8s.io images list | grep iam-operator
```

### 4. 重启Pod应用新镜像
```bash
kubectl delete pod -n iam-operator-system -l control-plane=controller-manager
kubectl get pods -n iam-operator-system -w
```

## 📋 配置参考

### 环境变量配置
```yaml
IAM_SERVICE_DISCOVERY_ENABLED: "true"      # 启用服务发现
IAM_SERVICE_NAMESPACE: "console"           # IAM服务所在命名空间
IAM_SERVICE_NAME: "iam-manage-xian"        # IAM服务名称
TARGET_NAMESPACES: "default,console,kube-system"  # 监控的命名空间
IAM_API_URL_FALLBACK: "http://iam.xian.dev7.abcstackint.com:35357"  # 备用URL
IAM_DOMAIN: "default"                      # IAM域
IAM_USERNAME: "proxy"                      # IAM用户名
IAM_PASSWORD: "proxy"                      # IAM密码
LOG_LEVEL: "info"                          # 日志级别
RETRY_INTERVAL: "5m"                       # 重试间隔
```

### 关键文件路径
```
/Users/<USER>/PublicService/iam-operator-go/
├── deploy/simple-deploy.yaml              # 部署配置文件
├── bin/manager-linux-amd64               # Linux二进制文件
├── Dockerfile                            # Docker构建文件
├── cmd/main.go                          # 主程序入口
└── internal/controller/configmap_controller.go  # ConfigMap控制器
```

## 🎯 测试用例

### 创建测试ConfigMap
```bash
# 包含IAM凭据的ConfigMap
kubectl create configmap test-iam-credentials \
  --from-literal=access_key=test-access-key \
  --from-literal=secret_key=test-secret-key \
  -n default

# 不包含IAM凭据的ConfigMap
kubectl create configmap test-normal-config \
  --from-literal=app=test \
  --from-literal=version=1.0 \
  -n default
```

### 验证处理结果
```bash
# 应该看到处理IAM凭据的日志
kubectl logs -n iam-operator-system -l control-plane=controller-manager | grep test-iam-credentials

# 应该看到跳过普通ConfigMap的日志
kubectl logs -n iam-operator-system -l control-plane=controller-manager | grep test-normal-config
```

## 📞 联系信息

**项目仓库：** `/Users/<USER>/PublicService/iam-operator-go`  
**部署环境：** dev7.abcstackint.com  
**运行节点：** iaas5-kangding.dev7.abcstackint.com  
**命名空间：** iam-operator-system  

## 🔖 重要提醒

1. **镜像更新** - 必须在iaas5节点上更新镜像，因为使用了nodeSelector
2. **权限模型** - 当前为只读权限，不会修改任何ConfigMap
3. **服务发现** - 依赖console/iam-manage-xian服务，如果服务不可用会回退到静态URL
4. **日志级别** - 默认为info，可以改为debug获取更详细日志
5. **备份恢复** - 重要配置变更前建议备份当前部署配置

---

**快速参考版本：** v1.0  
**最后更新：** 2025-07-30
