# IAM-Operator 需求背景

## 问题描述：[产品使用ak、sk需要动态注入iam数据库问题](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/ZIeThHHOUA/pt0u1Vr4UAUQ7i?t=mention&mt=doc&dt=doc)

每个云产品都会用到 ak/sk/password 去完成服务间调用，这些 ak/sk/password 在初次交付时由  蓝图平台 和 IAM 统一分配给各个产品方，当前存在两种交付形式，分别为 charm 和 chart 包。本次改造主要关注新云底座 chart 包部署交付时 ak/sk 分配的自动化。

## chart 现状：[自动注入 keys.json 对应 SQL](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/vSZMZk-ELQ/h-BSEqiwYzoOQM?t=mention&mt=doc&dt=doc)

每个产品用到的 ak/sk 明文事先会由蓝图统一生成，并注入到每个产品的 values.yaml 中；

然后蓝图会提供这些 ak/sk 对应的 SQL 交给沧竹平台创建对应的 configmap；

IAM 则基于这个 configmap 拿到 SQL 文件注入到 iam 数据库，保证蓝图分配给产品方的 ak/sk 可用；

针对已交付的环境，如果有新增产品，依然由蓝图产出明文 ak/sk/password，此时不会在产出 SQL 文件，此时完全由 IAM 侧负责生成 ak/sk/password 对应的 SQL，并注入到 IAM 数据库

## 解决方案

所以云底座同学提供了 Operator 方案，基于 IAM-Operator ，让产品方事先声明要用到的服务号，同时 IAM 提供注入 ak/sk/password 的接口，在升级服务时请求 IAM 这些接口进行 ak/sk 的注入，省去之前的一系列 SQL 动作

## 其它 Operator 参考实现

[基于Operator 实现XDB账户、DB动态注册方案](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/vMri-fRViV/ZIeThHHOUA/vZZxibiHrmrzw9?t=mention&mt=doc&dt=doc)

---

# IAM-Operator 逐步验证开发计划

## 需求澄清

基于与开发团队的讨论，明确了以下关键信息：

1. **IAM API 开发职责**：IAM API 服务由我们团队开发，暴露 REST 接口供 Operator 调用，直接操作 IAM 数据库
2. **服务号声明方式**：通过配置文件方式声明服务号，需要确定具体的配置文件格式和位置
3. **注入时机**：在服务升级时触发，对应 Kubernetes 中的 Deployment/StatefulSet 更新事件

## 开发阶段规划

### 阶段 1：理解需求和技术边界验证 ✅

- [x] **理解 IAM-Operator 需求和架构设计**
  - 已明确：IAM API 由我们开发并暴露给 Operator 调用
  - 通过配置文件方式声明服务号
  - 升级服务时触发（对应 k8s 中的 Deployment/StatefulSet 更新事件）

### 阶段 2：MVP - 验证 Operator 基础能力

- [ ] **设计 IAM 相关的 CRD 资源定义**
  - 重新设计：基于配置文件方式声明服务号
  - 考虑是扩展现有 XdbUser CRD 还是新建 IamServiceAccount CRD

- [ ] **验证 XDB-Operator 基础功能**
  - 部署 xdb-operator 并验证现有功能是否正常工作
  - 了解 Operator 的技术边界和扩展能力

- [ ] **开发 Mock IAM API 服务**
  - 创建简单的 HTTP API 模拟 IAM 数据库操作
  - 用于 Operator 开发测试，避免依赖真实 IAM 服务

- [ ] **实现基础 IAM Controller 逻辑**
  - 监听配置文件变化或 CRD 事件
  - 调用 Mock IAM API，验证基础 Operator 模式

- [ ] **验证 k8s 服务升级事件监听**
  - 测试 Operator 能否正确监听到 Deployment/StatefulSet 的更新事件
  - 验证事件触发时机和数据获取

### 阶段 3：架构决策

- [ ] **架构决策：新建 vs 扩展**
  - 评估在现有 XDB-Operator 中扩展 IAM 功能的复杂度
  - 评估新建独立 IAM-Operator 的优劣
  - 基于技术复杂度、维护成本、团队分工等因素确定最终架构方案

**扩展现有 XDB-Operator 的优势：**
- 统一管理数据库相关资源
- 复用现有的 RBAC 和部署结构
- 减少运维复杂度

**新建 IAM-Operator 的优势：**
- 职责分离，IAM 功能独立
- 可以独立升级和维护
- 不影响现有 XDB 功能

### 阶段 4：真实 IAM 集成开发

- [ ] **开发真实 IAM API 服务**
  - 实现操作 IAM 数据库的 REST API
  - 包括 ak/sk/password 的 CRUD 操作
  - 提供认证、授权、错误处理等完整功能

- [ ] **实现真实 IAM 集成**
  - 在 Operator 中集成真实的 IAM API 调用逻辑
  - 替换 Mock 实现，处理真实的网络调用、重试、错误处理

### 阶段 5：端到端测试验证

- [ ] **端到端测试验证**
  - 创建完整测试场景，验证从配置声明到服务升级时凭据注入的完整流程
  - 包括正常流程测试、异常情况处理、性能测试等

## 技术关键点

### 服务升级触发事件分析

服务升级在 Kubernetes 中会触发以下事件：
- **Deployment 更新**：`spec.template` 变化触发 Pod 重建
- **StatefulSet 更新**：滚动更新或重启
- **ConfigMap/Secret 更新**：如果挂载到 Pod 中
- **镜像版本变更**：最常见的升级场景

### 立即可验证的步骤

1. **验证 Operator 边界**
   ```bash
   # 部署 xdb-operator 看看现有功能
   kubectl apply -f deploy/deploy-operator.yaml

   # 测试现有 CRD 功能
   kubectl apply -f samples/example-xdb-user.yaml
   ```

2. **理解现有代码结构**
   - 查看现有 Controller 如何监听 CRD 事件
   - 了解如何添加新的事件监听器
   - 确定扩展点在哪里

## 下一步行动

建议从验证现有 XDB-Operator 的功能开始，了解其技术边界和扩展能力，这样可以为架构决策提供更好的依据。