# IAM Operator 最终部署指南

## 📋 概述

本指南基于成功验证的技术方案，提供 IAM Operator 在 dev7 集群的完整部署流程。

## 🎯 部署目标

- **功能**：ConfigMap AKSK 自动检测和验证
- **范围**：目标命名空间 `default`, `console`, `kube-system`
- **方式**：服务发现方式调用 IAM API
- **权限**：只读权限，安全合规

## 📦 部署资源

### 最终验证成功的镜像
- **镜像名称**：`iam-operator:local-v10`
- **架构**：`linux/amd64`
- **大小**：约 321MB
- **状态**：✅ 生产就绪

### 核心配置文件
- **部署配置**：`deploy/simple-deploy.yaml`
- **RBAC 权限**：只读权限（get, list, watch）
- **环境变量**：完整的 IAM 和服务发现配置

## 🚀 部署步骤

### 步骤1：准备镜像
```bash
# 1. 确保本地有最新的成功镜像
docker images | grep iam-operator
# iam-operator    local-v10    xxx    xxx    321MB

# 2. 导出镜像（如果需要）
docker save iam-operator:local-v10 -o iam-operator-v10-final.tar

# 3. 上传到集群节点
scp iam-operator-v10-final.tar <EMAIL>:/tmp/iam-operator-image/

# 4. 在集群节点导入镜像
sudo ctr -n k8s.io images import /tmp/iam-operator-image/iam-operator-v10-final.tar
```

### 步骤2：验证集群连接
```bash
# 确保连接到正确的集群
kubectl config current-context
# 应该显示 dev7 集群信息

# 验证集群状态
kubectl get nodes
kubectl get ns iam-operator-system
```

### 步骤3：部署 Operator
```bash
# 1. 应用部署配置
kubectl apply -f deploy/simple-deploy.yaml

# 2. 等待部署完成
kubectl rollout status deployment/iam-operator-controller-manager -n iam-operator-system

# 3. 验证 Pod 状态
kubectl get pod -n iam-operator-system
# NAME                                               READY   STATUS    RESTARTS   AGE
# iam-operator-controller-manager-xxx                1/1     Running   0          2m
```

### 步骤4：功能验证
```bash
# 1. 检查启动日志
kubectl logs deployment/iam-operator-controller-manager -n iam-operator-system | head -10

# 期望看到：
# INFO 🚀 IAM Operator starting up - DEBUG VERSION v7
# INFO Configuration loaded {"targetNamespaces": ["default", "console", "kube-system"], "targetNamespacesCount": 3}
# INFO Configuring manager to watch specific namespaces {"namespaces": ["default", "console", "kube-system"]}
# INFO Discovered IAM endpoint {"service": "console/iam-manage-xian", "endpoint": "http://100.69.244.105:8468/v3"}

# 2. 验证命名空间过滤
kubectl logs deployment/iam-operator-controller-manager -n iam-operator-system --tail=20 | grep "Reconciling ConfigMap"
# 应该只看到 console, default, kube-system 命名空间的 ConfigMaps

# 3. 测试 ConfigMap 处理
kubectl create configmap test-deployment --from-literal=access_key=test-ak --from-literal=secret_key=test-sk -n default
kubectl logs deployment/iam-operator-controller-manager -n iam-operator-system --tail=10 | grep test-deployment
# 应该看到完整的处理流程日志

# 4. 清理测试资源
kubectl delete configmap test-deployment -n default
```

## 📋 部署配置详情

### 环境变量配置
```yaml
env:
- name: TARGET_NAMESPACES
  value: "default,console,kube-system"
- name: IAM_SERVICE_DISCOVERY_ENABLED
  value: "true"
- name: IAM_SERVICE_NAME
  value: "iam-manage-xian"
- name: IAM_SERVICE_NAMESPACE
  value: "console"
- name: IAM_API_URL_FALLBACK
  value: "http://iam.xian.dev7.abcstackint.com:35357"
- name: IAM_DOMAIN
  value: "default"
- name: IAM_USERNAME
  value: "proxy"
- name: IAM_PASSWORD
  value: "proxy"
- name: LOG_LEVEL
  value: "info"
- name: RETRY_INTERVAL
  value: "5m"
- name: IAM_API_TIMEOUT
  value: "30s"
```

### RBAC 权限配置
```yaml
rules:
# ConfigMap 只读权限
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
# Service 只读权限（用于服务发现）
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch"]
# Leader election 权限
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
```

### 资源配置
```yaml
resources:
  limits:
    cpu: 500m
    memory: 128Mi
  requests:
    cpu: 10m
    memory: 64Mi
```

## 🔍 监控和运维

### 健康检查
```bash
# 1. Pod 健康状态
kubectl get pod -n iam-operator-system -o wide

# 2. 健康检查端点
kubectl port-forward -n iam-operator-system deployment/iam-operator-controller-manager 8081:8081
curl http://localhost:8081/healthz
# 应该返回 200 OK

# 3. 资源使用情况
kubectl top pod -n iam-operator-system
```

### 日志监控
```bash
# 1. 实时日志
kubectl logs -f deployment/iam-operator-controller-manager -n iam-operator-system

# 2. 错误日志
kubectl logs deployment/iam-operator-controller-manager -n iam-operator-system | grep ERROR

# 3. 处理统计
kubectl logs deployment/iam-operator-controller-manager -n iam-operator-system | grep "Successfully processed ConfigMap" | wc -l
```

### 关键指标
- **处理的 ConfigMaps 数量**：`grep "Successfully processed ConfigMap"`
- **认证成功率**：成功/失败的比例
- **服务发现状态**：`grep "Discovered IAM endpoint"`
- **错误率**：`grep ERROR` 的频率

## 🚨 故障排查

### 常见问题

#### 1. Pod 启动失败
```bash
# 检查 Pod 状态
kubectl describe pod -l control-plane=controller-manager -n iam-operator-system

# 常见原因：
# - 镜像拉取失败：检查镜像是否正确导入
# - 权限问题：检查 RBAC 配置
# - 资源不足：检查节点资源
```

#### 2. ConfigMap 未被处理
```bash
# 检查命名空间配置
kubectl logs deployment/iam-operator-controller-manager -n iam-operator-system | grep "Configuration loaded"

# 检查 ConfigMap 是否在目标命名空间
kubectl get configmap -n <namespace>

# 检查 ConfigMap 是否包含 IAM 凭据
kubectl get configmap <name> -n <namespace> -o yaml
```

#### 3. 服务发现失败
```bash
# 检查 IAM 服务状态
kubectl get service -n console | grep iam-manage

# 检查服务发现日志
kubectl logs deployment/iam-operator-controller-manager -n iam-operator-system | grep "service discovery"
```

#### 4. IAM API 调用失败
```bash
# 检查网络连通性
kubectl exec -n iam-operator-system deployment/iam-operator-controller-manager -- curl -I http://100.69.244.105:8468/v3

# 检查认证配置
kubectl get configmap iam-operator-config -n iam-operator-system -o yaml
```

### 紧急恢复
```bash
# 1. 重启 Operator
kubectl rollout restart deployment/iam-operator-controller-manager -n iam-operator-system

# 2. 回滚到上一个版本（如果需要）
kubectl rollout undo deployment/iam-operator-controller-manager -n iam-operator-system

# 3. 完全重新部署
kubectl delete -f deploy/simple-deploy.yaml
kubectl apply -f deploy/simple-deploy.yaml
```

## 📈 性能优化

### 资源调优
```yaml
# 根据实际负载调整资源限制
resources:
  limits:
    cpu: 1000m      # 高负载时增加
    memory: 256Mi   # 大量 ConfigMaps 时增加
  requests:
    cpu: 50m        # 基础资源需求
    memory: 128Mi   # 基础内存需求
```

### 配置优化
```yaml
# 调整重试间隔
- name: RETRY_INTERVAL
  value: "3m"       # 减少重试间隔提高响应速度

# 调整 API 超时
- name: IAM_API_TIMEOUT
  value: "15s"      # 根据网络情况调整
```

## ✅ 部署检查清单

### 部署前检查
- [ ] 集群连接正常
- [ ] 镜像已正确导入
- [ ] 目标命名空间存在
- [ ] IAM 服务正常运行

### 部署后验证
- [ ] Pod 状态为 Running
- [ ] 启动日志显示正确配置
- [ ] 命名空间过滤生效
- [ ] 服务发现成功
- [ ] ConfigMap 处理正常
- [ ] 健康检查通过

### 功能验证
- [ ] 创建测试 ConfigMap 被正确处理
- [ ] 错误处理机制正常
- [ ] 日志输出清晰可读
- [ ] 资源使用在合理范围

---
**🎉 部署指南完成！按照此指南可以成功部署生产就绪的 IAM Operator！**

*创建时间：2025-07-31*  
*版本：v10 (最终验证版本)*  
*状态：✅ 生产就绪*
