FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制源代码
COPY src/ ./src/

# 创建非 root 用户
RUN groupadd -r iam-operator && useradd -r -g iam-operator iam-operator

# 设置权限
RUN chown -R iam-operator:iam-operator /app

# 切换到非 root 用户
USER iam-operator

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8081/healthz')" || exit 1

# 暴露端口
EXPOSE 8080 8081

# 启动命令
CMD ["python", "-m", "src.main"]
