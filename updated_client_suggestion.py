
# 更新后的IAM客户端 - 基于实际API验证

class IAMServiceDiscoveryClient:
    def __init__(self, iam_namespace="console", service_name="iam-manage-xian"):
        self.iam_namespace = iam_namespace
        self.service_name = service_name
        # ... 服务发现逻辑保持不变
    
    def _get_auth_token(self) -> str:
        """获取认证Token - 基于实际验证的流程"""
        endpoint = self._discover_iam_endpoint()
        
        # 使用实际验证的认证格式
        auth_request = {
            "auth": {
                "identity": {
                    "methods": ["password"],
                    "password": {
                        "user": {
                            "domain": {"name": "Default"},
                            "name": config.get('iam_username', 'proxy'),
                            "password": config.get('iam_password', 'your-password')
                        }
                    }
                },
                "scope": {
                    "domain": {"id": "default"}
                }
            }
        }
        
        response = self.session.post(
            f"{endpoint}/auth/tokens",  # 注意：endpoint已包含/v3
            json=auth_request,
            timeout=self.timeout
        )
        
        if response.status_code == 201:
            # 从响应头获取token
            token = response.headers.get('X-Subject-Token')
            return token
        else:
            raise IAMAPIError(f"Authentication failed: {response.status_code}")
    
    def _call_api_with_auth(self, method, endpoint, **kwargs):
        """使用认证Token调用API"""
        token = self._get_auth_token()
        headers = kwargs.get('headers', {})
        headers['X-Auth-Token'] = token  # 或者 X-Subject-Token
        kwargs['headers'] = headers
        
        base_endpoint = self._discover_iam_endpoint()
        url = f"{base_endpoint}{endpoint}"
        
        return self.session.request(method, url, **kwargs)
