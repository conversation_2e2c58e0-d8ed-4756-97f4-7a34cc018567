#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   utils.py
@Time    :   2024/6/22 19:25
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   l<PERSON><PERSON><PERSON><EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import base64
import datetime
import hashlib
import json
import os
import random
import string
import threading
import time
import re
from datetime import timedelta

def b64decode(s: str) -> str:
    """
    将给定的字符串进行base64解码，并返回解码后的字符串。
    
    Args:
        s (str): 需要进行base64解码的字符串。
    
    Returns:
        str: 解码后的字符串，类型为str。
    """
    return base64.b64decode(s).decode("utf8")


def b64encode(s: str) -> str:
    """
    将字符串编码为base64格式的字符串。
    
    Args:
        s (str): 需要编码的字符串。
    
    Returns:
        str: 返回一个base64格式的字符串，使用ASCII编码。
    """
    return base64.b64encode(bytes(s, "utf8")).decode("ascii")


def sha256(s: str) -> str:
    """
    计算字符串的SHA-256哈希值，返回16进制字符串。
    
    Args:
        s (str): 需要计算SHA-256哈希值的字符串。
    
    Returns:
        str: 16进制格式的SHA-256哈希值，长度为64个字符。
    """
    return hashlib.sha256(bytes(s, "utf8")).hexdigest()


class EphemeralState:
    """
    State that's not persisted between operator restarts
    Use only if get() returning None is interpreted as "skip optimization"
    """

    def __init__(self):
        """
            初始化函数，用于初始化对象属性和方法。
        初始化后的对象包含以下属性和方法：
            - data: 一个字典类型，用于存放数据，默认为空字典。
            - context: 一个字典类型，用于存放上下文信息，默认为空字典。
            - time: 一个字典类型，用于存放时间相关信息，默认为空字典。
            - lock: 一个线程锁对象，用于保护多线程环境中对共享资源的并发访问。
        """
        self.data = {}
        self.context = {}
        self.time = {}
        self.lock = threading.Lock()

    def get(self, obj, key: str):
        """
            获取指定对象的指定键值，并返回该值。如果不存在则返回None。
        参数：
            obj (object): 包含命名空间和名称的对象，用于构造键值。
                namespace (str): 对象的命名空间。
                name (str): 对象的名称。
            key (str): 需要获取的键值。
        返回值（Union[Any, None]）：
            - 如果键值存在，则返回相应的值；
            - 如果键值不存在，则返回None。
        """
        key = obj.namespace + "/" + obj.name + "/" + key
        with self.lock:
            return self.data.get(key)

    def testset(self, obj, key: str, value, context: str):
        """
            设置对象的属性值，并返回旧的数据、上下文和时间。如果不存在该属性，则创建一个新的属性。
        参数：
            obj (object) - 包含命名空间和名称的对象，用于确定属性的键。
            key (str) - 要设置的属性的名称。
            value (Any) - 要设置的属性的值。
            context (str) - 属性的上下文信息。
        返回值（tuple）：
            (old_data, old_context, old_time) (Any, str, datetime.datetime) - 旧的数据、上下文和时间，如果不存在该属性，则为None。
        """
        key = obj.namespace + "/" + obj.name + "/" + key
        with self.lock:
            old_data = self.data.get(key)
            old_context = self.context.get(key)
            old_time = self.time.get(key)
            if old_data is None:
                self.data[key] = value
                self.context[key] = context
                self.time[key] = datetime.datetime.now()
        return (old_data, old_context, old_time)

    def set(self, obj, key: str, value, context: str) -> None:
        """
            设置对象的属性值，并记录当前时间。
        
        Args:
            obj (Any): 包含命名空间和名称的对象。
            key (str): 要设置的属性键名。
            value (Any): 要设置的属性值。
            context (str): 操作上下文信息，例如用户名或IP地址等。
        
        Returns:
            None: 无返回值，直接修改了数据字典和时间字典。
        """
        key = obj.namespace + "/" + obj.name + "/" + key
        with self.lock:
            self.data[key] = value
            self.context[key] = context
            self.time[key] = datetime.datetime.now()


g_ephemeral_pod_state = EphemeralState()


def isotime() -> str:
    """
    返回UTC时间的ISO格式字符串，不包含微秒。
    
    Args:
        无参数。
    
    Returns:
        str (str): UTC时间的ISO格式字符串，不包含微秒，例如：'2019-08-31T16:45:00Z'.
    """
    return datetime.datetime.utcnow().replace(microsecond=0).isoformat() + "Z"


def timestamp(dash: bool = True, four_digit_year: bool = True) -> str:
    """
    获取当前时间戳，可选是否包含破折号和四位数年份。
    
    Args:
        dash (bool, optional): 是否在日期中添加破折号（默认为True）. Defaults to True.
        four_digit_year (bool, optional): 是否使用四位数年份（默认为True）. Defaults to True.
    
    Returns:
        str: 返回格式为"YYYY-MM-DDHHMMSS"或"YY-MM-DDHHMMSS"的字符串，其中YY为两位或四位数年份。
    """
    dash_str = "-" if dash else ""
    year_str = "%Y" if four_digit_year else "%y"
    return datetime.datetime.now().replace(microsecond=0).strftime(f"{year_str}%m%d{dash_str}%H%M%S")


def merge_patch_object(base: dict, patch: dict, prefix: str = "", key: str = "") -> None:
    """
    将一个字典合并到另一个字典中，如果键存在于两个字典中，则会进行深度合并。
    当遇到列表或字典时，会按照名称对象进行合并。
    
    Args:
        base (dict): 基本字典，将被合并的字典。
        patch (dict): 补丁字典，包含需要合并的更新内容。
        prefix (str, optional): 前缀，默认为"". 用于记录合并过程中的路径信息。
        key (str, optional): 不支持此参数，默认为"". 未来可能实现。
    
    Raises:
        ValueError: 如果类型不匹配，将引发ValueError异常。
    
    Returns:
        None: 该函数没有返回值。
    """
    assert not key, "not implemented"  # TODO support key

    if type(base) != type(patch):
        raise ValueError(f"Invalid type in patch at {prefix}")
    if type(base) != dict:
        raise ValueError(f"Invalid type in base at {prefix}")

    def get_named_object(l, name):
        for o in l:
            assert type(o) == dict, f"{prefix}: {name} = {o}"
            if o["name"] == name:
                return o
        return None

    for k, v in patch.items():
        ov = base.get(k)

        if ov is not None:
            if type(ov) == dict:
                if type(v) != dict:
                    # TODO
                    raise ValueError(f"Invalid type in {prefix}")
                else:
                    merge_patch_object(ov, v, prefix + "." + k)
            elif type(ov) == list:
                if type(v) != list:
                    # TODO
                    raise ValueError(f"Invalid type in {prefix}")
                else:
                    if not ov:
                        base[k] = v
                    else:
                        if type(v[0]) != dict:
                            base[k] = v
                        else:
                            # When merging lists of objects, we matching objects by name
                            # If there's no matching object, we append
                            # If there's a matching object, recursively patch
                            for i, elem in enumerate(v):
                                if type(elem) != dict:
                                    raise ValueError(
                                        f"Invalid type in {prefix}")
                                name = elem.get("name")
                                if not name:
                                    raise ValueError(
                                        "Object in list must have name")
                                o = get_named_object(ov, name)
                                if o:
                                    merge_patch_object(
                                        o, elem, prefix + "." + k + "[" + str(i) + "]")
                                else:
                                    ov.append(elem)

            elif type(ov) not in (dict, list) and type(v) in (dict, list):
                raise ValueError(f"Invalid type in {prefix}")
            else:
                base[k] = v
        else:
            base[k] = v


def generate_password() -> str:
    """
    生成一个随机密码，由5组5位字符串组成，每组字符串由字母、数字、特殊字符中的任意3种组合而成。
    返回值为一个以"-"连接的字符串，每个组字符串之间用"-"分割。
    
    Args:
        None
    
    Returns:
        str (str): 生成的随机密码，格式为"字符串1-字符串2-字符串3-字符串4-字符串5"。
    """
    random.seed(int(str(time.time()).split(".")[-1]))
    return "-".join(
        "".join(random.choice(string.ascii_letters + string.digits + "_.=+-~") for i in range(5)) for ii in range(5))


def version_to_int(version: str) -> int:
    """
    将版本号转换为整数。
    版本号必须是n.n.n或n.n.n.n的格式，其中n是一个正整数。
    如果版本号不符合这种格式，则会引发ValueError异常。
    
    Args:
        version (str): 要转换的版本号字符串，例如'1.2.3'或'1.2.3.4'。
    
    Returns:
        int: 返回一个整数，表示版本号。
        如果版本号有四个部分，则最后一位被视为日期值，并且可以达到100000000。
        否则，最后一位被视为一个整数，并且可以达到1000000000。
    
    Raises:
        ValueError: 如果版本号不符合n.n.n或n.n.n.n的格式。
    """
    # x.y.z[.w]
    parts = version.split(".")
    if len(parts) > 4 or len(parts) < 3:
        raise ValueError(
            f"Invalid version number {version}. Must be n.n.n or n.n.n.n")

    parts = [int(p) for p in parts]

    # allow the last digit to be as long as a date value
    if len(parts) > 3:
        return parts[0] * 1000000000000 + parts[1] * 10000000000 + parts[2] + 100000000 + parts[3]
    else:
        return parts[0] * 1000000000000 + parts[1] * 10000000000 + parts[2] + 100000000


def indent(s: str, spaces: int) -> str:
    """
    对字符串进行缩进，每行前面添加指定数量的空格。
    
    Args:
        s (str): 需要缩进的字符串，如果为空则返回空字符串。
        spaces (int): 每行应添加的空格数量。
    
    Returns:
        str: 缩进后的字符串，包括换行符；如果输入字符串为空则返回空字符串。
    """
    if s:
        ind = "\n" + " " * spaces
        return " " * spaces + ind.join(s.split("\n"))
    return ""


def log_banner(path: str, logger) -> None:
    """
    打印一个日志横幅，包含操作员版本、时间戳和UID。
    
    Args:
        path (str): 文件路径。
        logger (logging.Logger): 用于记录日志的logger对象。
    
    Returns:
        None; 无返回值。
    """
    import pkg_resources
    from . import config

    kopf_version = pkg_resources.get_distribution('kopf').version
    ts = datetime.datetime.fromtimestamp(os.stat(path).st_mtime).isoformat()

    path = os.path.basename(path)
    logger.info(
        f"MySQL Operator/{path}={config.OPERATOR_VERSION} timestamp={ts} kopf={kopf_version} uid={os.getuid()}")


def dict_to_json_string(d: dict) -> str:
    """
    将字典转换为JSON格式的字符串，并返回该字符串。
    
    Args:
        d (dict): 需要转换为JSON字符串的字典。
    
    Returns:
        str: JSON格式的字符串，包含了原始字典中的键值对。
    """
    return json.dumps(d, indent=4)


def bytes_conversion(number):
    """ 
    字节单位转化 
    """
    if not isinstance(number,str):
        number = str(number)
    symbols = ('KB', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y')
    if number[-1].upper() in symbols:
        return number
    prefix = dict()
    for i, s in enumerate(symbols):
        prefix[s] = 1 << (i + 1) * 10
    for s in reversed(symbols):
        if int(number) >= prefix[s]:
            value = float(number) / prefix[s]
            return '%.2f%s' % (value, s)
    return "%sB" % number



def parse_expire_duration(expire_duration):
    """
    解析过期时长字符串，转换为 timedelta 对象。

    :param expire_duration: str, 例如 "72h", "5d", "3h30m"
    :return: timedelta 对象，表示相应的时间范围
    """
    # 正则表达式匹配格式: 数字后跟一个单位（d, h, m, s）
    pattern = r'(\d+)([dhms])'
    matches = re.findall(pattern, expire_duration)

    total_duration = timedelta()

    for value, unit in matches:
        value = int(value)
        if unit == 'd':
            total_duration += timedelta(days=value)
        elif unit == 'h':
            total_duration += timedelta(hours=value)
        elif unit == 'm':
            total_duration += timedelta(minutes=value)
        elif unit == 's':
            total_duration += timedelta(seconds=value)

    return total_duration

