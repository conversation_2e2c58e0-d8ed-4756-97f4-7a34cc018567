#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   database_api.py
@Time    :   2024/6/16 08:29
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   lixian<PERSON><EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""
import datetime
import typing
from enum import Enum
from kopf._cogs.structs.bodies import Body
import  logging 
from typing import Optional, cast
from logging import Logger

from xdboperator.controller import utils, consts
from xdboperator.controller.api_utils import dget_dict, dget_str
from xdboperator.controller.cluster.xdbcluster_api import AbstractServerSetSpec
from xdboperator.controller.cluster.xdbcluster_api import XDBCluster
from xdboperator.controller.k8sobject import K8sInterfaceObject
from xdboperator.controller.kubeutils import api_apps, api_customobj, api_kruise
from xdboperator.controller.kubeutils import client as api_client, ApiException

logger = logging.getLogger(__name__)

class XDBClusterDBSpecProperties(Enum):
    CLUSTER = "clusterRef"


class ClusterReferenceSpec(object):
    namespace: str = None
    name: str = ""

    def parse(self, spec: dict, prefix: str) -> None:
        """
            解析规格，将其中的命名空间和名称设置到当前对象中。
        如果规格包含命名空间，则将其设置为当前对象的命名空间；
        如果规格包含名称，则将其设置为当前对象的名称。
        
        Args:
            spec (dict): 一个字典，包含了要解析的规格信息。可能包含以下键值：
                - namespace (str, optional): 命名空间，默认为None。
                - name (str, optional): 名称，默认为None。
            prefix (str, optional): 规格中的键值对的前缀，默认为空字符串。
        
        Returns:
            None: 不返回任何值，直接修改当前对象的属性。
        """
        if "namespace" in spec:
            self.namespace = dget_str(spec, "namespace", prefix)

        if "name" in spec:
            self.name = dget_str(spec, "name", prefix)

    def __str__(self):
        """
            返回字符串格式的对象，包含命名空间和名称。
        例如：'default/my-pod'。
        
        Returns:
            str -- 字符串格式的对象。
        """
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        """
            Returns a string representation of the ClusterReferenceSpec object.
        
        Returns:
            str (str): String representation of the ClusterReferenceSpec object.
        """
        return f"<ClusterReferenceSpec {self.name}>"


class XDBDatabaseSpec(AbstractServerSetSpec):
    database: str = ""
    clusterRef: ClusterReferenceSpec = None

    def __init__(self, namespace: str, name: str, spec: dict):
        """
            初始化函数，用于初始化类的属性和方法。
        
        Args:
            namespace (str): 命名空间，即Pod所在的Namespace。
            name (str): Pod的名称。
            spec (dict): Pod的规格信息，包括容器信息等。
        
        Returns:
            None.
        
        Raises:
            None.
        """
        super().__init__(namespace, name, name, spec)
        self.load(spec)

    def load(self, spec: dict) -> None:
        """
            加载数据库规格，将字典形式的规格转换为对象。
        该方法会修改当前对象的属性值。
        
        Args:
            spec (dict): 包含数据库规格信息的字典，必须包含以下键值对：
                - database (str, optional): 数据库名称，默认为None。
                - clusterRef (dict, optional): 集群引用规格，默认为None。
        
            其中，clusterRef是一个包含以下键值对的字典：
                - clusterName (str, optional): 集群名称，默认为None。
                - clusterType (str, optional): 集群类型，默认为None。
        
            spec (dict): 包含数据库规格信息的字典，必须包含以下键值对：
                - database (str, optional): 数据库名称，默认为None。
                - clusterRef (dict, optional): 集群引用规格，默认为None。
        
            其中，clusterRef是一个包含以下键值对的字典：
                - clusterName (str, optional): 集群名称，默认为None。
                - clusterType (str, optional): 集群类型，默认为None。
        
        Raises:
            KeyError: 如果spec不包含必要的键值对，则抛出KeyError异常。
            ValueError: 如果spec中的值无效，则抛出ValueError异常。
        
        Returns:
            None: 该方法没有返回值。
        """
        self._load(spec, spec, "spec")
        print(f"XDBDatabaseSpec: {spec}")
        self.database = dget_str(spec, "database", "spec")
        self.clusterRef = ClusterReferenceSpec()
        section = XDBClusterDBSpecProperties.CLUSTER.value
        if section in spec:
            self.clusterRef.parse(dget_dict(spec, section, "spec"), "spec.clusterRef")

    def __str__(self):
        """
            返回字符串格式的对象，包含数据库名称和集群引用。
        返回值为：'<database>/<cluster_ref>'。
        
        Returns:
            str - 一个字符串，包含数据库名称和集群引用。
        """
        return f"{self.database}/{self.clusterRef}"

    def __repr__(self):
        """
            Return a string representation of the object.
        
        Returns:
            str: A string in the format "<XDBDatabaseSpec database>".
        """
        return f"<XDBDatabaseSpec {self.database}>"

    def validate(self, logger: Logger) -> None:
        """
            验证当前对象是否合法。如果不合法，则抛出异常。
        默认实现为空函数，可以根据需要重写该方法。
        
        Args:
            logger (Logger): 一个日志记录器，用于记录验证过程中的信息和错误。
                              Logger类型定义在logging_utils.py中。
        
        Raises:
            None: 如果当前对象合法，则不会抛出任何异常。
        
        Returns:
            None: 无返回值，该方法只是用来进行验证而已。
        """
        # TODO see if we can move some of these to a schema in the CRD
        pass


class XDBDatabase(K8sInterfaceObject):

    def __init__(self, cluster: Body) -> None:
        """
            初始化一个Cluster对象，用于管理集群相关的操作。
        
        Args:
            cluster (Body): 包含集群信息的Body类型对象，必须包含cluster字段。
                cluster字段为Body类型，内部包含了集群的详细信息，如名称、描述等。
        
        Raises:
            无
        
        Returns:
            无（初始化后直接返回）<class 'NoneType'>
        """
        super().__init__()
        self.obj: Body = cluster
        self._parsed_spec: Optional[XDBDatabaseSpec] = None

    def __str__(self):
        """
            返回字符串格式的对象，包含命名空间和名称。
        例如：'default/my-pod'。
        
        Returns:
            str -- 字符串格式的对象。
        """
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        """
            Returns a string representation of the XDBDatabase object.
        
        Returns:
            str: A string in the format "<XDBDatabase name>".
        """
        return f"<XDBDatabase {self.name}>"

    @classmethod
    def _get(cls, ns: str, name: str) -> Body:
        """
            根据名称获取指定命名空间下的自定义资源对象。
        
        Args:
            ns (str): 命名空间名称，不可为None或空字符串。
            name (str): 自定义资源对象名称，不可为None或空字符串。
        
        Returns:
            Body (typing.Any): 返回一个自定义资源对象，如果找不到则返回None。
        
        Raises:
            ApiException (kubernetes.client.rest.ApiException): Kubernetes API异常，包括HTTP错误码和错误信息。
        """
        try:
            ret = cast(Body, api_customobj.get_namespaced_custom_object(
                consts.GROUP, consts.VERSION, ns,
                consts.XDBDATABASE_PLURAL, name))
        except ApiException as e:
            raise e
        return ret

    @classmethod
    def _patch(cls, ns: str, name: str, patch: dict) -> Body:
        """
            修补指定命名空间下的CustomResource。
        参数：
            - ns (str) - 命名空间名称，必填项；
            - name (str) - CustomResource名称，必填项；
            - patch (dict) - 需要修补的内容，必填项，格式为字典类型；
        返回值（Body）：
            返回一个包装了修补后的CustomResource对象的Body实例。
        """
        return cast(Body, api_customobj.patch_namespaced_custom_object(
            consts.GROUP, consts.VERSION, ns,
            consts.XDBDATABASE_PLURAL, name, body=patch))

    @classmethod
    def _patch_status(cls, ns: str, name: str, patch: dict) -> Body:
        """
            修补状态字段，返回一个Body类型的对象。
        
        Args:
            ns (str): 命名空间名称。
            name (str): XdbDatabase资源名称。
            patch (dict): 需要修补的状态字段，格式为字典，例如{"status": {"phase": "Running"}}。
        
        Returns:
            Body (typing.cast(Body, ...)): 返回一个Body类型的对象，包含了修补后的状态字段。
        
        Raises:
            无。
        """
        return cast(Body, api_customobj.patch_namespaced_custom_object_status(
            consts.GROUP, consts.VERSION, ns,
            consts.XDBDATABASE_PLURAL, name, body=patch))

    @classmethod
    def read(cls, ns: str, name: str) -> 'XDBDatabaseSpec':
        """
            读取指定命名空间下的数据库信息，返回一个XDBDatabaseSpec对象。
        如果指定的数据库不存在，则抛出ValueError异常。
        
        Args:
            ns (str): 数据库所属的命名空间，例如'default'或'test'等。
            name (str): 数据库名称，例如'my_database'。
        
        Returns:
            XDBDatabaseSpec (str): 包含数据库信息的XDBDatabaseSpec对象。
        
        Raises:
            ValueError: 如果指定的数据库不存在。
        """
        return XDBDatabaseSpec(cls._get(ns, name))

    @property
    def metadata(self) -> dict:
        """
        返回元数据字典，包括名称、标签和注释等信息。
        
        返回值类型：dict，字典格式如下：
            {
                "name": str,  # 模型的名称
                "labels": list[str],  # 模型的标签列表，可能为空列表
                "comment": str  # 模型的注释信息，可能为空字符串
            }
        
        Returns:
            dict - 元数据字典，包括名称、标签和注释等信息。
        """
        return self.obj["metadata"]

    @property
    def annotations(self) -> dict:
        """
            返回当前对象的注解字典，包括所有已经存在的注解。如果没有注解，则返回一个空的字典。
        注解是键值对形式的，其中键是注解名称，值是注解的值。
        
        Returns:
            dict (dict): 注解字典，包括所有已经存在的注解。如果没有注解，则返回一个空的字典。
        """
        return self.metadata["annotations"]

    @property
    def spec(self) -> dict:
        """
            返回当前对象的规格信息，包括名称、标签和其他特性。
        
        返回值（dict）：
            - name (str, optional): 资源的名称，默认为 None。
            - labels (dict, optional): 资源的标签，默认为 None。
            - other_attrs (dict, optional): 其他特性，默认为 None。
        
        Returns:
            dict (dict): 规格信息，包括名称、标签和其他特性。
        """
        return self.obj["spec"]

    @property
    def status(self) -> dict:
        """
            获取当前对象的状态信息，包括资源使用情况、错误信息等。如果对象没有状态信息，则返回一个空字典。
        返回值是一个字典，其中包含以下键值对：
            - "used_resources" (dict, optional): 当前对象正在使用的资源，包括CPU、GPU和内存等。默认为None。
            - "error_message" (str, optional): 如果对象出现了错误，则此处显示错误信息；否则为None。默认为None。
            例如：{"used_resources": {"cpu": 10, "gpu": 2}, "error_message": "Out of memory"}
        
        Returns:
            dict: 当前对象的状态信息，包括资源使用情况和错误信息。如果对象没有状态信息，则返回一个空字典。
        """
        if "status" in self.obj:
            return self.obj["status"]
        return {}

    @property
    def name(self) -> str:
        """
            返回模型的名称，即metadata中的"name"字段。
        
        Returns:
            str (str): 模型的名称。
        """
        return self.metadata["name"]

    @property
    def namespace(self) -> str:
        """
            返回当前对象的命名空间。
        命名空间是一个字符串，用于标识Kubernetes对象在集群中的唯一性。
        
        返回值：str（字符串）
            - 返回当前对象的命名空间。如果对象没有命名空间，则返回空字符串。
        """
        return self.metadata["namespace"]

    @property
    def xdb_cluster_name(self) -> str:
        """
            返回xdb集群名称，如果未指定则为空字符串。
        
        Returns:
            str (str): XDB集群名称，如果未指定则为空字符串。
        """
        return self.parsed_spec.clusterRef.name

    @property
    def xdb_cluster_ns(self) -> str:
        """
            获取XDB集群的命名空间，如果没有指定则返回None。
        
        Returns:
            str, optional - 返回XDB集群的命名空间，如果没有指定则返回None。
        """
        return self.parsed_spec.clusterRef.namespace

    @property
    def deleting(self) -> bool:
        """
            返回当前资源是否处于删除状态，如果有deletionTimestamp属性且不为None则返回True。
        否则返回False。
        
        Returns:
            bool - True（处于删除状态）或 False（未处于删除状态）
        """
        return "deletionTimestamp" in self.metadata and self.metadata["deletionTimestamp"] is not None

    @property
    def db_name(self) -> str:
        """
            获取数据库名称。
        
        返回值：str（字符串） - 数据库名称
        
        """
        return self.parsed_spec.database

    @property
    def delete_policy(self):
        """
            获取资源删除策略，默认为"retain"（保留）。如果设置为"delete"（删除），则在删除CR时会自动删除相关的K8S资源。
        返回值：str {"retain" | "delete"}，默认为"retain"。
        """
        return self.annotations.get("xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy", "retain")

    @property
    def parsed_spec(self) -> XDBDatabaseSpec:
        """
            返回已解析的XDB数据库规格。如果尚未解析，则首先调用`parse_spec`方法进行解析。
        解析完成后，将缓存在属性中以提高性能。
        
        Returns:
            XDBDatabaseSpec (Union[str, Tuple[str, str]]): 已解析的XDB数据库规格，可以是字符串或元组形式。
            如果解析失败，将引发AssertionError。
        """
        if not self._parsed_spec:
            self.parse_spec()
            assert self._parsed_spec
        return self._parsed_spec

    def parse_spec(self) -> None:
        """
            解析数据库规格，并将其保存在XDBDatabaseSpec对象中。
        如果已经解析过，则不再重复解析。
        
        Args:
            None
        
        Returns:
            None: 无返回值，直接修改了类实例的属性。
        """
        self._parsed_spec = XDBDatabaseSpec(self.namespace, self.name, self.spec)

    @property
    def ready(self) -> bool:
        """
            判断当前对象是否已经准备好，可以进行操作。
        如果对象还未创建或初始化完成，则返回 False；否则返回 True。
        
        Returns:
            bool (bool): 若对象已准备好，返回 True；否则返回 False。
        """
        return cast(bool, self.get_create_time())

    def set_create_time(self, time: datetime.datetime = None) -> None:
        """
            设置创建时间，如果未传入参数则使用当前时间。
        默认情况下，创建时间是UTC时区的，并且秒和微秒部分被清零。
        
        Args:
            time (datetime.datetime, optional): 要设置的时间，默认为None，表示使用当前时间。 Default to None.
        
        Returns:
            None: 无返回值，直接修改了对象的状态字段。
        """
        if not time:
            time = datetime.datetime.now()
        self._set_status_field("createTime", time.replace(
            microsecond=0).isoformat() + "Z")

    def get_create_time(self) -> Optional[datetime.datetime]:
        """
            获取创建时间，返回值为datetime.datetime类型或None。如果不存在则返回None。
        
        Args:
            None
        
        Returns:
            Optional[datetime.datetime]: 创建时间，datetime.datetime类型；如果不存在则返回None。
        """
        dt = self._get_status_field("createTime")
        if dt:
            return datetime.datetime.fromisoformat(dt.rstrip("Z"))
        return None

    def _get_status_field(self, field: str) -> typing.Any:
        """
            获取状态字段的值，如果不存在则返回None。
        参数field (str) - 需要获取的状态字段名称，例如"state"或"message"等。
        返回值 (typing.Any) - 状态字段对应的值，如果不存在则返回None。
        """
        return cast(str, self.status.get(field))

    @property
    def uid(self) -> str:
        """
            返回实例的唯一标识符。该值是在创建实例时生成的，并且不会更改。
        
        返回值：str（字符串） - 实例的唯一标识符
        
        """
        return self.metadata["uid"]

    @property
    def xdbcluster_ready(self) -> bool:
        """
            判断 XDBCluster StatefulSet 是否准备就绪，返回布尔值。
        如果 StatefulSet 不存在或者没有就绪的副本，则返回 False。
        
        Returns:
            bool (bool): True 表示 XDBCluster StatefulSet 已就绪，False 表示未就绪。
        """
        sts = self.get_xdbcluster_stateful_set()
        if not sts:
            logger.warning(f"StatefulSet for cluster {self.name} not found, assuming not ready.")
            return False

        if isinstance(sts, api_client.V1StatefulSet):
            if not sts.status:
                logger.info(f"StatefulSet {self.name} has no status field yet.")
                return False
            # ready_replicas is optional, defaults to 0 if not present.
            ready_replicas = sts.status.ready_replicas if sts.status.ready_replicas is not None else 0
            return ready_replicas == sts.status.replicas
        elif isinstance(sts, dict):
            status = sts.get("status")
            if not status:
                logger.info(f"Kruise StatefulSet {self.name} has no status field yet. sts={sts}")
                return False

            replicas = status.get("replicas")
            if replicas is None:
                logger.info(f"Kruise StatefulSet {self.name} status has no 'replicas' field. status={status}")
                return False

            # readyReplicas might not be present if there are no ready replicas, so default to 0.
            ready_replicas = status.get("readyReplicas", 0)
            
            return ready_replicas == replicas
        else:
            logger.error(f"Invalid sts type for cluster {self.name}: {type(sts)}")
            raise ValueError(f"Invalid sts type: {type(sts)}")
        
        
    def get_xdbcluster_stateful_set(self) -> typing.Optional[api_client.V1StatefulSet]:
        """
        获取XDBCluster的StatefulSet对象，优先尝试获取传统的StatefulSet，如果不存在则尝试获取Kruise StatefulSet。
            如果发生API异常并且状态码为404，则返回None；否则抛出异常。
        
            Args:
                self (XDBCluster): XDBCluster实例。
        
            Returns:
                Optional[api_client.V1StatefulSet]: StatefulSet对象，如果不存在则返回None。
                    API异常时，如果状态码为404，则返回None；其他情况抛出异常。
        """
        try:
            # 首先尝试获取传统的 StatefulSet
            return cast(api_client.V1StatefulSet,
                        api_apps.read_namespaced_stateful_set(self.xdb_cluster_name,
                                                              self.xdb_cluster_ns))
        except ApiException as e:
            if e.status == 404:
                try:
                    # 如果传统的 StatefulSet 不存在，尝试获取 Kruise StatefulSet
                    kruise_sts = api_kruise.get_namespaced_custom_object(
                        group=consts.KRUISE_GROUP,
                        version=consts.KRUISE_VERSION,
                        namespace=self.xdb_cluster_ns,
                        plural=consts.KRUISE_STATEFULSET_PLURAL,
                        name=self.xdb_cluster_name
                    )
                    return cast(api_client.V1StatefulSet, kruise_sts)
                except ApiException as e2:
                    if e2.status == 404:
                        return None
                    raise e2
            raise e

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        """
            返回一个字典，包含当前对象的 apiVersion、kind、name、namespace、resourceVersion、uid 和可选的 fieldPath。
        如果 fieldPath 参数不为空，则将其添加到字典中。
        
        Args:
            field_path (Optional[str], optional): 字段路径（默认为None）. Defaults to None.
        
        Returns:
            dict: 包含当前对象信息的字典，包括 apiVersion、kind、name、namespace、resourceVersion、uid 和可选的 fieldPath.
        """
        ref = {
            "apiVersion": consts.API_VERSION,
            "kind": consts.XDBDATABASE_KIND,
            "name": self.name,
            "namespace": self.namespace,
            "resourceVersion": self.metadata["resourceVersion"],
            "uid": self.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    def get_database(self) -> typing.Optional['XDBDatabase']:
        """
        获取数据库对象，如果不存在则返回None。
        
            Args:
                self (XDBDatabase): XDBDatabase实例。
        
            Returns:
                typing.Optional[XDBDatabase]: XDBDatabase对象或None，如果数据库不存在。
                异常信息将打印到控制台中，如果状态码为404，则返回None；否则抛出ApiException异常。
        """
        try:
            return XDBDatabase.read(self.namespace, self.name)
        except ApiException as e:
            print(
                f"Could not get database {self.namespace}/{self.name}: {e}")
            if e.status == 404:
                return None
            raise

    def get_cluster(self) -> typing.Optional[XDBCluster]:
        """
        获取集群信息，如果不存在则返回None。
        如果获取失败，将打印错误信息并抛出ApiException异常。
        """
        try:
            return XDBCluster.read(self.parsed_spec.clusterRef.namespace,
                                   self.parsed_spec.clusterRef.name)
        except ApiException as e:
            print(
                f"Could not get cluster {self.parsed_spec.clusterRef.namespace}/{self.parsed_spec.clusterRef.name}: {e}")
            if e.status == 404:
                return None
            raise

    def _set_status_field(self, field: str, value: typing.Any) -> None:
        """
            设置资源的状态字段值。如果资源没有状态字段，则创建一个新的状态字段。
        该方法会修改当前对象的状态字段值，并返回None。
        
        Args:
            field (str): 需要设置的状态字段名称。
            value (typing.Any): 需要设置的状态字段值，可以是任何类型。
        
        Returns:
            None: 无返回值，直接修改当前对象的状态字段值。
        """
        obj = self._get(self.namespace, self.name)

        if "status" not in obj:
            patch = {"status": {}}
        else:
            patch = {"status": obj["status"]}
        patch["status"][field] = value
        self.obj = self._patch_status(self.namespace, self.name, patch)

    def set_status(self, status) -> None:
        """
            设置资源的状态，并更新本地对象。
        
        Args:
            status (dict): 需要更新的状态信息，格式为字典类型，例如{"phase": "Running"}。
        
        Returns:
            None: 无返回值，直接修改本地对象。
        """
        print(f"set_status namespace:{self.namespace} name:{self.name} ")
        obj = cast(dict, self._get(self.namespace, self.name))
        print(f"set_status obj:{obj}")
        if "status" not in obj:
            obj["status"] = status
        else:
            utils.merge_patch_object(obj["status"], status)
        self.obj = self._patch_status(self.namespace, self.name, obj)

    def set_db_status(self, cluster_status) -> None:
        """
            设置数据库状态。
        
        Args:
            cluster_status (str): 集群状态，可选值为 "up"、"down"。
        
        Returns:
            None: 无返回值，直接修改了实例的属性。
        """
        self._set_status_field("db", cluster_status)

    def get_db_status(self, field=None):  # TODO -> dict, remove field

        """
            Get the database status. If a specific field is specified, only that field will be returned.
        Args:
            field (str, optional): The name of the field to retrieve. Defaults to None.
        Returns:
            Union[dict, Any]: If no field is specified, returns the entire dictionary containing all fields. Otherwise,
                               returns the value associated with the specified field.
        """
        status = self._get_status_field("db")
        if status and field:
            return status.get(field)
        return status

    def log_database_info(self, logger: Logger) -> None:
        """
            记录数据库信息到日志中。
        
        Args:
            logger (Logger): 一个Logger对象，用于记录日志。
        
        Returns:
            None: 无返回值，直接修改传入的logger对象。
        """
        logger.info(f"Relation XDB, ns: {self.xdb_cluster_ns}/{self.xdb_cluster_name} ")
        logger.info(f"CRD databases name: {self.name} / {self.parsed_spec.namespace}")
        logger.info(f"Spec DB Name: {self.db_name} clusterRef:{self.parsed_spec.clusterRef}")
