#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   xdbuser_controller.py
@Time    :   2024/6/22 19:20
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""
from logging import Logger

from xdboperator.controller.database.database_api import XDBDatabase
from xdboperator.lightxdb.database import DatabaseService


class XDBDatabaseController(object):
    """
    数据库控制器，负责管理数据库的生命周期。
    """
    
    def __init__(self, xdbDB: XDBDatabase):
        """
        初始化函数。
        
        Args:
            xdbDB (XDBDatabase): XDBDatabase类型的对象
            
        Raises:
            ValueError: 当无法获取到关联的集群信息时抛出
        """
        self.xdbDB = xdbDB
        self._cluster = None
        self._db_service = None
        
    @property
    def cluster(self):
        """
        延迟加载集群信息。
        
        Returns:
            XDBCluster: 集群对象
            
        Raises:
            ValueError: 当无法获取到关联的集群信息时抛出
        """
        if self._cluster is None:
            self._cluster = self.xdbDB.get_cluster()
            if not self._cluster:
                raise ValueError(f"无法找到与数据库 {self.xdbDB.name} 关联的集群")
            
            if not hasattr(self._cluster, 'parsed_spec'):
                raise ValueError(f"集群 {self._cluster.name} 的规格未完成解析")
                
        return self._cluster
    
    @property
    def db_service(self):
        """
        延迟初始化数据库服务。
        
        Returns:
            DatabaseService: 数据库服务对象
        """
        if self._db_service is None:
            self._db_service = DatabaseService(
                self.cluster.parsed_spec.ZKDomain,
                self.cluster.parsed_spec.appID,
                self.logger,
                self.cluster.name
            )
        return self._db_service

    def create_database(self, logger: Logger):
        """
        创建数据库
        
        Args:
            logger (Logger): 日志记录器
            
        Returns:
            int: 操作结果代码
        """
        self.logger = logger  # 保存logger供db_service使用
        data = {
            "app_id": self.cluster.parsed_spec.appID,
            "database_info": {
                "dbname": self.xdbDB.db_name,
                "charset": 'utf8',
                "remark": ""
            }
        }
        ret = self.db_service.database_create(data)
        logger.info(f"database_create {self.xdbDB.db_name} return code: {ret}")
        return ret

    def delete_database(self, logger: Logger):
        """
        删除数据库
        
        Args:
            logger (Logger): 日志记录器
            
        Returns:
            int: 操作结果代码
        """
        self.logger = logger  # 保存logger供db_service使用
        data = {
            "app_id": self.cluster.parsed_spec.appID,
            "database_info": {
                "dbname": self.xdbDB.db_name,
                "charset": 'utf8mb4',
                "remark": ""
            }
        }
        ret = self.db_service.database_delete(data, self.xdbDB.delete_policy)
        logger.info(f"database_delete {self.xdbDB.db_name} return code: {ret}")
        return ret

    def check_database_is_exist(self, logger: Logger):
        """
        检查底层mysql和zk中是否都存在数据库
        
        Args:
            logger (Logger): 日志记录器
            
        Returns:
            int: 0表示都存在，-1表示不完全存在
        """
        self.logger = logger  # 保存logger供db_service使用
        mysql_exist = self.db_service.mysql_show_database(
            self.cluster.parsed_spec.appID, 
            self.xdbDB.db_name
        )
        zk_exist = self.db_service.zk_show_database(
            self.cluster.parsed_spec.appID, 
            self.xdbDB.db_name
        )
        logger.info(f"check_database_is_exist, mysql_exist:{mysql_exist} zk_exist:{zk_exist}.")
        return 0 if mysql_exist == zk_exist == 0 else -1
