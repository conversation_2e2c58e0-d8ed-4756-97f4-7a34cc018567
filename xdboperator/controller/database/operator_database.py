#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   operator_database.py
@Time    :   2024/7/3 15:34
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   l<PERSON><PERSON><PERSON><PERSON>@baidu.com
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import kopf
from kopf._cogs.structs.bodies import Body
from logging import Logger
from typing import Optional
import kubernetes.client.rest

from xdboperator.controller import consts, diagnose, config
from xdboperator.controller import utils
from xdboperator.controller.api_utils import ApiSpecError, ignore_404
from xdboperator.controller.database.database_api import XDBDatabase
from xdboperator.controller.database.database_controller import XDBDatabaseController
from xdboperator.controller.kubeutils import k8s_version


def update_status_with_retry(xdbdb, status_data, max_retries=5, logger=None):
    """
    使用重试机制更新XDBDatabase状态，处理冲突问题
    
    Args:
        xdbdb: XDBDatabase对象实例
        status_data: 要更新的状态数据
        max_retries: 最大重试次数
        logger: 日志记录器
    
    Returns:
        bool: 状态更新是否成功
    """
    retries = 0
    while retries < max_retries:
        try:
            xdbdb.set_status(status_data)
            return True
        except kubernetes.client.rest.ApiException as e:
            if e.status == 409:  # Conflict
                if logger:
                    logger.warning(f"发生冲突，重新获取资源并重试，第{retries+1}次重试")
                try:
                    xdbdb.refresh()
                    retries += 1
                    continue
                except Exception as refresh_err:
                    if logger:
                        logger.error(f"重新获取资源失败: {refresh_err}")
                    return False
            else:
                if logger:
                    logger.error(f"更新状态时发生API错误: {e}")
                return False
        except Exception as e:
            if logger:
                logger.error(f"更新状态时发生未知错误: {e}")
            return False
    
    if logger:
        logger.error(f"更新状态重试次数已达上限 ({max_retries}次)")
    return False

@kopf.on.create(consts.GROUP, consts.VERSION, consts.XDBDATABASE_PLURAL)
def on_database_create(name: str, namespace: Optional[str], body: Body,
                       logger: Logger, **kwargs) -> None:
    """
    处理创建XDB数据库的事件。
    
    Args:
        name (str): XDB数据库名称。
        namespace (Optional[str], optional): XDB数据库所在的命名空间，默认为None。
        body (Body): 包含XDB数据库资源定义的Body对象。
        logger (Logger, optional): 记录日志的Logger对象，默认为None。
        kwargs (dict, optional): 其他可选参数，默认为{}.
    
    Raises:
        kopf.PermanentError: 当XDB数据库规格无效时抛出该错误。
        kopf.TemporaryError: 当XDB集群未就绪时抛出该错误，并设置了一个延迟时间。
    
    Returns:
        None: 不返回任何值。
    """
    logger.info(f"Create XDB DB User name={name} namespace={namespace} on K8s {k8s_version()}")
    db_cr = XDBDatabase(body)
    
    db_cr.set_status({
        "db": {
            "status": diagnose.ClusterDiagStatus.CREATING.value,
            "ready": False,
            "lastProbeTime": utils.isotime()
        }})
    try:
        db_cr.parse_spec()
        db_cr.parsed_spec.validate(logger)
    except ApiSpecError as e:
        db_cr.set_status({
            "db": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }})
        db_cr.error(action="CreateXDBDatabase", reason="InvalidArgument", message=str(e))
        raise kopf.PermanentError(f"Error in XDBDatabase spec: {e}")

    db_cr.log_database_info(logger)
    
    try:
        db_ctl = XDBDatabaseController(db_cr)
    except ValueError as e:
        logger.error(f"初始化数据库控制器失败: {str(e)}")
        db_cr.set_status({
            "db": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }})
        db_cr.error(action="CreateXDBDatabase", reason="ControllerInitFailed", message=str(e))
        raise kopf.PermanentError(f"Failed to initialize database controller: {e}")

    # 检查集群是否存在和就绪
    # 首先检查集群是否存在
    cluster = db_cr.get_cluster()
    if not cluster:
        logger.error(f"找不到关联集群: {db_cr.xdb_cluster_ns}/{db_cr.xdb_cluster_name}")
        db_cr.set_status({
            "db": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }})
        db_cr.error(action="CreateXDBDatabase", reason="ClusterNotFound", 
                    message=f"找不到关联集群: {db_cr.xdb_cluster_ns}/{db_cr.xdb_cluster_name}")
        raise kopf.PermanentError(f"找不到关联集群: {db_cr.xdb_cluster_ns}/{db_cr.xdb_cluster_name}")
    
    # 然后检查集群是否就绪
    if not db_cr.xdbcluster_ready:
        db_cr.set_status({
            "db": {
                "status": diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }})
        db_cr.error(action="CreateXDBDatabase", reason="XDBClusterNotReady",
                    message="xdb cluster is not ready")
        raise kopf.TemporaryError("xdb cluster is not ready", delay=15)
    if db_cr.ready:
        db_cr.warn(action="CreateXDBDatabase", reason="ResourceExists",
                   message="Database CR is exists, but create requested again")
        return
    result = db_ctl.create_database(logger=logger)
    if result != 0:
        db_cr.set_status({
            "db": {
                "status": diagnose.ClusterDiagStatus.CREATE_FAILED.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }})
        logger.error(f"create_database failed, result:{result}")
        db_cr.error(action="CreateXDBDatabase", reason="CreateDBFailed", message="run create function db failed.")
        raise kopf.TemporaryError(f"create_database failed, result:{result}")
    if db_ctl.check_database_is_exist(logger) == 0:
        db_cr.set_status({
            "db": {
                "status": diagnose.ClusterDiagStatus.RUNNING.value,
                "ready": True,
                "lastProbeTime": utils.isotime()
            }})
        db_cr.set_create_time()
        db_cr.info(action="CreateXDBDatabase", reason="CreateSuccessfully", message="create database successfully.")
    else:
        db_cr.set_status({
            "db": {
                "status": diagnose.ClusterDiagStatus.CHECK_FAILED.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }})
        logger.error(f"create_database failed, result:{result}")
        db_cr.error(action="CreateXDBDatabase", reason="CheckFailed", message="Database check failed after creation.")


@kopf.on.delete(consts.GROUP, consts.VERSION, consts.XDBDATABASE_PLURAL)
def on_database_delete(name: str, namespace: Optional[str], body: Body,
                       logger: Logger, **kwargs) -> None:
    """
    删除xdb的提供的database
    """
    logger.info(f"Delete DB CR name={name} namespace={namespace} on K8s {k8s_version()}")
    db_cr = XDBDatabase(body)
    
    if db_cr.ready:
        db_cr.set_status({
            "db": {
                "status": diagnose.ClusterDiagStatus.TERMINATE.value
            }})
            
    try:
        db_ctl = XDBDatabaseController(db_cr)
    except ValueError as e:
        logger.warning(f"初始化数据库控制器失败: {str(e)}")
        msg = f"找不到关联集群，跳过删除数据库操作: {db_cr.db_name}"
        logger.warning(msg)
        db_cr.warn(action="DeleteXDBDatabase", reason="ClusterNotFound", message=msg)
        return

    existing_cluster = ignore_404(lambda: db_cr.get_xdbcluster_stateful_set())
    if existing_cluster:
        ret = db_ctl.delete_database(logger=logger)
        logger.info(f"Delete DB CR name={name} DB:{db_cr.db_name} ret:{ret}")
    else:
        logger.warning(f"XDB Cluster no exist, skip drop DB:{db_cr.db_name}.")


@kopf.on.resume(consts.GROUP, consts.VERSION, consts.XDBDATABASE_PLURAL)
def on_database_resume(name: str, namespace: Optional[str], body: Body,
                      logger: Logger, **kwargs) -> None:
    """
    处理 operator 重启时的 XDBDatabase 资源恢复。
    确保所有已存在的 XDBDatabase 资源状态一致。
    
    Args:
        name (str): XDBDatabase 资源的名称
        namespace (Optional[str]): XDBDatabase 资源所在的命名空间
        body (Body): 包含 XDBDatabase 资源的请求体
        logger (Logger): 日志记录器
        kwargs: 其他可选参数
    """
    logger.info(f"Resume handling XDBDatabase name={name} namespace={namespace}")
    db_cr = XDBDatabase(body)
    db_cr.info(action="ResumeXDBDatabase", reason="StartResume", 
               message=f"Resume handling XDBDatabase name={name} namespace={namespace}")
   
    # 如果资源正在删除中，跳过处理
    if db_cr.deleting:
        logger.debug(f"XDBDatabase {name} is being deleted, skip resume handling")
        db_cr.info(action="ResumeXDBDatabase", reason="Deleting", 
                   message=f"XDBDatabase {name} is being deleted, skip resume handling")
        return
        
    current_status = db_cr.get_db_status("status")
    logger.info(f"Current database status: {current_status}, database: {db_cr.db_name}")
    db_cr.info(action="ResumeXDBDatabase", reason="CurrentStatus", 
               message=f"Current database status: {current_status}, database: {db_cr.db_name}")
    
    try:
        db_ctl = XDBDatabaseController(db_cr)
    except ValueError as e:
        logger.warning(f"Failed to initialize XDBDatabaseController: {str(e)}")
        db_cr.error(action="ResumeXDBDatabase", reason="InitControllerFailed", message=str(e))
        status_data = {
            "db": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(db_cr, status_data, logger=logger):
            logger.error(f"Failed to update database status to INVALID: {name}")
        return
        
    # 检查集群是否存在和就绪
    # 首先检查集群是否存在
    cluster = db_cr.get_cluster()
    if not cluster:
        logger.error(f"找不到关联集群: {db_cr.xdb_cluster_ns}/{db_cr.xdb_cluster_name}")
        db_cr.error(action="ResumeXDBDatabase", reason="ClusterNotFound", 
                    message=f"找不到关联集群: {db_cr.xdb_cluster_ns}/{db_cr.xdb_cluster_name}")
        status_data = {
            "db": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(db_cr, status_data, logger=logger):
            logger.error(f"Failed to update database status to INVALID: {name}")
        return
        
    # 然后检查集群是否就绪
    if not db_cr.xdbcluster_ready:
        status_data = {
            "db": {
                "status": diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(db_cr, status_data, logger=logger):
            logger.error(f"Failed to update database status to WAITING_CLUSTER_READY: {name}")
        db_cr.warn(action="ResumeXDBDatabase", reason="ClusterNotReady", 
                   message="xdb cluster not ready during resume")
        return
        
    # 如果状态为空，说明是首次创建，设置为 CREATING 状态
    if current_status is None:
        status_data = {
            "db": {
                "status": diagnose.ClusterDiagStatus.CREATING.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(db_cr, status_data, logger=logger):
            logger.error(f"Failed to update database status to CREATING: {name}")
        db_cr.info(action="ResumeXDBDatabase", reason="SetCreating", 
                   message="Set database status to CREATING during resume")
        return
        
    # 根据当前状态进行相应处理
    if current_status == diagnose.ClusterDiagStatus.CREATING.value:
        # 重新执行创建流程
        result = db_ctl.create_database(logger=logger)
        if result == 0:
            if db_ctl.check_database_is_exist(logger) == 0:
                status_data = {
                    "db": {
                        "status": diagnose.ClusterDiagStatus.RUNNING.value,
                        "ready": True,
                        "lastProbeTime": utils.isotime()
                    }}
                if not update_status_with_retry(db_cr, status_data, logger=logger):
                    logger.error(f"Failed to update database status to RUNNING: {name}")
                db_cr.set_create_time()
                db_cr.info(action="ResumeXDBDatabase", reason="CreateSuccess", 
                           message="Database created successfully during resume.")
            else:
                status_data = {
                    "db": {
                        "status": diagnose.ClusterDiagStatus.CHECK_FAILED.value,
                        "ready": False,
                        "lastProbeTime": utils.isotime()
                    }}
                if not update_status_with_retry(db_cr, status_data, logger=logger):
                    logger.error(f"Failed to update database status to CHECK_FAILED: {name}")
                db_cr.warn(action="ResumeXDBDatabase", reason="CheckFailed", 
                           message="Database check failed after creation.")
        else:
            status_data = {
                "db": {
                    "status": diagnose.ClusterDiagStatus.CREATE_FAILED.value,
                    "ready": False,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(db_cr, status_data, logger=logger):
                logger.error(f"Failed to update database status to CREATE_FAILED: {name}")
            db_cr.error(action="ResumeXDBDatabase", reason="CreateFailed", 
                        message=f"Database create failed during resume, result: {result}")
    elif current_status == diagnose.ClusterDiagStatus.CREATE_FAILED.value:
        # CREATE_FAILED 状态重新执行创建流程
        logger.info(f"Retrying database creation for CREATE_FAILED status: {name}")
        result = db_ctl.create_database(logger=logger)
        if result == 0:
            if db_ctl.check_database_is_exist(logger) == 0:
                status_data = {
                    "db": {
                        "status": diagnose.ClusterDiagStatus.RUNNING.value,
                        "ready": True,
                        "lastProbeTime": utils.isotime()
                    }}
                if not update_status_with_retry(db_cr, status_data, logger=logger):
                    logger.error(f"Failed to update database status to RUNNING: {name}")
                db_cr.set_create_time()
                db_cr.info(action="ResumeXDBDatabase", reason="CreateFailedRetrySuccess", 
                           message="Database created successfully after CREATE_FAILED retry.")
            else:
                status_data = {
                    "db": {
                        "status": diagnose.ClusterDiagStatus.CHECK_FAILED.value,
                        "ready": False,
                        "lastProbeTime": utils.isotime()
                    }}
                if not update_status_with_retry(db_cr, status_data, logger=logger):
                    logger.error(f"Failed to update database status to CHECK_FAILED: {name}")
                db_cr.warn(action="ResumeXDBDatabase", reason="CreateFailedRetryCheckFailed", 
                           message="Database check failed after CREATE_FAILED retry.")
        else:
            status_data = {
                "db": {
                    "status": diagnose.ClusterDiagStatus.CREATE_FAILED.value,
                    "ready": False,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(db_cr, status_data, logger=logger):
                logger.error(f"Failed to update database status to CREATE_FAILED: {name}")
            db_cr.error(action="ResumeXDBDatabase", reason="CreateFailedRetryFailed", 
                        message=f"Database create failed again during CREATE_FAILED retry, result: {result}")
    elif current_status == diagnose.ClusterDiagStatus.CHECK_FAILED.value:
        # CHECK_FAILED 状态重新检查数据库是否存在
        logger.info(f"Retrying database check for CHECK_FAILED status: {name}")
        if db_ctl.check_database_is_exist(logger) == 0:
            status_data = {
                "db": {
                    "status": diagnose.ClusterDiagStatus.RUNNING.value,
                    "ready": True,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(db_cr, status_data, logger=logger):
                logger.error(f"Failed to update database status to RUNNING: {name}")
            db_cr.set_create_time()
            db_cr.info(action="ResumeXDBDatabase", reason="CheckFailedRetrySuccess", 
                       message="Database check passed after CHECK_FAILED retry.")
        else:
            status_data = {
                "db": {
                    "status": diagnose.ClusterDiagStatus.CHECK_FAILED.value,
                    "ready": False,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(db_cr, status_data, logger=logger):
                logger.error(f"Failed to update database status to CHECK_FAILED: {name}")
            db_cr.warn(action="ResumeXDBDatabase", reason="CheckFailedRetryFailed", 
                       message="Database check failed again during CHECK_FAILED retry.")
    elif current_status == diagnose.ClusterDiagStatus.RUNNING.value:
        # 验证数据库是否存在
        if db_ctl.check_database_is_exist(logger) != 0:
            status_data = {
                "db": {
                    "status": diagnose.ClusterDiagStatus.CREATING.value,
                    "ready": False,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(db_cr, status_data, logger=logger):
                logger.error(f"Failed to update database status to CREATING: {name}")
            db_cr.warn(action="ResumeXDBDatabase", reason="RunningLost", 
                       message="Database not found during resume, set to CREATING.")
