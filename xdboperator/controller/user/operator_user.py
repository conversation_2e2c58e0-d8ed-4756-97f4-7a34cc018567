#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   operator_user.py
@Time    :   2024/7/3 15:34
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   l<PERSON><PERSON><PERSON><PERSON>@baidu.com
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""
import kopf
from kopf._cogs.structs.bodies import Body
from logging import Logger
from typing import Optional
import kubernetes.client.rest

from xdboperator.controller import consts, diagnose, config
from xdboperator.controller import utils
from xdboperator.controller.api_utils import ApiSpecError, ignore_404
from xdboperator.controller.kubeutils import k8s_version
from xdboperator.controller.user.xdbuser_api import XDBMySQLUser
from xdboperator.controller.user.xdbuser_controller import XDBUserController
from xdboperator.lightxdb.account import AccountExistsError


def update_status_with_retry(xdbuser, status_data, max_retries=5, logger=None):
    """
    使用重试机制更新XDBUser状态，处理冲突问题
    
    Args:
        xdbuser: XDBMySQLUser对象实例
        status_data: 要更新的状态数据
        max_retries: 最大重试次数
        logger: 日志记录器
    
    Returns:
        bool: 状态更新是否成功
    """
    retries = 0
    while retries < max_retries:
        try:
            xdbuser.set_status(status_data)
            return True
        except kubernetes.client.rest.ApiException as e:
            if e.status == 409:  # Conflict
                if logger:
                    logger.warning(f"发生冲突，重新获取资源并重试，第{retries+1}次重试")
                # 重新获取最新的资源
                try:
                    xdbuser.refresh()
                    retries += 1
                    continue
                except Exception as refresh_err:
                    if logger:
                        logger.error(f"重新获取资源失败: {refresh_err}")
                    return False
            else:
                if logger:
                    logger.error(f"更新状态时发生API错误: {e}")
                return False
        except Exception as e:
            if logger:
                logger.error(f"更新状态时发生未知错误: {e}")
            return False
    
    if logger:
        logger.error(f"更新状态重试次数已达上限 ({max_retries}次)")
    return False


@kopf.on.create(consts.GROUP, consts.VERSION, consts.XDB_USER_PLURAL)
def on_xdbuser_create(name: str, namespace: Optional[str], body: Body,
                      logger: Logger, **kwargs) -> None:
    """
    处理XDBMySQLUser资源的创建事件。
    
    Args:
        name (str): XDBMySQLUser资源的名称。
        namespace (Optional[str], optional): XDBMySQLUser资源所在的命名空间，默认为None。
        body (Body): 包含XDBMySQLUser资源的请求体。
        logger (Logger): 日志记录器，由kopf提供。
        kwargs (dict, optional): 其他可选参数，默认为{}.
    
    Raises:
        kopf.PermanentError: 当XDBMySQLUser资源的spec部分无效时抛出。
        kopf.TemporaryError: 当xdb cluster topo不可用时抛出，并且会在指定的时间后重试。
    
    Returns:
        None: 该函数没有返回值。
    """
    logger.info(f"Init XDB Cluster User name={name} namespace={namespace} on K8s {k8s_version()}")
    xdbuser = XDBMySQLUser(body)
    try:
        xdbuser_ctl = XDBUserController(xdbuser, logger)
    except ValueError as e:
        logger.warning(f"初始化 XDBUserController 失败: {str(e)}")
        # 如果找不到关联集群，可以认为用户资源可以安全删除
        msg = f"找不到关联集群，跳过创建用户操作: {xdbuser.user_name}"
        logger.warning(msg)
        xdbuser.warn(action="CreateXDBUser", reason="ClusterNotFound", message=msg)
        status_data = {
            "user": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(xdbuser, status_data, logger=logger):
            logger.error(f"无法更新用户状态为INVALID: {name}")
        return
    if xdbuser.get_xdbuser_status("status") is None:
        status_data = {
            "user": {
                "status": diagnose.ClusterDiagStatus.CREATING.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(xdbuser, status_data, logger=logger):
            logger.error(f"无法更新用户状态为CREATING: {name}")
    try:
        xdbuser.parse_spec()
        xdbuser.parsed_spec.validate(logger)
    except ApiSpecError as e:
        status_data = {
            "user": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(xdbuser, status_data, logger=logger):
            logger.error(f"无法更新用户状态为INVALID: {name}")
        xdbuser.error(action="CreateXDBUser", reason="InvalidArgument", message=str(e))
        raise kopf.PermanentError(f"Error in XDBMySQLUser spec: {e}")
    xdbuser.log_user_info(logger)
    # 判断xdb cluster 是否存在并且运行正常
    if not xdbuser.xdbcluster_ready:
        status_data = {
            "user": {
                "status": diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(xdbuser, status_data, logger=logger):
            logger.error(f"无法更新用户状态为WAITING_CLUSTER_READY: {name}")
        xdbuser.warn(action="CreateXDBUser", reason="ReadyCheckFailed", message="xdb cluster topo no ready")
        raise kopf.TemporaryError("xdb cluster topo no ready, wait retry", delay=5)
    if xdbuser.ready:
        msg = "The resource already exists. Skip execution"
        logger.warning(msg)
        xdbuser.warn(action="CreateXDBUser", reason="resourceExists", message=msg)
        return
    if xdbuser.get_xdbuser_status("create_user_status") is None:
        logger.debug(f"create_user_status is none,need create use crd:{xdbuser.name}")
        try:
            result = xdbuser_ctl.create_xdbuser(logger=logger)
            # 创建成功
            if result == 0:
                status_data = {
                    "user": {
                        "status": diagnose.ClusterDiagStatus.WAIT_ACCESS_PASS.value,
                        "ready": False,
                        "create_user_status": "success",
                        "lastProbeTime": utils.isotime()
                    }}
                if not update_status_with_retry(xdbuser, status_data, logger=logger):
                    logger.error(f"无法更新用户状态为WAIT_ACCESS_PASS: {name}")
        except AccountExistsError as e:
            logger.warning(f"User already exists: {str(e)}")
            status_data = {
                "user": {
                    "status": diagnose.ClusterDiagStatus.USER_EXISTS.value,
                    "ready": False,
                    "create_user_status": "user_exists",
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(xdbuser, status_data, logger=logger):
                logger.error(f"无法更新用户状态为USER_EXISTS: {name}")
            xdbuser.warn(action="CreateXDBUser", reason="UserExists",
                        message=f"User already exists: {str(e)}")
            return
        except Exception as e:
            logger.error(f"Failed to create user: {str(e)}")
            status_data = {
                "user": {
                    "status": diagnose.ClusterDiagStatus.CREATE_FAILED.value,
                    "ready": False,
                    "create_user_status": "failed",
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(xdbuser, status_data, logger=logger):
                logger.error(f"无法更新用户状态为CREATE_FAILED: {name}")
            xdbuser.error(action="CreateXDBUser", reason="ExecuteFunError",
                         message=f"Failed to execute lightxdb script: {str(e)}")
            return
    # 保证幂等性，只在用户创建成功后进行用户访问测试
    if xdbuser.get_xdbuser_status("ready") is False and \
            xdbuser.get_xdbuser_status("create_user_status") == "success":
        result = xdbuser_ctl.check_user_access(logger)
        logger.debug(f"start check access, user:{xdbuser.name}, ret:{result}...")
        # 用户访问成功，设置创建时间
        if result:
            status_data = {
                "user": {
                    "status": diagnose.ClusterDiagStatus.RUNNING.value,
                    "ready": True,
                    "create_user_status": "success",
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(xdbuser, status_data, logger=logger):
                logger.error(f"无法更新用户状态为RUNNING: {name}")
                raise kopf.TemporaryError("无法更新用户状态，稍后重试", delay=5)
            xdbuser.set_create_time()
            xdbuser.info(action="CreateXDBUser", reason="CreateSuccessfully",
                         message="create user and check successfully.")
        else:
            logger.error("xdb user access no ready. wait retry! ")
            raise kopf.TemporaryError("xdb user access no ready, wait retry", delay=5)


@kopf.on.delete(consts.GROUP, consts.VERSION, consts.XDB_USER_PLURAL)
def on_xdbuser_delete(name: str, namespace: Optional[str], body: Body,
                      logger: Logger, **kwargs) -> None:
    """
    删除用户CR
    """
    logger.info(f"Delete XDB User CR name={name} namespace={namespace} on K8s {k8s_version()}")
    xdbuser = XDBMySQLUser(body)
    
    # 定义可以删除用户的状态集合
    deletable_states = {
        diagnose.ClusterDiagStatus.RUNNING.value,
        diagnose.ClusterDiagStatus.CREATE_FAILED.value,
        diagnose.ClusterDiagStatus.WAIT_ACCESS_PASS.value,
        diagnose.ClusterDiagStatus.UPDATE_FAILED.value
    }
    
    current_status = xdbuser.get_xdbuser_status("status")
    logger.info(f"Current user status: {current_status}, user: {xdbuser.user_name}")
    
    if current_status not in deletable_states:
        msg = f"Cannot delete user in current status: {current_status}, skip deletion for user: {xdbuser.user_name}"
        logger.warning(msg)
        xdbuser.warn(action="DeleteXDBUser", reason="InvalidStatus", message=msg)
        return
        
    if xdbuser.ready:
        xdbuser.set_status({
            "user": {
                "status": diagnose.ClusterDiagStatus.TERMINATE.value
            }})

    try:
        xdbuser_ctl = XDBUserController(xdbuser, logger)
    except ValueError as e:
        logger.warning(f"初始化 XDBUserController 失败: {str(e)}")
        # 如果找不到关联集群，可以认为用户资源可以安全删除
        msg = f"找不到关联集群，跳过删除用户操作: {xdbuser.user_name}"
        logger.warning(msg)
        xdbuser.warn(action="DeleteXDBUser", reason="ClusterNotFound", message=msg)
        return
    
    existing_cluster = ignore_404(xdbuser.get_xdbcluster_stateful_set)
    
    if existing_cluster and xdbuser.ready:
        if xdbuser_ctl.cluster.cluster_sts_ready:
            logger.info(f"Starting to delete user {xdbuser.user_name} in status: {current_status}")
            ret = xdbuser_ctl.delete_xdbuser(logger=logger)
            logger.info(f"Delete User CR name={name} User:{xdbuser.user_name} status:{current_status} ret:{ret}")
        else:
            msg = "relation xdb cluster sts, status replica not is ready or cr status not is running "
            xdbuser.error(action="DeleteXDBCluster", reason="XDBClusterNotReady", message=msg)
            raise kopf.TemporaryError(f"Error in XDBMySQLUser spec: {msg}")
    else:
        msg = f"user status is {current_status}, cr status:{xdbuser.ready} skip del User: {xdbuser.user_name}"
        logger.warning(msg)
        xdbuser.warn(action="DeleteXDBUser", reason="XDBUserNotExist", message=msg)


@kopf.on.field(consts.GROUP, consts.VERSION, consts.XDB_USER_PLURAL, field="spec.permissions", 
               retries=config.BOOTSTRAP_RETRIES * 30)
def on_xdbuser_field_permissions_change(old: str, new: str, body: Body,
                                 logger: Logger, **kwargs):
    """
    监听修改权限字段,只处理db 授权新增操作，如果有关联db权限减少逻辑，不做真实回收操作
    """
    logger.info(f"on_xdbuser_field_permissions_change.old:{old} new:{new}")
    
    if not old:
        # on IC object created, nothing to do here
        logger.debug(f"on_xdbuser_field_permissions_change: Old is empty, skip run.")
        return

    if old == new:
        return

    xdbuser = XDBMySQLUser(body)
    if xdbuser.deleting:
        logger.debug(f"xdbuser:{xdbuser} is deleing, skip change")
        return
    
    if not xdbuser.ready:
        logger.debug(f"on_xdbuser_field_permissions_change: Ignoring on_spec change for unready xdbuser")
        return

    if old is None:
        old = []
    if new is None:
        new = []
    current_status = xdbuser.get_xdbuser_status("status")
    if current_status == diagnose.ClusterDiagStatus.RUNNING.value:
        status_data = {
            "user": {
                "status": diagnose.ClusterDiagStatus.UPDATING.value,
                "ready": False,
                "create_user_status": None,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(xdbuser, status_data, logger=logger):
            logger.error(f"无法更新用户状态为UPDATING: {xdbuser.name}")
            raise kopf.TemporaryError("无法更新用户状态，稍后重试", delay=5)
    else:
        msg = f"xdbuser status is:{current_status} != running. wait retry! "
        logger.error(msg)
        raise kopf.TemporaryError(msg, delay=20)
    c = XDBUserController(xdbuser, logger)
    if xdbuser.get_xdbuser_status("create_user_status") is None:
        ret = c.on_xdbuser_dbauth_change(old, new)
        logger.debug(f"on_xdbuser_dbauth_change ret:{ret}")
        # 创建成功
        if ret == 0:
            status_data = {
                "user": {
                    "status": diagnose.ClusterDiagStatus.WAIT_ACCESS_PASS.value,
                    "ready": False,
                    "create_user_status": "success",
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(xdbuser, status_data, logger=logger):
                logger.error(f"无法更新用户状态为WAIT_ACCESS_PASS: {xdbuser.name}")
                raise kopf.TemporaryError("无法更新用户状态，稍后重试", delay=5)
        else:
            status_data = {
                "user": {
                    "status": diagnose.ClusterDiagStatus.UPDATE_FAILED.value,
                    "ready": False,
                    "create_user_status": "error",
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(xdbuser, status_data, logger=logger):
                logger.error(f"无法更新用户状态为UPDATE_FAILED: {xdbuser.name}")
            return 
            
    # 保证幂等性，只在用户创建成功后进行用户访问测试
    if xdbuser.get_xdbuser_status("ready") is False and \
            xdbuser.get_xdbuser_status("create_user_status") == "success":
        result = c.check_user_access(logger)
        logger.debug(f"start check access, user:{xdbuser.name}, ret:{result}...")
        # 用户访问成功，设置创建时间
        if result:
            status_data = {
                "user": {
                    "status": diagnose.ClusterDiagStatus.RUNNING.value,
                    "ready": True,
                    "create_user_status": "success",
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(xdbuser, status_data, logger=logger):
                logger.error(f"无法更新用户状态为RUNNING: {xdbuser.name}")
                raise kopf.TemporaryError("无法更新用户状态，稍后重试", delay=5)
            xdbuser.set_create_time()
            xdbuser.info(action="CreateXDBUser", reason="CreateSuccessfully",
                         message="create user and check successfully.")
        else:
            logger.error("xdb user access no ready. wait retry! ")
            raise kopf.TemporaryError("xdb user access no ready, wait retry", delay=5)


@kopf.on.field(consts.GROUP, consts.VERSION, consts.XDB_USER_PLURAL, field="spec.wrFlag", 
               retries=config.BOOTSTRAP_RETRIES * 30)
def on_xdbuser_field_rwflag_change(old: str, new: str, body: Body,
                                 logger: Logger, **kwargs):
    """
    监听修改权限字段
    """
    logger.info(f"on_xdbuser_field_rwflag_change. old:{old} new:{new}")
    
    if not old:
        # on IC object created, nothing to do here
        logger.debug(f"on_xdbuser_field_rwflag_change: Old is empty, skip run.")
        return
    
    if old == new:
        return
    
    xdbuser = XDBMySQLUser(body)
    if xdbuser.deleting:
        logger.debug(f"xdbuser:{xdbuser} is deleing, skip change")
        return
    if not xdbuser.ready:
        logger.debug(f"on_xdbuser_field_rwflag_change: Ignoring on_spec change for unready xdbuser")
        return
    c = XDBUserController(xdbuser, logger)
    ret = c.on_xdbuser_rwflag_change(old, new)
    if ret != 0:
        status_data = {
            "user": {
                "status": diagnose.ClusterDiagStatus.UPDATE_FAILED.value,
                "ready": False,
                "create_user_status": "error",
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(xdbuser, status_data, logger=logger):
            logger.error(f"无法更新用户状态为UPDATE_FAILED: {xdbuser.name}")
    logger.debug(f"on_xdbuser_field_rwflag_change ret: {ret}")
        

@kopf.on.resume(consts.GROUP, consts.VERSION, consts.XDB_USER_PLURAL)
def on_xdbuser_resume(name: str, namespace: Optional[str], body: Body,
                     logger: Logger, **kwargs) -> None:
    """
    处理 operator 重启时的 XDBUser 资源恢复。
    确保所有已存在的 XDBUser 资源状态一致。
    
    Args:
        name (str): XDBUser 资源的名称
        namespace (Optional[str]): XDBUser 资源所在的命名空间
        body (Body): 包含 XDBUser 资源的请求体
        logger (Logger): 日志记录器
        kwargs: 其他可选参数
    """
    logger.info(f"Resume handling XDBUser name={name} namespace={namespace}")
    xdbuser = XDBMySQLUser(body)
    xdbuser.info(action="ResumeXDBUser", reason="StartResume", 
                 message=f"Resume handling XDBUser name={name} namespace={namespace}")
    
    # 如果资源正在删除中，跳过处理
    if xdbuser.deleting:
        logger.debug(f"XDBUser {name} is being deleted, skip resume handling")
        xdbuser.info(action="ResumeXDBUser", reason="Deleting", 
                     message=f"XDBUser {name} is being deleted, skip resume handling")
        return
    
    current_status = xdbuser.get_xdbuser_status("status")
    logger.info(f"Current user status: {current_status}, user: {xdbuser.user_name}")
    xdbuser.info(action="ResumeXDBUser", reason="CurrentStatus",
                 message=f"Current user status: {current_status}, user: {xdbuser.user_name}")
    
    try:
        xdbuser_ctl = XDBUserController(xdbuser, logger)
    except ValueError as e:
        logger.warning(f"Failed to initialize XDBUserController: {str(e)}")
        xdbuser.error(action="ResumeXDBUser", reason="InitControllerFailed", message=str(e))
        status_data = {
            "user": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(xdbuser, status_data, logger=logger):
            logger.error(f"Failed to update user status to INVALID: {name}")
        return
    
    # 检查集群状态
    if not xdbuser.xdbcluster_ready:
        status_data = {
            "user": {
                "status": diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(xdbuser, status_data, logger=logger):
            logger.error(f"Failed to update user status to WAITING_CLUSTER_READY: {name}")
        xdbuser.warn(action="ResumeXDBUser", reason="ClusterNotReady", 
                     message="xdb cluster topo not ready during resume")
        return
    
    # 如果状态为空，说明是首次创建，设置为 CREATING 状态
    if current_status is None:
        status_data = {
            "user": {
                "status": diagnose.ClusterDiagStatus.CREATING.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(xdbuser, status_data, logger=logger):
            logger.error(f"Failed to update user status to CREATING: {name}")
        xdbuser.info(action="ResumeXDBUser", reason="SetCreating",
                     message="Set user status to CREATING during resume")
        return
    
    # 根据当前状态进行相应处理
    if current_status == diagnose.ClusterDiagStatus.CREATING.value:
        try:
            result = xdbuser_ctl.create_xdbuser(logger=logger)
            if result == 0:
                status_data = {
                    "user": {
                        "status": diagnose.ClusterDiagStatus.WAIT_ACCESS_PASS.value,
                        "ready": False,
                        "create_user_status": "success",
                        "lastProbeTime": utils.isotime()
                    }}
                if not update_status_with_retry(xdbuser, status_data, logger=logger):
                    logger.error(f"Failed to update user status to WAIT_ACCESS_PASS: {name}")
                xdbuser.info(action="ResumeXDBUser", reason="CreateSuccess",
                             message="User created successfully during resume.")
        except AccountExistsError as e:
            status_data = {
                "user": {
                    "status": diagnose.ClusterDiagStatus.USER_EXISTS.value,
                    "ready": False,
                    "create_user_status": "user_exists",
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(xdbuser, status_data, logger=logger):
                logger.error(f"Failed to update user status to USER_EXISTS: {name}")
            xdbuser.warn(action="ResumeXDBUser", reason="UserExists", 
                         message=f"User already exists: {str(e)}")
        except Exception as e:
            xdbuser.error(action="ResumeXDBUser", reason="Exception", message=str(e))
            logger.error(f"Exception during resume create: {str(e)}")
    elif current_status == diagnose.ClusterDiagStatus.USER_EXISTS.value:
        try:
            result = xdbuser_ctl.create_xdbuser(logger=logger)
            if result == 0:
                status_data = {
                    "user": {
                        "status": diagnose.ClusterDiagStatus.WAIT_ACCESS_PASS.value,
                        "ready": False,
                        "create_user_status": "success",
                        "lastProbeTime": utils.isotime()
                    }}
                if not update_status_with_retry(xdbuser, status_data, logger=logger):
                    logger.error(f"Failed to update user status to WAIT_ACCESS_PASS: {name}")
                xdbuser.info(action="ResumeXDBUser", reason="UserExistsFixed",
                             message="UserExists status fixed, user updated successfully during resume.")
                
                # 立即进行访问检查，避免等待下次重试
                logger.info(f"UserExists fixed, immediately checking user access for: {xdbuser.user_name}")
                access_result = xdbuser_ctl.check_user_access(logger)
                if access_result:
                    status_data = {
                        "user": {
                            "status": diagnose.ClusterDiagStatus.RUNNING.value,
                            "ready": True,
                            "create_user_status": "success",
                            "lastProbeTime": utils.isotime()
                        }}
                    if not update_status_with_retry(xdbuser, status_data, logger=logger):
                        logger.error(f"Failed to update user status to RUNNING: {name}")
                    xdbuser.info(action="ResumeXDBUser", reason="UserExistsFixedAndAccessPassed",
                                 message="UserExists status fixed and access check passed during resume.")
                else:
                    xdbuser.warn(action="ResumeXDBUser", reason="UserExistsFixedButAccessFailed",
                                 message="UserExists status fixed but access check failed during resume.")
            else:
                # 创建失败，保持 USER_EXISTS 状态
                xdbuser.warn(action="ResumeXDBUser", reason="UserExistsFixFailed",
                             message="Failed to fix UserExists status during resume.")
        except AccountExistsError as e:
            # 依然冲突，保持状态
            xdbuser.warn(action="ResumeXDBUser", reason="UserStillExists", message=f"User still exists: {str(e)}")
            logger.warning(f"User still exists during resume: {str(e)}")
        except Exception as e:
            xdbuser.error(action="ResumeXDBUser", reason="Exception", message=str(e))
            logger.error(f"Exception during resume USER_EXISTS: {str(e)}")
    elif current_status == diagnose.ClusterDiagStatus.WAIT_ACCESS_PASS.value:
        result = xdbuser_ctl.check_user_access(logger)
        if result:
            status_data = {
                "user": {
                    "status": diagnose.ClusterDiagStatus.RUNNING.value,
                    "ready": True,
                    "create_user_status": "success",
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(xdbuser, status_data, logger=logger):
                logger.error(f"Failed to update user status to RUNNING: {name}")
            xdbuser.info(action="ResumeXDBUser", reason="AccessCheckSuccess",
                         message="User access check passed during resume.")
        else:
            xdbuser.warn(action="ResumeXDBUser", reason="AccessCheckFailed",
                         message="User access check failed during resume.")
    elif current_status == diagnose.ClusterDiagStatus.RUNNING.value:
        result = xdbuser_ctl.check_user_access(logger)
        if not result:
            status_data = {
                "user": {
                    "status": diagnose.ClusterDiagStatus.WAIT_ACCESS_PASS.value,
                    "ready": False,
                    "create_user_status": "success",
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(xdbuser, status_data, logger=logger):
                logger.error(f"Failed to update user status to WAIT_ACCESS_PASS: {name}")
            xdbuser.warn(action="ResumeXDBUser", reason="RunningAccessLost", 
                         message="User access lost during resume, set to WAIT_ACCESS_PASS.")

    
    