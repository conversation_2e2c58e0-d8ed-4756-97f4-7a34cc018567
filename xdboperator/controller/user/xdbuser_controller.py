#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   xdbuser_controller.py
@Time    :   2024/6/22 19:20
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""
import logging
import time
import json

from xdboperator.controller import config
from xdboperator.controller.user.xdbuser_api import XDBMySQLUser
from xdboperator.lightxdb.account import AccountService
from xdboperator.lightxdb.zookeeper import ZookeeperService
from xdboperator.controller.cluster.xdbcluster_api import XDBCluster




class XDBUserController(object):
    """
    This is the controller for a xdbuser object.
    It's the main controller for a cluster and drives the lifecycle of the
    cluster including creation, scaling and restoring from outages.
    """

    def __init__(self, xdbUser: XDBMySQLUser, logger: logging.Logger):
        """
        初始化函数，用于设置XDBMySQLUser对象和集群信息。
        
        Args:
            xdbUser (XDBMySQLUser): XDBMySQLUser类型的对象，表示需要操作的XDB用户。
            logger (logging.Logger): 日志记录器。
        
        Raises:
            ValueError: 当无法获取到关联的集群信息时抛出。
        """
        self.xdbUser = xdbUser
        self.logger = logger
        
        # 获取集群并进行验证
        self.cluster = self.xdbUser.get_cluster()
        if not self.cluster:
            self.logger.error(f"无法找到与用户 {self.xdbUser.name} 关联的集群")
            raise ValueError(f"无法找到与用户 {self.xdbUser.name} 关联的集群")
            
        # 确保集群已经完成规格解析
        if not hasattr(self.cluster, 'parsed_spec'):
            self.logger.error(f"集群 {self.cluster.name} 的规格未完成解析")
            raise ValueError(f"集群 {self.cluster.name} 的规格未完成解析")
            
        # 初始化 AccountService
        self.account = AccountService(
            self.cluster.parsed_spec.ZKDomain,
            self.cluster.parsed_spec.mysqlVersion,
            self.cluster.parsed_spec.appID,
            logger
        )

    def _get_privileges(self, account_type: str):
        """
        获取权限
        这些权限在特定数据库中有效。
        ALL PRIVILEGES: 授予所有权限（不包括 GRANT OPTION）。
        ALTER: 允许使用 ALTER TABLE。
        ALTER ROUTINE: 允许修改或删除存储例程。
        CREATE: 允许创建表。
        CREATE ROUTINE: 允许创建存储例程。
        CREATE TEMPORARY TABLES: 允许创建临时表。
        CREATE VIEW: 允许创建视图。
        DELETE: 允许删除表中的记录。
        DROP: 允许删除表。
        EVENT: 允许创建、删除和更改事件。
        EXECUTE: 允许执行存储例程。
        INDEX: 允许创建和删除索引。
        INSERT: 允许插入表中的记录。
        LOCK TABLES: 允许使用 LOCK TABLES。
        SELECT: 允许查询表中的记录。
        SHOW VIEW: 允许查看视图定义。
        TRIGGER: 允许创建和删除触发器。
        UPDATE: 允许更新表中的记录。

        """
        privileges = "SELECT"
        if account_type.lower() == "read":
            privileges = "SELECT"
        elif account_type.lower() == "write":
            privileges = "SELECT,INSERT,UPDATE,DELETE"
        elif account_type.lower() == "all":
            privileges = "ALL"
        elif account_type.lower() == "dba":
            privileges = "SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, RELOAD, SHUTDOWN, PROCESS,\
                          FILE, REFERENCES, INDEX, ALTER, SHOW DATABASES, CREATE TEMPORARY TABLES, LOCK TABLES, \
                          EXECUTE, REPLICATION SLAVE, REPLICATION CLIENT, CREATE VIEW, SHOW VIEW, CREATE ROUTINE, \
                          ALTER ROUTINE, CREATE USER, EVENT, TRIGGER, CREATE TABLESPACE"
        elif  account_type.lower() == 'monitor':
            privileges = "PROCESS, REPLICATION CLIENT, SELECT"
        return privileges

    def _get_default_db(self, permissions: list) -> str:
        """
        从权限列表中获取默认数据库,dbproxy.conf 中不能使用*为默认数据库，否则无法连接
        """

        db = "information_schema"
        for permission in permissions:
            if permission.schema == "*":
                continue
            else:
                db = permission.schema
                break
        return db
    
    
    def _format_user_data(self):
        """
            基于当前cr 格式化账户信息
        Returns:
            _type_: _description_
        """
        password = self.xdbUser.get_user_password()
        permissions = self.xdbUser.parsed_spec.permissions
        json_conf = {
            "data": {
                "account_info": {
                    "authbns": [],
                    "authip": [],
                    "db_auth": [],
                    "authip_enable": 0,
                    "db_password": password,
                    "db_username": self.xdbUser.user_name,
                    "default_db": self._get_default_db(permissions),
                    "password": password,
                    "remark": "pro",
                    "type": "online",
                    "username": self.xdbUser.user_name,
                    "default_charset": "utf8",
                    "max_connections": self.xdbUser.parsed_spec.dbproxyUserMaxConnections,
                    "mysql_max_connections": self.xdbUser.parsed_spec.mysqlUserMaxConnections,
                    "wr_flag": self.xdbUser.parsed_spec.wrFlag,
                    "allowed_hosts": self.xdbUser.parsed_spec.allowedHosts
                },
                "app_id": self.cluster.parsed_spec.appID,
            },
            "task_id": 1,
            "task_name": "account_create"
        }
        for db in permissions:
            json_conf['data']['account_info']['db_auth'].append({
                    'dbname': db.schema,
                    'privileges': self._get_privileges(db.permissions)
                }
            )
        return json_conf

        
    def create_xdbuser(self, logger) -> int:
        """
        原始操作创建用户
        ./AccountService.py xdb_lxz_dev bce_charge_w vp4jqc0xbrqw@AbC24446 all bce_charge,bce_rds
        """
        json_conf = self._format_user_data()
        ret = self.account.account_create(json_conf['data'], self.xdbUser.parsed_spec.allowedHosts)
        logger.info(f"create_xdbuser: {json_conf['data']} return: {ret}")
        return ret

    def on_xdbuser_rwflag_change(self, old: int, new: int):
        """
        修改用户读写权限的cr触发的事件
        Args:
            old (int): _description_
            new (int): _description_
            logger (logging.Logger): _description_
        """
        json_conf = self._format_user_data()
        ret = self.account.update_account_rw_flag_to_zk(json_conf['data'], new)
        return ret
    
    def on_xdbuser_dbauth_change(self, old: list, new: list):
        """
        用户关联的DB更新
        """
        
        json_conf = self._format_user_data()
        ret = self.account.account_modify(json_conf['data'], self.xdbUser.parsed_spec.allowedHosts)
        self.logger.info(f"create_xdbuser: {json_conf['data']} return: {ret}")
        return ret 
        
    
    def sync_xdbproxy_new_node_user_permissions(self, dbproxy_ip: str, logger: logging.Logger) -> int:

        """
        从user CR 中解析获取出用户权限信息,生成sql并执行
        """
        db_auth = []
        permissions = self.xdbUser.parsed_spec.permissions
        password = self.xdbUser.get_user_password()

        app_id =  self.cluster.parsed_spec.appID
        cluster_id = self.cluster.parsed_spec.clusterID
        
        
        create_user_sql = self.account.generate_new_proxy_node_account_sql(dbproxy_ip, self.xdbUser.user_name, password)
        sqls = [create_user_sql]
        
        for db in permissions:
            sqls += self.account.generate_user_grant_sqls(self.xdbUser.user_name, dbproxy_ip,
                                                     db.schema, self._get_privileges(db.permissions))
            
        _, each_instance = self.account.get_master_node_from_zookeeper(app_id, cluster_id)
        if not  each_instance:
            raise Exception(f"Can not find master node from zk: {app_id}, {cluster_id}")

        for sql in sqls:
            logger.debug(f"Execute sql: {sql}")
            ret = self.account.xagent_service.xagent_sql(each_instance['ip'], each_instance['xagent_port'],
                                                    each_instance['port'], sql, 1)
            if ret is None and not sql.startswith('CREATE'):
                logger.warning("Execute sql[%s] failed: %s, %s, %s.", sql,
                                each_instance['ip'], each_instance['xagent_port'], each_instance['port'])
                return -1
        return 0

                
    def delete_xdbuser(self, logger: logging.Logger) -> int:
        """
        删除用户,只从zk中删除，不删除底层mysql中用户
        """
        user_name = self.xdbUser.user_name
        cluster = self.xdbUser.get_cluster()

        zk = ZookeeperService(self.cluster.parsed_spec.ZKDomain, logger=logger)
        _, account_list = zk.zk_account_list(cluster.parsed_spec.appID)
        if user_name in account_list:
            status, output = zk.zk_account_delete(cluster.parsed_spec.appID, user_name)
            logger.info(f"delete xdbuser {user_name} status: {status} output:{output}")
            return status
        else:
            logger.info(f"delete xdbuser {user_name} not exist")
            return 0

    def check_user_access(self, logger: logging.Logger, cluster_ip: str = None):
        """
        检查用户是否成功访问
        同时检查通过service入口和所有pod的IP入口
        """
        password = self.xdbUser.get_user_password()
        permissions = self.xdbUser.parsed_spec.permissions
        proxy_svc = self.cluster.get_service()
        if not proxy_svc:
            raise Exception("Cannot get dbproxy service")

        # 获取所有pod
        pods = self.cluster.get_pods()
        if not pods:
            raise Exception("Cannot get cluster pods")

        overall_flag = True  # 整体成功或失败的标志

        def log_and_check_access(db, ip):
            schema = db.schema if db.schema != '*' else 'information_schema'
            logger.info(f"Checking user access user: {self.xdbUser.user_name} "
                        f"ip: {ip} pwd: {password} db: {schema}")
            success = self.retry_connection(self.xdbUser.user_name, password, ip, schema, logger)
            if not success:
                logger.error(f"Failed to connect user: {self.xdbUser.user_name} "
                            f"ip: {ip} pwd: {password} db: {schema} after 3 attempts")
            return success

        # 检查service入口
        cluster_ip = cluster_ip or proxy_svc.spec.cluster_ip
        allowed_hosts = self.xdbUser.parsed_spec.allowedHosts
        if allowed_hosts:
            for host in allowed_hosts:
                if host == 'localhost':
                    continue
                for db in permissions:
                    overall_flag &= log_and_check_access(db, cluster_ip)
        else:
            for db in permissions:
                overall_flag &= log_and_check_access(db, cluster_ip)

        # 检查每个pod的IP入口
        for pod in pods:
            pod_ip = pod.pod_ip_address
            if not pod_ip:
                logger.warning(f"Pod {pod.name} has no IP address")
                continue
            
            if allowed_hosts:
                for host in allowed_hosts:
                    if host == 'localhost':
                        continue
                    for db in permissions:
                        overall_flag &= log_and_check_access(db, pod_ip)
            else:
                for db in permissions:
                    overall_flag &= log_and_check_access(db, pod_ip)

        if overall_flag:
            logger.info("All user access checks passed successfully")
        else:
            logger.error("Some user access checks failed")

        return overall_flag


    @staticmethod
    def retry_connection(username, password, cluster_ip, schema, logger, port=config.XDB_PROXY_PORT, max_retries=3):
        """
        尝试连接数据库，最多重试 max_retries 次
        """
        for attempt in range(1, max_retries + 1):
            ret = AccountService.check_mysql_account_connection(username, password, cluster_ip,
                                                                schema, port)
            if ret:
                logger.info(f"Connection successful on attempt {attempt} for user {username} schema {schema}")
                return True
            else:
                logger.warning(f"Connection attempt {attempt} failed for user {username} schema {schema}")
                time.sleep(1)
        return False

    @classmethod
    def check_user_access_status(cls, user: dict, cluster_ip: str, logger):
        """
        静态方法，用于检查用户是否成功访问
        """
       
        overall_flag = True  # 表示整体成功或失败的标志
        user_name = user['db_username']
        password = user['db_password']
        default_db = user['default_db']
        # for db in user.get('db_auth', []):
        logger.info(f"Checking user access user: {user_name}, " +
                    f"ip: {cluster_ip}, pwd: {password}, db: {default_db} ")
        success = cls.retry_connection(user_name, password,
                                        cluster_ip, default_db, logger)
        if not success:
            overall_flag = False
            logger.error(f"Failed to connect user: {user_name}, " +
                            f"ip: {cluster_ip}, pwd: {password}, db: {default_db} after 3 attempts")

        if overall_flag:
            logger.info("All user access checks passed successfully")
        else:
            logger.error("Some user access checks failed")
        return overall_flag    
    
    @staticmethod
    def sync_xdbproxy_new_node_user_permissions_to_zk(cluster: XDBCluster, dbproxy_ip: str,
                                                      user: dict, logger: logging.Logger) -> int:
        """
        对从zk获取的用户json信息,生成sql并执行
        """
        account = AccountService(cluster.parsed_spec.ZKDomain,
                                 cluster.parsed_spec.mysqlVersion,
                                 cluster.parsed_spec.appID,
                                 logger)

        app_id =  cluster.parsed_spec.appID
        cluster_id = cluster.parsed_spec.clusterID
        sqls = []
        user_name = user['db_username']
        password = user['db_password']
        sqls.append(account.generate_new_proxy_node_account_sql(dbproxy_ip, user_name, password))
        for db in user['db_auth']:
            sqls += account.generate_user_grant_sqls(user_name, dbproxy_ip,
                                                        db['dbname'], db['privileges'])
        _, each_instance = account.get_master_node_from_zookeeper(app_id, cluster_id)
        logger.debug(f"user_name:{user_name}, need execute sqls:{len(sqls)}")
        for sql in sqls:
            logger.debug(f"Execute sql: {sql}")
            ret = account.xagent_service.xagent_sql(each_instance['ip'], each_instance['xagent_port'],
                                                    each_instance['port'], sql, 1)
            if ret is None and not sql.startswith('CREATE'):
                logger.warning("Execute sql[%s] failed: %s, %s, %s.", sql,
                                each_instance['ip'], each_instance['xagent_port'], each_instance['port'])
                return -1  
        return 0
              