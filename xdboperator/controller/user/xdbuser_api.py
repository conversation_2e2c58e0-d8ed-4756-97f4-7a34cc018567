#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   xdbuser_api.py
@Time    :   2024/6/16 08:29
<AUTHOR>   lixian<PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""
import datetime
import json
import typing
import logging

from enum import Enum
from kopf._cogs.structs.bodies import Body
from logging import Logger
from typing import Optional, cast, List

from xdboperator.controller import utils, consts
from xdboperator.controller.api_utils import dget_dict, dget_str, dget_list, dget_int
from xdboperator.controller.cluster.xdbcluster_api import AbstractServerSetSpec
from xdboperator.controller.cluster.xdbcluster_api import XDBCluster
from xdboperator.controller.k8sobject import K8sInterfaceObject
from xdboperator.controller.kubeutils import api_core, api_apps, api_customobj
from xdboperator.controller.kubeutils import client as api_client, ApiException
from xdboperator.controller.kubeutils import api_kruise


# Must correspond to the names in the CRD
logger = logging.getLogger(__name__)


class XDBClusterUserSpecProperties(Enum):
    """
    属性枚举
    """
    PASSWORD = "password"
    CLUSTER = "clusterRef"
    PERMISSIONS = "permissions"


class ClusterReferenceSpec(object):
    namespace: str = None
    name: str = ""

    def parse(self, spec: dict, prefix: str) -> None:
        """
            解析规格，获取命名空间和名称。
        
        Args:
            spec (dict): 规格字典，包含"namespace"和"name"两个键值对，分别表示命名空间和名称。
                如果不存在这两个键，则使用默认的命名空间和名称。
            prefix (str, optional): 前缀，默认为"".
        
        Returns:
            None: 无返回值，直接修改类实例属性。
        """
        self.namespace = dget_str(spec, "namespace", prefix)
        self.name = dget_str(spec, "name", prefix)

    def __str__(self):
        """
            返回字符串格式的对象，包含命名空间和名称。
        例如：'default/my-pod'。
        
        Returns:
            str -- 字符串格式的对象。
        """
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        """
            Returns a string representation of the ClusterReferenceSpec object.
        
        Returns:
            str (str): String representation of the ClusterReferenceSpec object.
        """
        return f"<ClusterReferenceSpec {self.name}>"


class MysqlPermissionSpec(object):
    schema: str = None
    permissions: str = ""

    def parse(self, spec: dict, prefix: str) -> None:
        """
            解析规则，将规则中的字段值提取出来并存储在对象属性中。
        支持的字段包括：schema、permissions。
        
        Args:
            spec (dict): 规则字典，包含一个或多个需要提取的字段。
                - schema (str, optional): 数据库名称，默认为None。
                - permissions (str, optional): 权限信息，默认为None。
            prefix (str, optional): 前缀，默认为空字符串。用于处理嵌套字典时的键名。
        
        Returns:
            None: 不返回任何值，直接修改对象属性。
        
        Raises:
            None: 没有引发任何异常。
        """
        if "schema" in spec:
            self.schema = dget_str(spec, "schema", prefix)
        if "permissions" in spec:
            self.permissions = dget_str(spec, "permissions", prefix)

    def __str__(self):
        """
        将对象转换为字符串，格式为 schema/permissions。
        返回值类型：str，例如 "public/insert,select"。
        
        Args:
            None
        
        Returns:
            str (str): 对象的字符串表示形式，包括 schema 和 permissions。
        """
        return f"{self.schema}/{self.permissions}"

    def __repr__(self):
        """
        返回一个字符串，用于表示 MysqlPermissionSpec 对象。
        格式为 "<MysqlPermissionSpec schema>"，其中 schema 是 MysqlPermissionSpec 对象的属性。
        
        Returns:
            str - MysqlPermissionSpec 对象的字符串表示形式。
        """
        return f"<MysqlPermissionSpec {self.schema}>"


class MysqlSecretUserSpec(object):
    secretName: str = ""
    key: str = ""

    def parse(self, spec: dict, prefix: str) -> None:
        """
            解析字典，获取 secretName 和 key 的值，并赋值给对应属性。
        如果字典中不包含这两个键，则会将对应属性设置为 None。
        
        Args:
            spec (dict): 一个字典，包含了需要解析的信息。可能包含以下键：
                - secretName (str, optional): 存放秘密名称的键，默认值为 None。
                - key (str, optional): 存放秘密的键，默认值为 None。
            prefix (str, optional): 前缀，默认值为 "".
        
        Returns:
            None: 该函数没有返回值。
        """
        if "secretName" in spec:
            self.secretName = dget_str(spec, "secretName", prefix)

        if "key" in spec:
            self.key = dget_str(spec, "key", prefix)

    def get_password(self, ns: str) -> str:
        """
            获取指定命名空间下的密码，返回一个字符串类型的密码。
        
        Args:
            ns (str): 需要查询的命名空间名称。
        
        Returns:
            str: 返回一个字符串类型的密码。
        
        Raises:
            无。
        """
        secret = cast(api_client.V1Secret, api_core.read_namespaced_secret(
            self.secretName, ns))

        return utils.b64decode(secret.data["password"])

    def __str__(self):
        """
            返回一个字符串，包含 secretName 和 key。
        这个方法被 str() 函数调用，以便将 SecretKey 对象转换为字符串。
        
        Returns:
            str -- 包含 secretName 和 key 的字符串。例如：'my-secret/my-key'。
        """
        return f"{self.secretName}/{self.key}"

    def __repr__(self):
        """
            Return a string representation of the MysqlSecretUserSpec object.
        
        Returns:
            str: A string in the format "<MysqlSecretUserSpec secretName>".
        """
        return f"<MysqlSecretUserSpec {self.secretName}>"


class XDBMysqlUserSpec(AbstractServerSetSpec):
    """
    xdb user crd spec
    """
    user: str = ""
    password: MysqlSecretUserSpec = None
    clusterRef: ClusterReferenceSpec = None
    permissions: List[MysqlPermissionSpec] = []
    dbproxyUserMaxConnections: int = 500
    mysqlUserMaxConnections: int = 0
    allowedHosts: List[str] = ['%']
    wrFlag: int = 2
    

    def __init__(self, namespace: str, name: str, spec: dict):
        """
            初始化函数，用于初始化类的属性和方法。
        
        Args:
            namespace (str): 命名空间，即Pod所在的Namespace。
            name (str): Pod的名称。
            spec (dict): Pod的规格信息，包括容器信息等。
        
        Returns:
            None.
        
        Raises:
            None.
        """
        super().__init__(namespace, name, name, spec)
        self.load(spec)

    def _add_default_db_permissions(self):
        """
        为全部账户都增加默认db权限，供给xdb-schema-init中的 skeema使用
        """
        dbs = [{"db_name": "_skeema_tmp", "permissions": "ALL"}]
        objs = []
        for db in dbs:
            perm = MysqlPermissionSpec()
            perm.permissions = db["permissions"]
            perm.schema = db["db_name"]
            objs.append(perm)
        return objs

    def load(self, spec: dict) -> None:
        """
            加载规格信息，并将其应用于当前对象。
        该方法会更新当前对象的属性值，包括 user、clusterRef、permissions 和 password。
        
        Args:
            spec (dict): 一个字典，包含了要加载的规格信息。字典中必须包含以下键值对：
                          - user (str, optional): 用户名，默认为 None。
                          - clusterRef (dict, optional): 集群引用，默认为 None。
                          - permissions (List[dict], optional): 数据库权限列表，每个元素是一个字典，包含以下键值对：
                             - database (str, optional): 数据库名称，默认为 None。
                             - privileges (List[str], optional): 权限列表，默认为 None。
                          - password (dict, optional): MySQL密码，默认为 None。
        
        Raises:
            None
        
        Returns:
            None; 无返回值，直接修改当前对象的属性值。
        """
        self._load(spec, spec, "spec")

        self.user = dget_str(spec, "user", "spec")
        self.clusterRef = ClusterReferenceSpec()
        section = XDBClusterUserSpecProperties.CLUSTER.value
        if section in spec:
            self.clusterRef.parse(dget_dict(spec, section, "spec"), "spec.clusterRef")
        self.permissions = []
        section = XDBClusterUserSpecProperties.PERMISSIONS.value
        if section in spec:
            permissions = dget_list(spec, section, "spec", [], content_type=dict)
            for db_privileges in permissions:
                self.permissions.append(self._parse_use_permissions(db_privileges))
        self.password = MysqlSecretUserSpec()
        section = XDBClusterUserSpecProperties.PASSWORD.value
        if section in spec:
            self.password.parse(dget_dict(spec, section, "spec"), "spec.password")
            
        if "dbproxyUserMaxConnections" in spec:
            self.dbproxyUserMaxConnections =  dget_int(spec, "dbproxyUserMaxConnections", "spec")
        if "mysqlUserMaxConnections" in spec:
            self.mysqlUserMaxConnections =  dget_int(spec, "mysqlUserMaxConnections", "spec") 
        if "allowedHosts" in spec:
            self.allowedHosts = dget_list(spec, "allowedHosts", "spec", ['%'], content_type=str)
        if "wrFlag" in spec:
            self.wrFlag =  dget_int(spec, "wrFlag", "spec")
            
        
    def _parse_use_permissions(self, spec: dict) -> MysqlPermissionSpec:
        """
            解析 use_permissions 字段，返回一个 MysqlPermissionSpec 对象。
        该方法主要用于在 MySQL 数据库中创建或修改用户的权限时使用。
        
        Args:
            spec (dict): 包含 use_permissions 字段的字典，格式如下：
                {
                    "use_permissions": [
                        {"database": "db1", "privileges": ["SELECT"]},
                        {"database": "db2", "privileges": ["INSERT", "UPDATE"]}
                    ]
                }
                其中 database 是指定的数据库名称，privileges 是与该数据库相关联的权限列表。
        
            返回值 (MysqlPermissionSpec): 一个 MysqlPermissionSpec 对象，包含了解析出来的权限信息。
        
        Raises:
            无特定异常。
        
        """
        permission = MysqlPermissionSpec()
        permission.parse(spec, "spec.permissions")
        return permission

    def validate(self, logger: Logger) -> None:
        """
            验证当前对象是否有效。如果无效，则抛出异常。
        此方法应该被所有子类重写以实现自定义的验证逻辑。
        
        Args:
            logger (Logger): 一个日志记录器，用于记录验证过程中可能发生的任何错误或警告。
                              默认值为 None，表示不需要记录器。
        
        Raises:
            
            None: 如果当前对象有效，则不会抛出任何异常。
            Exception: 如果当前对象无效，则可能抛出任何异常。
        
        Returns:
            None: 返回 None，表示无返回值。
        """
        # TODO see if we can move some of these to a schema in the CRD
        super().validate(logger)
        


class XDBMySQLUser(K8sInterfaceObject):
    def __init__(self, cluster: Body) -> None:
        """
            初始化方法，用于实例化一个 XDBMysqlUser 对象。
        
        Args:
            cluster (Body): 包含集群信息的 Body 类型对象，必须提供。
                Body 类型对象应该是一个 dict，其中包含了集群的相关信息，如名称、描述等。
        
        Raises:
            无
        
        Returns:
            无 (None)
        """
        super().__init__()

        self.obj: Body = cluster
        self._parsed_spec: Optional[XDBMysqlUserSpec] = None

    def __str__(self):
        """
            返回字符串格式的对象，包含命名空间和名称。
        例如：'default/my-pod'。
        
        Returns:
            str -- 字符串格式的对象。
        """
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        """
            Returns a string representation of the XDBClusterUser object.
        
        Returns:
            str (str): String representation of the XDBClusterUser object.
        """
        return f"<XDBClusterUser {self.name}>"

    @classmethod
    def _get(cls, ns: str, name: str) -> Body:
        """
            获取指定命名空间和名称的自定义资源对象，返回Body类型。
        如果API调用失败，将抛出ApiException异常。
        
        Args:
            ns (str): 命名空间名称。
            name (str): 自定义资源对象名称。
        
        Returns:
            Body (typing.Any): 自定义资源对象Body类型。
        
        Raises:
            ApiException (kubernetes.client.rest.ApiException): API调用失败时抛出的异常。
        """
        try:
            ret = cast(Body, api_customobj.get_namespaced_custom_object(
                consts.GROUP, consts.VERSION, ns,
                consts.XDB_USER_PLURAL, name))
        except ApiException as e:
            raise e

        return ret

    @classmethod
    def _patch(cls, ns: str, name: str, patch: dict) -> Body:
        """
            修补指定命名空间下的自定义对象。
        
        Args:
            ns (str): 命名空间，必须提供。
            name (str): 自定义对象的名称，必须提供。
            patch (dict): 需要修补的内容，格式为字典，必须提供。
        
        Returns:
            Body (typing.cast[Body]): 返回一个包含修补后的自定义对象信息的 Body 类型对象。
        
        Raises:
            无。
        """
        return cast(Body, api_customobj.patch_namespaced_custom_object(
            consts.GROUP, consts.VERSION, ns,
            consts.XDB_USER_PLURAL, name, body=patch))

    @classmethod
    def _patch_status(cls, ns: str, name: str, patch: dict) -> Body:
        """
            修补状态字段，返回一个Body对象。
        
        Args:
            ns (str): 命名空间。
            name (str): 自定义对象的名称。
            patch (dict): 需要修补的状态字段，格式为JSON Patch。
        
        Returns:
            Body: 返回一个Body对象，包含了修补后的状态字段。
        
        Raises:
            无。
        """
        return cast(Body, api_customobj.patch_namespaced_custom_object_status(
            consts.GROUP, consts.VERSION, ns,
            consts.XDB_USER_PLURAL, name, body=patch))

    @classmethod
    def read(cls, ns: str, name: str) -> 'XDBMySQLUser':
        """
            读取一个 MySQL 用户的配置信息。
        参数：
            ns (str) - 命名空间，默认为 "default"。
            name (str) - 用户名。
        返回值（XDBMySQLUser）- 包含 MySQL 用户配置信息的对象实例。如果找不到该用户，则会引发 KeyError 异常。
        """
        return XDBMySQLUser(cls._get(ns, name))

    @property
    def metadata(self) -> dict:
        """
            返回元数据字典，包括名称、标签和注释等信息。
        
        返回值类型：dict，字典格式如下：
            {
                "name": str,  # 模型的名称
                "labels": list[str],  # 模型的标签列表，可能为空列表
                "comment": str  # 模型的注释信息，可能为空字符串
            }
        
        Returns:
            dict - 元数据字典，包括名称、标签和注释等信息。
        """
        return self.obj["metadata"]

    @property
    def annotations(self) -> dict:
        """
            返回当前对象的注解字典，包括所有已经存在的注解。如果没有注解，则返回一个空的字典。
        注解是键值对形式的，其中键是注解名称，值是注解的值。
        
        Returns:
            dict (dict): 注解字典，包括所有已经存在的注解。如果没有注解，则返回一个空的字典。
        """
        return self.metadata["annotations"]

    @property
    def delete_policy(self):
        """
            获取资源删除策略，默认为"retain"（保留）。如果设置为"delete"（删除），则在删除CR时会自动删除相关的K8S资源。
        返回值：str {"retain" | "delete"}，默认为"retain"。
        """
        return self.annotations.get("xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy", "retain")

    @property
    def spec(self) -> dict:
        """
            返回当前对象的规格信息，包括名称、标签和其他特性。
        
        返回值（dict）：
            - name (str, optional): 资源的名称，默认为 None。
            - labels (dict, optional): 资源的标签，默认为 None。
            - other_attrs (dict, optional): 其他特性，默认为 None。
        
        Returns:
            dict (dict): 规格信息，包括名称、标签和其他特性。
        """
        return self.obj["spec"]

    @property
    def status(self) -> dict:
        """
            获取当前对象的状态信息，包括资源使用情况、错误信息等。如果对象没有状态信息，则返回一个空字典。
        返回值是一个字典，其中包含以下键值对：
            - "used_resources" (dict, optional): 当前对象正在使用的资源，包括CPU、GPU和内存等。默认为None。
            - "error_message" (str, optional): 如果对象出现了错误，则此处显示错误信息；否则为None。默认为None。
            例如：{"used_resources": {"cpu": 10, "gpu": 2}, "error_message": "Out of memory"}
        
        Returns:
            dict: 当前对象的状态信息，包括资源使用情况和错误信息。如果对象没有状态信息，则返回一个空字典。
        """
        if "status" in self.obj:
            return self.obj["status"]
        return {}

    @property
    def name(self) -> str:
        """
            返回模型的名称，即metadata中的"name"字段。
        
        Returns:
            str (str): 模型的名称。
        """
        return self.metadata["name"]

    @property
    def namespace(self) -> str:
        """
            返回当前对象的命名空间。
        命名空间是一个字符串，用于标识Kubernetes对象在集群中的唯一性。
        
        返回值：str（字符串）
            - 返回当前对象的命名空间。如果对象没有命名空间，则返回空字符串。
        """
        return self.metadata["namespace"]

    @property
    def xdb_cluster_name(self) -> str:
        """
            返回xdb集群名称，如果未指定则为空字符串。
        
        Returns:
            str (str): XDB集群名称，如果未指定则为空字符串。
        """
        return self.parsed_spec.clusterRef.name

    @property
    def xdb_cluster_ns(self) -> str:
        """
            获取XDB集群的命名空间，如果没有指定则返回None。
        
        Returns:
            str, optional - 返回XDB集群的命名空间，如果没有指定则返回None。
        """
        return self.parsed_spec.clusterRef.namespace

    @property
    def deleting(self) -> bool:
        """
            返回当前资源是否处于删除状态，如果有deletionTimestamp属性且不为None则返回True。
        否则返回False。
        
        Returns:
            bool - True（处于删除状态）或 False（未处于删除状态）
        """
        return "deletionTimestamp" in self.metadata and self.metadata["deletionTimestamp"] is not None

    @property
    def user_name(self) -> str:
        """
            返回用户名。
        
        返回值类型：str
        
        Returns:
            str - 用户名字符串，如果未指定则为空字符串。
        """
        return self.parsed_spec.user

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        """
            返回一个字典，包含当前对象的apiVersion、kind、name、namespace、resourceVersion和uid等信息。如果提供了field_path参数，则将其添加到字典中。
        
        Args:
            field_path (Optional[str], optional): 可选参数，字段路径，默认为None。默认不使用字段路径。 Defaults to None.
        
        Returns:
            dict: 一个字典，包含当前对象的apiVersion、kind、name、namespace、resourceVersion和uid等信息，如果提供了field_path参数，则将其添加到字典中。
        """
        ref = {
            "apiVersion": consts.API_VERSION,
            "kind": consts.XDB_USER_KIND,
            "name": self.name,
            "namespace": self.namespace,
            "resourceVersion": self.metadata["resourceVersion"],
            "uid": self.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    @property
    def uid(self) -> str:
        """
            返回实例的唯一标识符。该值是在创建实例时生成的，并且不会更改。
        
        返回值：str（字符串） - 实例的唯一标识符
        
        """
        return self.metadata["uid"]

    @property
    def parsed_spec(self) -> XDBMysqlUserSpec:
        """
            获取解析后的用户规格，如果未解析则先进行解析。
        返回值为XDBMysqlUserSpec类型，表示解析后的用户规格。
        
        Returns:
            XDBMysqlUserSpec (XDBMysqlUserSpec): 解析后的用户规格。
        """
        if not self._parsed_spec:
            self.parse_spec()
            assert self._parsed_spec

        return self._parsed_spec

    def get_xdbcluster_stateful_set(self) -> typing.Optional[api_client.V1StatefulSet]:
        """
        获取XDBCluster的StatefulSet对象，优先尝试获取传统的StatefulSet，如果不存在则尝试获取Kruise StatefulSet。
            如果发生API异常并且状态码为404，则返回None；否则抛出异常。
        
            Args:
                self (XDBCluster): XDBCluster实例。
        
            Returns:
                Optional[api_client.V1StatefulSet]: StatefulSet对象，如果不存在则返回None。
                    API异常时，如果状态码为404，则返回None；其他情况抛出异常。
        """
        try:
            # 首先尝试获取传统的 StatefulSet
            return cast(api_client.V1StatefulSet,
                        api_apps.read_namespaced_stateful_set(self.xdb_cluster_name,
                                                              self.xdb_cluster_ns))
        except ApiException as e:
            if e.status == 404:
                try:
                    # 如果传统的 StatefulSet 不存在，尝试获取 Kruise StatefulSet
                    kruise_sts = api_kruise.get_namespaced_custom_object(
                        group=consts.KRUISE_GROUP,
                        version=consts.KRUISE_VERSION,
                        namespace=self.xdb_cluster_ns,
                        plural=consts.KRUISE_STATEFULSET_PLURAL,
                        name=self.xdb_cluster_name
                    )
                    return cast(api_client.V1StatefulSet, kruise_sts)
                except ApiException as e2:
                    if e2.status == 404:
                        return None
                    raise e2
            raise e

    @classmethod
    def read_xdbcluster_body(cls, ns: str, name: str) -> Body:
        """
        读取指定命名空间下的 XDBCluster 对象的 body 部分。
        
        Args:
            ns (str): 命名空间名称。
            name (str): XDBCluster 对象名称。
        
        Returns:
            Body (typing.Any): XDBCluster 对象的 body 部分，类型为 Any，需要根据具体情况进行类型转换。
            如果读取失败，将抛出 ApiException 异常。
        
        Raises:
            None.
        """
        try:
            ret = cast(Body, api_customobj.get_namespaced_custom_object(
                consts.GROUP, consts.VERSION, ns,
                consts.XDBFUSIONCLUSTER_PLURAL, name))
        except ApiException as e:
            raise e

        return ret

    def parse_spec(self) -> None:
        """
            解析资源的 spec，并将其转换为 XDBMysqlUserSpec 对象。
        如果已经解析过，则不再重复解析。
        
        Args:
            None
        
        Returns:
            None: 无返回值，直接修改类实例的 _parsed_spec 属性。
        """
        self._parsed_spec = XDBMysqlUserSpec(self.namespace, self.name, self.spec)

    def reload(self) -> None:
        """
            重新加载对象，并更新内部状态。如果对象不存在，则抛出异常。
        返回值：None，无返回值。
        """
        self.obj = self._get(self.namespace, self.name)

    def get_user_password(self) -> str:
        """
            获取用户密码，返回一个字符串类型的值。
        该函数会读取密码字段中指定的Kubernetes Secret，并解码其中的密码数据，然后返回解码后的密码。
        
        Args:
            None
        
        Returns:
            str (str): 解码后的用户密码。
        
        Raises:
            None
        """
        secret = cast(api_client.V1Secret, api_core.read_namespaced_secret(
            f"{self._parsed_spec.password.secretName}", self.namespace))
        return utils.b64decode(secret.data["password"])

    def _get_status_field(self, field: str) -> typing.Any:
        """
            获取状态字段的值，如果不存在则返回None。
        参数field (str) - 需要获取的状态字段名称，例如"state"或"message"等。
        返回值 (typing.Any) - 状态字段对应的值，如果不存在则返回None。
        """
        return cast(str, self.status.get(field))

    def _set_status_field(self, field: str, value: typing.Any) -> None:
        """
            设置资源的状态字段值。如果资源没有状态字段，则创建一个新的状态字段。
        该方法会修改当前对象的状态字段值，并返回None。
        
        Args:
            field (str): 需要设置的状态字段名称。
            value (typing.Any): 需要设置的状态字段值，可以是任何类型。
        
        Returns:
            None: 无返回值，直接修改当前对象的状态字段值。
        """
        obj = self._get(self.namespace, self.name)

        if "status" not in obj:
            patch = {"status": {}}
        else:
            patch = {"status": obj["status"]}
        patch["status"][field] = value
        self.obj = self._patch_status(self.namespace, self.name, patch)

    def set_xdbuser_status(self, cluster_status) -> None:
        """
            设置XDB用户的状态。
        
        Args:
            cluster_status (str): XDB用户的状态，可以是"online"或"offline"。
        
        Returns:
            None: 无返回值，直接修改了对象的属性。
        """
        self._set_status_field("user", cluster_status)

    def get_xdbuser_status(self, field=None):
        """
              Get the status of xdbuser. If field is specified, only that field will be returned.
          Args:
              field (str, optional): Field to retrieve from the status dictionary. Defaults to None.

          Returns:
              Union[dict, Any]: Dictionary containing all fields or a single field if field is specified.
              If field is not specified, returns the entire status dictionary.
          """
        status = self._get_status_field("user")
        if status and field:
            return status.get(field)
        return status

    def set_status(self, status) -> None:
        """
            设置资源的状态，如果不存在则创建，否则进行合并更新。
        
        Args:
            status (dict): 需要设置的状态信息，格式为字典类型。
        
        Returns:
            None: 无返回值，直接修改对象的状态。
        """
        obj = cast(dict, self._get(self.namespace, self.name))

        if "status" not in obj:
            obj["status"] = status
        else:
            utils.merge_patch_object(obj["status"], status)
        self.obj = self._patch_status(self.namespace, self.name, obj)

    def update_xdbuser_info(self, info: dict) -> None:
        """
        Set metadata about the cluster as an annotation.
        Information consumed by ourselves to manage the cluster should go here.
        Information consumed by external observers should go in status.
        """
        patch = {
            "metadata": {
                "annotations": {
                    "cloudbed.abcstack.com/xdbuser-info": json.dumps(info)
                }
            }
        }
        self.obj = self._patch(self.namespace, self.name, patch)

    # TODO remove field
    def get_xdbuser_info(self, field: typing.Optional[str] = None) -> typing.Optional[dict]:
        """
            获取 XDBUser 的信息，可以指定字段来获取对应的值。如果没有指定字段，则返回整个信息字典。
        默认情况下，XDBUser 的信息会被存放在注解中，格式为 JSON 字符串，键名为 "cloudbed.abcstack.com/xdbuser-info"。
        
        Args:
            field (str, optional): 要获取的字段名，默认为 None，表示获取所有信息。Defaults to None.
        
        Returns:
            typing.Optional[dict]: 如果指定了字段，则返回该字段的值；否则返回整个信息字典，如果不存在则返回 None。Defaults to None.
        """
        if self.annotations:
            info = self.annotations.get("cloudbed.abcstack.com/xdbuser-info", None)
            if info:
                info = json.loads(info)
                if field:
                    return info.get(field)
                return info
        return None

    def set_create_time(self, time: datetime.datetime = None) -> None:
        """
            设置创建时间，默认为当前时间。
        如果未指定时间，则使用当前时间。
        
        Args:
            time (datetime.datetime, optional): 创建时间，默认为None，即使用当前时间。 Default: None
        
        Returns:
            None: 无返回值
        """
        if not time:
            time = datetime.datetime.now()
        self._set_status_field("createTime", time.replace(
            microsecond=0).isoformat() + "Z")

    def get_create_time(self) -> Optional[datetime.datetime]:
        """
            获取创建时间，返回值为datetime.datetime类型或None。如果不存在则返回None。
        
        Args:
            None
        
        Returns:
            Optional[datetime.datetime]: 创建时间，datetime.datetime类型；如果不存在则返回None。
        """
        dt = self._get_status_field("createTime")
        if dt:
            return datetime.datetime.fromisoformat(dt.rstrip("Z"))
        return None

    @property
    def ready(self) -> bool:
        """
            判断当前对象是否已经准备好，可以进行操作。
        如果对象还未创建或初始化完成，则返回 False；否则返回 True。
        
        Returns:
            bool (bool): 若对象已准备好，返回 True；否则返回 False。
        """
        return cast(bool, self.get_create_time())

    @property
    def xdbcluster_ready(self) -> bool:
        """
            判断 XDBCluster StatefulSet 是否准备就绪，返回布尔值。
        如果 StatefulSet 不存在或者没有就绪的副本，则返回 False。
        
        Returns:
            bool (bool): True 表示 XDBCluster StatefulSet 已就绪，False 表示未就绪。
        """
        sts = self.get_xdbcluster_stateful_set()
        if not sts:
            logger.warning(f"StatefulSet for cluster {self.name} not found, assuming not ready.")
            return False

        if isinstance(sts, api_client.V1StatefulSet):
            if not sts.status:
                logger.info(f"StatefulSet {self.name} has no status field yet.")
                return False
            # ready_replicas is optional, defaults to 0 if not present.
            ready_replicas = sts.status.ready_replicas if sts.status.ready_replicas is not None else 0
            return ready_replicas == sts.status.replicas
        elif isinstance(sts, dict):
            status = sts.get("status")
            if not status:
                logger.info(f"Kruise StatefulSet {self.name} has no status field yet. sts={sts}")
                return False

            replicas = status.get("replicas")
            if replicas is None:
                logger.info(f"Kruise StatefulSet {self.name} status has no 'replicas' field. status={status}")
                return False
            # readyReplicas might not be present if there are no ready replicas, so default to 0.
            ready_replicas = status.get("readyReplicas", 0)
            return ready_replicas == replicas
        else:
            logger.error(f"Invalid sts type for cluster {self.name}: {type(sts)}")
            raise ValueError(f"Invalid sts type: {type(sts)}")
        

    def set_last_known_quorum(self, members):
        """
            设置最后一次知道的仲裁成员。
        该函数暂时未实现，请稍后更新。
        
        Args:
            members (list[str]): 仲裁成员列表，包含每个成员的IP地址和端口号。
        
        Returns:
            None.
        
        Raises:
            None.
        """
        # TODO
        pass

    def get_last_known_quorum(self):
        """
            获取最后一次知道的领导者投票结果，如果没有则返回None。
        该方法目前还未实现，请勿使用。
        
        Returns:
            Optional[int]: 最后一次知道的领导者投票结果，如果没有则返回None。
        """
        # TODO
        return None

    def _add_finalizer(self, fin: str) -> None:
        """
        Add the named token to the list of finalizers for the cluster object.
        The cluster object will be blocked from deletion until that token is
        removed from the list (remove_finalizer).
        """
        patch = {
            "metadata": {
                "finalizers": [fin]
            }
        }
        self.obj = self._patch(self.namespace, self.name, patch)

    def set_operator_version(self, version: str) -> None:
        """
            设置操作员版本号，如果不同则更新对象。
        参数：
            version (str) - 操作员版本号，必须是字符串类型。
        返回值：
            None，无返回值。
        """
        v = self.operator_version
        if v != version:
            patch = {"metadata": {"annotations": {"cloudbed.abcstack.com/xdb-operator-version": version}}}
            # TODO store the current server/router version + timestamp
            self.obj = self._patch(self.namespace, self.name, patch)

    @property
    def operator_version(self) -> Optional[str]:
        """
            获取操作符版本号，如果不存在则返回None。
        该属性只有在运行时才能使用，因为它需要访问到运行时的元数据信息。
        
        Args:
            None
        
        Returns:
            Optional[str]: 操作符版本号字符串，如果不存在则返回None。
        """
        return self.metadata.get("cloudbed.abcstack.com/xdb-operator-version")

    def set_current_version(self, version: str) -> None:
        """
            设置当前版本号，如果不同则更新状态字段。
        
        Args:
            version (str): 要设置的版本号。
        
        Returns:
            None: 无返回值，直接修改对象状态字段。
        """
        v = self.status.get("version")
        if v != version:
            patch = {"status": {"version": version}}

            # TODO store the current server/router version + timestamp
            # store previous versions in a version history log
            self.obj = self._patch_status(self.namespace, self.name, patch)

    def get_cluster(self) -> typing.Optional[XDBCluster]:
        """
        获取集群信息，如果不存在则返回None。
        
            Args:
                self (XDBJob): XDBJob实例化对象。
        
            Returns:
                typing.Optional[XDBCluster]: XDBCluster类型的对象，包含集群信息；如果不存在则返回None。
        
            Raises:
                ApiException: 当获取集群信息失败时，抛出ApiException异常，状态码为404表示集群不存在。
        """
        try:
            return XDBCluster.read(self.parsed_spec.clusterRef.namespace,
                                   self.parsed_spec.clusterRef.name)
        except ApiException as e:
            print(
                f"Could not get cluster {self.parsed_spec.clusterRef.namespace}/" +
                f"{self.parsed_spec.clusterRef.name}: {e}")
            if e.status == 404:
                return None
            raise

    def get_user(self) -> typing.Optional['XDBMySQLUser']:
        """
        获取用户信息，如果不存在则返回None。如果出现异常，除了404错误外，将抛出ApiException异常。
        
            Args:
                None
        
            Returns:
                Optional[XDBMySQLUser]: XDBMySQLUser类型的对象，包含用户信息；如果不存在则返回None。
        
            Raises:
                ApiException: 如果不是404错误，将抛出ApiException异常。
        """
        try:
            return XDBMySQLUser.read(self.namespace, self.name)
        except ApiException as e:
            print(f"Could not get user {self.namespace}/{self.name}: {e}")
            if e.status == 404:
                return None
            raise

    def log_user_info(self, logger: Logger) -> None:
        """
            将用户信息记录到日志中，包括名称、密码、集群引用和权限。
        
        Args:
            logger (Logger): 一个 Logger 对象，用于记录日志。
        
        Returns:
            None: 不返回任何值，直接修改传入的 logger 对象。
        """
        logger.info(f"XDBUser CRD {self.namespace}/{self.name} annotations({self.annotations}) ")
        logger.info(f"\tuser name:\t{self.parsed_spec.user}")
        logger.info(f"\tpassword :\t{self.parsed_spec.password}")
        logger.info(f"\tclusterRef:\t{self.parsed_spec.clusterRef}")
        logger.info(f"\tpermissions:\t{self.parsed_spec.permissions}")
