#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   config.py
@Time    :   2024/6/22 19:25
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   l<PERSON><PERSON><PERSON><PERSON>@baidu.com
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import os

from xdboperator.controller.api_utils import Edition, ImagePullPolicy
from xdboperator.controller.kubeutils import k8s_version

debug = False
# enable_mysqld_general_log = False

_pull_policy = os.getenv("XDB_OPERATOR_IMAGE_PULL_POLICY")
if _pull_policy:
    default_image_pull_policy = ImagePullPolicy[_pull_policy]
else:
    default_image_pull_policy = ImagePullPolicy.Always

# Constants
OPERATOR_VERSION = "1.0"
OPERATOR_EDITION = Edition.community
OPERATOR_EDITION_NAME_TO_ENUM = {edition.value: edition.name for edition in Edition}

MIN_BASE_SERVER_ID = 1
MAX_BASE_SERVER_ID = **********

# This is used for the sidecar. The operator version is deploy-operator.yaml
DEFAULT_OPERATOR_VERSION_TAG = os.getenv("DEFAULT_OPERATOR_VERSION_TAG", default="")
DEFAULT_PROXY_VERSION_TAG = os.getenv("DEFAULT_PROXY_VERSION_TAG", default="*******")
DEFAULT_MYSQL_VERSION_TAG = os.getenv("DEFAULT_MYSQL_VERSION_TAG", default="*******")
DEFAULT_IMAGE_REPOSITORY = os.getenv(
    "XDB_OPERATOR_DEFAULT_REGISTRY", default="iregistry.baidu-int.com/abc-stack").rstrip('/')
# retain or delete  When the xdbUser database is deleted,
# the mysql resource will be preserved
RESOURCE_DELETE_POLICY = os.getenv("RESOURCE_DELETE_POLICY", default="retain")
GLOBAL_RESOURCE_DELETE_POLICY = os.getenv("GLOBAL_RESOURCE_DELETE_POLICY", default="retain")

XDB_MYSQL_PORT = 3203
XDB_PROXY_PORT = 6203

XDB_MYSQL_SERVER_IMAGE = "xdb-mysql"
XDB_MYSQL_PROXY_IMAGE = "xdb-proxy"
XDB_MYSQL_ZM_IMAGE = "xdb-zm"

BOOTSTRAP_TIMEOUT: int = 3600 * 24
BOOTSTRAP_RETRIES: int = 5
BOOTSTRAP_RETRY_DELAY: int = 60
TIMER_INTERVAL: int = 10


def log_config_banner(logger) -> None:
    """
    打印配置信息的标题，包括Kubernetes版本、Operator版本、Operator版本类型和默认镜像仓库。
    
    Args:
        logger (logging.Logger): Logger对象，用于记录日志信息。
    
    Returns:
        None, 无返回值。
    """
    logger.info(f"KUBERNETES_VERSION = {k8s_version()}")
    logger.info(f"OPERATOR_VERSION   = {OPERATOR_VERSION}")
    logger.info(f"OPERATOR_EDITION   = {OPERATOR_EDITION.value}")
    logger.info(f"OPERATOR_EDITIONS  = {list(OPERATOR_EDITION_NAME_TO_ENUM)}")
    logger.info(f"SIDECAR_VERSION_TAG = {DEFAULT_OPERATOR_VERSION_TAG}")
    logger.info(f"DEFAULT_IMAGE_REPOSITORY = {DEFAULT_IMAGE_REPOSITORY}")


def config_from_env() -> None:
    """
    从环境变量中读取配置信息，并更新全局变量。
    
    Args:
        None
    
    Returns:
        None
        globally updates the following variables:
            debug (bool): 是否开启调试模式，默认为 False。
            enable_mysqld_general_log (bool): 是否启用 MySQL 的 general log，默认为 True。
            default_image_pull_policy (str): Kubernetes Pod 的镜像拉取策略，默认为 "IfNotPresent"。
            level (int, optional): 日志等级，默认为 INFO。可选值包括 CRITICAL、ERROR、WARNING、INFO 和 DEBUG。
    """
    global debug
    #    global enable_mysqld_general_log
    global default_image_pull_policy
    level = os.getenv("XDB_OPERATOR_DEBUG")
