#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   operato_backup.py
@Time    :   2024/9/3 15:34
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   lixian<PERSON><PERSON>@baidu.com
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""
import kopf
import logging
import traceback
from apscheduler.schedulers.background import BackgroundScheduler
from kopf._cogs.structs.bodies import Body
from logging import Logger
from typing import Optional

from xdboperator.controller import consts, diagnose
from xdboperator.controller import utils, config
from xdboperator.controller.api_utils import ApiSpecError, ignore_404
from xdboperator.controller.backup.backup_api import XDBBackup, XDBBackupStrategy
from xdboperator.controller.backup.backup_controller import XDBBackupController, process_backup_daemon
from xdboperator.controller.backup.backup_controller import XDBClusterBackupMutex, XDBBackupStrategyController
from xdboperator.controller.kubeutils import k8s_version


@kopf.on.create(consts.GROUP, consts.VERSION, consts.XDB_BACKUP_PLURAL, timeout=config.BOOTSTRAP_TIMEOUT,
                retries=config.BOOTSTRAP_RETRIES, backoff=config.BOOTSTRAP_RETRY_DELAY)
def on_backup_create(name: str, namespace: Optional[str], body: Body,
                     logger: Logger, **kwargs) -> None:
    """
    创建XDB备份CR，创建备份快照
    """
    logger.info(f"Create XDB Backup name={name} namespace={namespace} on K8s {k8s_version()}")
    backup = XDBBackup(body)
    backup.set_status({
        "snapshot": {
            "status": diagnose.ClusterDiagStatus.CREATING.value,
            "lastProbeTime": utils.isotime()
        }})
    try:
        backup.parse_spec()
        backup.parsed_spec.validate(logger)
    except ApiSpecError as e:
        backup.set_status({
            "snapshot": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }})
        backup.error(action="CreateXDBBackup", reason="InvalidArgument", message=str(e))
        raise kopf.PermanentError(f"Error in XDBBackup spec: {e}")
    backup.log_backup_info(logger)
    if not backup.xdbcluster_ready:
        backup.set_status({
            "snapshot": {
                "status": diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }})
        backup.error(action="CreateXDBBackup", reason="XDBClusterNotReady",
                     message="xdb cluster is not ready")
        raise kopf.TemporaryError("xdb cluster is not ready", delay=15)
    if backup.ready:
        backup.warn(action="CreateXDBBackup", reason="ResourceExists",
                    message="Database CR is exists, but create requested again")
        return
    
    with XDBClusterBackupMutex(backup.get_cluster(), backup):
        logger.info(f"Start to create snapshot for xdb cluster")
        ctl = XDBBackupController(backup)
        ctl.create_xdb_backup_snapshot(logger=logger)


@kopf.on.delete(consts.GROUP, consts.VERSION, consts.XDB_BACKUP_PLURAL, timeout=config.BOOTSTRAP_TIMEOUT,
                retries=config.BOOTSTRAP_RETRIES, backoff=config.BOOTSTRAP_RETRY_DELAY)
def on_backup_delete(name: str, namespace: Optional[str], body: Body,
                     logger: Logger, **kwargs) -> None:
    """
    处理备份CR的删除，本地删除 或者后期删除s3远端的记录
    """
    logger.info(f"Create XDB Backup name={name} namespace={namespace} on K8s {k8s_version()}")
    backup = XDBBackup(body)
    if backup.get_backup_status("status") ==  diagnose.BackupStatus.AVAILABLE.value:
        backup.set_status({
            "snapshot": {
                "status": diagnose.BackupStatus.TERMINATE.value,
            }})
    ctl = XDBBackupController(backup)
    ctl.delete_backup(logger=logger)


@kopf.timer(consts.GROUP, consts.VERSION, consts.XDB_BACKUP_STRATEGY_PLURAL, interval=consts.HOURS * 2,
    initial_delay=consts.HOURS * 4,
    retries=config.BOOTSTRAP_RETRIES,
    backoff=config.BOOTSTRAP_RETRY_DELAY
)
def on_timer_duration_backup_delete(name: str, namespace: Optional[str], body: Body, logger: Logger, **kwargs) -> None:
    """
        定时检查过期时间的备份,删除过期的备份
    """
    logger.info(f"start del duration_backup name={name} namespace={namespace} on K8s {k8s_version()}")
    backup = XDBBackupStrategy(body)
    ctl = XDBBackupStrategyController(backup)
    ctl.delete_expire_duration_backup(logger)



@kopf.on.create(consts.GROUP, consts.VERSION, consts.XDB_BACKUP_STRATEGY_PLURAL, backoff=config.BOOTSTRAP_RETRY_DELAY)
def on_backup_strategy_create(name: str, namespace: Optional[str], body: Body,
                              logger: Logger, **kwargs) -> None:
    """
    创建备份策略的CR
    """
    logger.info(f"Create XDB Backup strategy name={name} namespace={namespace} on K8s {k8s_version()}")
    backup = XDBBackupStrategy(body)
    backup.set_status({
        "strategy": {
            "status": diagnose.ClusterDiagStatus.CREATING.value,
            "lastProbeTime": utils.isotime()
        }})
    try:
        backup.parse_spec()
        backup.parsed_spec.validate(logger)
    except ApiSpecError as e:
        backup.set_status({
            "strategy:": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }})
        backup.error(action="CreateXDBBackupStrategy", reason="InvalidArgument", message=str(e))
        raise kopf.PermanentError(f"Error in XDBBackup spec: {e}")
    backup.log_backup_strategy_info(logger)
    if not backup.xdbcluster_ready:
        backup.set_status({
            "strategy:": {
                "status": diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value,
                "ready": False,
                "lastProbeTime": utils.isotime()
            }})
        backup.error(action="CreateXDBBackupStrategy", reason="XDBClusterNotReady",
                     message="xdb cluster is not ready")
        raise kopf.TemporaryError("xdb cluster is not ready", delay=15)
    if backup.ready:
        backup.warn(action="CreateXDBBackupStrategy", reason="ResourceExists",
                    message="Database CR is exists, but create requested again")
        return

    backup.set_status({
        "strategy": {
            "status": diagnose.BackupStatus.AVAILABLE.value,
        }})


# @kopf.on.create(consts.GROUP, consts.VERSION, consts.XDB_BACKUP_STRATEGY_PLURAL,   timeout=config.BOOTSTRAP_TIMEOUT,
#                 retries=config.BOOTSTRAP_RETRIES,backoff=config.BOOTSTRAP_RETRY_DELAY)
# def on_backup_strategy_delete(name: str, namespace: Optional[str], body: Body,
#                          logger: Logger, **kwargs) -> None:

#     pass


# @kopf.on.field(consts.GROUP, consts.VERSION, consts.XDB_BACKUP_STRATEGY_PLURAL,field="spec.Schedule")  # type: ignore
# def on_backup_strategy_field_instances(old, new, body: Body, logger: Logger, **kwargs):
#     pass


@kopf.daemon(consts.GROUP, consts.VERSION, consts.XDB_BACKUP_STRATEGY_PLURAL,
             backoff=config.BOOTSTRAP_RETRY_DELAY, initial_delay=30)
def on_backup_strategy_daemon(name: str, namespace: Optional[str], body: Body,
                              logger: Logger, stopped: kopf.DaemonStopped, **kwargs) -> None:
    """
    CRD 绑定的常驻守护进程
    """
    logger.info(f"XDB Backup strategy daemon name={name} namespace={namespace} on K8s {k8s_version()}")
    strategy = XDBBackupStrategy(body)
    # strategy = XDBBackupStrategyController(backup)
    # strategy.backup_daemon(stopped, logger)


    scheduler = BackgroundScheduler()
    job_defaults = {'coalesce': False, 'max_instances': 1}
    scheduler.configure(job_defaults)
    scheduler.start()
    try:
        while not stopped:
            process_backup_daemon(strategy=strategy, scheduler=scheduler, body=body, logger=logger)
            stopped.wait(60)
    except (ValueError, Exception):
        traceback.print_exc()
        traceback.format_exc()
    finally:
        logger.warning(
            f"backup_daemon with name: {strategy.metadata['name']}," + \
                f"namespace: {strategy.metadata['namespace']}, spec: {strategy.spec}")
        scheduler.remove_all_jobs()
        scheduler.shutdown()
        raise Exception("backup_daemon completed.")