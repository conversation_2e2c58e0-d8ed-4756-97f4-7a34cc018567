#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   backup_api.py
@Time    :   2024/9/16 08:29
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   l<PERSON>ian<PERSON><PERSON>@baidu.com
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""
import datetime
import typing
import logging

from enum import Enum
from kopf._cogs.structs.bodies import Body
from logging import Logger
from typing import Optional, cast

from xdboperator.controller import utils, consts, config
from xdboperator.controller.api_utils import dget_dict, dget_str
from xdboperator.controller.cluster.xdbcluster_api import AbstractServerSetSpec
from xdboperator.controller.cluster.xdbcluster_api import XDBCluster, XDBPod
from xdboperator.controller.k8sobject import K8sInterfaceObject
from xdboperator.controller.kubeutils import api_apps, api_customobj, api_kruise
from xdboperator.controller.kubeutils import client as api_client, ApiException, api_core



logger = logging.getLogger(__name__)

class BackupStrategySecretSpec(object):
    secretName: Optional[str] = None


    def parse(self, spec: dict, prefix: str) -> None:
        """
            解析字典，获取 secretName 和 key 的值，并赋值给对应属性。
        如果字典中不包含这两个键，则会将对应属性设置为 None。
        
        Args:
            spec (dict): 一个字典，包含了需要解析的信息。可能包含以下键：
                - secretName (str, optional): 存放秘密名称的键，默认值为 None。
                - key (str, optional): 存放秘密的键，默认值为 None。
            prefix (str, optional): 前缀，默认值为 "".
        
        Returns:
            None: 该函数没有返回值。
        """
        if "secretName" in spec:
            self.secretName = dget_str(spec, "secretName", prefix)

    
    def get_access_token(self, ns: str) -> str:
        """
            获取指定命名空间下的ak
        """
        secret = cast(api_client.V1Secret, api_core.read_namespaced_secret(
            self.secretName, ns))

        return utils.b64decode(secret.data["accessKey"])

    def get_secret_token(self, ns: str) -> str:
        """
            获取指定命名空间下的sk
        """
        secret = cast(api_client.V1Secret, api_core.read_namespaced_secret(
            self.secretName, ns))
        return utils.b64decode(secret.data["secretKey"])
    

    def __str__(self):
        """
            返回一个字符串，包含 secretName 和 key。
        这个方法被 str() 函数调用，以便将 SecretKey 对象转换为字符串。
        
        Returns:
            str -- 包含 secretName 和 key 的字符串。例如：'my-secret/my-key'。
        """
        return f"{self.secretName}/{self.key}"

    def __repr__(self):
        """
            Return a string representation of the MysqlSecretUserSpec object.
        
        Returns:
            str: A string in the format "<MysqlSecretUserSpec secretName>".
        """
        return f"<MysqlSecretUserSpec {self.secretName}>"



class XDBBackupSpec(AbstractServerSetSpec):
    """
    xdb backup spec
    """
    remoteDeletePolicy: str = ""
    clusterName: str = ""
    clusterNamespace: str = ""
    backupStrategyName: str = ""
    backupStrategyNamespace: str = ""

    def __init__(self, namespace: str, name: str, spec: dict):
        super().__init__(namespace, name, name, spec)
        self.load(spec)

    def load(self, spec: dict) -> None:
        """
        解析配置 加载参数到对象中
        """
        self._load(spec, spec, "spec")
        self.clusterName = dget_str(spec, "clusterName", "spec")
        self.clusterNamespace = dget_str(spec, "clusterNamespace", "spec")
        self.backupStrategyName = dget_str(spec, "backupStrategyName", "spec")
        self.backupStrategyNamespace = dget_str(spec, "backupStrategyNamespace", "spec")
        if "remoteDeletePolicy" in spec:
            self.remoteDeletePolicy = dget_str(spec, "remoteDeletePolicy", "spec")

    def __str__(self):
        return f"{self.database}/{self.clusterRef}"

    def __repr__(self):
        return f"<XDBBackupSpec {self.name}>"

    def validate(self, logger: Logger) -> None:
        """
        验证spec是否合法
        """
        # TODO see if we can move some of these to a schema in the CRD
        pass
    
    
    @property
    def mysql_image(self) -> str:
        """
        mysql_image 属性是用来获取MySQL镜像的，具体实现方式是通过调用format_image方法来完成的。
        Returns:
            str: _description_
        """
        if self.version:
            version = self.version
        else:
            version = config.DEFAULT_MYSQL_VERSION_TAG
        image = config.XDB_MYSQL_SERVER_IMAGE   
        return self.format_image(image, version)

    @property
    def mysql_image_pull_policy(self) -> str:
        """
        mysql_image_pull_policy 用来获取MySQL镜像拉取策略
        Returns:
            str: _description_
        """
        return self.imagePullPolicy.value
    
    def format_image(self, image, version):
        """
        format_image 函数用于格式化镜像名称，包括镜像仓库和镜像版本。
        Args:
            image (_type_): _description_
            version (_type_): _description_

        Returns:
            _type_: _description_
        """
        if self.imageRepository:
            return f"{self.imageRepository}/{image}:{version}"
        return f"{image}:{version}"


class XDBBackupStrategySpec(AbstractServerSetSpec):
    """
        xdb backup strategy spec
    """
    StorageType: str = ""
    SecretName: str = ""
    expireDuration: str = "72h"
    Schedule: str = "0 1 * * wed,sat"
    clusterName: str = ""
    clusterNamespace: str = ""
    kind: str = ""

    def __init__(self, namespace: str, name: str, spec: dict):
        super().__init__(namespace, name, name, spec)
        self.load(spec)

    def load(self, spec: dict) -> None:
        """
        解析配置 加载参数到对象中
        """
        self._load(spec, spec, "spec")
        self.storageType = dget_str(spec, "storageType", "spec")
        self.expireDuration = dget_str(spec, "expireDuration", "spec")
        self.schedule = dget_str(spec, "schedule", "spec")
        self.clusterName = dget_str(spec, "clusterName", "spec")
        self.clusterNamespace = dget_str(spec, "clusterNamespace", "spec")
        self.kind = dget_str(spec, "kind", "spec")
        if "secretName" in spec:
            self.secretName = dget_str(spec, "secretName", "spec")


    def __str__(self):
        """
        对象的字符串表示形式
        """
        return f"{self.kind}/{self.StorageType}/{self.clusterName}"

    def __repr__(self):

        return f"<XDBBackupStrategySpec {self.name}>"

    def validate(self, logger: Logger) -> None:
        """
        增加自定义参数验证
        """
        # TODO see if we can move some of these to a schema in the CRD
        pass


class XDBBackup(K8sInterfaceObject):
    """
    备份的资源对象
    """

    def __init__(self, body: Body) -> None:
        super().__init__()
        self.obj: Body = body
        self._parsed_spec: Optional[XDBBackupSpec] = None

    def __str__(self):
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        return f"<XDBDatabase {self.name}>"

    @classmethod
    def _get(cls, ns: str, name: str) -> Body:
        try:
            ret = cast(Body, api_customobj.get_namespaced_custom_object(
                consts.GROUP, consts.VERSION, ns,
                consts.XDB_BACKUP_PLURAL, name))
        except ApiException as e:
            raise e
        return ret

    @classmethod
    def _patch(cls, ns: str, name: str, patch: dict) -> Body:
        return cast(Body, api_customobj.patch_namespaced_custom_object(
            consts.GROUP, consts.VERSION, ns,
            consts.XDB_BACKUP_PLURAL, name, body=patch))

    @classmethod
    def _patch_status(cls, ns: str, name: str, patch: dict) -> Body:
        return cast(Body, api_customobj.patch_namespaced_custom_object_status(
            consts.GROUP, consts.VERSION, ns,
            consts.XDB_BACKUP_PLURAL, name, body=patch))

    @classmethod
    def read(cls, ns: str, name: str) -> 'XDBBackup':
        """
        读取备份资源对象
        """
        return XDBBackup(cls._get(ns, name))

    @property
    def metadata(self) -> dict:
        """
        获取对象的metadata
        """
        return self.obj["metadata"]

    @property
    def annotations(self) -> dict:
        """
        获取对象的annotations
        """
        return self.metadata["annotations"]

    @property
    def spec(self) -> dict:
        """
        获取对象的spec
        """
        return self.obj["spec"]

    @property
    def status(self) -> dict:
        """
        获取对象的status
        """
        if "status" in self.obj:
            return self.obj["status"]
        return {}

    @property
    def name(self) -> str:
        """
        获取资源对象的名称
        """
        return self.metadata["name"]

    @property
    def namespace(self) -> str:
        """
        获取资源对象的命名空间
        """
        return self.metadata["namespace"]

    @property
    def xdb_cluster_name(self) -> str:
        """
        获取资源关联的XDBCluster的名称
        """
        return self._parsed_spec.clusterName

    @property
    def xdb_cluster_ns(self) -> str:
        """
        获取资源关联的XDBCluster的命名空间
        """
        return self.parsed_spec.clusterNamespace

    @property
    def deleting(self) -> bool:
        """
        获取资源对象是否正在被删除
        """
        return "deletionTimestamp" in self.metadata and self.metadata["deletionTimestamp"] is not None

    @property
    def delete_policy(self):
        """
        获取资源对象的删除策略
        """
        return self.annotations.get("xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy", "retain")

    @property
    def parsed_spec(self) -> XDBBackupSpec:
        """
        获取资源对象的解析后的spec
        """
        if not self._parsed_spec:
            self.parse_spec()
            assert self._parsed_spec
        return self._parsed_spec

    def parse_spec(self) -> None:
        """
        解析资源对象的spec
        """
        self._parsed_spec = XDBBackupSpec(self.namespace, self.name, self.spec)

    @property
    def ready(self) -> bool:
        """
        判断资源对象是否已经准备好
        """
        return cast(bool, self.get_create_time())

    def set_create_time(self, time: datetime.datetime = None) -> None:
        """
        设置资源对象的创建时间
        """
        if not time:
            time = datetime.datetime.now()
        self._set_status_field("createTime", time.replace(
            microsecond=0).isoformat() + "Z")

    def get_create_time(self) -> Optional[datetime.datetime]:
        """
        获取资源对象的创建时间
        """
        dt = self._get_status_field("createTime")
        if dt:
            return datetime.datetime.fromisoformat(dt.rstrip("Z"))
        return None

    def _get_status_field(self, field: str) -> typing.Any:
        """
        从状态字段中获取指定字段的值
        """
        return cast(str, self.status.get(field))

    @property
    def uid(self) -> str:
        """
        获取资源对象的UID
        """
        return self.metadata["uid"]

    @property
    def xdbcluster_ready(self) -> bool:
        """
        判断XDBCluster是否已经准备好
        """
        sts = self.get_xdbcluster_stateful_set()
        if not sts:
            logger.warning(f"StatefulSet for cluster {self.name} not found, assuming not ready.")
            return False

        if isinstance(sts, api_client.V1StatefulSet):
            if not sts.status:
                logger.info(f"StatefulSet {self.name} has no status field yet.")
                return False
            # ready_replicas is optional, defaults to 0 if not present.
            ready_replicas = sts.status.ready_replicas if sts.status.ready_replicas is not None else 0
            return ready_replicas == sts.status.replicas
        elif isinstance(sts, dict):
            status = sts.get("status")
            if not status:
                logger.info(f"Kruise StatefulSet {self.name} has no status field yet. sts={sts}")
                return False

            replicas = status.get("replicas")
            if replicas is None:
                logger.info(f"Kruise StatefulSet {self.name} status has no 'replicas' field. status={status}")
                return False

            # readyReplicas might not be present if there are no ready replicas, so default to 0.
            ready_replicas = status.get("readyReplicas", 0)
            
            return ready_replicas == replicas
        else:
            logger.error(f"Invalid sts type for cluster {self.name}: {type(sts)}")
            raise ValueError(f"Invalid sts type: {type(sts)}")
        

    def get_xdbcluster_stateful_set(self) -> typing.Optional[api_client.V1StatefulSet]:
        """
        获取XDBCluster的StatefulSet对象，优先尝试获取传统的StatefulSet，如果不存在则尝试获取Kruise StatefulSet。
            如果发生API异常并且状态码为404，则返回None；否则抛出异常。
        
            Args:
                self (XDBCluster): XDBCluster实例。
        
            Returns:
                Optional[api_client.V1StatefulSet]: StatefulSet对象，如果不存在则返回None。
                    API异常时，如果状态码为404，则返回None；其他情况抛出异常。
        """
        try:
            # 首先尝试获取传统的 StatefulSet
            return cast(api_client.V1StatefulSet,
                        api_apps.read_namespaced_stateful_set(self.xdb_cluster_name,
                                                              self.xdb_cluster_ns))
        except ApiException as e:
            if e.status == 404:
                try:
                    # 如果传统的 StatefulSet 不存在，尝试获取 Kruise StatefulSet
                    kruise_sts = api_kruise.get_namespaced_custom_object(
                        group=consts.KRUISE_GROUP,
                        version=consts.KRUISE_VERSION,
                        namespace=self.xdb_cluster_ns,
                        plural=consts.KRUISE_STATEFULSET_PLURAL,
                        name=self.xdb_cluster_name
                    )
                    return cast(api_client.V1StatefulSet, kruise_sts)
                except ApiException as e2:
                    if e2.status == 404:
                        return None
                    raise e2
            raise e

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        """
        获取XDBBackup资源的引用
        """
        ref = {
            "apiVersion": consts.API_VERSION,
            "kind": consts.XDB_BACKUP_PLURAL,
            "name": self.name,
            "namespace": self.namespace,
            "resourceVersion": self.metadata["resourceVersion"],
            "uid": self.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    def get_backup(self) -> typing.Optional['XDBBackup']:
        """
        获取XDBBackup资源
        """
        try:
            return XDBBackup.read(self.namespace, self.name)
        except ApiException as e:
            print(
                f"Could not get database {self.namespace}/{self.name}: {e}")
            if e.status == 404:
                return None
            raise
    
    
    def get_backup_strategy(self) -> typing.Optional['XDBBackupStrategy']:
        """
        获取关联备份策略资源对象
        """
        try:
            return XDBBackupStrategy.read(self.parsed_spec.backupStrategyNamespace, self.parsed_spec.backupStrategyName)
        except ApiException as e:
            print(
                f"Could not get backup strategy {self.parsed_spec.backupStrategyNamespace}/{self.parsed_spec.backupStrategyName}: {e}")
            if e.status == 404:
                return None
            raise

    def get_cluster(self) -> typing.Optional[XDBCluster]:
        """
        获取XDBCluster资源
        """
        try:
            return XDBCluster.read(self.xdb_cluster_ns,
                                   self.xdb_cluster_name)
        except ApiException as e:
            print(
                f"Could not get cluster {self.xdb_cluster_ns}/{self.xdb_cluster_name}: {e}")
            if e.status == 404:
                return None
            raise

    def _set_status_field(self, field: str, value: typing.Any) -> None:
        """
        更新XDBBackup资源的状态信息
        """
        obj = self._get(self.namespace, self.name)

        if "status" not in obj:
            patch = {"status": {}}
        else:
            patch = {"status": obj["status"]}
        patch["status"][field] = value
        self.obj = self._patch_status(self.namespace, self.name, patch)

    def set_status(self, status) -> None:
        """
        更新XDBBackup资源的状态信息
        """
        obj = cast(dict, self._get(self.namespace, self.name))
        print(f"set_status obj:{obj}")
        if "status" not in obj:
            obj["status"] = status
        else:
            utils.merge_patch_object(obj["status"], status)
        self.obj = self._patch_status(self.namespace, self.name, obj)

    def get_backup_status(self, field=None):
        """
        获取crd 资源的status dict
        :param field:
        :return:
        """
        status = self._get_status_field("snapshot")
        if status and field:
            return status.get(field)
        return status

    def log_backup_info(self, logger: Logger) -> None:
        """
        记录备份信息到日志中
        """
        logger.info(f"Relation XDB, ns: {self.xdb_cluster_ns}/{self.xdb_cluster_name} ")
        logger.info(f"CRD Backup name: {self.name} / {self.parsed_spec.namespace}")

    @property
    def node_id(self):
        """
        获取备份快照执行的节点ID
        Returns:
            _type_: _description_
        """
        return self.get_backup_status("NodeID")
    
    @property
    def file_path(self):
        """
        获取备份快照文件路径
        """
        return self.get_backup_status("filePath")

    @property
    def origin_size(self):
        """
        获取备份快照原始大小
        Returns:
            _type_: _description_
        """
        return self.get_backup_status("originSize") + "i" if self.get_backup_status("originSize") else ""
    
    
    def generate_restore_containers(self):
        """
        生成init恢复容器
        """
        xdb = self.get_cluster()
        containers = {
                    "name": "restore-init",
                    "image": self.parsed_spec.mysql_image,  # 使用适当的 init 容器镜像
                    "command": ["/bin/sh", "-c", "/home/<USER>/lightxdb/deploy/control.sh  recover"],
                    "env": [
                        {
                            "name": "BackupFilePath",
                            "value": self.file_path
                        },
                         {
                            "name": "ZOOKEEPER_HOST",
                            "value": xdb.relation_zk_domain
                        }, {
                            "name": "XAGENT_PORT",
                            "value": "8500"
                        },
                        {
                            "name": "RECOVER_XDB_APPID",
                            "value": xdb.app_id
                        },
                        {
                            "name": "RECOVER_XDB_CLUSTER_ID",
                            "value": xdb.cluster_id
                        }
                        ],
                    "volumeMounts": [
                        {
                            "mountPath": "/home/<USER>/mysql_3203",
                            "name":  "mysql-data"  
                        },
                        {
                            "mountPath": "/opt",
                            "name":  "opt"  
                        },
                        {
                            "mountPath": "/home/<USER>/xagent",
                            "name":  "xagent"  
                        },
                        {
                            "mountPath": "/home/<USER>/_backups_",
                            "name":  "snapshots"  
                        }
                    ]
                }
        return containers
    
    def generate_volumes(self):
        """
        生成恢复容器挂载的卷
        """
        volumes = []
        pod = XDBPod.read(ns=self.xdb_cluster_ns, name=self.get_backup_status("NodeID"))
        if pod:
            tmp = {
                "hostPath": {
                    "path": pod.get_pvc_name_storage_path(),
                    "type": ""
                },
                "name": "snapshots"
            }
            volumes.append(tmp)
        return volumes
    
    def get_work_node_pod(self):
        """
        获取备份快照执行的节点pod
        Returns:
            _type_: _description_
        """
        return XDBPod.read(ns=self.xdb_cluster_ns, name=self.get_backup_status("NodeID"))
    

    
    
class XDBBackupStrategy(K8sInterfaceObject):
    """
    备份策略的资源对象
    """

    def __init__(self, body: Body) -> None:
        super().__init__()
        self.obj: Body = body
        self._parsed_spec: Optional[XDBBackupStrategySpec] = None

    def __str__(self):
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        return f"<XDBBackupStrategy {self.name}>"

    @classmethod
    def _get(cls, ns: str, name: str) -> Body:
        try:
            ret = cast(Body, api_customobj.get_namespaced_custom_object(
                consts.GROUP, consts.VERSION, ns,
                consts.XDB_BACKUP_STRATEGY_PLURAL, name))
        except ApiException as e:
            raise e
        return ret

    @classmethod
    def _patch(cls, ns: str, name: str, patch: dict) -> Body:
        """
        更新XDBBackupStrategy资源
        """
        return cast(Body, api_customobj.patch_namespaced_custom_object(
            consts.GROUP, consts.VERSION, ns,
            consts.XDB_BACKUP_STRATEGY_PLURAL, name, body=patch))

    @classmethod
    def _patch_status(cls, ns: str, name: str, patch: dict) -> Body:
        """
        更新XDBBackupStrategy资源的状态信息
        """
        return cast(Body, api_customobj.patch_namespaced_custom_object_status(
            consts.GROUP, consts.VERSION, ns,
            consts.XDB_BACKUP_STRATEGY_PLURAL, name, body=patch))

    @classmethod
    def read(cls, ns: str, name: str) -> 'XDBBackupStrategy':
        """
        读取XDBBackupStrategy资源
        """
        return XDBBackupStrategy(cls._get(ns, name))

    @property
    def metadata(self) -> dict:
        """
        获取资源的元数据信息
        """
        return self.obj["metadata"]

    @property
    def annotations(self) -> dict:
        """
        获取集群的注解信息
        """
        return self.metadata["annotations"]

    @property
    def spec(self) -> dict:
        """
        获取spec信息
        """
        return self.obj["spec"]

    @property
    def status(self) -> dict:
        """
        获取状态信息
        """
        if "status" in self.obj:
            return self.obj["status"]
        return {}

    @property
    def name(self) -> str:
        """
        获取资源name
        """
        return self.metadata["name"]

    @property
    def namespace(self) -> str:
        """
        获取资源所在的ns
        """
        return self.metadata["namespace"]

    @property
    def xdb_cluster_name(self) -> str:
        """
        获取集群name
        """
        return self.parsed_spec.clusterName

    @property
    def xdb_cluster_ns(self) -> str:
        """
        获取集群所在的ns
        """
        return self.parsed_spec.clusterNamespace

    @property
    def deleting(self) -> bool:
        """
        获取是否正在删除
        """
        return "deletionTimestamp" in self.metadata and self.metadata["deletionTimestamp"] is not None

    @property
    def delete_policy(self):
        """
        获取删除策略
        """
        return self.annotations.get("xdb-operator.cloudbed.abcstack.com/resourceDeletionPolicy", "retain")

    @property
    def parsed_spec(self) -> XDBBackupStrategySpec:
        """
        获取解析对象
        """
        if not self._parsed_spec:
            self.parse_spec()
            assert self._parsed_spec
        return self._parsed_spec

    @property
    def ready(self) -> bool:
        """
        资源是就绪
        """
        return cast(bool, self.get_create_time())
    
    @property
    def s3_access_token(self) -> str:
        """
            获取指定命名空间下的ak
        """
        secret = cast(api_client.V1Secret, api_core.read_namespaced_secret(
            self.secretName, self.namespace))

        return utils.b64decode(secret.data["accessKey"])
    @property
    def s3_secret_token(self) -> str:
        """
            获取指定命名空间下的sk
        """
        secret = cast(api_client.V1Secret, api_core.read_namespaced_secret(
            self.secretName, self.namespace))
        return utils.b64decode(secret.data["secretKey"])

    @property
    def s3_endpoint(self) -> str:
        """
            获取指定命名空间下的sk
        """
        secret = cast(api_client.V1Secret, api_core.read_namespaced_secret(
            self.secretName, self.namespace))
        return secret.data["endpoint"]
    @property
    def s3_region(self) -> str:
        """
            获取指定命名空间下的sk
        """
        secret = cast(api_client.V1Secret, api_core.read_namespaced_secret(
            self.secretName, self.namespace))
        return secret.data["region"]
    
    @property
    def s3_bucket(self) -> str:
        """
            获取指定命名空间下的sk
        """
        secret = cast(api_client.V1Secret, api_core.read_namespaced_secret(
            self.secretName, self.namespace))
        return secret.data["bucket"]

    @property
    def backup_conf_name(self) -> str:
        """
            获取备份配置名称
        """
        return f"{self.name}.conf"
    
    def parse_spec(self) -> None:
        """
        解析spec
        """
        self._parsed_spec = XDBBackupStrategySpec(self.namespace, self.name, self.spec)

    def set_create_time(self, time: datetime.datetime = None) -> None:
        """
        设置资源创建时间
        """
        if not time:
            time = datetime.datetime.now()
        self._set_status_field("createTime", time.replace(
            microsecond=0).isoformat() + "Z")

    def get_create_time(self) -> Optional[datetime.datetime]:
        """
        获取资源创建时间
        """
        dt = self._get_status_field("createTime")
        if dt:
            return datetime.datetime.fromisoformat(dt.rstrip("Z"))
        return None

    def _get_status_field(self, field: str) -> typing.Any:
        """
        获取状态字段
        """
        return cast(str, self.status.get(field))

    @property
    def uid(self) -> str:
        """
        获取资源uid
        """
        return self.metadata["uid"]

    @property
    def xdbcluster_ready(self) -> bool:
        """
        获取集群是否就绪
        """
        sts = self.get_xdbcluster_stateful_set()
        if not sts:
            logger.warning(f"StatefulSet for cluster {self.name} not found, assuming not ready.")
            return False

        if isinstance(sts, api_client.V1StatefulSet):
            if not sts.status:
                logger.info(f"StatefulSet {self.name} has no status field yet.")
                return False
            # ready_replicas is optional, defaults to 0 if not present.
            ready_replicas = sts.status.ready_replicas if sts.status.ready_replicas is not None else 0
            return ready_replicas == sts.status.replicas
        elif isinstance(sts, dict):
            status = sts.get("status")
            if not status:
                logger.info(f"Kruise StatefulSet {self.name} has no status field yet. sts={sts}")
                return False

            replicas = status.get("replicas")
            if replicas is None:
                logger.info(f"Kruise StatefulSet {self.name} status has no 'replicas' field. status={status}")
                return False

            # readyReplicas might not be present if there are no ready replicas, so default to 0.
            ready_replicas = status.get("readyReplicas", 0)
            
            return ready_replicas == replicas
        else:
            logger.error(f"Invalid sts type for cluster {self.name}: {type(sts)}")
            raise ValueError(f"Invalid sts type: {type(sts)}")
        
        
    def get_xdbcluster_stateful_set(self) -> typing.Optional[api_client.V1StatefulSet]:
        """
        获取XDBCluster的StatefulSet对象，优先尝试获取传统的StatefulSet，如果不存在则尝试获取Kruise StatefulSet。
            如果发生API异常并且状态码为404，则返回None；否则抛出异常。
        
            Args:
                self (XDBCluster): XDBCluster实例。
        
            Returns:
                Optional[api_client.V1StatefulSet]: StatefulSet对象，如果不存在则返回None。
                    API异常时，如果状态码为404，则返回None；其他情况抛出异常。
        """
        try:
            # 首先尝试获取传统的 StatefulSet
            return cast(api_client.V1StatefulSet,
                        api_apps.read_namespaced_stateful_set(self.xdb_cluster_name,
                                                              self.xdb_cluster_ns))
        except ApiException as e:
            if e.status == 404:
                try:
                    # 如果传统的 StatefulSet 不存在，尝试获取 Kruise StatefulSet
                    kruise_sts = api_kruise.get_namespaced_custom_object(
                        group=consts.KRUISE_GROUP,
                        version=consts.KRUISE_VERSION,
                        namespace=self.xdb_cluster_ns,
                        plural=consts.KRUISE_STATEFULSET_PLURAL,
                        name=self.xdb_cluster_name
                    )
                    return cast(api_client.V1StatefulSet, kruise_sts)
                except ApiException as e2:
                    if e2.status == 404:
                        return None
                    raise e2
            raise e
    

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        """
        获取当前资源的引用
        """
        ref = {
            "apiVersion": consts.API_VERSION,
            "kind": consts.XDB_BACKUP_STRATEGY_KIND,
            "name": self.name,
            "namespace": self.namespace,
            "clusterName": self.xdb_cluster_name,
            "uid": self.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    def get_backup_strategy(self) -> typing.Optional['XDBBackupStrategy']:
        """
        获取关联备份策略资源对象
        """
        try:
            return XDBBackupStrategy.read(self.namespace, self.name)
        except ApiException as e:
            print(
                f"Could not get backup strategy {self.namespace}/{self.name}: {e}")
            if e.status == 404:
                return None
            raise

    def get_cluster(self) -> typing.Optional[XDBCluster]:
        """
        获取关联集群资源对象
        """
        try:
            return XDBCluster.read(self.xdb_cluster_ns,
                                   self.xdb_cluster_name)
        except ApiException as e:
            print(
                f"Could not get cluster {self.xdb_cluster_ns}/{self.xdb_cluster_name}: {e}")
            if e.status == 404:
                return None
            raise

    def _set_status_field(self, field: str, value: typing.Any) -> None:
        """
        设置资源的某个状态字段
        """
        obj = self._get(self.namespace, self.name)

        if "status" not in obj:
            patch = {"status": {}}
        else:
            patch = {"status": obj["status"]}
        patch["status"][field] = value
        self.obj = self._patch_status(self.namespace, self.name, patch)

    def set_status(self, status) -> None:
        """
        设置资源的当前状态
        """
        obj = cast(dict, self._get(self.namespace, self.name))
        print(f"set_status obj:{obj}")
        if "status" not in obj:
            obj["status"] = status
        else:
            utils.merge_patch_object(obj["status"], status)
        self.obj = self._patch_status(self.namespace, self.name, obj)

    def set_strategy_status(self, cluster_status) -> None:
        """
        设置资源的当前状态
        """
        self._set_status_field("strategy", cluster_status)

    def get_strategy_status(self, field=None):
        """
        获取资源的当前状态
        """

        status = self._get_status_field("strategy")
        if status and field:
            return status.get(field)
        return status
    
    def log_backup_strategy_info(self, logger: Logger) -> None:
        """
        打印日志
        """
        logger.info(f"Relation XDB, ns: {self.xdb_cluster_ns}/{self.xdb_cluster_name} ")
        logger.info(f"CRD strategy name: {self.name} / {self.parsed_spec.namespace}")

    
    def get_backups_cr(self, ns) -> typing.List['XDBBackup']:
        """
        获取关联备份资源对象,过期时间为expire_duration
        """
        label_selector = f"cloudbed.abcstack.com/strategy-name={self.name}"
        if ns is None:
            objects = cast(api_client.V1CustomResourceDefinitionList, api_customobj.list_cluster_custom_object(
            consts.GROUP, consts.VERSION, consts.XDB_BACKUP_PLURAL, label_selector=label_selector))
        else:
            objects = cast(api_client.V1CustomResourceDefinitionList, api_customobj.list_namespaced_custom_object(
            consts.GROUP, consts.VERSION, ns, consts.XDB_BACKUP_PLURAL, label_selector=label_selector))
        return objects
    
    def delete_backup(self, ns, name) -> None:
        """
        删除关联备份资源对象
        """
        api_customobj.delete_namespaced_custom_object(consts.GROUP, consts.VERSION, ns, consts.XDB_BACKUP_PLURAL, name)