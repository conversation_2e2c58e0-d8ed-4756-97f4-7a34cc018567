#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   backup_controller.py
@Time    :   2024/9/05 19:20
<AUTHOR>   lixian<PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import copy
import json
import kopf
import logging
import re
import traceback
from datetime import datetime

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from logging import Logger
from typing import Dict, TypedDict, TypeVar, Optional, List, Optional, Callable, Tuple, Any
from kopf._cogs.structs.bodies import Body
from kubernetes.client.rest import ApiException

from xdboperator.controller import config, kubeutils, utils, consts, diagnose
from xdboperator.controller.backup import backup_objects
from xdboperator.controller.backup.backup_api import XDBBackup, XDBBackupStrategy
from xdboperator.controller.cluster.xdbcluster_api import XDBCluster
from xdboperator.controller.kubeutils import api_customobj



class XDBClusterBackupMutex:
    """
    A mutex that is used to ensure that only one pod is running at a time.
    """
    def __init__(self, cluster: XDBCluster, backup: Optional[XDBBackup] = None, context: str = "n/a"):
        self.cluster = cluster
        self.backup = backup
        self.context = context

    def __enter__(self, *args):
        owner_lock_creation_time: datetime
        (owner, owner_context, owner_lock_creation_time) = utils.g_ephemeral_pod_state.testset(
            self.cluster, "backup-cluster-mutex", self.backup.name \
                if self.backup else self.cluster.name, context=self.context)
        if owner:
            raise kopf.TemporaryError(
                f"{self.cluster.name} busy. lock_owner={owner} owner_context={owner_context}\
                    lock_created_at={owner_lock_creation_time.isoformat()}",
                delay=10)

    def __exit__(self, *args):
        utils.g_ephemeral_pod_state.set(self.cluster, "backup-cluster-mutex", None, context=self.context)



class XDBBackupController(object):

    def __init__(self, backup: XDBBackup):
        self.backup = backup
        self.strategy = backup.get_backup_strategy()
        
        self.xdbcluster = backup.get_cluster()

    def _init_fs_conf(self):
        """
        生成本地备份的配置
        """
        conf = f"""
g_app_id={self.xdbcluster.product_id}
g_slice_id={self.xdbcluster.cluster_id}
g_mysql_base_dir=/home/<USER>/mysql_3203/
g_storage_type=fs
g_fs_base_dir=/home/<USER>/_backups_
g_expire_duration={self.strategy.parsed_spec.expireDuration}
"""
        return conf
    def _init_s3_conf(self):
        """
        生成备份到s3的配置
        """
        conf = f"""
        # 必填：应用名称，存储上备份路径的一部分  
g_app_id={self.xdbcluster.product_id}
# 必填：分片名称，存储上备份路径的一部分
g_slice_id={self.xdbcluster.cluster_id}
# 必填：mysql实例部署目录
g_mysql_base_dir=/home/<USER>/mysql_3203/

# 存储类型: s3/fs
g_storage_type=s3

# 若g_storage_type=s3，以下配置生效
g_s3_endpoint={self.strategy.s3_endpoint}
g_s3_region={self.strategy.s3_region}
g_s3_access_key={self.strategy.s3_access_key}
g_s3_secret_key={self.strategy.s3_secret_key}
g_s3_bucket={self.strategy.s3_bucket}
g_expire_duration={self.strategy.parsed_spec.expireDuration}
"""      
        return conf


    def generation_backup_config(self, pod, logger):
        """
        生成备份配置文件
        """
        if self.strategy.parsed_spec.storageType == 'fs':
            conf_content = self._init_fs_conf()
        elif self.strategy.parsed_spec.storageType == 's3':
            conf_content = self._init_s3_conf()
        file_path = f"/home/<USER>/xagent/script/backup_lightxdb/{self.strategy.backup_conf_name}"
        cmd = [f'echo "{conf_content}" > {file_path}']
        stdout,stderr = kubeutils.pod_exec_command(pod.name, pod.namespace, cmd, logger)
        logger.debug(f"generation_backup_config cmd stdout: {stdout} stderr: {stderr}")
        return stdout, stderr

    def create_xdb_backup_snapshot(self, logger: logging.Logger) -> int:
        """
        通过工具远程支持备份，注意备份脚本在复制中断场景下依旧备份
        """
        
        
        cluster = self.backup.get_cluster()
        if cluster.replica_topo.backup_pods:
            pod = cluster.replica_topo.backup_pods[0]
        elif cluster.replica_topo.slave_pods:
            pod = cluster.replica_topo.slave_pods[0]
        else:
            self.backup.set_status({
            "snapshot": {
                "status": diagnose.BackupStatus.NO_READY_NODE.value,
                "beginTime": utils.isotime(),
            }})
            raise Exception(
                f"no backup pod found for cluster: {cluster.metadata['name']}")
        self.backup.set_status({
            "snapshot": {
                "status": diagnose.BackupStatus.EXECUTING.value,
                "beginTime": utils.isotime(),
            }})
        # 生成备份配置文件
        stdout, stderr = self.generation_backup_config(pod, logger)
        if stderr and not stdout:
                self.backup.set_status({
                "snapshot": {
                    "status": diagnose.BackupStatus.FAILED.value,
                    "endTime": utils.isotime(),
                }})
        
        cmd = ["/home/<USER>/xagent/script/backup_lightxdb/backup_lightxdb_snapshot.sh", 
               f"/home/<USER>/xagent/script/backup_lightxdb/{self.strategy.backup_conf_name}",
               "backup"]
        stdout, stderr = kubeutils.pod_exec_command(pod.name, pod.namespace, cmd, logger)
        logger.debug(f"exec backup cmd stdout: {stdout} stderr: {stderr}")
        if stderr and not stdout:
            self.backup.set_status({
                "snapshot": {
                    "status": diagnose.BackupStatus.FAILED.value,
                    "endTime": utils.isotime(),
                }})
            raise Exception(f"backup failed: {stderr}")
        result_file_path = self.re_result_path(stdout)
        if result_file_path:
            backup_result = self.parse_backup_file_result(pod, result_file_path, logger)
            logger.debug(f"exec parse_backup_file_result cmd result: {backup_result}")
            success_flag = "backup_lightxdb_snapshot.sh EXIT with code 0"
            if success_flag in stdout:
                self.backup.set_status({
                    "snapshot": {
                        "status": diagnose.BackupStatus.AVAILABLE.value,
                        "beginTime": backup_result['backup_timer']["start_time"],
                        "endTime": backup_result['backup_timer']["stop_time"],
                        "NodeID": pod.name,
                        "originSize": utils.bytes_conversion(backup_result["origin_size"]),
                        "storageSize": utils.bytes_conversion(backup_result['storage']["size"]),
                        "storageType": backup_result['storage']["type"],
                        "fileName": backup_result['storage']["file_path"].split("/")[-1],
                        "filePath": backup_result['storage']["file_path"],

                        
                    }})
                self.backup.set_create_time()
            else:
                self.backup.set_status({
                    "snapshot": {
                        "status": diagnose.BackupStatus.FAILED.value,
                        "endTime": utils.isotime(),
                    }})
        else:
            self.backup.set_status({
                "snapshot": {
                    "status": diagnose.BackupStatus.FAILED.value,
                    "endTime": utils.isotime(),
                    "filePath": "backup result file not found"
                }})

    def re_result_path(self, stdout: str):
        """
        正则表达式匹配结果路径
        """
        pattern = r"mybackup result:\s*([^\s]+)"
        match = re.search(pattern, stdout)
        return match.group(1) if match else None

    def parse_backup_file_result(self, pod, file_path: str, logger) -> Dict[str, Any]:
        """
        解析备份文件内容
        return:
        {
            "type": "snapshot",
            "storage": {
            "type": "fs",
            "prefix": "/home/<USER>/_backups_/xdbglobaldefault/xdbglobaldefault_0000/xdbglobaldefault-xdbglobaldefault_0000-snapshot-",
            "file_path": "/home/<USER>/_backups_/xdbglobaldefault/xdbglobaldefault_0000/xdbglobaldefault-xdbglobaldefault_0000-snapshot-backup-20240910_113737-3907-mybackup",
            "modify_time": "0001-01-01T00:00:00Z",
            "size": 14896966
            },
            "origin_size": 5428326284,
            "origin_count": 306,
            "pack_method": "xbstream",
            "size": 14896966,
            "md5_sum": "66841fbb3b265810a9205dc5b11c33d2",
            "expire_method": "by_duration",
            "expire_datetime": "0001-01-01T00:00:00Z",
            "expire_duration": 259200000000000,
            "upload_timer": {
            "start_time": "0001-01-01T00:00:00Z",
            "stop_time": "0001-01-01T00:00:00Z"
            },
            "download_timer": {
            "start_time": "0001-01-01T00:00:00Z",
            "stop_time": "0001-01-01T00:00:00Z"
            },
            "backup_timer": {
            "start_time": "2024-09-10T11:37:38.205421401+08:00",
            "duration": 3707901251,
            "stop_time": "2024-09-10T11:37:41.913322641+08:00"
            },
            "recover_timer": {
            "start_time": "0001-01-01T00:00:00Z",
            "stop_time": "0001-01-01T00:00:00Z"
            },
            "from_lsn": "0",
            "compress_switch": 1,
            "snapshot_time": "0001-01-01T00:00:00Z",
            "mysql_version": "5.7.44-baidu-1.0.0.3-log",
            "backup_snapshot_time": "0001-01-01T00:00:00Z",
            "master_snapshot_time": "0001-01-01T00:00:00Z",
            "prepare_timer": {
            "start_time": "0001-01-01T00:00:00Z",
            "stop_time": "0001-01-01T00:00:00Z"
            },
            "start_instance_timer": {
            "start_time": "0001-01-01T00:00:00Z",
            "stop_time": "0001-01-01T00:00:00Z"
            },
            "repl_sync_timer": {
            "start_time": "0001-01-01T00:00:00Z",
            "stop_time": "0001-01-01T00:00:00Z"
            }
        }
                    
        """
        cmd = [f"cat", file_path]
        stdout, stderr = kubeutils.pod_exec_command(pod.name, pod.namespace, cmd, logger)
        try:
            result = json.loads(stdout)
        except json.decoder.JSONDecodeError:
            logger.error(f"parse backup file result failed, {stdout}")
            result = {}
        return result

    def delete_backup(self, logger: Logger):
        """
        删除备份的物理文件
        Args:
            logger (Logger): _description_
        """
        status = self.backup.get_backup_status("status")
        if status in {diagnose.BackupStatus.AVAILABLE.value, diagnose.BackupStatus.TERMINATE.value}:
            logger.warning(f"start rm xdb snapshot: {self.backup.name}")
            #删除加锁，避免批量删除 导致POD假死,注意这里
            with XDBClusterBackupMutex(self.backup.get_cluster(), self.backup):
                logger.info(f"Start to create snapshot for xdb cluster")
                cmd = [f"rm", "-f", self.backup.file_path]
                stdout, stderr = kubeutils.pod_exec_command(self.backup.node_id, self.backup.namespace, cmd, logger)
                logger.warning(f"cm return stdout:{stdout} stderr:{stderr} ")
        else:
            logger.warning(f"backup status:{status} skip rm file. ")


    
class XDBBackupStrategyController(object):
    def __init__(self, strategy: XDBBackupStrategy):
        self.strategy = strategy
        
    def delete_expire_duration_backup(self, logger: Logger):
        """
        删除过期时间的备份
        """
        logger.debug("start exec func delete_expire_duration_backup")
        backups = self.strategy.get_backups_cr(self.strategy.namespace)
        logger.debug(f"xdb strategy name is: {self.strategy.name}, backups count is: {len(backups['items'])}")
        expire_duration = self.strategy.parsed_spec.expireDuration
        # 计算过期时间
        time_threshold = datetime.utcnow() - utils.parse_expire_duration(expire_duration)
        wait_del = []
        try:
            # 遍历所有 CR，找到过期的备份并删除
            for item in backups.get('items', []):
                # 获取创建时间
                cr_creation_time = item['metadata']['creationTimestamp']
                cr_creation_time = datetime.strptime(cr_creation_time, '%Y-%m-%dT%H:%M:%SZ')
                # 如果创建时间早于过期时间，则删除该 CR
                if cr_creation_time < time_threshold: 
                    wait_del.append(item)
            #分批删除
            for item in wait_del[:10]:
                cr_name = item['metadata']['name']
                ns = item['metadata']['namespace']
                logger.debug(f"Backups need to be deleted name: {cr_name}, CreatedAt: {cr_creation_time}")
                logger.debug(f"start delete Backup name: {cr_name}... ")
        
                self.strategy.delete_backup(ns, cr_name)
        except ApiException as e:
            logger.error(f"Exception when querying or deleting CR: {e}")

    
    
    


def process_backup_daemon(strategy: XDBBackupStrategy, body: Body, logger: logging.Logger, scheduler: BackgroundScheduler):
    """ 
        处理备份守护进程
    Args:
        strategy (XDBBackupStrategy): _description_
        body (Body): _description_
        logger (logging.Logger): _description_
        scheduler (BackgroundScheduler): _description_
    """
    backup_params = load_and_check_params(strategy, logger, consts.BACKUP_NEED_PARAMS)
    new_schedule = backup_params["schedule"]
    api_version = body["apiVersion"]
    kind = body["kind"]
    name =  body["metadata"]["name"]
    uid = body["metadata"]["uid"]
    
    
    jobs = scheduler.get_jobs()
    if not jobs:
        logger.info(
            f"current cronjob is empty, add cronjob with {new_schedule}")
        job = scheduler.add_job(func=create_xdb_backup_cr,
                                trigger=CronTrigger.from_crontab(new_schedule),
                                args=(strategy.xdb_cluster_name, strategy.xdb_cluster_ns,
                                    strategy.name, strategy.namespace, api_version,
                                    kind, name, uid, new_schedule))
        logger.info(f"add job success.")
    else:
        job = jobs[0]
        old_schedule = job.args[-1]
        logger.debug(f"old_schedule:{old_schedule} new_schedule:{new_schedule}")
        if old_schedule != new_schedule:
            job.remove()
            job = scheduler.add_job(
                func=create_xdb_backup_cr,
                trigger=CronTrigger.from_crontab(new_schedule),
                args=(strategy.xdb_cluster_name, strategy.xdb_cluster_ns,
                    strategy.name, strategy.namespace, api_version,
                                    kind, name, uid, new_schedule))
            logger.info(f"reschedule job success.")
        else:
            logger.info(f"job exists.")

    next_run_time = str(job.next_run_time).replace(" ", "T")
    logger.info(f"cronjob next run time is {next_run_time}")
    if next_run_time != strategy.get_strategy_status("nextRunTime"):
        strategy.set_status({
            "strategy": {
                "nextRunTime": next_run_time,
            }})
    


def load_and_check_params(strategy:XDBBackupStrategy,logger: Logger, need_params: Dict[str, str]):
    """
        加载并检查参数
    Args:
        strategy (XDBBackupStrategy): _description_
        logger (Logger): _description_
        need_params (Dict[str, str]): _description_

    Raises:
        kopf.PermanentError: _description_

    Returns:
        _type_: _description_
    """
    logger.info(f"load params:{need_params}.")
    res = dict()
    for k, v in need_params.items():
        if v is None:
            continue
        v_split = v.split('/')
        if len(v_split) > 1:
            temp = copy.deepcopy(strategy.spec)
            for i in range(0, len(v_split)):
                temp = temp.get(v_split[i], {})
                print(f"temp:{temp}")
            if temp:
                res[k] = temp
        elif len(v_split) == 1 and v is not None:
            res[k] = strategy.spec[v]

    res["name"] = strategy.name
    res["namespace"] = strategy.namespace
    logger.debug(f"self.strategy.spec:{strategy.spec}")
    if res["kind"] == "schedule":
        if res.get("schedule", None) is None:
            raise kopf.PermanentError(f"schedule backup but schedule is None")
    elif res["kind"] == "single":
        res.pop("schedule", None)
        logger.info(f"single backup will ignore schedule config")

    return res

def create_xdb_backup_cr(xdbcluster_name: str,
                         xdbcluster_ns: str,
                         strategy_name: str,
                         strategy_ns: str,
                         api_version: str,
                         kind: str,
                         name: str,
                         uid: str,
                         schedule: str = None,
                         ):
    """
    创建xdb备份cr 
    Args:
        xdbcluster_name (str): _description_
        xdbcluster_ns (str): _description_
        strategy_name (str): _description_
        strategy_ns (str): _description_
        body (kopf.Body, optional): _description_. Defaults to None.
    """
    
    backup = backup_objects.prepare_backup_customobj(xdbcluster_name, xdbcluster_ns,
                                                     strategy_name, strategy_ns)
    print(f"\tCreating Backups cr, schedule:{schedule} ...")
    # 设置 OwnerReferences 但避免级联删除
    # 手动设置 OwnerReferences，避免级联删除
    owner_reference = {
        "apiVersion": api_version,
        "kind": kind,
        "name": name,
        "uid": uid,
        "controller": False,  # 避免级联删除
        "blockOwnerDeletion": True  # 阻止删除父对象时删除子对象
    }
    print(f"\tCreating Backups owner_reference:{owner_reference} ...")

    # 将 OwnerReferences 添加到 backup 对象的 metadata
    if "metadata" not in backup:
        backup["metadata"] = {}
    backup["metadata"]["ownerReferences"] = [owner_reference]
    api_customobj.create_namespaced_custom_object(
        consts.GROUP, consts.VERSION, xdbcluster_ns, consts.XDB_BACKUP_PLURAL, backup)



