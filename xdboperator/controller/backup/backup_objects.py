#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   backup_objects.py
@Time    :   2024/8/26 19:26
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   l<PERSON><PERSON><PERSON><PERSON>@baidu.com
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import kopf
import yaml
from logging import Logger
from shlex import quote
from typing import Optional

from xdboperator.controller import utils
from xdboperator.controller.backup.backup_api import XDBBackupSpec, XDBBackup


def prepare_backup_customobj(xdbcluster_name: str,
                             xdbcluster_ns: str,
                             strategy_name: str,
                             strategy_ns: str) -> dict:
    """
    备份对象模板
    """
    tmpl = f"""
apiVersion: cloudbed.abcstack.com/v1
kind: XDBBackup     
metadata:
  name: auto-snapshot-{xdbcluster_name.replace("-","")}-{utils.timestamp()}
  namespace: {strategy_ns}
  labels:
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {xdbcluster_name}
    cloudbed.abcstack.com/instance-type: snapshot
    cloudbed.abcstack.com/component: snapshot
    cloudbed.abcstack.com/strategy-name: {strategy_name}
spec:   
  clusterName: {xdbcluster_name}
  clusterNamespace: {xdbcluster_ns}
  backupStrategyName: {strategy_name}
  backupStrategyNamespace: {strategy_ns}

"""
    backup = yaml.safe_load(tmpl)
    return backup
