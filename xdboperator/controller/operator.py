#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   operator.py
@Time    :   2024/6/16 08:29
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   l<PERSON><PERSON><PERSON><PERSON>@baidu.com
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import kopf
import logging
import time
from kubernetes.client.rest import ApiException
from logging import Logger
from pathlib import Path
from typing import Any

from xdboperator.controller import config, utils
# load crd resource

from xdboperator.controller.cluster import operator_cluster
from xdboperator.controller.database import operator_database
from xdboperator.controller.user import operator_user
from xdboperator.controller.backup import operator_backup
from xdboperator.controller.xdbproxy import operator_xdbproxy
from xdboperator.controller.mysql import operator_mysql


# load crd resource


def ignore_404(f) -> Any:
    """
    忽略404错误，返回None。如果不是404错误，则抛出ApiException。
    
    Args:
        f (Callable[[], Any]): 需要被包装的函数，该函数应该没有参数并返回任意类型的值。
    
    Returns:
        Any: 如果原始函数抛出了404错误，则返回None；否则，返回原始函数的返回值。
    
    Raises:
        ApiException: 如果原始函数抛出了非404错误，则会重新抛出ApiException。
    """
    try:
        return f()
    except ApiException as e:
        if e.status == 404:
            return None
        raise


@kopf.on.startup()  # type: ignore
def on_startup(settings: kopf.OperatorSettings, logger: Logger, *args, **_):
    """
     Called when the operator starts up.
    
     Args:
         settings (kopf.OperatorSettings): The operator settings object.
         logger (Logger): The logger instance.
         args (Any, optional): Additional positional arguments. Defaults to None.
         _ (Dict[str, Any], optional): Additional keyword arguments. Defaults to None.
    
     Returns:
         None: This function does not return anything.
    """
    utils.log_banner(__file__, logger)
    config.log_config_banner(logger)

    # don't post logger.debug() calls as k8s events
    settings.posting.level = logging.INFO
    # Timeout passed along to the Kubernetes API as timeoutSeconds=x
    settings.watching.server_timeout = 300
    # Total number of seconds for a whole watch request per aiohttp:
    # https://docs.aiohttp.org/en/stable/client_reference.html#aiohttp.ClientTimeout.total
    settings.watching.client_timeout = 300
    # Timeout for attempting to connect to the peer per aiohttp:
    # https://docs.aiohttp.org/en/stable/client_reference.html#aiohttp.ClientTimeout.sock_connect
    settings.watching.connect_timeout = 30
    # Wait for that many seconds between watching events
    settings.watching.reconnect_backoff = 1
    # setting the number of synchronous workers used by the operator for synchronous handlers
    settings.execution.max_workers = 1000
    settings.peering.clusterwide = True
    settings.peering.stealth = True
    settings.peering.name = "xdb-operator"
    settings.peering.priority = int(time.time() * 1000000)
    logger.debug(operator_user)
    logger.debug(operator_database)
    logger.debug(operator_cluster)
    logger.debug(operator_backup)
    logger.debug(operator_xdbproxy)
    logger.debug(operator_mysql)



    Path('/tmp/xdb-operator-ready').touch()


@kopf.on.cleanup()  # type: ignore
def on_shutdown(logger: Logger, *args, **kwargs):
    """
    当Kubernetes operator被关闭时，执行此函数。
    
    Args:
        logger (Logger): Kubernetes operator的日志记录器。
        args (Any, optional): 可变参数，默认为None。
        kwargs (Dict[str, Any], optional): 关键字参数，默认为空字典。
    
    Returns:
        None: 无返回值。
    
    Raises:
        None: 不会引发任何异常。
    """
    # g_group_monitor.stop()
    logger.info("operator on shutdown...")
