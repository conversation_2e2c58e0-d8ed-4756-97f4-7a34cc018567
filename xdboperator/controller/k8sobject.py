# !/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   k8sobject.py
@Time    :   2024/6/22 19:25
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   l<PERSON>ian<PERSON><EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import datetime
from typing import Optional

from .kubeutils import api_core

g_component = None
g_host = None


def post_event(namespace: str, object_ref: dict, type: str, action: str,
               reason: str, message: str) -> None:
    """
    发布事件。
    
    Args:
        namespace (str): 命名空间，字符串类型，必填项。
        object_ref (dict): 相关对象的引用，字典类型，包含以下键值对：kind（字符串）、name（字符串）和uid（字符串），必填项。
        type (str): 事件类型，字符串类型，可选项，默认为"Normal"。
        action (str): 执行的动作，字符串类型，必填项。
        reason (str): 原因，字符串类型，必填项。
        message (str, optional): 消息，字符串类型，可选项，默认为""。如果超过1024个字符，则会被截断。
    
    Returns:
        None: 无返回值。
    
    Raises:
        None: 没有异常抛出。
    """
    if len(message) > 1024:
        message = message[:1024]

    body = {
        # What action was taken/failed regarding to the regarding object.
        'action': action,

        'eventTime': datetime.datetime.now().isoformat() + "Z",

        'involvedObject': object_ref,

        'message': message,
        'metadata': {
            'namespace': namespace,
            'generateName': 'xdboperator-evt-',
        },

        # This should be a short, machine understandable string that gives the
        # reason for the transition into the object's current status.
        'reason': reason,

        'reportingComponent': f'cloudbed.abcstack.com/xdboperator-{g_component}',
        'reportingInstance': f'{g_host}',

        'source': {
            'component': g_component,
            'host': g_host
        },

        'type': type
    }
    api_core.create_namespaced_event(namespace, body)


class K8sInterfaceObject:
    """
    Base class for objects meant to interface with Kubernetes.
    """

    def __init__(self) -> None:
        """
            初始化函数，无参数需要返回None。
        该函数用于对象的创建和初始化工作，一般在使用对象之前调用。
        <return> None: 无返回值，表示该函数执行完成但没有返回任何结果。
        """
        pass

    @property
    def name(self) -> str:
        """
            返回该对象的名称，默认实现抛出异常。
        子类可重写此方法以提供自定义行为。
        
        Raises:
            Exception: 如果没有被正确重写，则始终抛出异常。
        
        Returns:
            str: 该对象的名称。
        """
        raise Exception("NotImplemented")

    @property
    def namespace(self) -> str:
        """
            返回当前对象的命名空间，默认实现抛出异常。
        子类可以重写该方法来提供自定义的命名空间。
        
        Raises:
            Exception: 如果没有被重写，则始终抛出异常。
        
        Returns:
            str: 当前对象的命名空间字符串。
        """
        raise Exception("NotImplemented")

    def self_ref(self, field: Optional[str] = None) -> dict:
        """
            返回一个包含自引用字段的字典，如果指定了field，则只返回该字段。
        如果没有指定field，则返回所有自引用字段。
        
        Args:
            field (Optional[str], optional): 要返回的自引用字段名称（默认为None）. Defaults to None.
        
        Raises:
            Exception: 总是抛出异常，因为这是一个未实现的方法。
        
        Returns:
            dict: 包含自引用字段的字典，如果指定了field，则只返回该字段；否则返回所有自引用字段。
        """
        raise Exception("NotImplemented")

    # ## Event Posting ##
    # Explicit events should only be used for high-level messages. Debugging or
    # low-level messages should go through the logging system.
    def info(self, *, action: str, reason: str, message: str,
             field: Optional[str] = None) -> None:
        """
            发布一个事件，用于记录有关资源的信息。
        该函数将在Kubernetes集群中创建一个名为“Normal”类型的事件，并包含指定的操作、原因和消息。
        如果提供了字段参数，则事件将针对特定的字段进行筛选。
        
        Args:
            action (str): 事件的操作，例如“Created”或“Updated”。
            reason (str): 事件的原因，例如“SuccessfulSync”或“FailedSync”。
            message (str): 事件的消息，描述了资源的状态更新。
            field (Optional[str], optional): 可选参数，默认值为None。如果提供了字段，则事件将仅针对该字段进行筛选。默认值为None。
        
        Raises:
            None
        
        Returns:
            None: 不返回任何值，直接在Kubernetes集群中创建一个事件。
        """
        post_event(self.namespace, self.self_ref(field), type="Normal",
                   action=action, reason=reason, message=message)

    def warn(self, *, action: str, reason: str, message: str,
             field: Optional[str] = None) -> None:
        """
        发送警告事件，包括操作、原因、消息和可选的字段。
        
        Args:
            action (str, optional): 操作名称，默认为None。
            reason (str, optional): 原因，默认为None。
            message (str): 消息。
            field (Optional[str], optional): 可选的字段，默认为None。
        
        Returns:
            None: 无返回值。
        """
        post_event(self.namespace, self.self_ref(field), type="Warning",
                   action=action, reason=reason, message=message)

    def error(self, *, action: str, reason: str, message: str,
              field: Optional[str] = None) -> None:
        """
            发送错误事件，包括操作、原因、消息和可选的字段。
        
        Args:
            action (str): 操作名称，例如 "create"、"update" 或 "delete"。
            reason (str): 错误的原因，例如 "InvalidData"、"NotFound" 或 "InternalError"。
            message (str): 错误信息，用于向用户提供有关错误的更多上下文。
            field (Optional[str], optional): 可选的字段名称，指示错误是否与特定字段相关。默认为 None。
        
        Returns:
            None: 无返回值，直接在事件中心发布错误事件。
        """
        post_event(self.namespace, self.self_ref(field), type="Error",
                   action=action, reason=reason, message=message)
