#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   xdbcluster_objects.py
@Time    :   2024/6/22 19:26
<AUTHOR>   lix<PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""


import kopf
import yaml
from logging import Logger
from shlex import quote
from typing import Optional

from xdboperator.controller.kubeutils import api_core, api_apps, api_customobj
from xdboperator.controller.kubeutils import client as api_client
from xdboperator.controller import utils

from xdboperator.controller.mysql.mysql_api import MySQLSingleSpec, AbstractServerSetSpec, MySQLSingle



def prepare_mysqlsingle_service(spec: MySQLSingleSpec) -> dict:
    """
    创建dbproxy使用的svc
    """
    tmpl = f"""
apiVersion: v1
kind: Service
metadata:
  name: {spec.name}-primary
  namespace: {spec.namespace}
  labels:
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: mysqlsingle
    cloudbed.abcstack.com/component: mysql
  annotations::
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: mysqlsingle
    cloudbed.abcstack.com/component: mysql
spec:
  ports:
  - name: xdb-mysql
    port: {spec.mysqlPort}
    protocol: TCP
    targetPort: {spec.mysqlPort}
  selector:
    component: mysql
    role: master
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: mysqlsingle
    app.kubernetes.io/managed-by: xdb-operator
  type: {spec.service.type}
"""
    service = yaml.safe_load(tmpl)

    if spec.service.annotations:
        service['metadata']['annotations'] = spec.service.annotations

    if spec.service.labels:
        service['metadata']['labels'] = spec.service.labels | service['metadata']['labels']

    return service


def prepare_mysqlsingle_service_hs(spec: MySQLSingleSpec) -> dict:
    """
    创建mysqlsingle使用的hs
    """

    tmpl = f"""
apiVersion: v1
kind: Service
metadata:
  name: {spec.name}-hs
  namespace: {spec.namespace}
  labels:
    cloudbed.abcstack.com/component: mysql
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: mysqlsingle
  annotations::
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: mysqlsingle
    cloudbed.abcstack.com/component: mysql
spec:
  clusterIP: None
  ports:
  - name: xdb-mysql
    port: {spec.mysqlPort}
    protocol: TCP
    targetPort: {spec.mysqlPort}
  - name: xdb-xagent
    port: {spec.xagentPort}
    protocol: TCP
    targetPort: {spec.xagentPort}
  selector:
    component: mysql
    role: master
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: mysqlsingle
    app.kubernetes.io/managed-by: xdb-operator
  type: {spec.service.type}
"""
    service = yaml.safe_load(tmpl)

    if spec.service.annotations:
        service['metadata']['annotations'] = spec.service.annotations
    if spec.service.labels:
        service['metadata']['labels'] = spec.service.labels | service['metadata']['labels']

    return service




def prepare_mysqlsingle_statefulset(cluster: MySQLSingle, logger, *,
                              init_only: bool = False) -> dict:
    """
    创建statefulset 类型的dbproxy 
    """
    spec: MySQLSingleSpec = cluster.parsed_spec
    start_command = ["/bin/bash", "-c", "/home/<USER>/lightxdb/deploy/control.sh start && sleep infinity"]
    stop_command = ["/bin/bash", "-c", "/home/<USER>/lightxdb/deploy/control.sh stop"]

    tmpl = f"""
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {spec.name}
  namespace: {spec.namespace}
  labels:
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: mysqlsingle
    app.kubernetes.io/name: mysqlsingle
    app.kubernetes.io/component: mysqlsingle
    app.kubernetes.io/managed-by: xdb-operator
    app.kubernetes.io/created-by: xdb-operator
  annotations::
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: xdbproxy
    cloudbed.abcstack.com/component: xdbproxy
spec:
  replicas: 1
  podManagementPolicy: Parallel
  serviceAccountName: {spec.name}
  serviceAccount: {spec.name}
  serviceName: {spec.name}-hs
  restartPolicy: Always
  schedulerName: default-scheduler
  securityContext: {{}}
  terminationGracePeriodSeconds: 30
  selector:
    matchLabels:
      component: mysql
      cloudbed.abcstack.com/cluster: {spec.name}
      cloudbed.abcstack.com/xdb: {spec.name} 
      karrier.abcstack.com/app: {spec.name} 
      cloudbed.abcstack.com/instance-type: mysqlsingle
      app.kubernetes.io/component: mysql
      app.kubernetes.io/managed-by: xdb-operator
      app.kubernetes.io/created-by: xdb-operator
  template:
    metadata:
      labels:
        component: mysql
        cloudbed.abcstack.com/cluster: {spec.name}
        cloudbed.abcstack.com/xdb: {spec.name} 
        karrier.abcstack.com/app: {spec.name} 
        cloudbed.abcstack.com/instance-type: mysqlsingle
        app.kubernetes.io/component: mysql
        app.kubernetes.io/managed-by: xdb-operator
        app.kubernetes.io/created-by: xdb-operator
    spec:
      securityContext: {{}}
      containers:
      - name: mysql
        image: {spec.mysql_image}
        imagePullPolicy: {spec.mysql_image_pull_policy}
        securityContext: {{}}
        env:
        - name: XAGENT_PORT
          value: "{spec.xagentPort}"
        volumeMounts:
        - mountPath: /home/<USER>/mysql_3203
          name: mysql-data
        - mountPath: /home/<USER>/xagent
          name: xagent
        - mountPath: /opt
          name: opt
        lifecycle:
          preStop:
            exec:
              command: {stop_command}
        ports:
        - containerPort: {spec.mysqlPort}
          name: dbproxy
        - containerPort: {spec.xagentPort}
          name: xagent
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 5
          periodSeconds: 30
          successThreshold: 1
          tcpSocket:
            port: 3203
          timeoutSeconds: 3
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 5
          periodSeconds: 30
          successThreshold: 1
          tcpSocket:
            port: 3203
          timeoutSeconds: 3
      volumes:
      - hostPath:
          path: /opt
          type: ""
        name: opt
      - name: xagent
        emptyDir: {{}}
  volumeClaimTemplates:
  - apiVersion: v1
    kind: PersistentVolumeClaim
    metadata:
      name: mysql-data
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: {spec.mysqlDataStorageSize}
      storageClassName: local-path
      volumeMode: Filesystem
"""
    sts = yaml.safe_load(tmpl)

    container = sts["spec"]["template"]["spec"]["containers"][0]
    container["args"] = start_command
    metadata = {}
    if len(metadata):
        utils.merge_patch_object(sts["spec"]["template"], {"metadata": metadata})
        
    #todo 如果有快照恢复，就需要查询旧快照所在物理机，然后在创建pod时指定nodeAffinity，同时查询pvc真实物理机路径，通过挂载的方式挂入新的节点
    if spec.snapshotRef:
      sts['spec']['template']['spec']['initContainers'] = cluster.generate_initContainers()
      sts['spec']['template']['spec']['nodeSelector'] = {"kubernetes.io/hostname": cluster.generate_node_name()}
      sts['spec']['template']['spec']['volumes'].extend(cluster.generate_volumes())
      #add env
      init_env = sts['spec']['template']['spec']['initContainers'][0]['env']
      sts['spec']['template']['spec']['containers'][0]['env'].extend(init_env)
      #add origin snapshots dir 
      snapshots_volume = {"mountPath": "/home/<USER>/_backups_", "name": "snapshots"}
      sts['spec']['template']['spec']['containers'][0]['volumeMounts'].append(snapshots_volume)
          
    logger.debug(f"mysqlsingle statefulset: {sts}")
    return sts
  
def prepare_service_account(spec: AbstractServerSetSpec) -> dict:
    """
    准备service account
    """
    account = f"""
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {spec.name}
  namespace: {spec.namespace}
  annotations:
      meta.operator/release-name: {spec.name}
      meta.operator/release-namespace: {spec.namespace}
  labels:
      cloudbed.abcstack.com/dbproxy-cluster: {spec.name}
      app.kubernetes.io/name: mysql
      app.kubernetes.io/component: mysql
      app.kubernetes.io/managed-by: xdb-operator
      app.kubernetes.io/created-by: xdb-operator
"""

    account = yaml.safe_load(account)

    return account

def prepare_cluster_pod_disruption_budget(spec: AbstractServerSetSpec) -> dict:
    """
      创建proxy使用的pdb
    """
    tmpl = f"""
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {spec.name}-pdb
spec:
  minAvailable: 1
  selector:
    matchLabels:
      component: mysql
      cloudbed.abcstack.com/cluster: {spec.name}
"""
    pdb = yaml.safe_load(tmpl.replace("\n\n", "\n"))
    return pdb