#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   operato_cluster.py
@Time    :   2024/7/3 15:34
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   lixian<PERSON><EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import kopf
from kopf._cogs.structs.bodies import Body
from logging import Logger
from typing import Optional

from xdboperator.controller import consts, diagnose, config 
from xdboperator.controller import utils
from xdboperator.controller.api_utils import ApiSpecError, ignore_404
from xdboperator.controller.mysql.mysql_api import MySQLSingle
from xdboperator.controller.mysql  import mysql_objects
from xdboperator.controller.kubeutils import api_core, api_apps, api_policy, k8s_version



@kopf.on.create(consts.GROUP, consts.VERSION, consts.MYSQL_SINGLE_PLURAL)
def on_mysqlsingle_create(name: str, namespace: Optional[str], body: Body,
                         logger: Logger, **kwargs) -> None:
    """
    安装单节点MySQL实例，主要是给快照恢复临时使用
    """
    
    logger.info(
        f"Initializing MySQLSingle Cluster name={name} \
            namespace={namespace} on K8s {k8s_version()}")
    cluster = MySQLSingle(body)

    try:
        cluster.parse_spec()
        cluster.parsed_spec.validate(logger)
    except ApiSpecError as e:
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }})
        cluster.error(action="CreateCluster",
                      reason="InvalidArgument", message=str(e))
        raise kopf.TemporaryError(f"Error in MySQLSingle spec: {e}")

    icspec = cluster.parsed_spec
    cluster.log_cluster_info(logger)
    # CR 已创建，检查状态
    if cluster.ready:
        if cluster.cluster_sts_ready:
            cluster.set_status({
                "cluster": {
                    "status": diagnose.ClusterDiagStatus.RUNNING.value,
                    "onlineInstances": cluster.online_instances,
                    "lastProbeTime": utils.isotime()
                }})
            logger.info(f"Create XDB Cluster {name} success")
            cluster.info(action="CreateXDBCluster", reason="Success",
                        message="success")
            return
        else:
            msg = "waiting for the sts cluster to be ready"
            logger.info(msg)
            cluster.info(action="CreateXDBCluster", reason="ReadyCheckFailed",
                     message=msg)
            raise kopf.TemporaryError(msg, delay=15)
    # CR 未创建过，则开始创建
    try:
        print("0. MysqlSingle Cluster PodDisruptionBudget")
        if not ignore_404(cluster.get_disruption_budget):
            print("\tPreparing...")
            disruption_budget = mysql_objects.prepare_cluster_pod_disruption_budget(icspec)
            print(f"\tCreating...{disruption_budget}")
            kopf.adopt(disruption_budget)
            api_policy.create_namespaced_pod_disruption_budget(namespace=namespace, body=disruption_budget)

        print("1. MysqlSingle Service")
        if not ignore_404(cluster.get_mysql_service):
            print("\tPreparing...")
            proxy_service = mysql_objects.prepare_mysqlsingle_service(icspec)
            print(f"\tCreating...{proxy_service}")
            kopf.adopt(proxy_service)
            api_core.create_namespaced_service(namespace=namespace, body=proxy_service)

        print("2. MysqlSingle headless Service")
        if not ignore_404(cluster.get_mysql_headless_service):
            print("\tPreparing...")
            proxy_service_hs = mysql_objects.prepare_mysqlsingle_service_hs(icspec)
            print(f"\tCreating...{proxy_service_hs}")
            kopf.adopt(proxy_service_hs)
            api_core.create_namespaced_service(namespace=namespace, body=proxy_service_hs)

        print("3. MysqlSingle ServiceAccount")
        existing_sa = ignore_404(lambda: cluster.get_service_account(icspec))
        print(f"\tExisting SA: {existing_sa}")
        if not existing_sa:
            print("\tPreparing...")
            sa = mysql_objects.prepare_service_account(icspec)
            print(f"\tCreating...{sa}")
            kopf.adopt(sa)
            api_core.create_namespaced_service_account(namespace=namespace, body=sa)
        print("4. MysqlSingle Statefulset")
        if not ignore_404(cluster.get_stateful_set):
            print("\tPreparing...") 
            mysql_sts = mysql_objects.prepare_mysqlsingle_statefulset(cluster, logger)
            print(f"\tCreating...{mysql_sts}")
            kopf.adopt(mysql_sts)
            api_apps.create_namespaced_stateful_set(namespace=namespace, body=mysql_sts)
    except Exception as exc:
        cluster.warn(action="CreateMysqlSingleCluster", reason="CreateResourceFailed",
                        message=f"{exc}")
        raise 

    print(f"3. Setting operator version for the IC to {config.DEFAULT_OPERATOR_VERSION_TAG}")
    cluster.set_operator_version(config.DEFAULT_OPERATOR_VERSION_TAG)
    cluster.info(action="CreateCluster", reason="ResourcesCreated",
                    message="Dependency resources created, switching status to CREATING")
    cluster.set_create_time()
    cluster.set_status({
        "cluster": {
            "status": diagnose.ClusterDiagStatus.CREATING.value,
            "onlineInstances": 0,
            "lastProbeTime": utils.isotime()
        }})
    # start retry check
    if not cluster.cluster_sts_ready:
        msg = "waiting for the sts cluster to be ready"
        logger.info(msg)
        raise kopf.TemporaryError(msg, delay=15)




@kopf.on.delete(consts.GROUP, consts.VERSION, consts.MYSQL_SINGLE_PLURAL)
def on_mysqlsingle_delete(name: str, namespace: Optional[str], body: Body,
                       logger: Logger, **kwargs) -> None:
    """
    删除xdb的提供的database
``  """

    logger.info(f"Delete MysqlSingle CR name={name} namespace={namespace} on K8s {k8s_version()}")
    single = MySQLSingle(body)
    if single.ready:
        single.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.TERMINATE.value
            }})
    
