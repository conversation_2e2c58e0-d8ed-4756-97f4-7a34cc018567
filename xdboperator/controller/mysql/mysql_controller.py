#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   cluster_controller.py
@Time    :   2024/6/22 19:20
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   lixian<PERSON><PERSON>@baidu.com
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import datetime
import kopf
import logging
import typing
from logging import Logger
from typing import Optional, TYPE_CHECKING, Dict

from xdboperator.controller import config, utils
from xdboperator.controller.mysql.mysql_api import  MySQLSingle



class MysqlSingleController(object):
    """
    """
    def __init__(self, single: MySQLSingle):
        self.mysqlsingle = single                                                                                                        



    
    
