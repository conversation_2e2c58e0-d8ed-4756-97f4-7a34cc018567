#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   xdbcluster_api.py
@Time    :   2024/6/22 19:25
<AUTHOR>   lixian<PERSON>hu
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import abc
import datetime
import json
import logging
import typing
from kopf._cogs.structs.bodies import Body
from kubernetes import client
from logging import Logger
from typing import Optional, cast, overload, List

from xdboperator.controller import utils, consts, config, diagnose
from xdboperator.controller.api_utils import ApiSpecError
from xdboperator.controller.api_utils import dget_dict, dget_str, dget_list, dget_int
from xdboperator.controller.k8sobject import K8sInterfaceObject
from xdboperator.controller.kubeutils import api_core, api_apps, api_customobj, api_policy, api_kruise
from xdboperator.controller.kubeutils import client as api_client, ApiException, k8s_cluster_domain
from xdboperator.controller.cluster.xdbcluster_api import AbstractServerSetSpec
from xdboperator.controller.backup.backup_api import XDBBackup



MAX_CLUSTER_NAME_LEN = 64

logger = logging.getLogger(__name__)


class ServiceSpec(object):
    """
    service spec
    """
    type: str = "ClusterIP"
    annotations: dict = {}
    labels: dict = {}
    defaultPort: str = "xdb-rw"


    def parse(self, spec: dict, prefix: str) -> None:
        """
        处理svc的参数
        """
        if "type" in spec:
            self.type = dget_str(spec, "type", prefix)

        if "annotations" in spec:
            self.annotations = dget_dict(spec, "annotations", prefix)

        if "labels" in spec:
            self.labels = dget_dict(spec, "labels", prefix)

        if "defaultPort" in spec:
            self.defaultPort = dget_str(spec, "defaultPort", prefix)

class SnapshotReferenceSpec(object):
    """
    快照引用规格，包含命名空间和名称。
    """
    namespace: str = None
    name: str = ""

    def parse(self, spec: dict, prefix: str) -> None:
        """
            解析规格，获取命名空间和名称。
        
        Args:
            spec (dict): 规格字典，包含"namespace"和"name"两个键值对，分别表示命名空间和名称。
                如果不存在这两个键，则使用默认的命名空间和名称。
            prefix (str, optional): 前缀，默认为"".
        
        Returns:
            None: 无返回值，直接修改类实例属性。
        """
        self.namespace = dget_str(spec, "namespace", prefix)
        self.name = dget_str(spec, "name", prefix)

    def __str__(self):
        """
            返回字符串格式的对象，包含命名空间和名称。
        例如：'default/my-pod'。
        
        Returns:
            str -- 字符串格式的对象。
        """
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        """
            Returns a string representation of the SnapshotReferenceSpec object.
        
        Returns:
            str (str): String representation of the SnapshotReferenceSpec object.
        """
        return f"<SnapshotReferenceSpec {self.name}>"



class MySQLSingleSpec(AbstractServerSetSpec):
    """
    单节点创建的 crd spec
    """
    snapshotRef: SnapshotReferenceSpec = None
    mysqlDataStorageSize: str = "50Gi"
    instance: int = 1
    mysqlVersion: str = "5.7"
    mysqlPort: int = 3203
    xagentPort: int = 8500
    image: Optional[str] = None

    service: ServiceSpec = ServiceSpec()


    
    def __init__(self, namespace: str, name: str, spec: dict):
        """
            初始化函数，用于初始化类的属性和方法。
        
        Args:
            namespace (str): 命名空间，即Pod所在的Namespace。
            name (str): Pod的名称。
            spec (dict): Pod的规格信息，包括容器信息等。
        
        Returns:
            None.
        
        Raises:
            None.
        """
        super().__init__(namespace, name, name, spec)
        self.load(spec)
    
    def load(self, spec: dict) -> None:
        """
        从给定的字典中提取出需要的信息，并将其赋值给相应的属性。
        Args:
            spec (dict): 包含了所有信息的字典。
        
        Returns:
            None.
        
        Raises:
            None.
        """
        self._load(spec, spec, "spec")
        self.mysqlDataStorageSize = dget_str(spec, "mysqlDataStorageSize", "spec")
        self.mysqlVersion = dget_str(spec, "mysqlVersion", "spec")
        if "image" in spec:
            self.image = dget_str(spec, "image", "spec")
        if "snapshotRef" in spec:
            self.snapshotRef = SnapshotReferenceSpec()
            self.snapshotRef.parse(dget_dict(spec, "snapshotRef", "spec"), "spec.snapshotRef")
    
    
    def validate(self, logger: Logger) -> None:
        """
        验证当前对象是否有效，如果无效则抛出异常。
        此方法会被继承并在子类中使用。
        
        Args:
            logger (Logger): 一个日志记录器，用于记录验证过程中发生的任何异常信息。
            默认值为None，表示不需要记录日志。
        
        Raises:
            None: 如果当前对象有效，则不会抛出任何异常。
            Exception: 如果当前对象无效，则会抛出一个异常，提供更多的错误信息。
        """
        super().validate(logger)
        # check snapshotRef is valid
        if self.snapshotRef:
            backup = XDBBackup.read(self.namespace, self.snapshotRef.name)
            if backup.deleting or backup.get_backup_status("status") != diagnose.BackupStatus.AVAILABLE.value:
                raise ApiSpecError(f"{self.snapshotRef} is not available")
            
    @property
    def mysql_image(self) -> str:
        """
            获取MySQL镜像
        Returns:
            str: _description_
        """
        if self.image:
            return self.image
        else:
            return super().mysql_image

class MySQLSingle(K8sInterfaceObject):
    """
    单节点创建的 crd object
    """
    def __init__(self, cluster: Body) -> None:
        """
            初始化方法，用于初始化对象属性和私有变量。
        
        Args:
            cluster (Body): 一个包含集群信息的Body类型对象，包括名称、版本等信息。
                cluster (Body) 是必需参数，无默认值。
        
        Returns:
            None，初始化完成后不返回任何值。
        """
        super().__init__()
        self.obj: Body = cluster
        self._parsed_spec: Optional[MySQLSingleSpec] = None
        
    def get_init_containers(self):
        """
            获取初始化容器列表(如果指定了快照，则添加快照恢复容器)       
        Returns:
            List[Container] -- 初始化容器列表。
        """
        pass
        

    def __str__(self):
        """
            返回字符串格式的对象，包含命名空间和名称。
        例如：'default/my-pod'。
        
        Returns:
            str -- 字符串格式的对象。
        """
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        """
            Returns a string representation of the MySQLSingle object.
        
        Returns:
            str (str): String representation of the MySQLSingle object, including its name.
        """
        return f"<MySQLSingle {self.name}>"

    @classmethod
    def _get(cls, ns: str, name: str) -> Body:
        """
            根据名称获取指定命名空间下的 CustomObject。
        如果找不到，则会引发 ApiException。
        
        Args:
            ns (str): 命名空间名称。
            name (str): CustomObject 名称。
        
        Returns:
            Body (typing.Any): CustomObject 对象。
            如果找不到，则返回 None。
        
        Raises:
            ApiException: 当 API 调用失败时引发该异常。
        """
        try:
            ret = cast(Body, api_customobj.get_namespaced_custom_object(
                consts.GROUP, consts.VERSION, ns,
                consts.MYSQL_SINGLE_PLURAL, name))
        except ApiException as e:
            raise e
        return ret
    
    
    @classmethod
    def _patch(cls, ns: str, name: str, patch: dict) -> Body:
        """
            修补指定命名空间中的自定义对象。
        """
        return cast(Body, api_customobj.patch_namespaced_custom_object(
            consts.GROUP, consts.VERSION, ns,
            consts.MYSQL_SINGLE_PLURAL, name, body=patch))
    
    @classmethod
    def _patch_status(cls, ns: str, name: str, patch: dict) -> Body:
        """
            修补状态字段，返回Body类型的对象。
        参数：
            - ns (str) - 命名空间名称
            - name (str) - XDBFusionCluster CRD 名称
            - patch (dict) - 需要修补的状态字段，格式为字典，例如 {"status": {"phase": "Running"}}
        返回值（Body）：
            返回一个包含修补后的状态字段的 Body 对象，用于进行下一步操作。
        """
        return cast(Body, api_customobj.patch_namespaced_custom_object_status(
            consts.GROUP, consts.VERSION, ns,
            consts.MYSQL_SINGLE_PLURAL, name, body=patch))

    @classmethod
    def read(cls, ns: str, name: str) -> 'MySQLSingle':
        """
            读取指定命名空间下的XDB集群信息。
        
        Args:
            ns (str): 命名空间，默认为'default'。
            name (str): XDB集群名称。
        
        Returns:
            XDBCluster (str): XDB集群对象，包含了集群的基本信息和相关操作接口。
        
        Raises:
            None.
        """
        return MySQLSingle(cls._get(ns, name))

    def get_create_time(self) -> Optional[datetime.datetime]:
        """
            获取创建时间，返回值为datetime.datetime类型或None。如果不存在则返回None。
        
        Args:
            None
        
        Returns:
            Optional[datetime.datetime]: 创建时间，datetime.datetime类型；如果不存在则返回None。
        """
        dt = self._get_status_field("createTime")
        if dt:
            return datetime.datetime.fromisoformat(dt.rstrip("Z"))
        return None
    
    def _get_status_field(self, field: str) -> typing.Any:
        """
            获取状态字段的值，如果不存在则返回None。
        参数field (str) - 需要获取的状态字段名称，例如"state"或"message"等。
        返回值 (typing.Any) - 状态字段对应的值，如果不存在则返回None。
        """
        return cast(str, self.status.get(field))
    
    def _set_status_field(self, field: str, value: typing.Any) -> None:
        """
            设置状态字段的值，并更新对象。如果对象没有状态字段，则创建一个空状态字段。
        参数：
            - field (str): 要设置的字段名称。
            - value (typing.Any): 要设置的字段值。
        返回值：
            - None（无返回值）
        """
        obj = self._get(self.namespace, self.name)
        if "status" not in obj:
            patch = {"status": {}}
        else:
            patch = {"status": obj["status"]}
        patch["status"][field] = value
        self.obj = self._patch_status(self.namespace, self.name, patch)
        
    @property
    def cluster_sts_ready(self) -> bool:
        """
        获取 sts 状态,主要是副本数与ready 数量是否一致
        """
        sts = self.get_stateful_set()
        return sts.status.ready_replicas == sts.status.replicas if sts else False
    
    @property
    def online_instances(self) -> int:
        """
        获取在线实例数
        """
        sts = self.get_stateful_set()
        return sts.status.ready_replicas if sts else 0
    
    @property
    def ready(self) -> bool:
        """
            判断当前对象是否已经准备好，可以进行操作。
        如果对象还未创建或初始化完成，则返回 False；否则返回 True。
        
        Returns:
            bool (bool): 若对象已准备好，返回 True；否则返回 False。
        """
        return cast(bool, self.get_create_time())


    @property
    def metadata(self) -> dict:
        """
            返回元数据字典，包括名称、标签和注释等信息。
        
        返回值类型：dict，字典格式如下：
            {
                "name": str,  # 模型的名称
                "labels": list[str],  # 模型的标签列表，可能为空列表
                "comment": str  # 模型的注释信息，可能为空字符串
            }
        
        Returns:
            dict - 元数据字典，包括名称、标签和注释等信息。
        """
        return self.obj["metadata"]

    @property
    def product_id(self):
        """
        集群ID
        """
        return self.parsed_spec.appID

    @property
    def app_id(self):
        """
        集群ID
        """
        return self.parsed_spec.appID

    @property
    def cluster_id(self):
        """
        集群ID
        """
        return self.parsed_spec.clusterID
    
    @property
    def annotations(self) -> dict:
        """
            返回当前对象的注解字典，包括所有已经存在的注解。如果没有注解，则返回一个空的字典。
        注解是键值对形式的，其中键是注解名称，值是注解的值。
        
        Returns:
            dict (dict): 注解字典，包括所有已经存在的注解。如果没有注解，则返回一个空的字典。
        """
        return self.metadata["annotations"]

    @property
    def spec(self) -> dict:
        """
            返回当前对象的规格信息，包括名称、标签和其他特性。
        
        返回值（dict）：
            - name (str, optional): 资源的名称，默认为 None。
            - labels (dict, optional): 资源的标签，默认为 None。
            - other_attrs (dict, optional): 其他特性，默认为 None。
        
        Returns:
            dict (dict): 规格信息，包括名称、标签和其他特性。
        """
        return self.obj["spec"]

    
    @property
    def status(self) -> dict:
        """
            获取当前对象的状态信息，包括资源使用情况、错误信息等。如果对象没有状态信息，则返回一个空字典。
        返回值是一个字典，其中包含以下键值对：
            - "used_resources" (dict, optional): 当前对象正在使用的资源，包括CPU、GPU和内存等。默认为None。
            - "error_message" (str, optional): 如果对象出现了错误，则此处显示错误信息；否则为None。默认为None。
            例如：{"used_resources": {"cpu": 10, "gpu": 2}, "error_message": "Out of memory"}
        
        Returns:
            dict: 当前对象的状态信息，包括资源使用情况和错误信息。如果对象没有状态信息，则返回一个空字典。
        """
        if "status" in self.obj:
            return self.obj["status"]
        return {}

    @property
    def name(self) -> str:
        """
            返回模型的名称，即metadata中的"name"字段。
        
        Returns:
            str (str): 模型的名称。
        """
        return self.metadata["name"]

    @property
    def namespace(self) -> str:
        """
            返回当前对象的命名空间。
        命名空间是一个字符串，用于标识Kubernetes对象在集群中的唯一性。
        
        返回值：str（字符串）
            - 返回当前对象的命名空间。如果对象没有命名空间，则返回空字符串。
        """
        return self.metadata["namespace"]
    
    
    @property
    def uid(self) -> str:
        """
            返回实例的唯一标识符。该值是在创建实例时生成的，并且不会更改。
        
        返回值：str（字符串） - 实例的唯一标识符
        
        """
        return self.metadata["uid"]

    @property
    def deleting(self) -> bool:
        """
            返回当前资源是否处于删除状态，如果有deletionTimestamp属性且不为None则返回True。
        否则返回False。
        
        Returns:
            bool - True（处于删除状态）或 False（未处于删除状态）
        """
        return "deletionTimestamp" in self.metadata and self.metadata["deletionTimestamp"] is not None

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        """
        返回一个包含当前对象的引用信息的字典，可选地指定字段路径。
        如果指定了字段路径，则将其添加到引用字典中。
        
        Args:
            field_path (Optional[str], optional): 字段路径，默认为None。可选参数。默认值为None。
        
        Returns:
            dict: 包含当前对象的引用信息的字典，格式如下：
                {
                    "apiVersion": "xdb.mongo.com/v1",
                    "kind": "XDBFusionCluster",
                    "name": <当前对象的名称>,
                    "namespace": <当前对象所在的命名空间>,
                    "resourceVersion": <当前对象的资源版本号>,
                    "uid": <当前对象的UID>
                }
            如果指定了字段路径，则还会包含以下键值对：
                {
                    "fieldPath": <字段路径>
                }
        
        """
        ref = {
            "apiVersion": consts.API_VERSION,
            "kind": consts.MYSQL_SINGLE_PLURAL,
            "name": self.name,
            "namespace": self.namespace,
            "resourceVersion": self.metadata["resourceVersion"],
            "uid": self.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    @property
    def parsed_spec(self) -> MySQLSingleSpec:
        """
        解析mysql单机临时实例参数
        Returns:
        """
        if not self._parsed_spec:
            self.parse_spec()
            assert self._parsed_spec

        return self._parsed_spec
    
    def parse_spec(self) -> None:
        """
            解析规格，并将其保存在属性中。
        如果已经解析过，则不再重复解析。
        
        Args:
            None
        
        Returns:
            None: 无返回值，直接修改了类实例的属性 _parsed_spec。
        """
        self._parsed_spec = MySQLSingleSpec(self.namespace, self.name, self.spec)

    def reload(self) -> None:
        """
            重新加载对象，并更新内部状态。如果对象不存在，则抛出异常。
        返回值：None，无返回值。
        """
        self.obj = self._get(self.namespace, self.name)
        
    def get_disruption_budget(self) -> typing.Optional[api_client.V1PodDisruptionBudget]:
        """
        获取pod disruption budget
        """
        try:
            return cast(api_client.V1PodDisruptionBudget,
                        api_policy.read_namespaced_pod_disruption_budget(self.name + "-pdb", self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def get_mysql_headless_service(self) -> typing.Optional[api_client.V1Service]:
        """
        获取mysql headless service
        Returns:
            typing.Optional[api_client.V1Service]: _description_
        """
        try:
            return cast(api_client.V1Service,
                        api_core.read_namespaced_service(self.name + "-hs", self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def get_stateful_set(self) -> typing.Optional[api_client.V1StatefulSet]:
        """
        获取当前 StatefulSet，优先尝试获取传统的StatefulSet，如果不存在则尝试获取Kruise StatefulSet。
        
        Args:
            self (K8sResource): K8sResource 实例，表示当前资源对象。
        
        Returns:
            typing.Optional[api_client.V1StatefulSet]: V1StatefulSet 类型的可选对象，表示当前 StatefulSet，如果不存在则为 None。
        
        Raises:
            ApiException (ApiException): 如果 API 调用失败，将抛出 ApiException 异常，包含 HTTP 状态码和错误信息。
        """
        try:
            # 首先尝试获取传统的 StatefulSet
            return cast(api_client.V1StatefulSet,
                        api_apps.read_namespaced_stateful_set(self.name, self.namespace))
        except ApiException as e:
            if e.status == 404:
                try:
                    # 如果传统的 StatefulSet 不存在，尝试获取 Kruise StatefulSet
                    kruise_sts = api_kruise.get_namespaced_custom_object(
                        group=consts.KRUISE_GROUP,
                        version=consts.KRUISE_VERSION,
                        namespace=self.namespace,
                        plural=consts.KRUISE_STATEFULSET_PLURAL,
                        name=self.name
                    )
                    return cast(api_client.V1StatefulSet, kruise_sts)
                except ApiException as e2:
                    if e2.status == 404:
                        return None
                    raise e2
            raise e
        
    def get_mysql_service(self) -> typing.Optional[api_client.V1Service]:
        """
        获取mysql svc
        """
        try:
            return cast(api_client.V1Service,
                        api_core.read_namespaced_service(self.name + "-primary", self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise
        
    @classmethod
    def get_service_account(cls, spec: AbstractServerSetSpec) -> api_client.V1ServiceAccount:
        """
        获取关联proxy集群的使用的service account
        """
        return cast(api_client.V1ServiceAccount,
                    api_core.read_namespaced_service_account(spec.name, spec.namespace))
    
    def log_cluster_info(self, logger: Logger) -> None:
        """
        记录集群信息
        """
        logger.info(f"MySQLSingle {self.namespace}/{self.name} Version({self.parsed_spec.mysqlVersion})")
        

    def set_operator_version(self, version: str) -> None:
        """
        设置operator版本
        Args:
            version (str): _description_
        """
        v = self.operator_version
        if v != version:
            patch = {"metadata": {"annotations": {"cloudbed.abcstack.com/xdb-operator-version": version}}}
            # TODO store the current server/router version + timestamp
            # store previous versions in a version history log
            self.obj = self._patch(self.namespace, self.name, patch)
            
    @property
    def operator_version(self) -> Optional[str]:
        """
            获取操作符版本号，如果不存在则返回None。
        该属性只有在运行时才能使用，因为它需要访问到运行时的元数据信息。
        
        Args:
            None
        
        Returns:
            Optional[str]: 操作符版本号字符串，如果不存在则返回None。
        """
        return self.metadata.get("cloudbed.abcstack.com/xdb-operator-version")
    
    def set_create_time(self, time: datetime.datetime = None) -> None:
        """
        设置创建时间
        Args:
            time (datetime.datetime, optional): _description_. Defaults to None.
        """
        if not time:
            time = datetime.datetime.now()
        self._set_status_field("createTime", time.replace(
            microsecond=0).isoformat() + "Z")
        
    def set_status(self, status) -> None:
        """
        设置状态
        Args:
            status (_type_): _description_
        """
    
        obj = cast(dict, self._get(self.namespace, self.name))

        if "status" not in obj:
            obj["status"] = status
        else:
            utils.merge_patch_object(obj["status"], status)
        self.obj = self._patch_status(self.namespace, self.name, obj)
    
    def get_backup_cr(self) -> typing.Optional[XDBBackup]:
        """
        获取备份CR
        Returns:
            typing.Optional[XDBBackup]: _description_
        """
        if not self.parsed_spec.snapshotRef:
            return None
        else:
            return XDBBackup.read(self.parsed_spec.snapshotRef.namespace,
                                  self.parsed_spec.snapshotRef.name)
    
    def generate_initContainers(self):
        """
        根据要恢复的快照,生成初始化容器
        """
        containers = []
        if self.parsed_spec.snapshotRef:
            backup = self.get_backup_cr()
            if backup is None:
                raise ApiSpecError(f"Snapshot ref {self.parsed_spec.snapshotRef.name} not found")
            containers.append(backup.generate_restore_containers())
        return containers
        
    def generate_volumes(self):
        """
        生成挂载卷
        Raises:
            ApiSpecError: _description_

        Returns:
            _type_: _description_
        """
        volumes = []
        if self.parsed_spec.snapshotRef:
            backup = self.get_backup_cr()
            if backup is None:
                raise ApiSpecError(f"Snapshot ref {self.parsed_spec.snapshotRef.name} not found")
            volumes.extend(backup.generate_volumes())
        return volumes
    
    def generate_node_name(self):
        """
        生成节点名称

        """
        NodeName = ""
        if self.parsed_spec.snapshotRef:
            backup = self.get_backup_cr()
            if backup is None:
                raise ApiSpecError(f"Snapshot ref {self.parsed_spec.snapshotRef.name} not found")
            pod = backup.get_work_node_pod()
            if pod is None:
                raise ApiSpecError(f"Work node pod for snapshot ref {self.parsed_spec.snapshotRef.name} not found")
            NodeName = pod.node_name
        return NodeName
    
