#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   consts.py
@Time    :   2024/6/22 19:25
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   l<PERSON><PERSON><PERSON><PERSON>@baidu.com
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import enum

GROUP = "cloudbed.abcstack.com"
VERSION = "v1"
API_VERSION = GROUP + "/" + VERSION

# Kruise API 相关常量
KRUISE_GROUP = "apps.kruise.io"
KRUISE_VERSION = "v1beta1"
KRUISE_API_VERSION = KRUISE_GROUP + "/" + KRUISE_VERSION
KRUISE_STATEFULSET_PLURAL = "statefulsets"

KRUISE_STATEFULSET_KIND = "StatefulSet"

APP_V1_GROUP = "apps"
APP_V1_VERSION = "v1"
APP_V1_API_VERSION = APP_V1_GROUP + "/" + APP_V1_VERSION

APP_V1_STATEFULSET_KIND = "StatefulSet"

XDBDATABASE_KIND = "XDBDataBase"
XDBDATABASE_PLURAL = "xdbdatabases"

XDBFUSIONCLUSTER_KIND = "XDBFusionCluster"
XDBFUSIONCLUSTER_PLURAL = "xdbfusionclusters"

MYSQL_SINGLE_KIND = "MySQLSingle"
MYSQL_SINGLE_PLURAL = "mysqlsingles"

XDBPROXY_KIND = "XDBProxy"
XDBPROXY_PLURAL = "xdbproxys"

XDB_USER_KIND = "XDBUser"
XDB_USER_PLURAL = "xdbusers"

XDB_BACKUP_STRATEGY_KIND = "XDBBackupStrategy"
XDB_BACKUP_STRATEGY_PLURAL = "xdbbackupstrategys"

XDB_BACKUP_KIND = "XDBBackup"
XDB_BACKUP_PLURAL = "xdbbackups"

PODSPEC_CONTAINERS_XDB_CONTAINER = "xdb"
PODSPEC_CONTAINERS_XDBPROXY_CONTAINER = "dbproxy"

SUCCESS = "exec_success"
FAILED = "exec_failed"

# time
SECONDS = 1
MINUTES = SECONDS * 60
HOURS = MINUTES * 60
DAYS = HOURS * 24


class ResourceDeletePolicy(enum.Enum):
    """
    资源删除动作枚举
    """
    DELETE = "delete"
    RETAIN = "retain"


SPEC_CLUSTERNAME = "clusterName"
SPEC_CLUSTERNAMESPACE = "clusterNamespace"
SPEC_BACKUP = "backup"
SPEC_S3 = "S3"
SPEC_FS = "gs"
SPEC_BACKUP_BACKUPTOS3_KIND = "kind"
SPEC_BACKUP_BACKUPTOS3_KIND_SIGNAL = "single"
SPEC_BACKUP_BACKUPTOS3_KIND_SCHEDULE = "schedule"
SPEC_BACKUP_BACKUPTOS3_SCHEDULE = "schedule"

SPEC_BACKUP_BACKUP_NEXT_RUN_TIME = "nextRunTime"

# {'clusterName': 'clusterName', 'kind': 'backup/backupToS3/kind', 'clusterNamespace': 'clusterNamespace', 'deletes3': 'deletes3', 'schedule': 'backup/backupToS3/schedule'}


BACKUP_NEED_PARAMS = {
    SPEC_CLUSTERNAME: SPEC_CLUSTERNAME,
    SPEC_CLUSTERNAMESPACE: SPEC_CLUSTERNAMESPACE,
    SPEC_BACKUP_BACKUPTOS3_KIND:  SPEC_BACKUP_BACKUPTOS3_KIND,
    SPEC_BACKUP_BACKUPTOS3_SCHEDULE:  SPEC_BACKUP_BACKUPTOS3_SCHEDULE,
}
