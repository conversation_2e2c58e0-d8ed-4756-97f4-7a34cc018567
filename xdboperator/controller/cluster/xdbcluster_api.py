#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   xdbcluster_api.py
@Time    :   2024/6/22 19:25
<AUTHOR>   lixian<PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import abc
import datetime
import json
import logging
import typing
from kopf._cogs.structs.bodies import Body
from kubernetes import client
from logging import Logger
from typing import Optional, cast, overload, List

from xdboperator.controller import utils, consts, config
from xdboperator.controller.api_utils import ApiSpecError,ImagePullPolicy
from xdboperator.controller.api_utils import dget_dict, dget_str, dget_list, dget_int
from xdboperator.controller.k8sobject import K8sInterfaceObject
from xdboperator.controller.kubeutils import api_core, api_apps, api_customobj, api_policy
from xdboperator.controller.kubeutils import client as api_client, ApiException, k8s_cluster_domain
from xdboperator.lightxdb.zookeeper import ZookeeperService
from xdboperator.controller.kubeutils import api_kruise
from xdboperator.controller.kubeutils import api_directpv

MAX_CLUSTER_NAME_LEN = 64

logger = logging.getLogger(__name__)



class SnapshotReferenceSpec(object):
    namespace: str = None
    name: str = ""

    def parse(self, spec: dict, prefix: str) -> None:
        """
            解析规格，获取命名空间和名称。
        
        Args:
            spec (dict): 规格字典，包含"namespace"和"name"两个键值对，分别表示命名空间和名称。
                如果不存在这两个键，则使用默认的命名空间和名称。
            prefix (str, optional): 前缀，默认为"".
        
        Returns:
            None: 无返回值，直接修改类实例属性。
        """
        self.namespace = dget_str(spec, "namespace", prefix)
        self.name = dget_str(spec, "name", prefix)

    def __str__(self):
        """
            返回字符串格式的对象，包含命名空间和名称。
        例如：'default/my-pod'。
        
        Returns:
            str -- 字符串格式的对象。
        """
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        """
            Returns a string representation of the SnapshotReferenceSpec object.
        
        Returns:
            str (str): String representation of the SnapshotReferenceSpec object.
        """
        return f"<SnapshotReferenceSpec {self.name}>"


        
class AbstractServerSetSpec(abc.ABC):
    """
    spec 抽象类
    """
    imagePullPolicy: ImagePullPolicy = config.default_image_pull_policy
    imagePullSecrets: Optional[List[dict]] = None
    imageRepository: str = config.DEFAULT_IMAGE_REPOSITORY
    version: str = ""


    def __init__(self, namespace: str, name: str, cluster_name: str, spec: dict):
        """
            初始化KubernetesResource对象。
        
        Args:
            namespace (str): Kubernetes资源的命名空间。
            name (str): Kubernetes资源的名称。
            cluster_name (str): Kubernetes集群的名称。
            spec (dict): Kubernetes资源的spec部分，包含了资源的配置信息。
        
        Returns:
            None.
        """
        self.namespace = namespace
        self.name = name
        self.cluster_name = cluster_name

    # @abc .abstracmethod
    def load(self, spec: dict) -> None:
        """
            加载规格信息，将规格信息转换为对象属性。
        该方法会在初始化或反序列化时被调用。
        
        Args:
            spec (dict): 包含规格信息的字典，其中每个键值对表示一个属性和对应的值。
                        属性名称应与类中定义的属性名称相同，值应是可以直接赋给对应属性的合法值。
        
        Raises:
            ValueError: 如果规格信息中存在不支持的属性或属性值。
        
        Returns:
            None: 无返回值，直接修改对象的属性。
        """
        ...

    def _load(self, spec_root: dict, spec_specific: dict, where_specific: str) -> None:
        """
            加载规格信息，并将其保存到对应的字典中。
        如果在特定位置找不到规格信息，则会尝试从根目录中获取。
        
        Args:
            spec_root (dict): 根目录下的规格信息字典，包含所有可能出现的规格信息。
            spec_specific (dict): 特定位置（where_specific）下的规格信息字典，包含该位置可能出现的规格信息。
            where_specific (str): 特定位置，用于指示需要从哪个位置获取规格信息。
        
        Returns:
            None; 无返回值，直接修改传入的字典。
        """
        pass

    def validate(self, logger: Logger) -> None:
        """
            验证当前对象是否合法，如果不合法则抛出ApiSpecError异常。
        该方法会在CRD的validation部分被调用。
        
        Args:
            logger (Logger): Logger类型的日志记录器，可以为None。
                默认值为None，表示不使用日志记录器。
        
        Raises:
            ApiSpecError: 如果当前对象不合法，将抛出此异常。
        Returns:
            None: 无返回值，该函数没有返回值。
        """
        # TODO see if we can move some of these to a schema in the CRD

        if len(self.name) > MAX_CLUSTER_NAME_LEN:
            raise ApiSpecError(
                f"CR name {self.name} is too long. Must be < {MAX_CLUSTER_NAME_LEN}")

        # self.logs.validate()
        
    @property
    def mysql_image(self) -> str:
        if self.version:
            version = self.version
        else:
            version = config.DEFAULT_MYSQL_VERSION_TAG
        image = config.XDB_MYSQL_SERVER_IMAGE
        return self.format_image(image, version)

    @property
    def mysql_image_pull_policy(self) -> str:
        return self.imagePullPolicy.value

    def format_image(self, image, version):
        """
        format_image 函数用于格式化镜像名称，包括镜像仓库和镜像版本。
        Args:
            image (_type_): _description_
            version (_type_): _description_

        Returns:
            _type_: _description_
        """
        if self.imageRepository:
            return f"{self.imageRepository}/{image}:{version}"
        return f"{image}:{version}"



class XDBClusterSpec(AbstractServerSetSpec):
    """
    集群创建的 crd spec
    """
    instance: int = 3
    mysqlVersion: str = "5.7"
    appID: str = ""
    clusterID: str = ""
    ZKDomain: str = ""
    mysqlPort: int = 3203
    xagentPort: int = 8500

    def __init__(self, namespace: str, name: str, spec: dict):
        """
            初始化函数，用于实例化一个K8sObject对象。
        
        Args:
            namespace (str): Kubernetes命名空间的名称。
            name (str): Kubernetes对象的名称。
            spec (dict): Kubernetes对象的规格信息，包括元数据和具体的资源配置。
                metadata部分包括名称、标签等基本信息，spec部分包括具体的资源配置。
                spec中的内容可能会因不同类型的Kubernetes对象而有所不同。
        
        Returns:
            None.
        
        Raises:
            None.
        """
        super().__init__(namespace, name, name, spec)
        self.namespace = namespace
        self.name = name
        self.load(spec)

    def load(self, spec: dict) -> None:
        """
            加载规格信息，并将其保存到对象中。
        如果规格信息不完整或无效，则会引发异常。
        
        Args:
            spec (dict): 包含规格信息的字典，必须包含以下键值对：
                - instances (int): 实例数量，必须为正整数。
                - mysqlVersion (str): MySQL版本号，必须是一个非空字符串。
                - appID (str): 应用ID，必须是一个非空字符串。
                - clusterID (str): 集群ID，必须是一个非空字符串。
                - ZKDomain (str): Zookeeper域名，必须是一个非空字符串。
        
            返回值：None，该函数没有返回值。
        
            Raises:
                ValueError: 当输入的规格信息不完整或无效时，会引发此异常。
        """
        self._load(spec, spec, "spec")
        print(f"XDBClusterSpec:{spec} ")
        self.instance = dget_int(spec, "instances", "spec")
        self.mysqlVersion = dget_str(spec, "mysqlVersion", "spec")
        self.appID = dget_str(spec, "appID", "spec")
        self.clusterID = dget_str(spec, "clusterID", "spec")
        self.ZKDomain = dget_str(spec, "ZKDomain", "spec")

    def validate(self, logger: Logger) -> None:
        """
            验证当前对象是否有效，如果无效则抛出异常。
        此方法会被继承并在子类中使用。
        
        Args:
            logger (Logger): 一个日志记录器，用于记录验证过程中发生的任何异常信息。
                              默认值为None，表示不需要记录日志。
        
        Raises:
            None: 如果当前对象有效，则不会抛出任何异常。
            Exception: 如果当前对象无效，则会抛出一个异常，提供更多的错误信息。
        """
        super().validate(logger)

        # check that the secret exists and it contains rootPassword

        
class XDBCluster(K8sInterfaceObject):
    """
    xdb cluster cr object
    """

    def __init__(self, cluster: Body) -> None:
        """
            初始化方法，用于初始化对象属性和私有变量。
        
        Args:
            cluster (Body): 一个包含集群信息的Body类型对象，包括名称、版本等信息。
                cluster (Body) 是必需参数，无默认值。
        
        Returns:
            None，初始化完成后不返回任何值。
        """
        super().__init__()
        self.obj: Body = cluster
        self._parsed_spec: Optional[XDBClusterSpec] = None

    def __str__(self):
        """
            返回字符串格式的对象，包含命名空间和名称。
        例如：'default/my-pod'。
        
        Returns:
            str -- 字符串格式的对象。
        """
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        """
            Returns a string representation of the XDBCluster object.
        
        Returns:
            str (str): String representation of the XDBCluster object, including its name.
        """
        return f"<XDBCluster {self.name}>"

    @classmethod
    def _get(cls, ns: str, name: str) -> Body:
        """
            根据名称获取指定命名空间下的 CustomObject。
        如果找不到，则会引发 ApiException。
        
        Args:
            ns (str): 命名空间名称。
            name (str): CustomObject 名称。
        
        Returns:
            Body (typing.Any): CustomObject 对象。
            如果找不到，则返回 None。
        
        Raises:
            ApiException: 当 API 调用失败时引发该异常。
        """
        try:
            ret = cast(Body, api_customobj.get_namespaced_custom_object(
                consts.GROUP, consts.VERSION, ns,
                consts.XDBFUSIONCLUSTER_PLURAL, name))
        except ApiException as e:
            raise e
        return ret

    @classmethod
    def _patch(cls, ns: str, name: str, patch: dict) -> Body:
        """
            修补指定命名空间中的自定义对象。
        参数：
            - ns (str) - 命名空间名称，必须提供。
            - name (str) - 自定义对象名称，必须提供。
            - patch (dict) - 需要修补的内容，必须提供。
        返回值（Body）：
            返回一个包含修补后的自定义对象信息的 Body 类型对象。如果修补失败，将会抛出异常。
        """
        return cast(Body, api_customobj.patch_namespaced_custom_object(
            consts.GROUP, consts.VERSION, ns,
            consts.XDBFUSIONCLUSTER_PLURAL, name, body=patch))

    @classmethod
    def _patch_status(cls, ns: str, name: str, patch: dict) -> Body:
        """
            修补状态字段，返回Body类型的对象。
        参数：
            - ns (str) - 命名空间名称
            - name (str) - XDBFusionCluster CRD 名称
            - patch (dict) - 需要修补的状态字段，格式为字典，例如 {"status": {"phase": "Running"}}
        返回值（Body）：
            返回一个包含修补后的状态字段的 Body 对象，用于进行下一步操作。
        """
        return cast(Body, api_customobj.patch_namespaced_custom_object_status(
            consts.GROUP, consts.VERSION, ns,
            consts.XDBFUSIONCLUSTER_PLURAL, name, body=patch))

    @classmethod
    def read(cls, ns: str, name: str) -> 'XDBCluster':
        """
            读取指定命名空间下的XDB集群信息。
        
        Args:
            ns (str): 命名空间，默认为'default'。
            name (str): XDB集群名称。
        
        Returns:
            XDBCluster (str): XDB集群对象，包含了集群的基本信息和相关操作接口。
        
        Raises:
            None.
        """
        return XDBCluster(cls._get(ns, name))

    @property
    def metadata(self) -> dict:
        """
            返回元数据字典，包括名称、标签和注释等信息。
        
        返回值类型：dict，字典格式如下：
            {
                "name": str,  # 模型的名称
                "labels": list[str],  # 模型的标签列表，可能为空列表
                "comment": str  # 模型的注释信息，可能为空字符串
            }
        
        Returns:
            dict - 元数据字典，包括名称、标签和注释等信息。
        """
        return self.obj["metadata"]

    @property
    def product_id(self):
        """
        集群ID
        """
        return self.parsed_spec.appID

    @property
    def app_id(self):
        """
        集群ID
        """
        return self.parsed_spec.appID

    @property
    def cluster_id(self):
        """
        集群ID
        """
        return self.parsed_spec.clusterID
    
    @property
    def relation_zk_domain(self):
        """
        XDB 关联的使用的ZK
        """
        return self.parsed_spec.ZKDomain

    @property
    def annotations(self) -> dict:
        """
            返回当前对象的注解字典，包括所有已经存在的注解。如果没有注解，则返回一个空的字典。
        注解是键值对形式的，其中键是注解名称，值是注解的值。
        
        Returns:
            dict (dict): 注解字典，包括所有已经存在的注解。如果没有注解，则返回一个空的字典。
        """
        return self.metadata["annotations"]

    @property
    def spec(self) -> dict:
        """
            返回当前对象的规格信息，包括名称、标签和其他特性。
        
        返回值（dict）：
            - name (str, optional): 资源的名称，默认为 None。
            - labels (dict, optional): 资源的标签，默认为 None。
            - other_attrs (dict, optional): 其他特性，默认为 None。
        
        Returns:
            dict (dict): 规格信息，包括名称、标签和其他特性。
        """
        return self.obj["spec"]

    @property
    def status(self) -> dict:
        """
            获取当前对象的状态信息，包括资源使用情况、错误信息等。如果对象没有状态信息，则返回一个空字典。
        返回值是一个字典，其中包含以下键值对：
            - "used_resources" (dict, optional): 当前对象正在使用的资源，包括CPU、GPU和内存等。默认为None。
            - "error_message" (str, optional): 如果对象出现了错误，则此处显示错误信息；否则为None。默认为None。
            例如：{"used_resources": {"cpu": 10, "gpu": 2}, "error_message": "Out of memory"}
        
        Returns:
            dict: 当前对象的状态信息，包括资源使用情况和错误信息。如果对象没有状态信息，则返回一个空字典。
        """
        if "status" in self.obj:
            return self.obj["status"]
        return {}

    @property
    def name(self) -> str:
        """
            返回模型的名称，即metadata中的"name"字段。
        
        Returns:
            str (str): 模型的名称。
        """
        return self.metadata["name"]

    @property
    def namespace(self) -> str:
        """
            返回当前对象的命名空间。
        命名空间是一个字符串，用于标识Kubernetes对象在集群中的唯一性。
        
        返回值：str（字符串）
            - 返回当前对象的命名空间。如果对象没有命名空间，则返回空字符串。
        """
        return self.metadata["namespace"]

    @property
    def uid(self) -> str:
        """
            返回实例的唯一标识符。该值是在创建实例时生成的，并且不会更改。
        
        返回值：str（字符串） - 实例的唯一标识符
        
        """
        return self.metadata["uid"]

    @property
    def deleting(self) -> bool:
        """
            返回当前资源是否处于删除状态，如果有deletionTimestamp属性且不为None则返回True。
        否则返回False。
        
        Returns:
            bool - True（处于删除状态）或 False（未处于删除状态）
        """
        return "deletionTimestamp" in self.metadata and self.metadata["deletionTimestamp"] is not None

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        """
            返回一个包含当前对象的引用信息的字典，可选地指定字段路径。
        如果指定了字段路径，则将其添加到引用字典中。
        
        Args:
            field_path (Optional[str], optional): 字段路径，默认为None。可选参数。默认值为None。
        
        Returns:
            dict: 包含当前对象的引用信息的字典，格式如下：
                {
                    "apiVersion": "xdb.mongo.com/v1",
                    "kind": "XDBFusionCluster",
                    "name": <当前对象的名称>,
                    "namespace": <当前对象所在的命名空间>,
                    "resourceVersion": <当前对象的资源版本号>,
                    "uid": <当前对象的UID>
                }
            如果指定了字段路径，则还会包含以下键值对：
                {
                    "fieldPath": <字段路径>
                }
        
        """
        ref = {
            "apiVersion": consts.API_VERSION,
            "kind": consts.XDBFUSIONCLUSTER_PLURAL,
            "name": self.name,
            "namespace": self.namespace,
            "resourceVersion": self.metadata["resourceVersion"],
            "uid": self.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    @property
    def parsed_spec(self) -> XDBClusterSpec:
        """
            返回已解析的XDB集群规格，如果未解析则先调用`parse_spec`方法进行解析。
        
        Returns:
            XDBClusterSpec (XDBClusterSpec): 已解析的XDB集群规格对象。
        """
        if not self._parsed_spec:
            self.parse_spec()
            assert self._parsed_spec

        return self._parsed_spec

    def parse_spec(self) -> None:
        """
            解析规格，并将其保存在属性中。
        如果已经解析过，则不再重复解析。
        
        Args:
            None
        
        Returns:
            None: 无返回值，直接修改了类实例的属性 _parsed_spec。
        """
        self._parsed_spec = XDBClusterSpec(self.namespace, self.name, self.spec)

    def reload(self) -> None:
        """
            重新加载对象，并更新内部状态。如果对象不存在，则抛出异常。
        返回值：None，无返回值。
        """
        self.obj = self._get(self.namespace, self.name)

    def owns_pod(self, pod: api_client.V1Pod) -> bool:
        """
            判断一个Pod是否属于当前StatefulSet。
        参数：
            - pod (kubernetes.client.models.V1Pod): Kubernetes Pod对象。
        返回值（bool）：
            - True，如果Pod的ownerReference中包含了当前StatefulSet；
            - False，否则。
        """
        owner_sts = pod.owner_reference(consts.APP_V1_API_VERSION, consts.APP_V1_STATEFULSET_KIND)
        if owner_sts:
            return owner_sts.name == self.name
        else:
            owner_sts = pod.owner_reference(consts.KRUISE_API_VERSION, consts.KRUISE_STATEFULSET_KIND)
            if owner_sts:
                return owner_sts.name == self.name
            else:
                return False

    def get_pod(self, index) -> 'XDBPod':
        """
            获取指定索引的Pod对象，并返回一个XDBPod实例。
        
        Args:
            index (int): Pod的索引值，从0开始。
        
        Returns:
            XDBPod (str): XDBPod类型的对象，包含了Pod的信息。
        
        Raises:
             None.
        """
        pod = cast(api_client.V1Pod, api_core.read_namespaced_pod(
            "%s-%i" % (self.name, index), self.namespace))
        return XDBPod(pod)

    def get_sts(self) -> 'XDBSts':
        """
        获取sts对象
        """
        sts = cast(api_client.V1StatefulSet, api_apps.read_namespaced_stateful_set(
            self.name, self.namespace))
        return XDBSts(sts)

    def get_pods(self) -> typing.List['XDBPod']:
        """
            获取所有属于同一容器的 Pod。
        返回值为包含 'XDBPod' 对象的列表，按照索引顺序排序。
        
        Args:
            None
        
        Returns:
            List[XDBPod]: 包含 'XDBPod' 对象的列表，按照索引顺序排序。
        """
        objects = cast(api_client.V1PodList, api_core.list_namespaced_pod(
            self.namespace, label_selector=f"karrier.abcstack.com/component=xdb,karrier.abcstack.com/app={self.name}"))
        pods = []
        for o in objects.items:
            pod = XDBPod(o)
            if self.owns_pod(pod):
                pods.append(pod)
        pods.sort(key=lambda pod: pod.index)
        return pods

    def get_service(self) -> typing.Optional[api_client.V1Service]:
        """
            获取数据库代理服务，如果不存在则返回None。
        如果读取失败，将抛出ApiException异常。
        
        Args:
            None
        
        Returns:
            typing.Optional[api_client.V1Service]: 返回一个包含数据库代理服务信息的V1Service对象，如果不存在则返回None。
        
        Raises:
            None
        """
        try:
            return cast(api_client.V1Service,
                        api_core.read_namespaced_service(self.name, self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise
    def get_master_service(self):
        """
        获取直连MySQL主节点的SVC
        Returns:
            _type_: _description_
        """
        try:
            return cast(api_client.V1Service,
                        api_core.read_namespaced_service(self.name + "-mysql-master", self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def get_xdbcluster_stateful_set(self) -> typing.Optional[api_client.V1StatefulSet]:
        """
        获取XDBCluster的StatefulSet对象，优先尝试获取传统的StatefulSet，如果不存在则尝试获取Kruise StatefulSet。
            如果发生API异常并且状态码为404，则返回None；否则抛出异常。
        
            Args:
                self (XDBCluster): XDBCluster实例。
        
            Returns:
                Optional[api_client.V1StatefulSet]: StatefulSet对象，如果不存在则返回None。
                    API异常时，如果状态码为404，则返回None；其他情况抛出异常。
        """
        try:
            # 首先尝试获取传统的 StatefulSet
            return cast(api_client.V1StatefulSet,
                        api_apps.read_namespaced_stateful_set(self.name,
                                                              self.namespace))
        except ApiException as e:
            if e.status == 404:
                try:
                    # 如果传统的 StatefulSet 不存在，尝试获取 Kruise StatefulSet
                    kruise_sts = api_kruise.get_namespaced_custom_object(
                        group=consts.KRUISE_GROUP,
                        version=consts.KRUISE_VERSION,
                        namespace=self.namespace,
                        plural=consts.KRUISE_STATEFULSET_PLURAL,
                        name=self.name
                    )
                    return cast(api_client.V1StatefulSet, kruise_sts)
                except ApiException as e2:
                    if e2.status == 404:
                        return None
                    raise e2
            raise e

    def get_stateful_set(self) -> typing.Optional[api_client.V1StatefulSet]:
        """
        获取当前 StatefulSet，优先尝试获取传统的StatefulSet，如果不存在则尝试获取Kruise StatefulSet。
        
            Args:
                self (K8sResource): K8sResource 实例，表示当前资源对象。
        
            Returns:
                typing.Optional[api_client.V1StatefulSet]: V1StatefulSet 类型的可选对象，表示当前 StatefulSet，如果不存在则为 None。
        
            Raises:
                ApiException (ApiException): 如果 API 调用失败，将抛出 ApiException 异常，包含 HTTP 状态码和错误信息。
        """
        try:
            # 首先尝试获取传统的 StatefulSet
            return cast(api_client.V1StatefulSet,
                        api_apps.read_namespaced_stateful_set(self.name, self.namespace))
        except ApiException as e:
            if e.status == 404:
                try:
                    # 如果传统的 StatefulSet 不存在，尝试获取 Kruise StatefulSet
                    kruise_sts = api_kruise.get_namespaced_custom_object(
                        group=consts.KRUISE_GROUP,
                        version=consts.KRUISE_VERSION,
                        namespace=self.namespace,
                        plural=consts.KRUISE_STATEFULSET_PLURAL,
                        name=self.name
                    )
                    return cast(api_client.V1StatefulSet, kruise_sts)
                except ApiException as e2:
                    if e2.status == 404:
                        return None
                    raise e2
            raise e

    def get_headless_service(self) -> typing.Optional[api_client.V1Service]:
        """
            获取无头服务（Headless Service），如果不存在则返回None。
        无头服务是一种特殊的Kubernetes服务，允许多个Pod共享同一个IP地址和端口号。
        
        Args:
            None
        
        Returns:
            Optional[api_client.V1Service]: 无头服务对象，如果不存在则返回None。
        
        Raises:
            None
        """
        try:
            return cast(api_client.V1Service,
                        api_core.read_namespaced_service(self.name + "-hs", self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def get_read_replica_service(self, name: str) -> typing.Optional[api_client.V1Service]:
        """
        获取指定名称的读取副本服务，如果不存在则返回None。
        
        Args:
            name (str): 服务名称，包括后缀"-replicas"。
        
        Returns:
            Optional[api_client.V1Service]: 返回一个V1Service对象，表示读取副本服务，如果不存在则返回None。
        
        Raises:
            ApiException: 当API调用失败时会引发ApiException异常，状态码为404表示服务不存在。
        """
        try:
            return cast(api_client.V1Service,
                        api_core.read_namespaced_service(name + "-replicas", self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def delete_configmap(self, cm_name: str) -> typing.Optional[api_client.V1Status]:
        """
            删除指定命名空间下的 ConfigMap。如果不存在，则返回 None。
        如果发生异常（例如 ConfigMap 正在被使用），将抛出 ApiException。
        
        Args:
            cm_name (str): ConfigMap 的名称。
        
        Returns:
            Optional[api_client.V1Status]: 如果成功删除，返回一个 V1Status 对象；否则返回 None。
        
        Raises:
            ApiException: 如果发生 API 请求异常，包括 404（ConfigMap 不存在）。
        """
        try:
            body = api_client.V1DeleteOptions(grace_period_seconds=0)
            status = cast(api_client.V1Status,
                          api_core.delete_namespaced_config_map(cm_name, self.namespace, body=body))
            return status
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def get_configmap(self, cm_name: str) -> typing.Optional[api_client.V1ConfigMap]:
        """
            获取指定名称的 ConfigMap，如果不存在则返回 None。
        如果访问失败（例如 ConfigMap 不存在）将抛出 ApiException。
        
        Args:
            cm_name (str): ConfigMap 的名称。
        
        Returns:
            Optional[api_client.V1ConfigMap]: ConfigMap 对象或者 None，如果不存在。
            如果访问失败，将抛出 ApiException。
        """
        try:
            cm = cast(api_client.V1ConfigMap,
                      api_core.read_namespaced_config_map(cm_name, self.namespace))
            return cm
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def get_secret(self, s_name: str) -> typing.Optional[api_client.V1Secret]:
        """
        获取指定名称的 Secret，如果不存在则返回 None。
        
        Args:
            s_name (str): Secret 的名称。
        
        Returns:
            Optional[api_client.V1Secret]: 返回一个 V1Secret 对象，如果不存在则返回 None。
        
        Raises:
            ApiException (ApiException): 当 API 调用失败时抛出该异常，包括 HTTP 状态码为 404（未找到）时也会抛出此异常。
        """
        try:
            cm = cast(api_client.V1Secret,
                      api_core.read_namespaced_secret(s_name, self.namespace))
            return cm
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    @classmethod
    def get_xdb_cm(cls, spec: AbstractServerSetSpec) -> typing.Optional[api_client.V1ConfigMap]:
        """
            获取指定服务器集合的XDB配置信息，返回一个V1ConfigMap对象或None。如果读取失败，将抛出ApiException异常。
        如果XDB配置不存在，则返回None。
        
        Args:
            spec (AbstractServerSetSpec): 服务器集合的规格信息，包含名称和命名空间。
        
        Returns:
            typing.Optional[api_client.V1ConfigMap]: V1ConfigMap对象，包含XDB配置信息；如果XDB配置不存在，则返回None。
        
        Raises:
            ApiException: 当读取XDB配置时发生API异常，并且异常状态码不是404（未找到）时抛出该异常。
        """
        try:
            return cast(api_client.V1ConfigMap,
                        api_core.read_namespaced_config_map(spec.name, spec.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def _get_status_field(self, field: str) -> typing.Any:
        """
            获取状态字段的值，如果不存在则返回None。
        参数field (str) - 需要获取的状态字段名称，例如"state"或"message"等。
        返回值 (typing.Any) - 状态字段对应的值，如果不存在则返回None。
        """
        return cast(str, self.status.get(field))

    def _set_status_field(self, field: str, value: typing.Any) -> None:
        """
            设置状态字段的值，并更新对象。如果对象没有状态字段，则创建一个空状态字段。
        参数：
            - field (str): 要设置的字段名称。
            - value (typing.Any): 要设置的字段值。
        返回值：
            - None（无返回值）
        """
        obj = self._get(self.namespace, self.name)
        if "status" not in obj:
            patch = {"status": {}}
        else:
            patch = {"status": obj["status"]}
        patch["status"][field] = value
        self.obj = self._patch_status(self.namespace, self.name, patch)

    def set_cluster_status(self, cluster_status) -> None:
        """
            设置集群状态。
        
        Args:
            cluster_status (str): 集群的状态，可以是 "running"、"stopped" 或 "terminated"。
        
        Returns:
            None: 无返回值，直接修改了对象内部状态。
        """
        self._set_status_field("cluster", cluster_status)

    def get_cluster_status(self, field=None):
        """
        获取crd 资源的status dict
        :param field:
        :return:
        """
        status = self._get_status_field("cluster")
        if status and field:
            return status.get(field)
        return status

    @property
    def cluster_status(self):
        """
        获取集群状态
        """
        return self.get_cluster_status("status")

    @property
    def online_instances(self) -> int:
        """
        获取在线实例数
        """
        sts = self.get_stateful_set()
        if isinstance(sts, api_client.V1StatefulSet):
            return sts.status.ready_replicas
        elif isinstance(sts, dict):
            return sts["status"]["readyReplicas"]
        else:
            raise ValueError(f"Invalid sts type: {type(sts)}")  

    @property
    def cluster_sts_ready(self) -> bool:
        """
        获取 sts 状态,主要是副本数与ready 数量是否一致
        """
        sts = self.get_stateful_set()
        if not sts:
            logger.warning(f"StatefulSet for cluster {self.name} not found, assuming not ready.")
            return False

        if isinstance(sts, api_client.V1StatefulSet):
            if not sts.status:
                logger.info(f"StatefulSet {self.name} has no status field yet.")
                return False
            # ready_replicas is optional, defaults to 0 if not present.
            ready_replicas = sts.status.ready_replicas if sts.status.ready_replicas is not None else 0
            return ready_replicas == sts.status.replicas
        elif isinstance(sts, dict):
            status = sts.get("status")
            if not status:
                logger.info(f"Kruise StatefulSet {self.name} has no status field yet. sts={sts}")
                return False

            replicas = status.get("replicas")
            if replicas is None:
                logger.info(f"Kruise StatefulSet {self.name} status has no 'replicas' field. status={status}")
                return False

            # readyReplicas might not be present if there are no ready replicas, so default to 0.
            ready_replicas = status.get("readyReplicas", 0)
            
            return ready_replicas == replicas
        else:
            logger.error(f"Invalid sts type for cluster {self.name}: {type(sts)}")
            raise ValueError(f"Invalid sts type: {type(sts)}")

    def set_status(self, status) -> None:
        """
            设置资源的状态，如果不存在则创建，否则进行合并更新。
        
        Args:
            status (dict): 需要设置的状态信息，格式为字典类型。
        
        Returns:
            None: 无返回值，直接修改对象的状态。
        """
        obj = cast(dict, self._get(self.namespace, self.name))

        if "status" not in obj:
            obj["status"] = status
        else:
            utils.merge_patch_object(obj["status"], status)
        self.obj = self._patch_status(self.namespace, self.name, obj)

    def update_cluster_info(self, info: dict) -> None:
        """
        Set metadata about the cluster as an annotation.
        Information consumed by ourselves to manage the cluster should go here.
        Information consumed by external observers should go in status.
        """
        patch = {
            "metadata": {
                "annotations": {
                    "cloudbed.abcstack.com/cluster-info": json.dumps(info)
                }
            }
        }
        self.obj = self._patch(self.namespace, self.name, patch)

    # TODO remove field
    def get_cluster_info(self, field: typing.Optional[str] = None) -> typing.Optional[dict]:
        """
            获取集群信息，可选指定字段。如果没有指定字段，则返回所有信息。
        如果集群信息不存在或解析失败，则返回None。
        
        Args:
            field (str, optional): 指定需要获取的字段名，默认为None，表示获取所有信息。 Default is None.
        
        Returns:
            dict, optional: 集群信息，包含指定的字段或所有字段，如果集群信息不存在或解析失败，则返回None。 Default is None.
        """
        if self.annotations:
            info = self.annotations.get("cloudbed.abcstack.com/cluster-info", None)
            if info:
                info = json.loads(info)
                if field:
                    return info.get(field)
                return info
        return None

    def set_create_time(self, time: datetime.datetime = None) -> None:
        """
            设置创建时间，如果未传入时间则使用当前时间。
        默认情况下，时间是以UTC时区的ISO8601格式表示的，不包括微秒信息。
        
        Args:
            time (datetime.datetime, optional): 要设置的时间，默认为None，此时将使用当前时间。 Default to None.
        
        Returns:
            None: 无返回值，直接修改了对象的状态字段。
        """
        if not time:
            time = datetime.datetime.now()
        self._set_status_field("createTime", time.replace(
            microsecond=0).isoformat() + "Z")

    def get_create_time(self) -> Optional[datetime.datetime]:
        """
            获取创建时间，返回值为datetime.datetime类型或None。如果不存在则返回None。
        
        Args:
            None
        
        Returns:
            Optional[datetime.datetime]: 创建时间，datetime.datetime类型；如果不存在则返回None。
        """
        dt = self._get_status_field("createTime")
        if dt:
            return datetime.datetime.fromisoformat(dt.rstrip("Z"))
        return None

    @property
    def ready(self) -> bool:
        """
            判断当前对象是否已经准备好，可以进行操作。
        如果对象还未创建或初始化完成，则返回 False；否则返回 True。
        
        Returns:
            bool (bool): 若对象已准备好，返回 True；否则返回 False。
        """
        return cast(bool, self.get_create_time())

    def set_last_known_quorum(self, members):
        """
            设置最后一次知道的仲裁成员。
        该函数暂时未实现，请稍后更新。
        
        Args:
            members (list[str]): 仲裁成员列表，包含每个成员的IP地址和端口号。
        
        Returns:
            None.
        
        Raises:
            None.
        """
        # TODO
        pass

    def get_last_known_quorum(self):
        """
            获取最后一次知道的领导者投票结果，如果没有则返回None。
        该方法目前还未实现，请勿使用。
        
        Returns:
            Optional[int]: 最后一次知道的领导者投票结果，如果没有则返回None。
        """
        # TODO
        return None

    def _add_finalizer(self, fin: str) -> None:
        """
        Add the named token to the list of finalizers for the cluster object.
        The cluster object will be blocked from deletion until that token is
        removed from the list (remove_finalizer).
        """
        patch = {
            "metadata": {
                "finalizers": [fin]
            }
        }
        self.obj = self._patch(self.namespace, self.name, patch)

    def _remove_finalizer(self, fin: str) -> None:
        """
            从对象的 finalizers 列表中移除指定的 finalizer。
        如果该 finalizer 不存在于列表中，则不执行任何操作。
        
        Args:
            fin (str): 要移除的 finalizer 名称。
        
        Returns:
            None: 无返回值，直接修改了对象的 finalizers 列表。
        """
        # TODO strategic merge patch not working here??
        # patch = { "metadata": { "$deleteFromPrimitiveList/finalizers": [fin] }}
        patch = {"metadata": {"finalizers": [
            f for f in self.metadata["finalizers"] if f != fin]}}

        self.obj = self._patch(self.namespace, self.name, patch)

    def add_cluster_finalizer(self) -> None:
        """
            添加集群的最终化器，用于在执行完成后释放资源。
        该方法只能在构造函数中调用一次。
        
        Args:
            None
        
        Returns:
            None, 无返回值
        """
        self._add_finalizer("cloudbed.abcstack.com/cluster")

    def remove_cluster_finalizer(self, cluster_body: dict = None) -> None:
        """
            移除集群的最终化器，如果提供了cluster_body参数则修改其内部使用的JSON数据以更新最终化列表。
        如果最终化器已经被删除，则不会再次尝试删除。
        
        Args:
            cluster_body (dict, optional): 包含集群元数据的字典，默认为None。可选，但建议传入以更新其内部使用的JSON数据。
        
        Raises:
            无
        
        Returns:
            None: 无返回值，直接在函数中执行操作。
        """
        self._remove_finalizer("cloudbed.abcstack.com/cluster")
        if cluster_body:
            # modify the JSON data used internally by kopf to update its finalizer list
            cluster_body["metadata"]["finalizers"].remove(
                "cloudbed.abcstack.com/cluster")

    def set_operator_version(self, version: str) -> None:
        """
            设置操作员版本号。如果不同于当前的版本号，则更新对象的元数据中的注释。
        该函数会修改对象的属性，因此需要在调用后立即使用新的版本号。
        
        Args:
            version (str): 操作员版本号，字符串类型。
        
        Returns:
            None: 无返回值，直接修改了对象的属性。
        """
        v = self.operator_version
        if v != version:
            patch = {"metadata": {"annotations": {"cloudbed.abcstack.com/xdb-operator-version": version}}}

            # TODO store the current server/router version + timestamp
            # store previous versions in a version history log
            self.obj = self._patch(self.namespace, self.name, patch)

    @property
    def operator_version(self) -> Optional[str]:
        """
            获取操作符版本号，如果不存在则返回None。
        该属性只有在运行时才能使用，因为它需要访问到运行时的元数据信息。
        
        Args:
            None
        
        Returns:
            Optional[str]: 操作符版本号字符串，如果不存在则返回None。
        """
        return self.metadata.get("cloudbed.abcstack.com/xdb-operator-version")

    def set_current_version(self, version: str) -> None:
        """
            设置当前版本号，如果不同则更新状态字段。
        
        Args:
            version (str): 要设置的版本号。
        
        Returns:
            None: 无返回值，直接修改对象状态字段。
        """
        v = self.status.get("version")
        if v != version:
            patch = {"status": {"version": version}}

            # TODO store the current server/router version + timestamp
            # store previous versions in a version history log
            self.obj = self._patch_status(self.namespace, self.name, patch)

    def log_cluster_info(self, logger: Logger) -> None:
        """
            记录集群信息到日志中。
        
        Args:
            logger (Logger): 一个Logger对象，用于记录日志。
        
        Returns:
            None: 无返回值，直接修改传入的logger对象。
        """
        logger.info(f"InnoDB Cluster {self.namespace}/{self.name} Version({self.parsed_spec.mysqlVersion}) ")

    @property
    def replica_topo(self):
        """
        返回拓扑 server
        Returns:
            _type_: _description_
        """
        return XDBClusterZKServer(self) 
    
    @property
    def zk_server(self):
        """
        返回拓扑 server
        Returns:
            _type_: _description_
        """
        return  XDBClusterZKServer(self) 

def get_all_clusters(ns: str = None) -> typing.List[XDBCluster]:
    """
    获取所有集群，可选指定命名空间。如果未指定命名空间，则返回全部集群；否则返回指定命名空间下的所有集群。
    
    Args:
        ns (str, optional): 命名空间字符串，默认为None，表示不指定命名空间。默认为None。
    
    Returns:
        List[XDBCluster]: XDBCluster类型的列表，包含所有集群信息。
    """
    if ns is None:
        objects = cast(dict, api_customobj.list_cluster_custom_object(
            consts.GROUP, consts.VERSION, consts.XDBFUSIONCLUSTER_PLURAL))
    else:
        objects = cast(dict, api_customobj.list_namespaced_custom_object(
            consts.GROUP, consts.VERSION, ns, consts.XDBFUSIONCLUSTER_PLURAL))
    return [XDBCluster(o) for o in objects["items"]]


class XDBSts(K8sInterfaceObject):
    """
    xdb cluster sts
    """
    logger: Optional[Logger] = None

    def __init__(self, sts: client.V1StatefulSet):
        """
            初始化函数，用于初始化StatefulSet对象。
        
        Args:
            sts (client.V1StatefulSet): StatefulSet对象，包含了StatefulSet的相关信息。
            需要包括以下字段：metadata、spec、status。
        
        Returns:
            None: 无返回值，直接初始化StatefulSet对象。
        """
        super().__init__()
        self.sts: client.V1StatefulSet = sts

    def __str__(self) -> str:
        """
            返回字符串格式的对象，通常用于打印或日志。
        默认实现是返回对象名称。
        
        Returns:
            str - 字符串格式的对象。
        """
        return self.name

    def __repr__(self) -> str:
        """
            返回字符串表示当前对象。
        格式为 "<XDBSts name>"，其中 "name" 是当前对象的名称。
        
        Returns:
            str - 一个字符串，表示当前对象。
        """
        return f"<XDBSts {self.name}>"


    def __eq__(self, other):
        return self.name == other.name
    
    @classmethod
    def read(cls, name: str, ns: str) -> 'XDBSts':
        """
            读取指定名称和命名空间的 StatefulSet，并返回一个 XDBSts 实例。
        
        Args:
            name (str): StatefulSet 的名称。
            ns (str): StatefulSet 所在的命名空间。
        
        Returns:
            XDBSts (XDBSts): 包含了 StatefulSet 信息的 XDBSts 实例。
        
        Raises:
            无。
        """
        return XDBSts(cast(api_client.V1StatefulSet,
                           api_apps.read_namespaced_stateful_set(name, ns)))

    @property
    def metadata(self) -> api_client.V1ObjectMeta:
        """
            返回一个 V1ObjectMeta 对象，包含了 StatefulSet 的元数据信息。
        
        Returns:
            api_client.V1ObjectMeta (Union[str, int]): V1ObjectMeta 对象，包含了 StatefulSet 的元数据信息。
                返回值类型为 Union[str, int]，表示可能会出现字符串或整数类型的错误信息。
        """
        return cast(api_client.V1ObjectMeta, self.sts.metadata)

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        """
            返回一个包含对象自身引用信息的字典，可选地指定字段路径。
        如果字段路径被指定，则将其添加到字典中。
        
        Args:
            field_path (Optional[str], optional): 字段路径，默认为None。可选参数。默认值为None。
        
        Returns:
            dict: 包含对象自身引用信息的字典，格式如下：
                {
                    "apiVersion": str,  # 对象API版本
                    "kind": str,  # 对象种类
                    "name": str,  # 对象名称
                    "namespace": str,  # 对象命名空间
                    "resourceVersion": str,  # 资源版本号
                    "uid": str,  # 对象UID
                    "fieldPath": Optional[str]  # 可选项，字段路径，默认为None
                }
        """
        ref = {
            "apiVersion": self.sts.api_version,
            "kind": self.sts.kind,
            "name": self.name,
            "namespace": self.namespace,
            "resourceVersion": self.metadata.resource_version,
            "uid": self.metadata.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    @property
    def status(self) -> api_client.V1StatefulSetStatus:
        """
            获取状态副本集的状态信息，返回类型为V1StatefulSetStatus。
        
        Returns:
            api_client.V1StatefulSetStatus (Union[str, int]): 状态副本集的状态信息，包括当前可用副本数、期望副本数等信息，返回类型为V1StatefulSetStatus。
        """
        return cast(api_client.V1StatefulSetStatus, self.sts.status)

    @property
    def namespace(self) -> str:
        """
            获取当前对象的命名空间，如果没有设置则返回None。
        
        Returns:
            str, optional - 返回字符串类型，表示当前对象的命名空间，如果没有设置则返回None。
        """
        return cast(str, self.metadata.namespace)

    @property
    def deleting(self) -> bool:
        """
            判断当前对象是否处于删除状态，返回布尔值。
        如果对象的 metadata 字段中包含 deletion_timestamp 属性，则认为该对象正在被删除。
        
        Returns:
            bool (bool): 如果对象正在被删除，则返回 True；否则返回 False。
        """
        return self.metadata.deletion_timestamp is not None

    @property
    def spec(self) -> api_client.V1PodSpec:
        """
            返回一个包含当前 StatefulSet 的 Pod 规格信息的 V1PodSpec 对象。
        
        返回值 (V1PodSpec):
            - 包含当前 StatefulSet 的 Pod 规格信息，包括容器、卷等相关信息。
        
        Returns:
            api_client.V1PodSpec (Union[Dict[str, Any], List[Any]]): V1PodSpec 对象，包含 Pod 规格信息。
        
        Raises:
            无。
        """
        return cast(api_client.V1PodSpec, self.sts.spec)

    @property
    def name(self) -> str:
        """
            返回模型的名称，该名称是在创建模型时指定的。
        
        返回值：str，模型的名称。
        
        Raises:
            无。
        
        Returns:
            str - 模型的名称。
        """
        return cast(str, self.metadata.name)

    @property
    def ready(self):
        """
            返回 True，当所有副本都已就绪时。否则返回 False。
        
        Returns
        -------
        bool
            True，当所有副本都已就绪时；False，否则。
        """
        return self.status.ready_replicas == self.status.replicas


class XDBPod(K8sInterfaceObject):
    """
    xdb cluster pod
    """
    logger: Optional[Logger] = None

    def __init__(self, pod: client.V1Pod):
        """
            初始化一个 MySQLPodInfo 对象。
        
        Args:
            pod (client.V1Pod): Kubernetes Pod 对象，包含了 MySQL 的信息。
        
        Returns:
            None; 无返回值，直接初始化一个 MySQLPodInfo 对象。
        """
        super().__init__()

        self.pod: client.V1Pod = pod

        self.mysql_port = 3203
        self.dbproxy_port = 6203
        self.zm_port = 1234
        self.admin_account = None

    @overload
    @classmethod
    def from_json(cls, pod: str) -> 'XDBPod':
        """
            从 JSON 字符串中解析出 XDBPod 对象。
        
        Args:
            pod (str): JSON 格式的字符串，包含 XDBPod 的相关信息。
        
        Returns:
            XDBPod (str): 返回一个 XDBPod 对象，包含解析出来的信息。
        
        Raises:
            None.
        """
        ...

    @overload
    @classmethod
    def from_json(cls, pod: Body) -> 'XDBPod':
        """
            从 JSON 对象中创建一个 XDBPod 实例。
        
        Args:
            pod (Body, str): JSON 对象或字符串，包含 XDBPod 的信息。如果是字符串，将会被解析为 JSON 对象。
        
        Returns:
            XDBPod (str): 返回一个 XDBPod 实例，其属性与传入的 JSON 对象相同。
        
        Raises:
            TypeError: 如果传入的不是字典或字符串类型。
            ValueError: 如果传入的字符串无法解析为有效的 JSON 对象。
        """
        ...

    @classmethod
    def from_json(cls, pod) -> 'XDBPod':
        """
            将字典类型的 pod 转换为 XDBPod 对象，如果传入的不是字符串类型，则会先序列化为字符串再进行反序列化。
        该方法主要用于从 JSON 格式的字符串中解析出 XDBPod 对象。
        
        Args:
            pod (Union[Dict[str, Any], str]): 包含 Pod 信息的字典或者 JSON 格式的字符串。
        
        Returns:
            XDBPod (Union[Dict[str, Any], str]): 返回一个 XDBPod 对象，包含了原始的 Pod 信息。
        
        Raises:
            无。
        """

        class Wrapper:
            """
            包装器
            """

            def __init__(self, data):
                self.data = json.dumps(data)

        if not isinstance(pod, str):
            pod = eval(str(pod))

        return XDBPod(cast(client.V1Pod, api_core.api_client.deserialize(
            Wrapper(pod), client.V1Pod)))

    def __str__(self) -> str:
        """
            返回字符串格式的对象，通常用于打印或日志。
        默认实现是返回对象名称。
        
        Returns:
            str - 字符串格式的对象。
        """
        return self.name

    def __repr__(self) -> str:
        """
            返回字符串表示形式，格式为 "<XDBPod name>"。
        
        Returns:
            str - 一个包含 XDBPod 名称的字符串。
        """
        return f"<XDBPod {self.name}>"

    @classmethod
    def read(cls, name: str, ns: str) -> 'XDBPod':
        """
            读取指定名称和命名空间的 Pod。
        
        Args:
            name (str): Pod 的名称。
            ns (str): Pod 所在的命名空间。
        
        Returns:
            XDBPod (XDBPod): 包含了读取到的 Pod 信息的 XDBPod 对象。
        
        Raises:
             None.
        """
        return XDBPod(cast(client.V1Pod,
                           api_core.read_namespaced_pod(name, ns)))

    @property
    def metadata(self) -> api_client.V1ObjectMeta:
        """
            返回Pod的元数据信息，包括名称、标签等。
        
        Returns:
            api_client.V1ObjectMeta (Union[Dict[str, Any], List[Any]]): Pod的元数据信息，类型为Dict或List，包含名称、标签等信息。
        
        Raises:
             None.
        
        Returns:
            Union[Dict[str, Any], List[Any]]: Pod的元数据信息，包括名称、标签等信息。
        """
        return cast(api_client.V1ObjectMeta, self.pod.metadata)

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        """
            返回一个包含自身引用信息的字典，可选地指定字段路径。
        如果字段路径被提供，则将其添加到字典中。
        
        Args:
            field_path (Optional[str], optional): 字段路径（默认为None）. Defaults to None.
        
        Returns:
            dict: 包含自身引用信息的字典，格式如下：
                {
                    "apiVersion": str,  # API版本
                    "kind": str,  # Kubernetes资源类型
                    "name": str,  # 名称
                    "namespace": str,  # 命名空间
                    "resourceVersion": str,  # 资源版本号
                    "uid": str,  # UID
                    "fieldPath": Optional[str]  # 可选的字段路径
                }
        """
        ref = {
            "apiVersion": self.pod.api_version,
            "kind": self.pod.kind,
            "name": self.name,
            "namespace": self.namespace,
            "resourceVersion": self.metadata.resource_version,
            "uid": self.metadata.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    @property
    def status(self) -> api_client.V1PodStatus:
        """
            获取当前 Pod 的状态，返回类型为 V1PodStatus。
        如果 Pod 不存在或者还未被创建，则返回 None。
        
        Returns:
            api_client.V1PodStatus (Optional): 当前 Pod 的状态，包括状态码、容器运行时间等信息。如果 Pod 不存在或者还未被创建，则返回 None。
        """
        return cast(api_client.V1PodStatus, self.pod.status)

    @property
    def node_id(self) -> str:
        """
            获取当前Pod所属节点的ID。
        
        Returns:
            str (str): 当前Pod所属节点的ID。
        """
        return f"node_{self.pod_ip_address}_{self.mysql_port}"
        
    @property
    def phase(self) -> str:
        """
            获取当前Pod的状态，返回值为字符串类型。
        可能的返回值包括："Pending", "Running", "Succeeded", "Failed", "Unknown"。
        
        Returns:
            str (str): Pod的当前状态，可能的值有："Pending", "Running", "Succeeded", "Failed", "Unknown"。
        """
        return cast(str, self.status.phase)

    @property
    def deleting(self) -> bool:
        """
            判断当前对象是否处于删除状态，返回布尔值。
        如果对象的 metadata 字段中包含 deletion_timestamp 属性，则认为该对象正在被删除。
        
        Returns:
            bool (bool): 如果对象正在被删除，则返回 True；否则返回 False。
        """
        return self.metadata.deletion_timestamp is not None

    @property
    def spec(self) -> api_client.V1PodSpec:
        """
            获取当前 Pod 的规格信息，返回类型为 V1PodSpec。
        
        返回值：
            - V1PodSpec (api_client.V1PodSpec) - Pod 的规格信息，包括容器、卷等相关配置。
        
        Returns:
            api_client.V1PodSpec (Union[Dict[str, Any], List[Any]]) - Pod 的规格信息。
        """
        return cast(api_client.V1PodSpec, self.pod.spec)

    @property
    def name(self) -> str:
        """
            返回模型的名称，该名称是在创建模型时指定的。
        
        返回值：str，模型的名称。
        
        Raises:
            无。
        
        Returns:
            str - 模型的名称。
        """
        return cast(str, self.metadata.name)

    @property
    def index(self) -> int:
        """
            返回当前对象的索引，即从0开始的整数。
        如果名称不符合规则，将会抛出ValueError异常。
        
        Returns:
            int (int): 当前对象的索引。
        
        Raises:
            ValueError: 如果名称不符合规则。
        """
        return int(self.name.rpartition("-")[-1])

    @property
    def namespace(self) -> str:
        """
            获取当前对象的命名空间，如果没有设置则返回None。
        
        Returns:
            str, optional - 返回字符串类型，表示当前对象的命名空间，如果没有设置则返回None。
        """
        return cast(str, self.metadata.namespace)

    @property
    def cluster_name(self) -> str:

        return self.pod.metadata.labels["cloudbed.abcstack.com/cluster"]
    
    @property
    def role(self) -> str:
        """
        获取POD当前的角色
        Returns:
            str: _description_
        """
        return self.pod.metadata.labels.get("role", "")

    @property
    def instance_type(self) -> str:
        """
            获取实例类型，如果存在标签 cloudbed.abcstack.com/instance-type，则返回该值；否则返回 "group-member"。
        返回值类型：str，可能的值为 "group-member"、"instance-type1"、"instance-type2"等。
        
        Args:
            无参数，直接调用。
        
        Returns:
            str (str): 实例类型，可能的值为 "group-member"、"instance-type1"、"instance-type2"等。
        """
        if "cloudbed.abcstack.com/instance-type" in self.pod.metadata.labels:
            return self.pod.metadata.labels["cloudbed.abcstack.com/instance-type"]
        else:
            # With old clusters the label may be missing
            return "group-member"

    @property
    def read_replica_name(self) -> str:
        """
            获取当前 Pod 的读取副本名称，该名称是一个标签，格式为 "cloudbed.abcstack.com/read-replica"。
        返回值类型：str，返回值为字符串类型。
        
        Returns:
            str (str) - 当前 Pod 的读取副本名称，格式为 "cloudbed.abcstack.com/read-replica"。
        """
        return self.pod.metadata.labels["cloudbed.abcstack.com/read-replica"]

    @property
    def address(self) -> str:
        """
            返回该对象的地址，格式为"名称.域名"。
        其中名称是对象的名称，域名是对象所属的域名。
        
        Returns:
            str (str): 对象的地址，格式为"名称.域名"。
        """
        return self.name + "." + cast(str, self.spec.subdomain)

    @property
    def address_fqdn(self) -> str:
        """
            获取地址的完全限定域名（FQDN）。
        返回值：str，格式为 "<name>.<subdomain>.<namespace>.svc.<cluster-domain>"。
        
        Args:
            无参数。
        
        Returns:
            str (str): 地址的FQDN字符串。
        """
        return self.name + "." + cast(str, self.spec.subdomain) + "." + self.namespace + ".svc." + k8s_cluster_domain(
            self.logger)

    @property
    def pod_ip_address(self) -> str:
        """
            获取当前 Pod 的 IP 地址。
        
        返回值：str（字符串） - Pod 的 IP 地址，如果 Pod 没有 IP 则为空字符串。
        
        """
        return self.pod.status.pod_ip

    def reload(self) -> None:
        """
            重新加载 Pod 信息。
        如果 Pod 不存在，则会引发异常。
        
        Args:
            None
        
        Returns:
            None: 无返回值，直接修改了实例的属性。
        
        Raises:
            None: 该方法没有引发任何异常。
        """
        self.pod = cast(api_client.V1Pod, api_core.read_namespaced_pod(
            self.name, self.namespace))

    def owner_reference(self, api_version, kind) -> typing.Optional[api_client.V1OwnerReference]:
        """
        获取指定API版本和类型的所有者引用，如果没有则返回None。
        
        Args:
            api_version (str): API版本名称。
            kind (str): Kubernetes资源类型名称。
        
        Returns:
            Optional[api_client.V1OwnerReference]: 返回一个包含所有者引用信息的V1OwnerReference对象，如果找不到则返回None。
        """
        for owner in self.metadata.owner_references:
            if owner.api_version == api_version and owner.kind == kind:
                return owner

        return None

    def get_cluster(self):
        """
            获取集群信息，如果不存在则返回None。
        如果出现API异常并且状态码为404，则返回None；否则抛出异常。
        
        Args:
            无参数。
        
        Returns:
            XDBCluster (Optional[XDBCluster]): 集群对象，可能为None。如果集群不存在，则返回None。
        
        Raises:
            ApiException (ApiException): API请求失败时抛出的异常，包含错误代码和消息。
                如果集群不存在，则错误代码为404。
            Exception (Exception): 其他情况下抛出的异常。
        """
        try:
            return XDBCluster.read(self.namespace, self.cluster_name)
        except ApiException as e:
            print(
                f"Could not get cluster {self.namespace}/{self.cluster_name}: {e}")
            if e.status == 404:
                return None
            raise

    def check_condition(self, cond_type: str) -> typing.Optional[bool]:
        """
            检查指定类型的条件是否存在，如果存在则返回True，否则返回None。
        如果状态中有多个同类型的条件，只会返回第一个。
        
        Args:
            cond_type (str): 要检查的条件类型，例如"Ready"、"Available"等。
        
        Returns:
            Optional[bool]: 如果找到了指定类型的条件，并且状态为True，则返回True；否则返回None。
        """
        if self.status and self.status.conditions:
            for c in self.status.conditions:
                if c.type == cond_type:
                    return c.status == "True"
        return None

    def check_containers_ready(self) -> typing.Optional[bool]:
        """
            检查容器是否就绪。
        如果所有的容器都已准备好，则返回 True；否则返回 None。
        
        Args:
            None
        
        Returns:
            Optional[bool]: 如果所有的容器都已准备好，则返回 True；否则返回 None。
        """
        return self.check_condition("ContainersReady")

    def check_container_ready(self, container_name: str) -> typing.Optional[bool]:
        """
            检查容器是否就绪，返回一个布尔值或者None。如果容器不存在，则返回None。
        该方法会遍历Pod的所有ContainerStatus，并且只要找到指定名称的ContainerStatus，就会返回该ContainerStatus的ready状态。
        如果没有找到指定名称的ContainerStatus，则返回None。
        
        Args:
            container_name (str): 需要检查的容器名称。
        
        Returns:
            Optional[bool]: 如果找到指定名称的ContainerStatus，则返回该ContainerStatus的ready状态；如果没有找到指定名称的ContainerStatus，则返回None。
        """
        if self.status.container_statuses:
            for cs in self.status.container_statuses:
                if cs.name == container_name:
                    return cs.ready
        return None

    def get_container_restarts(self, container_name: str) -> typing.Optional[int]:
        """
            获取指定容器的重启次数，如果不存在则返回None。
        参数container_name (str) - 容器名称，必须为字符串类型。
        返回值 (typing.Optional[int]) - 容器重启次数，如果不存在该容器则返回None。
        """
        if self.status.container_statuses:
            for cs in self.status.container_statuses:
                if cs.name == container_name:
                    return cs.restart_count
        return None

    def get_member_readiness_gate(self, gate: str) -> typing.Optional[bool]:
        """
            获取成员的就绪门控开关，用于判断成员是否已经准备好进行操作。
        如果成员没有设置该门控开关，则默认返回None。
        
        Args:
            gate (str): 门控开关名称，例如 "cloudbed.abcstack.com/ready"。
        
                - cloudbed.abcstack.com/ready：成员是否已经准备好进行操作。
                - cloudbed.abcstack.com/login：成员是否已经登录。
                - cloudbed.abcstack.com/logout：成员是否已经注销。
        
            返回值 (Optional[bool]): 如果成员已经设置了该门控开关，则返回True或False；如果成员没有设置该门控开关，则返回None。
        """
        return self.check_condition(f"cloudbed.abcstack.com/{gate}")

    def update_member_readiness_gate(self, gate: str, value: bool) -> None:
        """
            更新成员的就绪状态网关，并返回是否发生变化。
        如果当前值与给定值不同，则更新状态网关，并将其标记为已更改。
        否则，将其标记为未更改。
        
        Args:
            gate (str): 要更新的状态网关名称。
            value (bool): 要设置的状态网关值。
        
        Returns:
            None: 无返回值，直接修改 Pod 对象的状态字段。
        
        Raises:
            无。
        """
        now = utils.isotime()

        if self.check_condition(f"cloudbed.abcstack.com/{gate}") != value:
            changed = True
        else:
            changed = False

        patch = {"status": {
            "conditions": [{
                "type": f"cloudbed.abcstack.com/{gate}",
                "status": "True" if value else "False",
                "lastProbeTime": '%s' % now,
                "lastTransitionTime": '%s' % now if changed else None
            }]}}
        print(f"Updating readiness gate {gate} with patch {patch}")
        self.pod = cast(api_client.V1Pod, api_core.patch_namespaced_pod_status(
            self.name, self.namespace, body=patch))

    # TODO remove field
    def get_membership_info(self, field: str = None) -> typing.Optional[dict]:
        """
            获取成员信息，可指定字段。如果没有注解或者注解中不存在该字段，则返回None。
        默认情况下，会返回一个包含所有信息的字典，如果指定了字段，则只返回该字段对应的值。
        
        Args:
            field (str, optional): 要获取的字段名称，默认为None，表示返回所有信息。 Defaults to None.
        
        Returns:
            typing.Optional[dict]: 成员信息，如果没有注解或者注解中不存在该字段，则返回None；如果指定了字段，则返回该字段对应的值。 Defaults to None.
        """
        if self.metadata.annotations:
            info = self.metadata.annotations.get(
                "cloudbed.abcstack.com/membership-info", None)
            if info:
                info = json.loads(info)
                if info and field:
                    return info.get(field)
                return info
        return None

    def update_membership_status(self, member_id: str, role: str, status: str,
                                 view_id: str, version: str,
                                 joined: bool = False) -> None:
        """
            更新成员的状态，包括角色、状态和视图ID等信息。如果成员加入了群组，则会记录加入时间。
        如果当前状态与之前的状态不同或者视图ID、成员ID、角色或版本有变化，则会记录最近一次状态转换时间。
        
        Args:
            member_id (str): 成员的ID。
            role (str): 成员的角色，可以是"OWNER"、"EDITOR"或"VIEWER"中的任意一个。
            status (str): 成员的状态，可以是"OFFLINE"、"ONLINE"或"BANNED"中的任意一个。
            view_id (str): 成员所在的群组视图的ID。
            version (str): 群组的版本号，用于标识群组的历史版本。
            joined (bool, optional): 默认为False，表示成员未加入群组；如果为True，则表示成员已经加入群组。
        
        Raises:
            None
        
        Returns:
            None: 无返回值，直接修改了成员的状态信息。
        """
        now = utils.isotime()
        last_probe_time = now

        info = self.get_membership_info() or {}
        if not info or info.get("role") != role or info.get("status") != status or info.get(
                "groupViewId") != view_id or info.get("memberId") != member_id:
            last_transition_time = now
        else:
            last_transition_time = info.get("lastTransitionTime")

        info.update({
            "memberId": member_id,
            "lastTransitionTime": last_transition_time,
            "lastProbeTime": last_probe_time,
            "groupViewId": view_id,
            "status": status,
            "version": version,
            "role": role
        })
        if joined:
            info["joinTime"] = now

        patch = {
            "metadata": {
                "labels": {
                    "cloudbed.abcstack.com/cluster-role": role if status == "ONLINE" else None
                },
                "annotations": {
                    "cloudbed.abcstack.com/membership-info": json.dumps(info)
                }
            }
        }
        self.pod = cast(api_client.V1Pod, api_core.patch_namespaced_pod(
            self.name, self.namespace, patch))

    def add_member_finalizer(self) -> None:
        """
            添加成员的最终化器，用于在销毁时删除成员。
        该方法只能在初始化后调用一次。
        
        Args:
            None
        
        Returns:
            None, 无返回值
        """
        self._add_finalizer("cloudbed.abcstack.com/membership")

    def remove_member_finalizer(self, pod_body: Body = None) -> None:
        """
            移除成员的 finalizer，用于在删除 Pod 时防止其被重新加入集群。
        如果不指定 pod_body，则会从当前 Pod 中移除 finalizer。
        
        Args:
            pod_body (Body, optional): 要操作的 Pod 体，默认为 None。如果不指定，将使用当前 Pod 的信息。 Default value is None.
        
        Raises:
            None.
        
        Returns:
            None: 无返回值。
        """
        self._remove_finalizer("cloudbed.abcstack.com/membership", pod_body)

    def _add_finalizer(self, fin: str) -> None:
        """
        Add the named token to the list of finalizers for the Pod.
        The Pod will be blocked from deletion until that token is
        removed from the list (remove_finalizer).
        """
        patch = {"metadata": {"finalizers": [fin]}}
        self.obj = api_core.patch_namespaced_pod(
            self.name, self.namespace, body=patch)

    def _remove_finalizer(self, fin: str, pod_body: Body = None) -> None:
        """
            从Pod的finalizer列表中移除一个finalizer。如果指定了pod_body，则更新内部kopf使用的JSON数据以反映最新的finalizer列表。
        如果finalizer不在Pod的finalizer列表中，则不会进行任何操作。
        
        Args:
            fin (str): 要移除的finalizer名称。
            pod_body (Body, optional): 可选参数，包含当前Pod的JSON数据。默认为None。
        
        Raises:
            无。
        
        Returns:
            无返回值，直接修改内部kopf使用的JSON数据。
        """
        patch = {"metadata": {"$deleteFromPrimitiveList/finalizers": [fin]}}
        self.obj = api_core.patch_namespaced_pod(
            self.name, self.namespace, body=patch)

        if pod_body:
            # modify the JSON data used internally by kopf to update its finalizer list
            if fin in pod_body["metadata"]["finalizers"]:
                pod_body["metadata"]["finalizers"].remove(fin)

    def pvc_name(self, volume_name="xdb-snapshot-backup"):
        """
        获取Pod对应的PVC名称。
        """
        # 查找 PVC 名称
        pvc_name = None
        for volume in self.pod.spec.volumes:
            # print(volume.persistent_volume_claim)
            if volume.persistent_volume_claim:
                if volume.persistent_volume_claim.claim_name.startswith(volume_name):
                    pvc_name = volume.persistent_volume_claim.claim_name
                    break
        return pvc_name
    
    def get_pvc_name_storage_path(self, volume_name="xdb-snapshot-backup"):
        """
        Returns:
            _type_: _description_
        """
        
        storage_path = None
        pvc_name = self.pvc_name(volume_name)
        if pvc_name:
            # 查询 PVC 信息
            pvc = api_core.read_namespaced_persistent_volume_claim(name=pvc_name, namespace=self.namespace)
            pv_name = pvc.spec.volume_name
            # 查询 PV 信息
            pv = api_core.read_persistent_volume(name=pv_name)
            
            # 检查是否是 DirectPV 卷
            if pv.spec.csi and pv.spec.csi.driver == "directpv-min-io":
                try:
                    # 查询 DirectPV 卷信息
                    directpv_volume = api_directpv.get_namespaced_custom_object(
                        group="directpv.min.io",
                        version="v1beta1",
                        namespace="kube-system",
                        plural="directpvvolumes",
                        name=pv_name
                    )
                    if directpv_volume and "status" in directpv_volume:
                        storage_path = directpv_volume["status"].get("dataPath")
                except Exception as e:
                    logger.error(f"Failed to get DirectPV volume info: {e}")
            elif pv.spec.host_path:
                storage_path = pv.spec.host_path.path
            elif pv.spec.gce_persistent_disk:
                storage_path = pv.spec.gce_persistent_disk.pd_name
            elif pv.spec.local:
                storage_path = pv.spec.local.path
            else:
                storage_path = None
        return storage_path
    
    @property
    def node_name(self):
        """
        获取当前pod 所在的node节点
        Returns:
            _type_: _description_
        """
        return self.pod.spec.node_name

class XDBClusterZKServer(object):
    """
    处理XDB集群的Zookeeper副本拓扑结构，包括主从复制、备份等。
    """
    def __init__(self, xdbcluster: XDBCluster):
        self.xdbcluster = xdbcluster
        self.zk = ZookeeperService(self.xdbcluster.parsed_spec.ZKDomain, logger=logger)
        self.parse_zk_topo()

    def parse_zk_topo(self) -> None:
        """
        从Zookeeper中获取集群的拓扑结构，并将其解析为一个字典，其中包含了集群的所有节点信息。
        """
        _, topo_info = self.zk.zk_replicate_topo_show(self.xdbcluster.app_id, self.xdbcluster.cluster_id)
        self.topo_info = json.loads(topo_info)
        master_pod = None
        slave_pods = []
        backup_pods = []
        unknown_pods = []

        xdb_pods = self.xdbcluster.get_pods()
        print(f"topo_info:{self.topo_info} xdb_pods:{xdb_pods}")
        for pod in xdb_pods:
            node = pod.node_id
            if node in self.topo_info[self.xdbcluster.cluster_id]['slave']:
                slave_pods.append(pod)
            elif node in self.topo_info[self.xdbcluster.cluster_id]['backup']:
                backup_pods.append(pod)
            elif node == self.topo_info[self.xdbcluster.cluster_id]['master']:
                master_pod = pod
            else:
                unknown_pods.append(pod)
        self.role_pod_map = {
            "master": master_pod,
            "slaves": slave_pods,
            "backups": backup_pods,
            "unknowns": unknown_pods
        }
        print(f"role_pod_map: {self.role_pod_map}")

    @property
    def master_pod(self) -> XDBPod:
        """
        Get the master pod of this XDB cluster.
        """
        return self.role_pod_map['master']

    @property
    def slave_pods(self) -> typing.List[XDBPod]:
        """
        Get the slave pods of this XDB cluster.
        """
        return self.role_pod_map['slaves']
    
    @property
    def slave_pods_node_id(self) -> typing.List[XDBPod]:
        """
        Get the slave pods of this XDB cluster.
        """
        return [ i.node_id for i in self.role_pod_map['slaves']]
    
    @property
    def backup_pods(self) -> XDBPod:
        """
        """
        return self.role_pod_map['backups']
    
    @property
    def backup_pods_node_id(self) -> typing.List[XDBPod]:
        """
        Get the slave pods of this XDB cluster.
        """
        return [ i.node_id for i in self.role_pod_map['backups']]
    

    @property
    def unknown_pods(self) -> XDBPod:
        """
        Get the unknown pods of this XDB cluster.
        """
        return self.role_pod_map['unknowns']
    
    @property
    def unknown_pods_node_id(self) -> typing.List[XDBPod]:
        """
        Get the slave pods of this XDB cluster.
        """
        return [ i.node_id for i in self.role_pod_map['unknowns']]

    def get_role_pods(self) -> typing.Dict[str, typing.Union[XDBPod, typing.List[XDBPod]]]:
        """
        Get all pods of this XDB cluster with their roles.
        """
        return self.role_pod_map

    def get_product_accounts(self):
        """
        获取用户列表
        ["dns_master_c_lxz"]
        """
        _, users = self.zk.zk_account_list(self.xdbcluster.app_id)
        return users
    
    def get_product_account_by_name(self, name):
        """
        获取单个用户关联的账户信息
        {"authbns":[],"authip":[],"authip_enable":0,"db_auth":[{"dbname":"bce_dns","privileges":"ALL"},
        {"dbname":"bce_dns2","privileges":"ALL"}],
        "db_password":"xdb@instance-zw6nzhqv6ecr","db_password_d":"xdb@instance-zw6nzhqv6ecr",
        "db_username":"dns_master_c_lxz",
        "default_charset":"utf8","default_db":"bce_dns","groups":["group_0000"],
        "max_connections":500,"password":"xdb@instance-zw6nzhqv6ecr",
        "password_d":"xdb@instance-zw6nzhqv6ecr","remark":"pro","type":"online",
        "username":"dns_master_c_lxz","wr_flag":2}
        """
        _, user = self.zk.zk_account_show(self.xdbcluster.app_id, name)
        return user
    
    def get_product_all_account_detail(self):
        """
        获取全部用户关联的账户信息详情
        """
        users_detail = []
        users = self.get_product_accounts()
        for user in users:
            users_detail.append(self.get_product_account_by_name(user))
        return users_detail