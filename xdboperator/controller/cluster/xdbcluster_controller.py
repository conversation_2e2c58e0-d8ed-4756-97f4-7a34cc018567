#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   cluster_controller.py
@Time    :   2024/6/22 19:20
<AUTHOR>   lixian<PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import datetime
import kopf
import logging
import typing
from logging import Logger
from typing import Optional, TYPE_CHECKING, Dict, cast

from xdboperator.controller import config, utils, diagnose
from xdboperator.controller.cluster.xdbcluster_api import XDBCluster, XDBPod
from xdboperator.controller.kubeutils import api_core
from xdboperator.controller.kubeutils import client as api_client
from xdboperator.lightxdb.zookeeper import ZookeeperService

common_gr_options = {
    "exitStateAction": "ABORT_SERVER"
}


class XDBClusterMutex:
    """
    A mutex that is used to ensure that only one pod is running at a time.
    """
    def __init__(self, cluster: XDBCluster, pod: Optional[XDBPod] = None, context: str = "n/a"):
        self.cluster = cluster
        self.pod = pod
        self.context = context

    def __enter__(self, *args):
        owner_lock_creation_time: datetime.datetime
        (owner, owner_context, owner_lock_creation_time) = utils.g_ephemeral_pod_state.testset(
            self.cluster, "xdbcluster-mutex", self.pod.name if self.pod else self.cluster.name, context=self.context)
        if owner:
            raise kopf.TemporaryError(
                f"{self.cluster.name} busy. lock_owner={owner} owner_context={owner_context}\
                    lock_created_at={owner_lock_creation_time.isoformat()}",
                delay=10)

    def __exit__(self, *args):
        utils.g_ephemeral_pod_state.set(self.cluster, "xdbcluster-mutex", None, context=self.context)


class XDBClusterController(object):
    """
    This is the controller for a xdbcluster object.
    It's the main controller for a cluster and drives the lifecycle of the
    cluster including creation, scaling and restoring from outages.
    """

    def __init__(self, xdbcluster: XDBCluster):
        """
            初始化函数，用于设置XDBMySQLUser对象和集群信息。

        Args:
            xdbcluster (XDBCluster): xdb 集群 cr

        Returns:
            None，无返回值。

        Raises:
            无异常抛出。
        """
        self.xdbcluster = xdbcluster

    @property
    def app_id(self) -> str:
        """
        获取应用ID
        """
        return self.xdbcluster.parsed_spec.appID
    
    @property
    def zk_domain(self) -> str:
        """
        获取ZK域名
        """
        return self.xdbcluster.parsed_spec.ZKDomain
    
    def delete_xdbcluster(self, logger: logging.Logger) -> int:
        """
        从zk中删除集群
        """
        zk = ZookeeperService(self.zk_domain, logger=logger)
        _, app_list = zk.zk_application_list()
        
        if self.app_id not in app_list:
            logger.warning(f"cluster app:{self.app_id} \
                not in zk_product_list:{app_list}, skip delete")
            return
        status, output = zk.zk_application_drop(self.app_id)
        if status != 0:
            logger.error(f"delete xdbcluster {self.app_id} status: {status} output:{output}")
            raise kopf.TemporaryError(f"delete xdbcluster {self.app_id} status: {status} output:{output}", delay=15)
        else:
            logger.info(f"delete xdbcluster success {self.app_id} status: {status} output:{output}")
      


    def correct_xdbcluster_role(self, logger: Logger):
        """
        校准XDB当前POD 在ZK登记的角色,更新label, 供给svc链接使用
        """
        pods = self.xdbcluster.get_pods()
        xdb_topology = self.xdbcluster.replica_topo
        logger.debug(f"xdb_topology: {xdb_topology}")
        patch_body = None
        for pod in pods:
            current_role = pod.role
            node_id = pod.node_id
            logger.debug(f"current_role: {current_role},pod: {pod}")
            if node_id == xdb_topology.master_pod.node_id and current_role != diagnose.XDBNodeRoleType.MASTER.value:
                logger.info("set pod " + pod.name + " to primary")
                patch_body = self._patch_role_body(diagnose.XDBNodeRoleType.MASTER.value)
            elif node_id in xdb_topology.backup_pods_node_id and current_role != diagnose.XDBNodeRoleType.BACKUP.value:
                logger.info("set pod " + pod.name + " to backup")
                patch_body = self._patch_role_body(diagnose.XDBNodeRoleType.BACKUP.value)
            elif node_id in xdb_topology.slave_pods_node_id and current_role != diagnose.XDBNodeRoleType.SLAVE.value:
                logger.info("set pod " + pod.name + " to slave")
                patch_body = self._patch_role_body(diagnose.XDBNodeRoleType.SLAVE.value)
            elif node_id in xdb_topology.unknown_pods_node_id and \
                                    current_role != diagnose.XDBNodeRoleType.UNKNOWN.value:
                logger.info("set pod " + pod.name + " to unknown")
                patch_body = self._patch_role_body(diagnose.XDBNodeRoleType.UNKNOWN.value)
            logger.debug(f"patch_body: {patch_body}")
            if patch_body is not None:
                try:
                    api_core.patch_namespaced_pod(pod.name, pod.namespace, patch_body)
                except Exception as e:
                    logger.error(f"Exception when calling AppsV1Api->patch_namespaced_pod: {e} \n")

    def _patch_role_body(self, role):
        role_body = {"metadata": {"labels": {"mysql-role": role}}}
        return role_body

        
        
        
        