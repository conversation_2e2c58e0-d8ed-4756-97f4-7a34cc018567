#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   operato_cluster.py
@Time    :   2024/7/3 15:34
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   lixian<PERSON><EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import kopf
from kopf._cogs.structs.bodies import Body
from logging import Logger
from typing import Optional
import os
import kubernetes.client.rest

from xdboperator.controller import consts, diagnose, config 
from xdboperator.controller import utils
from xdboperator.controller.api_utils import ApiSpecError, ignore_404
from xdboperator.controller.cluster.xdbcluster_api import XDBCluster
from xdboperator.controller.cluster.xdbcluster_controller import XDBClusterController
from xdboperator.controller.cluster import xdbcluster_objects
from xdboperator.controller.kubeutils import api_core, k8s_version
from xdboperator.tools.backup_zk_data import XDBZKBackup


def update_status_with_retry(cluster, status_data, max_retries=5, logger=None):
    """
    使用重试机制更新XDBCluster状态，处理冲突问题
    
    Args:
        cluster: XDBCluster对象实例
        status_data: 要更新的状态数据
        max_retries: 最大重试次数
        logger: 日志记录器
    
    Returns:
        bool: 状态更新是否成功
    """
    retries = 0
    while retries < max_retries:
        try:
            cluster.set_status(status_data)
            return True
        except kubernetes.client.rest.ApiException as e:
            if e.status == 409:  # Conflict
                if logger:
                    logger.warning(f"发生冲突，重新获取资源并重试，第{retries+1}次重试")
                try:
                    cluster.refresh()
                    retries += 1
                    continue
                except Exception as refresh_err:
                    if logger:
                        logger.error(f"重新获取资源失败: {refresh_err}")
                    return False
            else:
                if logger:
                    logger.error(f"更新状态时发生API错误: {e}")
                return False
        except Exception as e:
            if logger:
                logger.error(f"更新状态时发生未知错误: {e}")
            return False
    
    if logger:
        logger.error(f"更新状态重试次数已达上限 ({max_retries}次)")
    return False


@kopf.on.create(consts.GROUP, consts.VERSION, consts.XDBFUSIONCLUSTER_PLURAL)
def on_xdbcluster_create(name: str, namespace: Optional[str], body: Body,
                         logger: Logger, **kwargs) -> None:
    """
    XDB 集群创建目前通过helm进行创建，所以这里不做任何事情,只做资源的标记
    """
    cluster = XDBCluster(body)
    logger.info(f"Init XDB Cluster  name={name} namespace={namespace} on K8s {k8s_version()}")
    cluster.set_status({
        "cluster": {
            "status": diagnose.ClusterDiagStatus.CREATING.value,
            "onlineInstances": 0,
            "lastProbeTime": utils.isotime()
        }})
    try:
        cluster.parse_spec()
        cluster.parsed_spec.validate(logger)
    except ApiSpecError as e:
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "lastProbeTime": utils.isotime()
            }})
        cluster.error(action="CreateXDBCluster", reason="InvalidArgument", message=str(e))
        raise kopf.PermanentError(f"Error in CreateXDBCluster spec: {e}")
    cluster.log_cluster_info(logger)
    if cluster.ready:
        msg = "The resource already exists. Skip execution"
        logger.warning(msg)
        cluster.warn(action="CreateXDBUser", reason="resourceExists", message=msg)
        return
    if cluster.cluster_sts_ready:
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.RUNNING.value,
                "onlineInstances": cluster.online_instances,
                "lastProbeTime": utils.isotime()
            }})
        logger.info(f"Create XDB Cluster {name} success")
        cluster.info(action="CreateXDBCluster", reason="Success",
                     message="success")
        cluster.set_create_time()
    else:
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value,
                "onlineInstances": cluster.online_instances,
                "lastProbeTime": utils.isotime()
            }})
        msg = "waiting for the sts cluster to be ready"
        logger.info(msg)
        cluster.info(action="CreateXDBCluster", reason="ReadyCheckFailed",
                     message=msg)
        raise kopf.TemporaryError(msg, delay=15)


@kopf.on.delete(consts.GROUP, consts.VERSION, consts.XDBFUSIONCLUSTER_PLURAL, retries=config.BOOTSTRAP_RETRIES)
def on_xdbcluster_delete(name: str, namespace: Optional[str], body: Body,
                         logger: Logger, **kwargs) -> None:
    """
    处理删除 XDBMySQLUser 资源的事件，如果 XDBCluster 存在则更新其状态为 TERMINATE。
    
    Args:
        name (str): XDBMySQLUser 资源的名称。
        namespace (Optional[str], optional): XDBMySQLUser 资源的命名空间，默认为 None。
        body (Body): Kubernetes 客户端请求体，包含 XDBMySQLUser 资源的信息。
        logger (Logger, optional): 日志记录器，默认为 None。
        kwargs (dict, optional): 可选参数，默认为空字典。
    
    Returns:
        None: 该函数没有返回值。
    """
    cluster = XDBCluster(body)
    sts = cluster.get_xdbcluster_stateful_set()
    # 当前版本xdb使用过chart部署的，sts归属chart包。这里判断sts 只有不存在的才删除记录\
    if sts:
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.WAIT_STS_DELETE.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }})
        msg = f"StatefulSet still exists for XDBCluster {name}, waiting for Helm to clean up"
        logger.warning(msg)
        cluster.warning(action="DeleteXDBCluster", reason="WaitingForCleanup", 
                    message="StatefulSet still exists, waiting for Helm cleanup")
        raise kopf.TemporaryError(msg, delay=30)
    else :
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.TERMINATE.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }})
        cluster = XDBCluster(body)
        ctl = XDBClusterController(cluster)
        ctl.delete_xdbcluster(logger)
        logger.info(f"Deleting XDBCluster {name}")
        cluster.info(action="DeleteXDBCluster", reason="DeleteXDBCluster", message="success")



@kopf.timer(consts.GROUP, consts.VERSION, consts.XDBFUSIONCLUSTER_PLURAL, 
            interval=consts.MINUTES * 60, initial_delay=consts.MINUTES * 10)
def on_xdbcluster_backup(name: str, namespace: Optional[str], body: Body, logger: Logger, **kwargs) -> None:
    """
    定时备份XDB集群的ZK数据
    """
    cluster = XDBCluster(body)
    logger.info(f"Starting backup for XDBCluster {name} in namespace {namespace}")
    
    try:
        # 获取CR中的信息
        spec = cluster.parsed_spec
        app_id = spec.appID  # 从CR spec中获取应用ID
        
        # 构建备份目录
        backup_base_dir = os.getenv("XDB_BACKUP_BASE_DIR", "/backups")
        cluster_backup_dir = os.path.join(backup_base_dir, namespace, name)
        
        # 初始化备份工具
        backup_tool = XDBZKBackup(
            zk_host=spec.ZKDomain,
            backup_dir=cluster_backup_dir,
            retention_days=180
        )
        
        # 执行备份
        backup_file = backup_tool.run_backup(app_id)
        
        if backup_file:
            # 更新CR状态
            cluster.set_status({
                "backup": {
                    "lastBackupTime": utils.isotime(),
                    "lastBackupFile": backup_file,
                    "lastBackupStatus": "Success",
                    "application": app_id
                }
            })
            logger.info(f"Backup completed successfully for cluster {name}, app {app_id}. Backup file: {backup_file}")
        else:
            cluster.set_status({
                "backup": {
                    "lastBackupTime": utils.isotime(),
                    "lastBackupStatus": "Failed",
                    "application": app_id
                }
            })
            logger.error(f"Backup failed for cluster {name}, app {app_id}")
            
    except Exception as e:
        logger.error(f"Error during backup for cluster {name}: {str(e)}")


@kopf.timer(consts.GROUP, consts.VERSION, consts.XDBFUSIONCLUSTER_PLURAL, 
            interval=consts.SECONDS * 30, initial_delay=consts.MINUTES)
def on_xdbcluster_role_correct(name: str, namespace: Optional[str], body: Body, logger: Logger, **kwargs) -> None:
    """
    check the status of the cluster
    """
    logger.info(f"Checking XDBCluster role Cluster name={name} namespace={namespace} on K8s {k8s_version()}")
    cluster = XDBCluster(body)
    icspec = cluster.parsed_spec
    if cluster.cluster_status == diagnose.ClusterDiagStatus.CREATING.value:
        print("Cluster is not ready, skip role correct")
        return
    print("1. Master Service")
    if not ignore_404(cluster.get_master_service):
        print("\tPreparing...")
        # svc = xdbcluster_objects.prepare_xdb_mysql_primary_service(icspec)
        # print(f"\tCreating...{svc}")
        # kopf.adopt(svc)
        # api_core.create_namespaced_service(namespace=namespace, body=svc)
    #svc 通过helm 安装，这里制作校准
    else:
        print("\tService already exists, start correct role")
        cluster_ctl = XDBClusterController(cluster)
        cluster_ctl.correct_xdbcluster_role(logger)


@kopf.on.resume(consts.GROUP, consts.VERSION, consts.XDBFUSIONCLUSTER_PLURAL)
def on_xdbcluster_resume(name: str, namespace: Optional[str], body: Body,
                        logger: Logger, **kwargs) -> None:
    """
    处理 operator 重启时的 XDBCluster 资源恢复。
    确保所有已存在的 XDBCluster 资源状态一致。
    
    Args:
        name (str): XDBCluster 资源的名称
        namespace (Optional[str]): XDBCluster 资源所在的命名空间
        body (Body): 包含 XDBCluster 资源的请求体
        logger (Logger): 日志记录器
        kwargs: 其他可选参数
    """
    logger.info(f"Resume handling XDBCluster name={name} namespace={namespace}")
    cluster = XDBCluster(body)
    cluster.info(action="ResumeXDBCluster", reason="StartResume", 
                 message=f"Resume handling XDBCluster name={name} namespace={namespace}")
    
    # 如果资源正在删除中，跳过处理
    if cluster.deleting:
        logger.debug(f"XDBCluster {name} is being deleted, skip resume handling")
        cluster.info(action="ResumeXDBCluster", reason="Deleting", 
                     message=f"XDBCluster {name} is being deleted, skip resume handling")
        return
        
    current_status = cluster.cluster_status
    logger.info(f"Current cluster status: {current_status}")
    cluster.info(action="ResumeXDBCluster", reason="CurrentStatus", 
                 message=f"Current cluster status: {current_status}")
    
    try:
        cluster.parse_spec()
        cluster.parsed_spec.validate(logger)
    except ApiSpecError as e:
        status_data = {
            "cluster": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(cluster, status_data, logger=logger):
            logger.error(f"Failed to update cluster status to INVALID: {name}")
        cluster.error(action="ResumeXDBCluster", reason="SpecInvalid", message=str(e))
        return
        
    # 如果状态为空，说明是首次创建，设置为 CREATING 状态
    if current_status is None:
        status_data = {
            "cluster": {
                "status": diagnose.ClusterDiagStatus.CREATING.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(cluster, status_data, logger=logger):
            logger.error(f"Failed to update cluster status to CREATING: {name}")
            raise kopf.TemporaryError("Failed to update cluster status, will retry", delay=5)
        cluster.info(action="ResumeXDBCluster", reason="SetCreating", 
                     message="Set cluster status to CREATING during resume")
        return
        
    # 检查 StatefulSet 状态
    sts = cluster.get_xdbcluster_stateful_set()
    if not sts:
        status_data = {
            "cluster": {
                "status": diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(cluster, status_data, logger=logger):
            logger.error(f"Failed to update cluster status to WAITING_CLUSTER_READY: {name}")
        cluster.warn(action="ResumeXDBCluster", reason="NoStatefulSet", 
                     message="StatefulSet not found during resume, set status to WAITING_CLUSTER_READY")
        return
        
    # 根据当前状态进行相应处理
    if current_status == diagnose.ClusterDiagStatus.CREATING.value:
        if cluster.cluster_sts_ready:
            status_data = {
                "cluster": {
                    "status": diagnose.ClusterDiagStatus.RUNNING.value,
                    "onlineInstances": cluster.online_instances,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(cluster, status_data, logger=logger):
                logger.error(f"Failed to update cluster status to RUNNING: {name}")
            cluster.set_create_time()
            cluster.info(action="ResumeXDBCluster", reason="ClusterReady", 
                         message="Cluster is ready during resume, set status to RUNNING")
        else:
            status_data = {
                "cluster": {
                    "status": diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value,
                    "onlineInstances": cluster.online_instances,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(cluster, status_data, logger=logger):
                logger.error(f"Failed to update cluster status to WAITING_CLUSTER_READY: {name}")
            cluster.warn(action="ResumeXDBCluster", reason="ClusterNotReady", 
                         message="Cluster not ready during resume, set status to WAITING_CLUSTER_READY")
    elif current_status == diagnose.ClusterDiagStatus.RUNNING.value:
        # 验证集群状态
        if not cluster.cluster_sts_ready:
            status_data = {
                "cluster": {
                    "status": diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value,
                    "onlineInstances": cluster.online_instances,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(cluster, status_data, logger=logger):
                logger.error(f"Failed to update cluster status to WAITING_CLUSTER_READY: {name}")
            cluster.warn(action="ResumeXDBCluster", reason="ClusterLost", 
                         message="Cluster lost during resume, set status to WAITING_CLUSTER_READY")
        else:
            # 更新在线实例数
            status_data = {
                "cluster": {
                    "status": diagnose.ClusterDiagStatus.RUNNING.value,
                    "onlineInstances": cluster.online_instances,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(cluster, status_data, logger=logger):
                logger.error(f"Failed to update cluster status to RUNNING: {name}")
            cluster.info(action="ResumeXDBCluster", reason="ClusterStillReady", 
                         message="Cluster still running and ready during resume.")