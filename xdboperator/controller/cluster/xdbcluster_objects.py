#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   xdbcluster_objects.py
@Time    :   2024/6/22 19:26
<AUTHOR>   lixian<PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""


import kopf
import yaml
from logging import Logger
from shlex import quote
from typing import Optional

from xdboperator.controller.kubeutils import api_core, api_apps, api_customobj
from xdboperator.controller.kubeutils import client as api_client
from xdboperator.controller import utils

from xdboperator.controller.cluster.xdbcluster_api import XDBCluster, XDBClusterSpec



def prepare_xdb_mysql_primary_service(spec: XDBClusterSpec) -> dict:
    """
    创建dbproxy使用的svc
    """
    tmpl = f"""
apiVersion: v1
kind: Service
metadata:
  name: {spec.name}-mysql-master
  namespace: {spec.namespace}
  labels:
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: mysql
    cloudbed.abcstack.com/component: xdb
  annotations::
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: mysql
    cloudbed.abcstack.com/component: xdb
spec:
  ports:
  - name: xdb-mysql
    port: {spec.mysqlPort}
    protocol: TCP
    targetPort: {spec.mysqlPort}
  selector:
    mysql-role: master
    karrier.abcstack.com/app: {spec.name}
    karrier.abcstack.com/component: xdb
    app:  {spec.name}
  type: LoadBalancer
"""
    service = yaml.safe_load(tmpl)
    return service
