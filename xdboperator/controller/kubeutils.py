#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   kubeutils.py
@Time    :   2024/6/22 19:25
<AUTHOR>   lix<PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import kopf
import os
import socket
import sys
import time
from kubernetes import client, config
from kubernetes.client.rest import ApiException
from kubernetes.stream import stream
from logging import Logger
from typing import Callable, Optional, TypeVar

try:
    # outside k8s
    config.load_kube_config()
except config.config_exception.ConfigException:
    try:
        # inside a k8s pod
        config.load_incluster_config()
    except config.config_exception.ConfigException:
        raise Exception(
            "Could not configure kubernetes python client")

api_core: client.CoreV1Api = client.CoreV1Api()
api_customobj: client.CustomObjectsApi = client.CustomObjectsApi()
api_apps: client.AppsV1Api = client.AppsV1Api()
api_kruise = client.CustomObjectsApi()  # 用于访问 kruise API
api_batch: client.BatchV1Api = client.BatchV1Api()
api_cron_job: client.BatchV1Api = client.BatchV1Api()
api_policy: client.PolicyV1Api = client.PolicyV1Api()
api_rbac: client.RbacAuthorizationV1Api = client.RbacAuthorizationV1Api()
api_client: client.ApiClient = client.ApiClient()
api_apis: client.ApisApi() = client.ApisApi() # type: ignore

# DirectPV API client
api_directpv = client.CustomObjectsApi()  # 用于访问 DirectPV API

T = TypeVar("T")


def catch_404(f: Callable[..., T]) -> Optional[T]:
    """
    用于捕获函数f的ApiException，当状态码为404时返回None，否则重新raise。
    
    Args:
        f (Callable[..., T]): 需要被捕获的函数，必须有返回值。
    
    Returns:
        Optional[T]: 如果函数f抛出了一个带有状态码为404的ApiException，则返回None；否则，返回函数f的返回值。可以是任何类型。默认值为None。
    
    Raises:
        无
    """
    try:
        return f()
    except ApiException as e:
        if e.status == 404:
            return None
        raise


def available_apis():
    """
    获取当前可用的API列表。
    
    返回值（list[str]）：一个包含所有可用API名称的列表，每个API名称都是字符串类型。
    """
    return api_apis.get_api_versions()


def k8s_version() -> str:
    """
    获取当前 Kubernetes 版本号，格式为 "X.Y"。
    
    Args:
        无参数。
    
    Returns:
        str (str): Kubernetes 版本号，格式为 "X.Y"。
    
    Raises:
        无异常抛出。
    """
    api_instance = client.VersionApi(api_client)

    api_response = api_instance.get_code()
    return f"{api_response.major}.{api_response.minor}"


_k8s_cluster_domain = None


def k8s_cluster_domain(logger: Optional[Logger], ns="kube-system") -> str:
    """Get the Kubernetes Cluster's Domain. Can
    be overwritten using environment XDB_OPERATOR_K8S_CLUSTER_DOMAIN.

    If this fails to detect it will retry in a blocking loop. This should only
    happen in operator_main before startup. If it constantly fails the process
    will be terminated.
    """

    global _k8s_cluster_domain

    # We use the cached value instead of querying multiple times
    if _k8s_cluster_domain:
        return _k8s_cluster_domain

    # The user could override the lookup using env
    _k8s_cluster_domain = os.getenv("XDB_OPERATOR_K8S_CLUSTER_DOMAIN")
    if _k8s_cluster_domain:
        if logger:
            logger.info(f"Environment provided cluster domain: {_k8s_cluster_domain}")
        return _k8s_cluster_domain

    for _ in range(15):
        try:
            # Try reverse lookup via some service having a cluster_ip set. Operator
            # is allowed to list all services and we assume some service is in
            # kube-system namespace.
            ip = next(
                filter(
                    lambda ip: ip,
                    map(
                        lambda service: service.spec.cluster_ip,
                        api_core.list_namespaced_service(ns).items
                    )
                )
            )

            if ip:
                fqdn = socket.gethostbyaddr(ip)[0]
                [_, _, _, _k8s_cluster_domain] = fqdn.split('.', maxsplit=3)
                if logger:
                    logger.info(f"Auto-detected cluster domain: {_k8s_cluster_domain}")

                return _k8s_cluster_domain
        except Exception as e:
            if logger:
                logger.warning("Failed to detect cluster domain. "
                               f"Reason: {e}")
            time.sleep(2)

    logger.error(
        """Failed to automatically identify the cluster domain. If this
        persists try setting XDB_OPERATOR_K8S_CLUSTER_DOMAIN via environment."""
    )

    sys.exit(1)


def pod_exec_command(name: str,
                     namespace: str,
                     cmd: [str],
                     logger: Logger,
                     interrupt: bool = True,
                     user: str = "",
                     container: str = "xdb",
                     timeout: int = 86400) -> str:
    """
    通过pod_exec_command 执行命令

    Args:
        name (str): _description_
        namespace (str): _description_
        cmd (str]): _description_
        logger (Logger): _description_
        interrupt (bool, optional): _description_. Defaults to True.
        user (str, optional): _description_. Defaults to "work".
        container (str, optional): _description_. Defaults to "xdb".
        timeout (int, optional): _description_. Defaults to 30.

    Raises:
        kopf.PermanentError: _description_

    Returns:
        str: _description_
    """
    if user:
        cmd = ["/bin/bash", "-c", " ".join(['gosu', user] + cmd)]
    else:
        cmd = ["/bin/bash", "-c", " ".join(cmd)]
    logger.debug(f"pod {name} exec command({cmd})")
    api_core: client.CoreV1Api = client.CoreV1Api()
    try:
        resp = stream(
            api_core.connect_get_namespaced_pod_exec,
            name,
            namespace,
            command=cmd,
            stderr=True,
            container=container,
            stdin=False,
            stdout=True,
            tty=False,
            _preload_content=False)
        resp.run_forever(timeout=timeout)
        return resp.read_stdout(), resp.read_stderr()
    except Exception as e:
        if interrupt:
            raise kopf.PermanentError(
                f"pod {name} exec command({cmd}) failed {e}")
        else:
            logger.error(f"pod {name} exec command({cmd}) failed {e}")
            return "failed"
