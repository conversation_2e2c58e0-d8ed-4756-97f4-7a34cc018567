#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   api_utils.py.py
@Time    :   2024/6/22 19:25
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import typing
from enum import Enum
from kubernetes.client.rest import ApiException
from typing import Optional, Type, cast, Any

T = typing.TypeVar("T")
E = typing.TypeVar("E")


class ApiSpecError(Exception):
    """
    api error
    """
    pass


def ignore_404(f) -> Any:
    """
    忽略404错误，返回None。如果不是404错误，则抛出ApiException。
    
    Args:
        f (Callable[[], Any]): 需要被包装的函数，该函数应该没有参数并返回任意类型的值。
    
    Returns:
        Any: 如果原始函数抛出了404错误，则返回None；否则，返回原始函数的返回值。
    
    Raises:
        ApiException: 如果原始函数抛出了非404错误，则会重新抛出ApiException。
    """
    try:
        return f()
    except ApiException as e:
        if e.status == 404:
            return None
        raise


class ImagePullPolicy(Enum):
    """
    拉取进行枚举常量
    """
    Never = "Never"
    IfNotPresent = "IfNotPresent"
    Always = "Always"


class Edition(Enum):
    """
    版本枚举
    """
    community = "community"
    enterprise = "enterprise"


def typename(type: type) -> str:
    """
    获取类型的名称，如果是字典、字符串、整数、布尔值或列表等内置类型，则返回对应的名称，否则返回类型的名称。
    
    Args:
        type (type): 需要获取名称的类型。
    
    Returns:
        str: 类型的名称，如果是内置类型，则为"Map"、"String"、"Integer"、"Boolean"或"List"；否则为类型的名称。
    """
    CONTENT_TYPE_NAMES = {"dict": "Map", "str": "String",
                          "int": "Integer", "bool": "Boolean", "list": "List"}
    if type.__name__ not in CONTENT_TYPE_NAMES:
        return type.__name__
    return CONTENT_TYPE_NAMES[type.__name__]


def _dget(d: dict, key: str, what: str, default_value: Optional[T], expected_type: Type[T]) -> T:
    """
    从字典中获取指定键的值，并进行类型检查。如果不存在该键或者为None，则返回默认值；否则进行类型检查。
    如果没有设置默认值且键不存在于字典中，将引发ApiSpecError异常。
    
    Args:
        d (dict): 包含要获取值的字典。
        key (str): 要获取的键名。
        what (str): 用于错误信息的标记，表示这个值来自哪里（例如："api_spec.parameters"）。
        default_value (Optional[T], optional): 当键不存在时返回的默认值，默认为None. Defaults to None.
        expected_type (Type[T]): 期望的值类型。
    
    Raises:
        ApiSpecError: 如果没有设置默认值且键不存在于字典中，将引发此异常。
        ApiSpecError: 如果值不是期望的类型，将引发此异常。
    
    Returns:
        T: 返回字典中对应键的值，转换成了期望的类型。
    """
    if default_value is None and key not in d:
        raise ApiSpecError(f"{what}.{key} is mandatory, but is not set")
    value = d.get(key, default_value)
    if not isinstance(value, expected_type):
        raise ApiSpecError(
            f"{what}.{key} expected to be a {typename(expected_type)} " +
            f"but is {typename(type(value)) if value is not None else 'not set'}")
    return cast(T, value)


def dget_dict(d: dict, key: str, what: str, default_value: Optional[dict] = None) -> dict:
    """
    从字典中获取指定键对应的值，如果该值不存在则返回默认值。
    
    Args:
        d (dict): 需要查询的字典。
        key (str): 需要查询的键名。
        what (str): 需要查询的值类型，可选值为 "int", "float", "bool" 或 "str"。
        default_value (Optional[dict], optional): 默认值，默认为 None. Defaults to None.
    
    Returns:
        dict: 返回查询到的值，如果该值不存在则返回默认值。
    """
    return _dget(d, key, what, default_value, dict)


def dget_list(d: dict, key: str, what: str, default_value: Optional[list] = None,
              content_type: Optional[type] = None) -> list:
    """
    从字典中获取指定键对应的列表值，并根据需要进行类型检查。如果不存在该键或者其值不是列表，则返回默认值。
    
    Args:
        d (dict): 包含所需信息的字典。
        key (str): 需要获取的键名。
        what (str): 用于错误处理时标记当前操作的字符串，例如 "path parameters"。
        default_value (Optional[list], optional): 默认值，如果键不存在或者其值不是列表，则使用此默认值. Defaults to None.
        content_type (Optional[type], optional): 如果提供了此参数，则会对列表中每个元素进行类型检查，确保都是此类型。 Defaults to None.
    
    Raises:
        ApiSpecError: 如果列表中任何一个元素的类型与content_type不匹配，将引发ApiSpecError异常。
    
    Returns:
        list: 返回获取到的列表值，如果不存在该键或者其值不是列表，则返回默认值。
    """
    l = _dget(d, key, what, default_value, list)
    if l and content_type is not None:
        for i, elem in enumerate(l):
            if not isinstance(elem, content_type):
                raise ApiSpecError(
                    f"{what}.{key}[{i}] expected to be a {typename(content_type)} but is {typename(type(elem))}")
    return l


def dget_str(d: dict, key: str, what: str, *, default_value: Optional[str] = None) -> str:
    """
    从字典中获取指定键对应的字符串值，如果不存在则返回默认值。
    
    Args:
        d (dict): 待查询的字典。
        key (str): 需要查询的键名。
        what (str): 需要查询的值类型，必须为'str'。
        default_value (Optional[str], optional): 默认返回值，默认为None. Defaults to None.
    
    Returns:
        str: 返回键对应的字符串值，如果不存在则返回默认值。
    """
    return _dget(d, key, what, default_value, str)


def dget_enum(d: dict, key: str, what: str, *, default_value: Optional[E], enum_type: Type[Enum]) -> E:
    """
    从字典中获取枚举类型的值，如果不存在则返回默认值。
    
    Args:
        d (dict): 包含要查询的键值对的字典。
        key (str): 需要查询的键名。
        what (str): 用于错误信息的标记，表示查询的是什么内容（例如函数名或者类名）。
        default_value (Optional[E], optional): 默认值，如果键不存在，将会返回该值. Defaults to None.
        enum_type (Type[Enum]): 枚举类型，用于校验返回值是否合法。
    
    Raises:
        ApiSpecError: 如果返回值不是指定的枚举类型中的一个，将会引发此异常。
    
    Returns:
        E: 返回枚举类型中的一个值，如果不存在默认值将会被返回。
    """
    s = _dget(d, key, what, default_value, str)
    for v in enum_type:
        if v.name == s:
            return cast(E, v)
    raise ApiSpecError(
        f"{what}.{key} has invalid value '{s}' but must be one of {','.join([x.name for x in enum_type])}")


def dget_int(d: dict, key: str, what: str, *, default_value: Optional[int] = None) -> int:
    """
    从字典中获取指定键对应的整数值，如果不存在则返回默认值。
    
    Args:
        d (dict): 待查询的字典。
        key (str): 需要查询的键名。
        what (str): 用于错误提示的字符串，表示需要获取的是什么类型的值。
        default_value (Optional[int], optional): 默认返回值，默认为None. Defaults to None.
    
    Returns:
        int: 字典中对应键的整数值，如果不存在则返回默认值。
    """
    return _dget(d, key, what, default_value, int)


def dget_float(d: dict, key: str, what: str, *, default_value: Optional[float] = None) -> int:
    """
    从字典中获取一个浮点数值，如果不存在则返回默认值。
    
    Args:
        d (dict): 包含要查询的键和值的字典。
        key (str): 要查询的键名。
        what (str): 用于错误信息的字符串，表示要查询的值类型。
        default_value (Optional[float], optional): 如果键不存在，返回的默认值（默认为None）. Defaults to None.
    
    Returns:
        int: 如果键存在且可以转换为浮点数，则返回该浮点数；否则返回默认值。
    """
    return _dget(d, key, what, default_value, float)


def dget_bool(d: dict, key: str, what: str, *, default_value: Optional[bool] = None) -> bool:
    """
    从字典中获取一个布尔值，如果不存在则返回默认值。
    
    Args:
        d (dict): 包含键值对的字典。
        key (str): 需要获取的键名。
        what (str): 用于描述该键的信息，例如"是否使用SSL连接"。
        default_value (Optional[bool], optional): 默认值，默认为None。默认情况下，如果键不存在，将引发KeyError异常。. Default: None.
    
    Returns:
        bool: 字典中指定键对应的布尔值，如果键不存在则返回默认值。
    
    Raises:
        KeyError: 如果键不存在且default_value未指定。
    """
    return _dget(d, key, what, default_value, bool)
