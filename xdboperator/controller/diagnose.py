#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   diagnose.py
@Time    :   2024/6/22 19:25
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   l<PERSON><PERSON><PERSON><PERSON>@baidu.com
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import enum
from typing import Optional


class InstanceDiagStatus(enum.Enum):
    """
    状态枚举
    """
    ONLINE = "ONLINE"

    RECOVERING = "RECOVERING"

    ERROR = "ERROR"

    OFFLINE = "OFFLINE"

    NOT_MANAGED = "NOT_MANAGED"

    UNMANAGED = "UNMANAGED"

    UNREACHABLE = "UNREACHABLE"

    # Uncertain because we can't connect or query it
    UNKNOWN = "UNKNOWN"


class CandidateDiagStatus(enum.Enum):
    """
    条件枚举
    """
    UNKNOWN = None

    MEMBER = "MEMBER"

    REJOINABLE = "REJOINABLE"

    JOINABLE = "JOINABLE"

    UNSUITABLE = "UNSUITABLE"

    UNREACHABLE = "UNREACHABLE"


class CandidateStatus:
    """
    状态枚举
    """
    status: CandidateDiagStatus = CandidateDiagStatus.UNKNOWN


class ClusterDiagStatus(enum.Enum):
    """
    资源状态枚举
    """
    ONLINE = "ONLINE"
    ONLINE_PARTIAL = "ONLINE_PARTIAL"
    OFFLINE = "OFFLINE"
    NO_QUORUM = "NO_QUORUM"
    SPLIT_BRAIN = "SPLIT_BRAIN"
    ONLINE_UNCERTAIN = "ONLINE_UNCERTAIN"
    OFFLINE_UNCERTAIN = "OFFLINE_UNCERTAIN"
    NO_QUORUM_UNCERTAIN = "NO_QUORUM_UNCERTAIN"
    SPLIT_BRAIN_UNCERTAIN = "SPLIT_BRAIN_UNCERTAIN"
    UNKNOWN = "UNKNOWN"
    INITIALIZING = "INITIALIZING"
    FINALIZING = "FINALIZING"
    INVALID = "INVALID"
    PENDING = "Pending"
    CREATING = "Creating"
    UPDATING = "Updating"
    RUNNING = "Running"
    DELETING = "Deleting"
    WAIT_STS_DELETE = "WaitSTSDelete"
    CREATE_FAILED = "CreateFailed"
    UPDATE_FAILED = "UpdateFailed"
    REGISTER_ZK_FAILED = "RegisterZkFailed"
    TERMINATE = "Terminating"
    STOP = "Stop"
    WAITING_CLUSTER_READY = "WaitXDBReady"
    XDB_STS_NOT_FOUND = "XDBSTSNotFound"
    WAIT_ACCESS_PASS = "WaitAccessProbePass"
    DOUBLE_CHECK_FAILED = "DoubleCheckFailed"
    CHECK_FAILED = "CheckFailed"
    USER_EXISTS = "UserExistsError"


class BackupStatus(enum.Enum):
    """
    备份状态枚举
    """
    AVAILABLE = "Available"
    SUCCESS = "Success"
    FAILED = "Failed"
    PENDING = "Pending"
    UNKNOWN = "Unknown"
    EXECUTING = "Executing"
    TERMINATE = "Terminating"
    NO_READY_NODE = "NoReadyNode"

class BackupType(enum.Enum):
    """
    备份类型枚举
    """
    FULL = "full"
    INCREMENTAL = "incremental"
    SNAPSHOT = "snapshot"


class XDBNodeRoleType(enum.Enum):
    """
    集群角色信息
    """
    MASTER = "master"
    BACKUP = "backup"
    SLAVE = "slave"
    UNKNOWN = "unknown"
