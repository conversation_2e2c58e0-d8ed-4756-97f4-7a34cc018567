#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   xdbproxy_controller.py
@Time    :   2024/8/28 19:20
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import datetime
import kopf
import logging
import typing
from kopf._cogs.structs.bodies import Body
from logging import Logger
from typing import Optional, TYPE_CHECKING, Dict, cast, List

from xdboperator.controller import config, utils, diagnose
from xdboperator.controller.cluster.xdbcluster_api import XDBCluster
from xdboperator.controller.xdbproxy.xdbproxy_api import XDBProxyCluster, XDBProxyPod, XDBProxyStatefulSet
from xdboperator.controller.user.xdbuser_controller import XDBUserController
from xdboperator.lightxdb.zookeeper import ZookeeperService


common_gr_options = {
    # Abort the server if member is kicked out of the group, which would trigger
    # an event from the container restart, which we can catch and act upon.
    # This also makes autoRejoinTries irrelevant.
    "exitStateAction": "ABORT_SERVER"
}


class XDBProxyClusterMutex:
    """
    xdbproxy集群锁,防止多个任务同时操作同一个集群
    """
    def __init__(self, cluster: XDBProxyCluster, pod: Optional[XDBProxyPod] = None, context: str = "n/a"):
        """
        init 入口
        """
        self.cluster = cluster
        self.pod = pod
        self.context = context

    def __enter__(self, *args):
        """
        __enter__
        Raises:
            kopf.TemporaryError: _description_
        """
        owner_lock_creation_time: datetime.datetime
        (owner, owner_context, owner_lock_creation_time) = utils.g_ephemeral_pod_state.testset(
            self.cluster, "xdbproxy-mutex", self.pod.name if self.pod else self.cluster.name, context=self.context)
        if owner:
            raise kopf.TemporaryError(
                f"{self.cluster.name} busy. lock_owner={owner} owner_context={owner_context} \
                    lock_created_at={owner_lock_creation_time.isoformat()}",
                delay=10)

    def __exit__(self, *args):
        """
        __exit__
        """
        utils.g_ephemeral_pod_state.set(self.cluster, "xdbproxy-mutex", None, context=self.context)


class XDBProxyController(object):
    """
    xdbproxy控制器
    """

    def __init__(self, cluster: XDBProxyCluster):
        self.dbproxy = cluster

    def delete_dbproxy_node_from_zk(self, dbproxyPod: XDBProxyPod, logger: logging.Logger) -> int:
        """
        从zk中删除集群
        """
        cluster = dbproxyPod.get_cluster()
        if not cluster:
            return 0
        zk = ZookeeperService(cluster.parsed_spec.ZKDomain, logger=logger)
        _, app_list = zk.zk_proxy_list(cluster.app_id)
        if dbproxyPod.proxy_id in app_list:
            status, output = zk.zk_proxy_drop(cluster.app_id, dbproxyPod.proxy_id)
            logger.info(f"delete_proxy_node {cluster.app_id} status: {status} output:{output}")
        else:
            status, output  = 0, ""
            logger.warning(f"proxy_id:{dbproxyPod.proxy_id} not exist," + \
                           f"skipping proxy drop zk {cluster.app_id} status: {status} output:{output}")
        return status 
    
    def delete_dbproxy_nodes(self, pods: Optional[List[XDBProxyPod]], logger: logging.Logger):
        """
        删除当前集群的所有dbproxy节点
        """
        for pod in pods:
            self.delete_dbproxy_node_from_zk(pod, logger)

    def add_dbproxy_node_from_zk(self, dbproxyPod: XDBProxyPod, logger: logging.Logger) -> int:
        """
        将新pod节点IP注册到zk中，添加dbproxy节点
        """

        cluster = dbproxyPod.get_cluster()
        zk = ZookeeperService(cluster.parsed_spec.ZKDomain, logger=logger)
        _, proxy_list = zk.zk_proxy_list(cluster.app_id)
        if dbproxyPod.proxy_id not in proxy_list:
            status, output = zk.zk_proxy_create(cluster.app_id, dbproxyPod.pod_ip_address,
                                                dbproxyPod.dbproxy_port,
                                                self._format_zk_dbproxy_info(dbproxyPod, cluster))
            logger.info(f"add_dbproxy_node_from_zk {cluster.app_id} status: {status} output:{output}")
        else:
            status, output  = 0, ""
            logger.warning(f"proxy_id:{dbproxyPod.proxy_id} already exist," + \
                           f" skipping proxy register zk ... {cluster.app_id} status: {status} output:{output}")
            
        return status
    
    def _format_zk_dbproxy_info(self, dbproxyPod: XDBProxyPod, cluster: XDBCluster):
        """
        格式化zk中的dbproxy信息
        """
        zk_dbproxy_info = {
            'ip': dbproxyPod.pod_ip_address,
            'port': dbproxyPod.dbproxy_port,
            'version': self.dbproxy.parsed_spec.version,
            'basedir': dbproxyPod.basedir,
            'container_id': dbproxyPod.pod_ip_address,
            'app_id': cluster.app_id,
            'region': self.dbproxy.parsed_spec.region,
            'xagent_port': dbproxyPod.xagent_port,
            'status': 'serving',
            'destStatus': 'serving',
            'map': {
                cluster.app_id: ['group_0000']
            },
            "backlog": 1024,
            "max_threads": 1,
            "log_maxsize": 1800,
            "log_level": 15,
            "log_query_min_time": 0,
            "log_query_time_switch": 1,
            "max_log_per_info_size": 512,
            "log_migrate_switch": 1,
            "log_buffer_size": 100,
            "client_timeout": 600,
            "server_timeout": 600,
            "server_timeout_short": 300,
            "write_time_interval": 200000,
            "conn_pool_socket_max_serve_client_times": 100000,
            "max_query_size": 1024,
            "max_query_num": 5000,
            "switch_status_check": 1,
            "switch_status_forbid": 1,
            "switch_query_with_pk": 1,
            "autoload_local_conf_enable": 1,
            "stream_transport_enable": 1,
            "get_ip_list_interval": 60,
            "switch_same_db_username": 0,
            "switch_baas": 0,
            "safe_check_length": 1024,
            "sql_check_switch_len": 128,
            "sec_process_flag": 0,
            "bandwidth_limit_mb": 100,
            "switch_flow_control": 1,
            "flow_control_enter_standard": 20,
            "sql_flex_length_byte": 500,
            "slow_sql_standard_ms": 1000,
            "white_list_miss_forbid_switch": 0,
            "switch_multi_query": 1,
            "db2cache_enable": 0,
            "timeout_check_interval": 10,
            "reconnect_times": 3,
            "proxy_status_interval": 0,
            "slow_query_threshold": 5000,
            "log_dir": "./log/",
            "log_filename": "dbproxy.log",
            "ns_dir": "./conf",
            "default_charset": "utf8",
            "switch_mysql_8": 0
        }
        return zk_dbproxy_info

    def publish_status(self) -> None:
        """
            Publishes the current cluster status
        """
        cluster_status = self.dbproxy.get_cluster_status()
        if cluster_status and cluster_status["status"] != self.dbproxy.sts_status:
            self.dbproxy.info(action="ClusterStatus", reason="StatusChange",
                              message=f"Cluster status changed to {self.dbproxy.sts_status}.{self.dbproxy.online_instances} \
                                  member(s) ONLINE")

        cluster_status = {
            "status": self.dbproxy.sts_status,
            "onlineInstances": self.dbproxy.online_instances,
            "lastProbeTime": utils.isotime()
        }
        self.dbproxy.set_cluster_status(cluster_status)

    def probe_status(self, logger):
        """
        探测集群状态
        """
        if not self.dbproxy.deleting:
            self.publish_status()
        logger.info(f"xdbproxy cluster probe: status={self.dbproxy.sts_status} online={self.dbproxy.online_instances}")

    def on_cr_probe_pod_xdbuser_access_status(self, pod: XDBProxyPod, logger: logging.Logger):
        """
        探测 new Pod的XDBUser访问结果，需要保证全部账户都能从new Pod访问
        注意：
        """
        result = True
        users = self.dbproxy.get_xdb_users_from_cr()
        logger.debug(f"new pod start check user access, users count:{len(users)}...")
        for xdbuser in users:
            xdbuser_ctl = XDBUserController(xdbuser, logger)
            result = xdbuser_ctl.check_user_access(logger, pod.pod_ip_address)
            logger.debug(f"new pod start check user access, user:{xdbuser.name}, ret:{result}...")
            if not result:
                result = False
                break
        return result
    
    def on_cr_add_new_node_mysql_account_and_grants_db(self, dbproxyPod: XDBProxyPod, logger: logging.Logger):
        """
        为新加入的dbproxy节点创建用户和授权
        """
        users = self.dbproxy.get_xdb_users_from_cr()
        logger.info(f"new pod start create user and grants db, users count:{len(users)}...")
        for xdbuser in users:
            if xdbuser.parsed_spec.allowedHosts:
                continue
            else:
                user_ctrl = XDBUserController(xdbuser, logger)
                ret =  user_ctrl.sync_xdbproxy_new_node_user_permissions(dbproxyPod.pod_ip_address, logger)
                if ret == -1:
                    logger.error(f"create new node mysql account and grants db failed,ret:{ret} user:{xdbuser.name}")
                    return ret
                elif ret == 0:
                    logger.info(f"create new node mysql account and grants db success, user:{xdbuser.name}")
                else:
                    logger.warning(f"create new node mysql account and grants db failed,ret:{ret} user:{xdbuser.name}")
        return 0
    
    def on_zk_probe_pod_xdbuser_access_status(self, pod: XDBProxyPod, logger: logging.Logger):
        """
        探测 new Pod的XDBUser访问结果，需要保证全部账户都能从new Pod访问
        注意：zk 账户信息最全，可能有一部分账户没有通过cr创建的，所以需要通过zk获取
        Args:
            pod (XDBProxyPod): _description_
            logger (logging.Logger): _description_

        Returns:
            _type_: _description_
        """
        result = True
        users = self.dbproxy.get_xdb_user_from_zk()
        logger.debug(f"new pod start check user access, users count:{len(users)}...")
        for user in users:
              #跳过 localhost类型的用户
            if user.get("allowed_hosts", []) and "localhost" in user["allowed_hosts"]:
                continue
            result = XDBUserController.check_user_access_status(user, pod.pod_ip_address, logger)
            logger.debug(f"new pod start check user access, user:{user['db_username']}, ret:{result}...")
            if not result:
                break
        return result
    
    def on_zk_add_new_node_mysql_account_and_grants_db(self, pod: XDBProxyPod, logger: logging.Logger):
        """
        为新加入的dbproxy节点创建用户和授权
        注意：zk 账户信息最全，可能有一部分账户没有通过cr创建的，所以需要通过zk获取
        Args:
            pod (XDBProxyPod): _description_
            logger (logging.Logger): _description_

        Returns:
            _type_: _description_
        """
        users = self.dbproxy.get_xdb_user_from_zk()
        xdb = self.dbproxy.get_cluster()
        logger.info(f"new pod start create user and grants db, users count:{len(users)}...")
        for user in users:
            #跳过 localhost、% 类型的用户
            if user.get("allowed_hosts", []) and "%" in user["allowed_hosts"]:
                continue
            result = XDBUserController.sync_xdbproxy_new_node_user_permissions_to_zk(xdb, pod.pod_ip_address,
                                                                                     user, logger)
            if result == -1:
                logger.error("create new node mysql account and grants db failed," + \
                             f"ret:{result} user:{user['db_username']}")
                return result
            elif result == 0:
                logger.info(f"create new node mysql account and grants db success," + \
                            f"user:{user['db_username']}")
            else:
                logger.warning(f"create new node mysql account and grants db failed," + \
                               f"ret:{result} user:{user['db_username']}")
        return 0
    
    def on_pod_created(self, pod: XDBProxyPod, logger: logging.Logger) -> None:
        """
        在创建XDBProxyPod时触发，执行相应操作
        """
        
        self.probe_status(logger)
        # init member readiness gate
        if pod.get_member_readiness_gate("registerZk") is None:
            pod.update_member_readiness_gate("registerZk", False)
        if pod.get_member_readiness_gate("ready") is None:
            pod.update_member_readiness_gate("ready", False)
        if pod.get_member_readiness_gate("grants") is None:
            pod.update_member_readiness_gate("grants", False)
        
        if not pod.pod_ip_address:
            msg = "waiting for the dbproxy pod ip to be ready"
            logger.warning(msg)
            self.dbproxy.info(action="CreateDBProxyCluster", reason="ReadyCheckFailed", message=msg)
            raise kopf.TemporaryError("dbproxy pod ip is not yet ready", delay=15)
        if  pod.get_member_readiness_gate("registerZk") is False: 
            if self.add_dbproxy_node_from_zk(pod, logger) == 0:
                pod.update_member_readiness_gate("registerZk", True)
            else:
                msg = "register db proxy node to zk failed"
                logger.error(msg)
                self.dbproxy.warn(action="CreateDBProxyCluster", reason="CreateResourceFailed",
                            message=f"register db proxy node to zk failed")
                raise kopf.TemporaryError("register db proxy node to zk failed", delay=30)
        if  pod.get_member_readiness_gate("grants") is False:
             if self.on_zk_add_new_node_mysql_account_and_grants_db(pod, logger) == 0:
                 pod.update_member_readiness_gate("grants", True)
             else:
                 msg = "create new node mysql account and grants db failed"
                 logger.error(msg)
                 self.dbproxy.warn(action="CreateDBProxyCluster", reason="CreateResourceFailed",
                                message=f"create new node mysql account and grants db failed")
                 raise kopf.TemporaryError("create new node mysql account and grants db failed", delay=30)
    
        if  pod.get_member_readiness_gate("ready") is False:
            if not self.on_zk_probe_pod_xdbuser_access_status(pod, logger):
                msg = f"check pod:{pod.name} node user access failed"
                logger.error(msg)
                raise kopf.TemporaryError("xdb user access no ready, wait retry", delay=30)
            else:
                pod.update_member_readiness_gate("ready", True)
                msg = f"check pod:{pod.name} node user access success"
                logger.debug(msg)
        self.probe_status(logger)


    def on_pod_deleted(self, pod: XDBProxyPod, pod_body: Body, logger: logging.Logger) -> None:
        """
        处理Pod删除事件

        Args:
            pod (XDBProxyPod): 删除的Pod对象
            pod_body (Body): Pod的Body对象
            logger (Logger): 日志记录器

        Returns:
            None

        """
        logger.debug(f"on_pod_deleted: pod={pod.name}")
        if pod.deleting:
            ret = self.delete_dbproxy_node_from_zk(pod, logger)
            if ret == 0:
                pod.remove_member_finalizer(pod_body)
            else:
                logger.error(f"delete dbproxy node from zk failed, error code: {ret}")
