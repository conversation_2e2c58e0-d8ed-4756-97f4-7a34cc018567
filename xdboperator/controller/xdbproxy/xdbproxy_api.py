#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   xdbproxy_api.py
@Time    :   2024/8/22 19:25
<AUTHOR>   lixianzhu
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import abc
from  enum import Enum
import datetime
import json
import typing
from kopf._cogs.structs.bodies import Body
from kubernetes import client
from logging import Logger
from typing import Optional, cast, overload, List, Dict, Union

from xdboperator.controller import utils, consts, config, diagnose
from xdboperator.controller.api_utils import ApiSpecError
from xdboperator.controller.api_utils import dget_str, dget_int, Edition, dget_bool, dget_dict, dget_enum, dget_str, \
    dget_int, dget_float, dget_list, ApiSpecError, ImagePullPolicy
from xdboperator.controller.cluster.xdbcluster_api import XDBCluster
from xdboperator.controller.user.xdbuser_api import XDBMySQLUser
from xdboperator.controller.k8sobject import K8sInterfaceObject
from xdboperator.controller.kubeutils import api_core, api_apps, api_customobj, api_policy
from xdboperator.controller.kubeutils import client as api_client, ApiException, k8s_cluster_domain
from xdboperator.controller.kubeutils import api_kruise

MAX_CLUSTER_NAME_LEN = 64


class XDBProxySpecProperties(Enum):
    CLUSTER = "clusterRef"


class ClusterReferenceSpec(object):
    namespace: str = None
    name: str = ""

    def parse(self, spec: dict, prefix: str) -> None:
        if "namespace" in spec:
            self.namespace = dget_str(spec, "namespace", prefix)

        if "name" in spec:
            self.name = dget_str(spec, "name", prefix)

    def __str__(self):
        return f"{self.namespace}/{self.name}"

    def __repr__(self):
        return f"<ClusterReferenceSpec {self.name}>"


class AbstractServerSetSpec(abc.ABC):
    """
    spec 抽象类
    """
    imagePullPolicy: ImagePullPolicy = config.default_image_pull_policy
    imagePullSecrets: Optional[List[dict]] = None
    imageRepository: str = config.DEFAULT_IMAGE_REPOSITORY
    serviceAccountName: Optional[str] = None


    def __init__(self, namespace: str, name: str, cluster_name: str, spec: dict):
        self.namespace = namespace
        self.name = name
        self.cluster_name = cluster_name

    # @abc .abstracmethod
    def load(self, spec: dict) -> None:
        ...

    def _load(self, spec_root: dict, spec_specific: dict, where_specific: str) -> None:
        if "imageRepository" not in spec_root:
            self.imageRepository = config.DEFAULT_IMAGE_REPOSITORY

        if "imageRepository" in spec_root:
            self.imageRepository = dget_str(spec_root, "imageRepository", "spec")

    def validate(self, logger: Logger) -> None:
        # TODO see if we can move some of these to a schema in the CRD

        if len(self.name) > MAX_CLUSTER_NAME_LEN:
            raise ApiSpecError(
                f"CR name {self.name} is too long. Must be < {MAX_CLUSTER_NAME_LEN}")

        # self.logs.validate()


class ServiceSpec(object):
    """
    service spec
    """
    type: str = "ClusterIP"
    annotations: dict = {}
    labels: dict = {}
    defaultPort: str = "xdb-rw"


    def parse(self, spec: dict, prefix: str) -> None:
        """
        处理svc的参数
        """
        if "type" in spec:
            self.type = dget_str(spec, "type", prefix)

        if "annotations" in spec:
            self.annotations = dget_dict(spec, "annotations", prefix)

        if "labels" in spec:
            self.labels = dget_dict(spec, "labels", prefix)

        if "defaultPort" in spec:
            self.defaultPort = dget_str(spec, "defaultPort", prefix)


class ResourceRequirements(object):
    """
    资源配置类型
    """
    def __init__(self):
        self.requests: Dict[str, str] = {}  # cpu/memory requests
        self.limits: Dict[str, str] = {}    # cpu/memory limits
        
    def parse(self, spec: dict, prefix: str) -> None:
        """
        解析参数
        """
        if "requests" in spec:
            self.requests = dget_dict(spec, "requests", prefix)
        if "limits" in spec:
            self.limits = dget_dict(spec, "limits", prefix)

class StorageConfig(object):
    """
    存储配置类型
    """
    def __init__(self):
        self.storageType: str = "emptyDir"  # emptyDir 或 persistentVolume
        self.storageClass: Optional[str] = None
        self.size: Optional[str] = None
        
    def parse(self, spec: dict, prefix: str) -> None:
        """
        解析参数
        """
        if "storageType" in spec:
            self.storageType = dget_str(spec, "storageType", prefix)
        if "storageClass" in spec:
            self.storageClass = dget_str(spec, "storageClass", prefix)
        if "size" in spec:
            self.size = dget_str(spec, "size", prefix)

class StorageSpec(object):
    """
    存储规格类型
    """
    def __init__(self):
        self.xdbproxy: StorageConfig = StorageConfig()
        self.xagent: StorageConfig = StorageConfig()
        
    def parse(self, spec: dict, prefix: str) -> None:
        """
        解析参数
        """
        if "xdbproxy" in spec:
            self.xdbproxy.parse(dget_dict(spec, "xdbproxy", prefix), f"{prefix}.xdbproxy")
        if "xagent" in spec:
            self.xagent.parse(dget_dict(spec, "xagent", prefix), f"{prefix}.xagent")

class XDBProxyDefaults:
    """XDBProxy 默认配置"""
    
    # 默认资源配置
    DEFAULT_RESOURCES = {
        "requests": {
            "cpu": "2",
            "memory": "2Gi"
        },
        "limits": {
            "cpu": "2", 
            "memory": "2Gi"
        }
    }
    
    # 默认 affinity 配置
    DEFAULT_AFFINITY = {
        "podAntiAffinity": {
            "preferredDuringSchedulingIgnoredDuringExecution": [{
                "weight": 100,
                "podAffinityTerm": {
                    "labelSelector": {
                        "matchLabels": {
                            "component": "xdbproxy",
                            "cloudbed.abcstack.com/cluster": "{cluster_name}"  # 将在使用时替换
                        }
                    },
                    "topologyKey": "kubernetes.io/hostname"
                }
            }]
        }
    }
    
    # 默认存储配置
    DEFAULT_STORAGE = {
        "xdbproxy": {
            "storageType": "emptyDir",
            "storageClass": None,
            "size": None
        },
        "xagent": {
            "storageType": "emptyDir", 
            "storageClass": None,
            "size": None
        }
    }

class XDBProxyClusterSpec(AbstractServerSetSpec):
    """
    dbproxy 集群创建的 crd spec
    """
    instances: int = 3
    clusterRef: ClusterReferenceSpec = None
    version: str = "********"
    image: Optional[str] = None
    reserved_dbproxy_logs_size: str = "50G"
    xagent_port: int = 8500
    dbproxy_port: int = 6203
    region: str = "cn-beijing"
    service: ServiceSpec = ServiceSpec()
    affinity: Optional[dict] = None  # 保持dict类型以支持完整的k8s affinity配置
    resources: Optional[ResourceRequirements] = None
    storage: Optional[StorageSpec] = None
    resources: Optional[dict] = None
    
    def __init__(self, namespace: str, name: str, spec: dict):
        super().__init__(namespace, name, name, spec)
        self.namespace = namespace
        self.name = name
        self.load(spec)
        

    def load(self, spec: dict) -> None:
        self._load(spec, spec, "spec")
        self.instances = dget_int(spec, "instances", "spec")
        if "image" in spec:
            self.image = dget_str(spec, "image", "spec")
        self.clusterRef = ClusterReferenceSpec()
        section = XDBProxySpecProperties.CLUSTER.value
        if section in spec:
            self.clusterRef.parse(dget_dict(spec, section, "spec"), "spec.clusterRef")
        if "reserved_dbproxy_logs_size" in spec:
            self.reserved_dbproxy_logs_size = dget_str(spec, "reserved_dbproxy_logs_size", "spec")
        if "version" in spec:
            self.version = dget_str(spec, "version", "spec")
        if "affinity" in spec:
            self.affinity = dget_dict(spec, "affinity", "spec")
        if "resources" in spec:
            self.resources = ResourceRequirements()
            self.resources.parse(dget_dict(spec, "resources", "spec"), "spec.resources")
        if "storage" in spec:
            self.storage = StorageSpec()
            self.storage.parse(dget_dict(spec, "storage", "spec"), "spec.storage")

    def validate(self, logger: Logger) -> None:
        super().validate(logger)

    @property
    def dbproxy_image(self) -> str:
        if self.version:
            version = self.version
        else:
            version = config.DEFAULT_PROXY_VERSION_TAG
        image = config.XDB_MYSQL_PROXY_IMAGE
        return self.format_image(image, version)

    @property
    def dbproxy_image_pull_policy(self) -> str:
        return self.imagePullPolicy.value

    def format_image(self, image, version):
        if self.imageRepository:
            return f"{self.imageRepository}/{image}:{version}"
        return f"{image}:{version}"


class XDBProxyCluster(K8sInterfaceObject):
    """
    xdb dbproxy cluster cr object
    """

    def __init__(self, cluster: Body) -> None:

        super().__init__()
        self.obj: Body = cluster
        self._parsed_spec: Optional[XDBProxyClusterSpec] = None

    def __str__(self):
        return f"{self.namespace}/{self.name}"

    def __repr__(self):

        return f"<XDBProxyCluster {self.name}>"

    @classmethod
    def _get(cls, ns: str, name: str) -> Body:

        try:
            ret = cast(Body, api_customobj.get_namespaced_custom_object(
                consts.GROUP, consts.VERSION, ns,
                consts.XDBPROXY_PLURAL, name))
        except ApiException as e:
            raise e
        return ret

    @classmethod
    def _patch(cls, ns: str, name: str, patch: dict) -> Body:
        return cast(Body, api_customobj.patch_namespaced_custom_object(
            consts.GROUP, consts.VERSION, ns,
            consts.XDBPROXY_PLURAL, name, body=patch))

    @classmethod
    def _patch_status(cls, ns: str, name: str, patch: dict) -> Body:
        return cast(Body, api_customobj.patch_namespaced_custom_object_status(
            consts.GROUP, consts.VERSION, ns,
            consts.XDBPROXY_PLURAL, name, body=patch))

    @classmethod
    def read(cls, ns: str, name: str) -> 'XDBProxyCluster':
        return XDBProxyCluster(cls._get(ns, name))

    @property
    def metadata(self) -> dict:
        return self.obj["metadata"]

    @property
    def annotations(self) -> dict:
        return self.metadata["annotations"]

    @property
    def spec(self) -> dict:
        return self.obj["spec"]

    @property
    def status(self) -> dict:
        if "status" in self.obj:
            return self.obj["status"]
        return {}

    @property
    def name(self) -> str:
        return self.metadata["name"]

    @property
    def namespace(self) -> str:
        return self.metadata["namespace"]

    @property
    def uid(self) -> str:
        return self.metadata["uid"]

    @property
    def deleting(self) -> bool:
        return "deletionTimestamp" in self.metadata and self.metadata["deletionTimestamp"] is not None

    @property
    def xdb_cluster_name(self) -> str:
        return self.parsed_spec.clusterRef.name

    @property
    def xdb_cluster_ns(self) -> str:
        return self.parsed_spec.clusterRef.namespace

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        ref = {
            "apiVersion": consts.API_VERSION,
            "kind": consts.XDBFUSIONCLUSTER_PLURAL,
            "name": self.name,
            "namespace": self.namespace,
            "resourceVersion": self.metadata["resourceVersion"],
            "uid": self.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    @property
    def parsed_spec(self) -> XDBProxyClusterSpec:
        if not self._parsed_spec:
            self.parse_spec()
            assert self._parsed_spec

        return self._parsed_spec

    def parse_spec(self) -> None:
        self._parsed_spec = XDBProxyClusterSpec(self.namespace, self.name, self.spec)

    def reload(self) -> None:
        self.obj = self._get(self.namespace, self.name)

    def owns_pod(self, pod) -> bool:
        owner_sts = pod.owner_reference(consts.APP_V1_API_VERSION, consts.APP_V1_STATEFULSET_KIND)
        return owner_sts.name == self.name

    def get_pod(self, index) -> 'XDBProxyPod':
        pod = cast(api_client.V1Pod, api_core.read_namespaced_pod(
            "%s-%i" % (self.name, index), self.namespace))
        return XDBProxyPod(pod)

    def get_sts(self) -> 'XDBProxyStatefulSet':
        """
        获取xdbproxy的sts对象
        """
        sts = cast(api_client.V1StatefulSet, api_apps.read_namespaced_stateful_set(
            self.name, self.namespace))
        return XDBProxyStatefulSet(sts)

    
    def get_xdb_cluster(self):
        """
            获取集群信息，如果不存在则返回None。
        如果出现API异常并且状态码为404，则返回None；否则抛出异常。
        
        Args:
            无参数。
        
        Returns:
            XDBCluster (Optional[XDBCluster]): 集群对象，可能为None。如果集群不存在，则返回None。
        
        Raises:
            ApiException (ApiException): API请求失败时抛出的异常，包含错误代码和消息。
                如果集群不存在，则错误代码为404。
            Exception (Exception): 其他情况下抛出的异常。
        """
        try:
            return XDBCluster.read(self.xdb_cluster_ns, self.xdb_cluster_name)
        except ApiException as e:
            print(
                f"Could not get cluster {self.xdb_cluster_ns}/{self.xdb_cluster_name}: {e}")
            if e.status == 404:
                return None
            raise
    
    def get_pods(self) -> typing.List['XDBProxyPod']:
        # get all pods that belong to the same container
        objects = cast(api_client.V1PodList, api_core.list_namespaced_pod(
            self.namespace, label_selector="cloudbed.abcstack.com/subsystem=XDBProxy"))
        pods = []
        for o in objects.items:
            pod = XDBProxyPod(o)
            if self.owns_pod(pod):
                pods.append(pod)
        pods.sort(key=lambda pod: pod.index)
        return pods
    
    def get_xdb_users_from_cr(self, ns: str = None) -> typing.List['XDBMySQLUser']:
        """
        根据 field 过滤出当前xdb集群的关联全部用户
        问：为什么不根据label，因为早期设计user cr 的时候忘记添加绑定集群的label了。。。
        """
        # field_selector = f'spec.clusterRef.name={self.xdb_cluster_name}'
        users = []
        if ns is None:
            objects = cast(api_client.V1CustomResourceDefinitionList, api_customobj.list_cluster_custom_object(
            consts.GROUP, consts.VERSION, consts.XDB_USER_PLURAL))
        else:
            objects = cast(api_client.V1CustomResourceDefinitionList, api_customobj.list_namespaced_custom_object(
            consts.GROUP, consts.VERSION, ns, consts.XDB_USER_PLURAL))
            
        for o in objects["items"]:
            user = XDBMySQLUser(o)
            print(f"xdb user obj: {user} user.xdb_cluster_name: {user.xdb_cluster_name} \
                  self.xdb_cluster_name: {self.xdb_cluster_name} user.status: {user.status}")
            if user.xdb_cluster_name == self.xdb_cluster_name and \
            user.get_xdbuser_status("status") in  {diagnose.ClusterDiagStatus.RUNNING.value,
                                                   diagnose.ClusterDiagStatus.WAIT_ACCESS_PASS.value,
                                                   diagnose.ClusterDiagStatus.CREATING.value,
                                                   }:
                users.append(user)
        return users
    
    def get_xdb_user_from_zk(self):
        """
        从zk获取集群关联的信息
        """
        xdb = self.get_xdb_cluster()
        users = xdb.zk_server.get_product_all_account_detail()
        return users
        

    def get_dbproxy_service(self) -> typing.Optional[api_client.V1Service]:
        try:
            return cast(api_client.V1Service,
                        api_core.read_namespaced_service(self.name, self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise
        
    @classmethod
    def get_service_account(cls, spec: AbstractServerSetSpec) -> api_client.V1ServiceAccount:
        """
        获取关联proxy集群的使用的service account
        """
        return cast(api_client.V1ServiceAccount,
                    api_core.read_namespaced_service_account(spec.name, spec.namespace))
    
    def get_xdb_service(self) -> typing.Optional[api_client.V1Service]:
        """
       
        获取关联xdb集群的使用的service
        
        Args:
            None
        
        Returns:
            typing.Optional[api_client.V1Service]: 返回一个包含数据库代理服务信息的V1Service对象，如果不存在则返回None。
        
        Raises:
            None
        """
        try:
            return cast(api_client.V1Service,
                        api_core.read_namespaced_service(self.xdb_cluster_name, self.xdb_cluster_ns))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    # As of K8s 1.21 this is no more beta.
    # Thus, eventually this needs to be upgraded to V1PodDisruptionBudget and api_policy to PolicyV1Api
    def get_disruption_budget(self) -> typing.Optional[api_client.V1PodDisruptionBudget]:
        try:
            return cast(api_client.V1PodDisruptionBudget,
                        api_policy.read_namespaced_pod_disruption_budget(self.name + "-pdb", self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def get_dbproxy_headless_service(self) -> typing.Optional[api_client.V1Service]:
        try:
            return cast(api_client.V1Service,
                        api_core.read_namespaced_service(self.name + "-hs", self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def get_xdbcluster_stateful_set(self) -> typing.Optional[api_client.V1StatefulSet]:
        """
        获取XDBCluster的StatefulSet对象，优先尝试获取传统的StatefulSet，如果不存在则尝试获取Kruise StatefulSet。
            如果发生API异常并且状态码为404，则返回None；否则抛出异常。
        
            Args:
                self (XDBCluster): XDBCluster实例。
        
            Returns:
                Optional[api_client.V1StatefulSet]: StatefulSet对象，如果不存在则返回None。
                    API异常时，如果状态码为404，则返回None；其他情况抛出异常。
        """
        try:
            # 首先尝试获取传统的 StatefulSet
            return cast(api_client.V1StatefulSet,
                        api_apps.read_namespaced_stateful_set(self.xdb_cluster_name,
                                                              self.xdb_cluster_ns))
        except ApiException as e:
            if e.status == 404:
                try:
                    # 如果传统的 StatefulSet 不存在，尝试获取 Kruise StatefulSet
                    kruise_sts = api_kruise.get_namespaced_custom_object(
                        group=consts.KRUISE_GROUP,
                        version=consts.KRUISE_VERSION,
                        namespace=self.xdb_cluster_ns,
                        plural=consts.KRUISE_STATEFULSET_PLURAL,
                        name=self.xdb_cluster_name
                    )
                    return cast(api_client.V1StatefulSet, kruise_sts)
                except ApiException as e2:
                    if e2.status == 404:
                        return None
                    raise e2
            raise e

    def get_stateful_set(self) -> typing.Optional[api_client.V1StatefulSet]:
        """
        获取当前 StatefulSet，优先尝试获取传统的StatefulSet，如果不存在则尝试获取Kruise StatefulSet。
        
            Args:
                self (K8sResource): K8sResource 实例，表示当前资源对象。
        
            Returns:
                typing.Optional[api_client.V1StatefulSet]: V1StatefulSet 类型的可选对象，表示当前 StatefulSet，如果不存在则为 None。
        
            Raises:
                ApiException (ApiException): 如果 API 调用失败，将抛出 ApiException 异常，包含 HTTP 状态码和错误信息。
        """
        try:
            return cast(api_client.V1StatefulSet,
                        api_apps.read_namespaced_stateful_set(self.name, self.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    @classmethod
    def get_xdb_cm(cls, spec: AbstractServerSetSpec) -> typing.Optional[api_client.V1ConfigMap]:
        try:
            return cast(api_client.V1ConfigMap,
                        api_core.read_namespaced_config_map(spec.name, spec.namespace))
        except ApiException as e:
            if e.status == 404:
                return None
            raise

    def _get_status_field(self, field: str) -> typing.Any:
        return cast(str, self.status.get(field))

    def _set_status_field(self, field: str, value: typing.Any) -> None:
        obj = self._get(self.namespace, self.name)
        if "status" not in obj:
            patch = {"status": {}}
        else:
            patch = {"status": obj["status"]}
        patch["status"][field] = value
        self.obj = self._patch_status(self.namespace, self.name, patch)

    def set_cluster_status(self, cluster_status) -> None:
        self._set_status_field("cluster", cluster_status)

    def get_cluster_status(self, field=None):
        status = self._get_status_field("cluster")
        if status and field:
            return status.get(field)
        return status

    @property
    def online_instances(self) -> int:
        """
        获取在线实例数
        """
        sts = self.get_stateful_set()
        return sts.status.ready_replicas if sts else 0
    
    @property
    def cluster_sts_ready(self) -> bool:
        sts = self.get_stateful_set()
        return sts.status.ready_replicas == sts.status.replicas if sts else False
    
    @property
    def sts_status(self):
        """
        根据sts 就绪节点，返回集群状态
        """
        return diagnose.ClusterDiagStatus.RUNNING.value if self.cluster_sts_ready \
            else diagnose.ClusterDiagStatus.CREATING.value
        
    def set_status(self, status) -> None:

        obj = cast(dict, self._get(self.namespace, self.name))

        if "status" not in obj:
            obj["status"] = status
        else:
            utils.merge_patch_object(obj["status"], status)
        self.obj = self._patch_status(self.namespace, self.name, obj)

    def update_cluster_info(self, info: dict) -> None:
        patch = {
            "metadata": {
                "annotations": {
                    "cloudbed.abcstack.com/cluster-info": json.dumps(info)
                }
            }
        }
        self.obj = self._patch(self.namespace, self.name, patch)

    # TODO remove field
    def get_cluster_info(self, field: typing.Optional[str] = None) -> typing.Optional[dict]:
        if self.annotations:
            info = self.annotations.get("cloudbed.abcstack.com/cluster-info", None)
            if info:
                info = json.loads(info)
                if field:
                    return info.get(field)
                return info
        return None

    def set_create_time(self, time: datetime.datetime = None) -> None:
        if not time:
            time = datetime.datetime.now()
        self._set_status_field("createTime", time.replace(
            microsecond=0).isoformat() + "Z")

    def get_create_time(self) -> Optional[datetime.datetime]:
        dt = self._get_status_field("createTime")
        if dt:
            return datetime.datetime.fromisoformat(dt.rstrip("Z"))
        return None

    @property
    def ready(self) -> bool:
        """
        获取集群状态是否就绪
        """
        return cast(bool, self.get_create_time())

    def set_last_known_quorum(self, members):
        # TODO
        pass

    def get_last_known_quorum(self):
        # TODO
        return None

    def _add_finalizer(self, fin: str) -> None:
        patch = {
            "metadata": {
                "finalizers": [fin]
            }
        }
        self.obj = self._patch(self.namespace, self.name, patch)

    def _remove_finalizer(self, fin: str) -> None:

        # TODO strategic merge patch not working here??
        # patch = { "metadata": { "$deleteFromPrimitiveList/finalizers": [fin] }}
        patch = {"metadata": {"finalizers": [
            f for f in self.metadata["finalizers"] if f != fin]}}

        self.obj = self._patch(self.namespace, self.name, patch)

    def add_cluster_finalizer(self) -> None:
        self._add_finalizer("cloudbed.abcstack.com/cluster")

    def remove_cluster_finalizer(self, cluster_body: dict = None) -> None:

        self._remove_finalizer("cloudbed.abcstack.com/cluster")
        if cluster_body:
            # modify the JSON data used internally by kopf to update its finalizer list
            cluster_body["metadata"]["finalizers"].remove(
                "cloudbed.abcstack.com/cluster")

    def set_operator_version(self, version: str) -> None:

        v = self.operator_version
        if v != version:
            patch = {"metadata": {"annotations": {"cloudbed.abcstack.com/xdb-operator-version": version}}}
            # TODO store the current server/router version + timestamp
            # store previous versions in a version history log
            self.obj = self._patch(self.namespace, self.name, patch)

    @property
    def operator_version(self) -> Optional[str]:
        return self.metadata.get("cloudbed.abcstack.com/xdb-operator-version")

    def set_current_version(self, version: str) -> None:

        v = self.status.get("version")
        if v != version:
            patch = {"status": {"version": version}}

            # TODO store the current server/router version + timestamp
            # store previous versions in a version history log
            self.obj = self._patch_status(self.namespace, self.name, patch)

    def log_cluster_info(self, logger: Logger) -> None:
        logger.info(f"XDBProxyCluster {self.namespace}/{self.name}" + \
            f"{self.parsed_spec.clusterRef.namespace} / {self.parsed_spec.clusterRef.name}")

    def get_cluster(self):

        """
            获取集群信息，如果不存在则返回None。
        如果出现API异常并且状态码为404，则返回None；否则抛出异常。
        
        Args:
            无参数。
        
        Returns:
            XDBCluster (Optional[XDBCluster]): 集群对象，可能为None。如果集群不存在，则返回None。
        
        Raises:
            ApiException (ApiException): API请求失败时抛出的异常，包含错误代码和消息。
                如果集群不存在，则错误代码为404。
            Exception (Exception): 其他情况下抛出的异常。
        """
        try:
            return XDBCluster.read(self.xdb_cluster_ns, self.xdb_cluster_name)
        except ApiException as e:
            print(
                f"Could not get cluster {self.xdb_cluster_ns}/{self.xdb_cluster_name}: {e}")
            if e.status == 404:
                return None
            raise
        
class XDBProxyStatefulSet(K8sInterfaceObject):
    """
    xdb proxy cluster deployment
    """
    logger: Optional[Logger] = None

    def __init__(self, deploy: client.V1StatefulSet):
        super().__init__()
        self.sts: client.V1StatefulSet = deploy

    def __str__(self) -> str:
        return self.name

    def __repr__(self) -> str:
        return f"<XDBSts {self.name}>"

    @classmethod
    def read(cls, name: str, ns: str) -> 'XDBProxyStatefulSet':
        """
        获取XDBProxyStatefulSet对象
        """
        return XDBProxyStatefulSet(cast(api_client.V1Deployment,
                                       api_apps.read_namespaced_stateful_set(name, ns)))

    @property
    def metadata(self) -> api_client.V1ObjectMeta:
        return cast(api_client.V1ObjectMeta, self.sts.metadata)

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        ref = {
            "apiVersion": self.sts.api_version,
            "kind": self.sts.kind,
            "name": self.name,
            "namespace": self.namespace,
            "resourceVersion": self.metadata.resource_version,
            "uid": self.metadata.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    @property
    def status(self) -> api_client.V1StatefulSet:
        """
        获取sts的状态
        """
        return cast(api_client.V1StatefulSet, self.sts.status)

    @property
    def namespace(self) -> str:
        """
        namespace
        """
        return cast(str, self.metadata.namespace)

    @property
    def deleting(self) -> bool:
        return self.metadata.deletion_timestamp is not None

    @property
    def spec(self) -> api_client.V1PodSpec:
        return cast(api_client.V1PodSpec, self.sts.spec)

    @property
    def name(self) -> str:
        return cast(str, self.metadata.name)


class XDBProxyPod(K8sInterfaceObject):
    """
    xdb proxy pod
    """
    logger: Optional[Logger] = None

    def __init__(self, pod: client.V1Pod):
        super().__init__()

        self.pod: client.V1Pod = pod
        self.dbproxy_port = 6203
        self.xagent_port = 8500
        self.admin_account = None
        self.basedir = "/home/<USER>/dbproxy_6203"

    @overload
    @classmethod
    def from_json(cls, pod: str) -> 'XDBProxyPod':
        ...

    @overload
    @classmethod
    def from_json(cls, pod: Body) -> 'XDBProxyPod':
        ...

    @classmethod
    def from_json(cls, pod) -> 'XDBProxyPod':
        class Wrapper:
            def __init__(self, data):
                self.data = json.dumps(data)

        if not isinstance(pod, str):
            pod = eval(str(pod))

        return XDBProxyPod(cast(client.V1Pod, api_core.api_client.deserialize(
            Wrapper(pod), client.V1Pod)))

    def __str__(self) -> str:
        return self.name

    def __repr__(self) -> str:
        return f"<XDBProxyPod {self.name}>"

    @classmethod
    def read(cls, name: str, ns: str) -> 'XDBProxyPod':
        return XDBProxyPod(cast(client.V1Pod,
                                api_core.read_namespaced_pod(name, ns)))

    @property
    def metadata(self) -> api_client.V1ObjectMeta:
        return cast(api_client.V1ObjectMeta, self.pod.metadata)

    def self_ref(self, field_path: Optional[str] = None) -> dict:
        ref = {
            "apiVersion": self.pod.api_version,
            "kind": self.pod.kind,
            "name": self.name,
            "namespace": self.namespace,
            "resourceVersion": self.metadata.resource_version,
            "uid": self.metadata.uid
        }
        if field_path:
            ref["fieldPath"] = field_path
        return ref

    @property
    def status(self) -> api_client.V1PodStatus:
        return cast(api_client.V1PodStatus, self.pod.status)

    @property
    def phase(self) -> str:
        return cast(str, self.status.phase)

    @property
    def deleting(self) -> bool:
        return self.metadata.deletion_timestamp is not None

    @property
    def spec(self) -> api_client.V1PodSpec:
        return cast(api_client.V1PodSpec, self.pod.spec)

    @property
    def name(self) -> str:
        return cast(str, self.metadata.name)

    @property
    def index(self) -> int:
        return int(self.name.rpartition("-")[-1])
    
    @property
    def sts_name(self) -> str:
        """
        获取sts的name
        """
        return self.name.rpartition("-")[0]
    @property
    def proxy_id(self) -> str:
        """
        获取proxy的id，在xdb控制面zk记录中是唯一的
        """
        return f"dbproxy_{self.pod_ip_address}_{self.dbproxy_port}"

    @property
    def namespace(self) -> str:
        return cast(str, self.metadata.namespace)

    @property
    def xdbcluster_name(self) -> str:
        return self.pod.metadata.labels["cloudbed.abcstack.com/xdb"]

    @property
    def xdbproxy_cluster_name(self) -> str:
        return self.pod.metadata.labels["cloudbed.abcstack.com/cluster"]

    @property
    def instance_type(self) -> str:
        if "cloudbed.abcstack.com/instance-type" in self.pod.metadata.labels:
            return self.pod.metadata.labels["cloudbed.abcstack.com/instance-type"]
        else:
            # With old clusters the label may be missing
            return "group-member"

    @property
    def address(self) -> str:
        return self.name + "." + cast(str, self.spec.subdomain)

    @property
    def address_fqdn(self) -> str:
        return self.name + "." + cast(str, self.spec.subdomain) + "." + self.namespace + ".svc." + k8s_cluster_domain(
            self.logger)

    @property
    def pod_ip_address(self) -> str:
        return self.pod.status.pod_ip

    def reload(self) -> None:
        self.pod = cast(api_client.V1Pod, api_core.read_namespaced_pod(
            self.name, self.namespace))

    def owner_reference(self, api_version, kind) -> typing.Optional[api_client.V1OwnerReference]:
        """
        获取指定API版本和类型的所有者引用，如果没有则返回None。
        
        Args:
            api_version (str): API版本名称。
            kind (str): Kubernetes资源类型名称。
        
        Returns:
            Optional[api_client.V1OwnerReference]: 返回一个包含所有者引用信息的V1OwnerReference对象，如果找不到则返回None。
        """
        for owner in self.metadata.owner_references:
            if owner.api_version == api_version and owner.kind == kind:
                return owner

        return None

    def get_cluster(self):
        """
            获取集群信息，如果不存在则返回None。
        如果出现API异常并且状态码为404，则返回None；否则抛出异常。
        
        Args:
            无参数。
        
        Returns:
            XDBCluster (Optional[XDBCluster]): 集群对象，可能为None。如果集群不存在，则返回None。
        
        Raises:
            ApiException (ApiException): API请求失败时抛出的异常，包含错误代码和消息。
                如果集群不存在，则错误代码为404。
            Exception (Exception): 其他情况下抛出的异常。
        """
        try:
            return XDBCluster.read(self.namespace, self.xdbcluster_name)
        except ApiException as e:
            print(
                f"Could not get cluster {self.namespace}/{self.xdbcluster_name}: {e}")
            if e.status == 404:
                return None
            raise

    def get_xdbproxy_cluster(self):
        try:
            return XDBProxyCluster.read(self.namespace, self.xdbproxy_cluster_name)
        except ApiException as e:
            print(
                f"Could not get cluster {self.namespace}/{self.xdbproxy_cluster_name}: {e}")
            if e.status == 404:
                return None
            raise

    def check_condition(self, cond_type: str) -> typing.Optional[bool]:
        """
        检查指定类型的条件是否存在，如果存在则返回True，否则返回None。
        如果状态中有多个同类型的条件，只会返回第一个。
        
        Args:
            cond_type (str): 要检查的条件类型，例如"Ready"、"Available"等。
        
        Returns:
            Optional[bool]: 如果找到了指定类型的条件，并且状态为True，则返回True；否则返回None。
        """
        if self.status and self.status.conditions:
            for c in self.status.conditions:
                if c.type == cond_type:
                    return c.status == "True"
        return None

    def check_containers_ready(self) -> typing.Optional[bool]:
        """
        检查容器是否就绪。
        如果所有的容器都已准备好，则返回 True；否则返回 None。
        
        Args:
            None
        
        Returns:
            Optional[bool]: 如果所有的容器都已准备好，则返回 True；否则返回 None。
        """
        return self.check_condition("ContainersReady")

    def check_container_ready(self, container_name: str) -> typing.Optional[bool]:
        """
        检查容器是否就绪，返回一个布尔值或者None。如果容器不存在，则返回None。
        该方法会遍历Pod的所有ContainerStatus，并且只要找到指定名称的ContainerStatus，就会返回该ContainerStatus的ready状态。
        如果没有找到指定名称的ContainerStatus，则返回None。
        
        Args:
            container_name (str): 需要检查的容器名称。
        
        Returns:
            Optional[bool]: 如果找到指定名称的ContainerStatus，则返回该ContainerStatus的ready状态；如果没有找到指定名称的ContainerStatus，则返回None。
        """
        if self.status.container_statuses:
            for cs in self.status.container_statuses:
                if cs.name == container_name:
                    return cs.ready
        return None

    def get_container_restarts(self, container_name: str) -> typing.Optional[int]:
        """
        获取指定容器的重启次数，如果不存在则返回None。
        参数container_name (str) - 容器名称，必须为字符串类型。
        返回值 (typing.Optional[int]) - 容器重启次数，果不存在该容器则返回None。
        """
        if self.status.container_statuses:
            for cs in self.status.container_statuses:
                if cs.name == container_name:
                    return cs.restart_count
        return None

    def get_member_readiness_gate(self, gate: str) -> typing.Optional[bool]:
        return self.check_condition(f"cloudbed.abcstack.com/{gate}")

    def update_member_readiness_gate(self, gate: str, value: bool) -> None:
        """
        更新成员的就绪状态网关，并返回是否发生变化。
        如果当前值与给定值不同，则更新状态网关，并将其标记为已更改。
        否则，将其标记为未更改。
        
        Args:
            gate (str): 要更新的状态网关名称。
            value (bool): 要设置的状态网关值。
        
        Returns:
            None: 无返回值，直接修改 Pod 对象的状态字段。
        
        Raises:
            无。
        """
        now = utils.isotime()

        if self.check_condition(f"cloudbed.abcstack.com/{gate}") != value:
            changed = True
        else:
            changed = False

        patch = {"status": {
            "conditions": [{
                "type": f"cloudbed.abcstack.com/{gate}",
                "status": "True" if value else "False",
                "lastProbeTime": '%s' % now,
                "lastTransitionTime": '%s' % now if changed else None
            }]}}
        print(f"Updating readiness gate {gate} with patch {patch}")
        self.pod = cast(api_client.V1Pod, api_core.patch_namespaced_pod_status(
            self.name, self.namespace, body=patch))

    # TODO remove field
    def get_membership_info(self, field: str = None) -> typing.Optional[dict]:
        """
            获取成员信息，可指定字段。如果没有注解或者注解中不存在该字段，则返回None。
        默认情况下，会返回一个包含所有信息的字典，如果指定了字段，则只返回该字段对应的值。
        
        Args:
            field (str, optional): 要获取的字段名称，默认为None，表示返回所有信息。 Defaults to None.
        
        Returns:
            typing.Optional[dict]: 成员信息，如果没有注解或者注解中不存在该字段，则返回None；如果指定了字段，则返回该字段对应的值。 Defaults to None.
        """
        if self.metadata.annotations:
            info = self.metadata.annotations.get(
                "cloudbed.abcstack.com/membership-info", None)
            if info:
                info = json.loads(info)
                if info and field:
                    return info.get(field)
                return info
        return None

    def update_membership_status(self, member_id: str, role: str, status: str,
                                 view_id: str, version: str,
                                 joined: bool = False) -> None:
        """
            更新成员的状态，包括角色、状态和视图ID等信息。如果成员加入了群组，则会记录加入时间。
        如果当前状态与之前的状态不同或者视图ID、成员ID、角色或版本有变化，则会记录最近一次状态转换时间。
        
        Args:
            member_id (str): 成员的ID。
            role (str): 成的角色，可以是"OWNER"、"EDITOR"或"VIEWER"中的任意一个。
            status (str): 成员的状态，可以是"OFFLINE"、"ONLINE"或"BANNED"中的任意一个。
            view_id (str): 成员所在的群组视图的ID。
            version (str): 群组的版本号，用于标识群组的历史版本。
            joined (bool, optional): 默认为False，表示成员未加入群组；如果为True，则表示成员已经加入群组。
        
        Raises:
            None
        
        Returns:
            None: 无返回值，直接修改了成员的状态信息。
        """
        now = utils.isotime()
        last_probe_time = now

        info = self.get_membership_info() or {}
        if not info or info.get("role") != role or info.get("status") != status or info.get(
                "groupViewId") != view_id or info.get("memberId") != member_id:
            last_transition_time = now
        else:
            last_transition_time = info.get("lastTransitionTime")

        info.update({
            "memberId": member_id,
            "lastTransitionTime": last_transition_time,
            "lastProbeTime": last_probe_time,
            "groupViewId": view_id,
            "status": status,
            "version": version,
            "role": role
        })
        if joined:
            info["joinTime"] = now

        patch = {
            "metadata": {
                "labels": {
                    "cloudbed.abcstack.com/cluster-role": role if status == "ONLINE" else None
                },
                "annotations": {
                    "cloudbed.abcstack.com/membership-info": json.dumps(info)
                }
            }
        }
        self.pod = cast(api_client.V1Pod, api_core.patch_namespaced_pod(
            self.name, self.namespace, patch))

    def add_member_finalizer(self) -> None:
        """
            添加成员的最终化器，用于在销毁时删除成员。
            该方法只能在初始化后调用一次。
        
        Args:
            None
        
        Returns:
            None, 无返回值
        """
        self._add_finalizer("cloudbed.abcstack.com/membership")

    def remove_member_finalizer(self, pod_body: Body = None) -> None:
        """
        移除成员的 finalizer，用于在删除 Pod 时防止其被重新加入集群。
        如果不指定 pod_body，则会从当前 Pod 中移除 finalizer。
        
        Args:
            pod_body (Body, optional): 要操作的 Pod 体，默认为 None。如果不指定，将使用当前 Pod 的信息��� Default value is None.
        
        Raises:
            None.
        
        Returns:
            None: 无返回值。
        """
        self._remove_finalizer("cloudbed.abcstack.com/membership", pod_body)

    def _add_finalizer(self, fin: str) -> None:
        """
        Add the named token to the list of finalizers for the Pod.
        The Pod will be blocked from deletion until that token is
        removed from the list (remove_finalizer).
        """
        patch = {"metadata": {"finalizers": [fin]}}
        self.obj = api_core.patch_namespaced_pod(
            self.name, self.namespace, body=patch)

    def _remove_finalizer(self, fin: str, pod_body: Body = None) -> None:
        """
            从Pod的finalizer列表中移除一个finalizer。如果指定了pod_body，则更新内部kopf使用的JSON数据以反映最新的finalizer列表。
        如果finalizer不在Pod的finalizer列表中，则不会进行任何操作。
        
        Args:
            fin (str): 要移除的finalizer名称。
            pod_body (Body, optional): 可选参数，包含当前Pod的JSON数据。默认为None。
        
        Raises:
            无。
        
        Returns:
            无返回值，直接修改内部kopf使用的JSON数据。
        """
        patch = {"metadata": {"$deleteFromPrimitiveList/finalizers": [fin]}}
        self.obj = api_core.patch_namespaced_pod(
            self.name, self.namespace, body=patch)

        if pod_body:
            # modify the JSON data used internally by kopf to update its finalizer list
            if fin in pod_body["metadata"]["finalizers"]:
                pod_body["metadata"]["finalizers"].remove(fin)
