#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   xdbproxy_objects.py
@Time    :   2024/8/26 19:26
<AUTHOR>   lixian<PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import kopf
import yaml
from logging import Logger
from shlex import quote
from typing import Optional

from xdboperator.controller import utils
from xdboperator.controller.xdbproxy.xdbproxy_api import XDBProxyClusterSpec, XDBProxyCluster, AbstractServerSetSpec
from xdboperator.controller.xdbproxy.xdbproxy_api import  XDBProxyDefaults

from xdboperator.controller.kubeutils import api_core, api_apps, api_customobj
from xdboperator.controller.kubeutils import client as api_client


def prepare_dbproxy_service(spec: XDBProxyClusterSpec) -> dict:
    """
    创建dbproxy使用的svc
    """
    tmpl = f"""
apiVersion: v1
kind: Service
metadata:
  name: {spec.name}
  namespace: {spec.namespace}
  labels:
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: xdbproxy
    cloudbed.abcstack.com/component: xdbproxy
  annotations::
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: xdbproxy
    cloudbed.abcstack.com/component: xdbproxy
spec:
  ports:
  - name: xdb-dbproxy-extension
    port: {spec.dbproxy_port}
    protocol: TCP
    targetPort: {spec.dbproxy_port}
  selector:
    component: xdbproxy
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: xdbproxy
    app.kubernetes.io/managed-by: xdb-operator
  type: {spec.service.type}
"""
    service = yaml.safe_load(tmpl)

    if spec.service.annotations:
        service['metadata']['annotations'] = spec.service.annotations

    if spec.service.labels:
        service['metadata']['labels'] = spec.service.labels | service['metadata']['labels']

    return service


def prepare_dbproxy_service_hs(spec: XDBProxyClusterSpec) -> dict:
    """
    创建dbproxy使用的hs
    """

    tmpl = f"""
apiVersion: v1
kind: Service
metadata:
  name: {spec.name}-hs
  namespace: {spec.namespace}
  labels:
    component: xdbproxy
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: xdbproxy
  annotations::
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: xdbproxy
    cloudbed.abcstack.com/component: xdbproxy
spec:
  clusterIP: None
  ports:
  - name: xdb-dbproxy-extension
    port: {spec.dbproxy_port}
    protocol: TCP
    targetPort: {spec.dbproxy_port}
  - name: xdb-xagent
    port: {spec.xagent_port}
    protocol: TCP
    targetPort: {spec.xagent_port}
  selector:
    component: xdbproxy
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: xdbproxy
    app.kubernetes.io/managed-by: xdb-operator
  type: {spec.service.type}
"""
    service = yaml.safe_load(tmpl)

    if spec.service.annotations:
        service['metadata']['annotations'] = spec.service.annotations

    if spec.service.labels:
        service['metadata']['labels'] = spec.service.labels | service['metadata']['labels']

    return service
  

def prepare_xdbproxy_statefulset(cluster: XDBProxyCluster, logger, *,
                              init_only: bool = False) -> dict:
    """
    创建statefulset 类型的dbproxy 
    """
    spec = cluster.parsed_spec
    xdb = cluster.get_xdb_cluster()
    
    # 使用自定义镜像或默认镜像
    image = spec.image if spec.image else spec.dbproxy_image

    # 1. 统一label定义
    base_labels = {
        "component": "xdbproxy",
        "cloudbed.abcstack.com/cluster": spec.name,
        "cloudbed.abcstack.com/xdb": spec.clusterRef.name,
        "karrier.abcstack.com/app": spec.clusterRef.name,
        "app": spec.clusterRef.name, 
        "karrier.abcstack.com/component": "xdb",
        "cloudbed.abcstack.com/instance-type": "xdbproxy",
        "mysql-role": "xdbproxy",
        "app.kubernetes.io/name": "xdb",
        "app.kubernetes.io/instance": f"xdbcluster-{spec.name}-dbproxy",
        "app.kubernetes.io/component": "xdbproxy",
        "app.kubernetes.io/managed-by": "xdb-operator",
        "app.kubernetes.io/created-by": "xdb-operator",
    }
    # 2. 获取CR的labels并合并，CR优先
    cr_labels = cluster.metadata.get("labels", {})
    final_labels = {**base_labels, **cr_labels}

    tmpl = f"""
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {spec.name}
  labels: {final_labels}
  annotations::
    app.kubernetes.io/managed-by: xdb-operator
    cloudbed.abcstack.com/cluster: {spec.name}
    cloudbed.abcstack.com/instance-type: xdbproxy
    cloudbed.abcstack.com/component: xdbproxy
spec:
  replicas: {spec.instances}
  podManagementPolicy: Parallel
  serviceAccountName: {spec.name}
  selector:
    matchLabels: {final_labels}
  template:
    metadata:
      labels: {final_labels}
    spec:
      readinessGates:
      - conditionType: "cloudbed.abcstack.com/registerZk"
      - conditionType: "cloudbed.abcstack.com/ready"
      - conditionType: "cloudbed.abcstack.com/grants"
      initContainers:
      - name: init
        image: {image}
        command: ["/home/<USER>/lightxdb/deploy/control.sh"]
        args: ["init"]
        securityContext:
          runAsUser: 0
          privileged: true
        volumeMounts:
        - mountPath: /home/<USER>/dbproxy_6203
          name: xdbproxy
        - mountPath: /home/<USER>/xagent
          name: xagent
        env:
        - name: ZOOKEEPER_HOST
          value: {xdb.parsed_spec.ZKDomain}
        - name: XAGENT_PORT
          value: "{spec.xagent_port}"
        - name: DBPROXY_LOGS_SIZE
          value: "{spec.reserved_dbproxy_logs_size}"
        - name: RELATION_BIND_XDB_APPID
          value: "{xdb.app_id}"
      containers:
      - name: xdbproxy
        image: {image}
        imagePullPolicy: {spec.dbproxy_image_pull_policy}
        command: ["/usr/local/bin/dumb-init", "--"]
        args: ["/home/<USER>/lightxdb/deploy/control.sh", "start"]
        securityContext:
          runAsUser: 1000
          privileged: true
        env:
        - name: ZOOKEEPER_HOST
          value: {xdb.parsed_spec.ZKDomain}
        - name: XAGENT_PORT
          value: "{spec.xagent_port}"
        - name: DBPROXY_LOGS_SIZE
          value: "{spec.reserved_dbproxy_logs_size}"
        - name: RELATION_BIND_XDB_APPID
          value: "{xdb.app_id}"
        volumeMounts:
        - mountPath: /home/<USER>/dbproxy_6203
          name: xdbproxy
        - mountPath: /home/<USER>/xagent
          name: xagent
        ports:
        - containerPort: {spec.dbproxy_port}
          name: dbproxy
        - containerPort: {spec.xagent_port}
          name: xagent
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 5
          periodSeconds: 30
          successThreshold: 1
          tcpSocket:
            port: 6203
          timeoutSeconds: 3
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 5
          periodSeconds: 30
          successThreshold: 1
          tcpSocket:
            port: 6203
          timeoutSeconds: 3
      volumes:
      - name: xdbproxy
        emptyDir: {{}}
      - name: xagent
        emptyDir: {{}}
"""
    sts = yaml.safe_load(tmpl)

    # 处理 affinity
    if spec.affinity:
        sts["spec"]["template"]["spec"]["affinity"] = spec.affinity
    else:
        # 使用默认的 affinity 配置
        default_affinity = XDBProxyDefaults.DEFAULT_AFFINITY.copy()
        # 替换集群名称
        default_affinity["podAntiAffinity"]["preferredDuringSchedulingIgnoredDuringExecution"][0]\
            ["podAffinityTerm"]["labelSelector"]["matchLabels"]["cloudbed.abcstack.com/cluster"] = spec.name
        sts["spec"]["template"]["spec"]["affinity"] = default_affinity
    
    # 处理 resources
    container = sts["spec"]["template"]["spec"]["containers"][0]
    if spec.resources and (spec.resources.requests or spec.resources.limits):
        container["resources"] = {
            "requests": spec.resources.requests,
            "limits": spec.resources.limits
        }
    else:
        # 使用默认的 resources 配置
        container["resources"] = XDBProxyDefaults.DEFAULT_RESOURCES
    
    # 处理存储配置
    volumes = []
    volume_mounts = []
    
    # 添加固定的opt挂载,镜像里面已经集成了
    # volumes.append({
    #     "hostPath": {
    #         "path": "/opt",
    #         "type": ""
    #     },
    #     "name": "opt"
    # })
    # volume_mounts.append({
    #     "mountPath": "/opt",
    #     "name": "opt"
    # })
    
    # 处理xdbproxy存储
    def configure_storage(storage_config, name, mount_path):
        if storage_config and storage_config.storageType == "persistentVolume":
            volumes.append({
                "name": name,
                "persistentVolumeClaim": {
                    "claimName": f"{spec.name}-{name}"
                }
            })
            
            # 添加volumeClaimTemplates
            if "volumeClaimTemplates" not in sts["spec"]:
                sts["spec"]["volumeClaimTemplates"] = []
                
            sts["spec"]["volumeClaimTemplates"].append({
                "metadata": {"name": name},
                "spec": {
                    "accessModes": ["ReadWriteOnce"],
                    "storageClassName": storage_config.storageClass,
                    "resources": {
                        "requests": {
                            "storage": storage_config.size
                        }
                    }
                }
            })
        else:
            volumes.append({
                "name": name,
                "emptyDir": {}
            })
            
        volume_mounts.append({
            "mountPath": mount_path,
            "name": name
        })
    
    # 配置 xdbproxy 存储
    configure_storage(
        spec.storage.xdbproxy if spec.storage else None,
        "xdbproxy",
        "/home/<USER>/dbproxy_6203"
    )
    
    # 配置 xagent 存储
    configure_storage(
        spec.storage.xagent if spec.storage else None,
        "xagent", 
        "/home/<USER>/xagent"
    )
    
    # 更新volumes和volumeMounts配置
    sts["spec"]["template"]["spec"]["volumes"] = volumes
    container["volumeMounts"] = volume_mounts
    
    return sts
  
def prepare_service_account(spec: AbstractServerSetSpec) -> dict:
    """
    准备service account
    """
    account = f"""
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {spec.name}
  namespace: {spec.namespace}
  annotations:
      meta.operator/release-name: {spec.name}
      meta.operator/release-namespace: {spec.namespace}
  labels:
      cloudbed.abcstack.com/dbproxy-cluster: {spec.name}
      app.kubernetes.io/name: xdbproxy
      app.kubernetes.io/component: xdbproxy
      app.kubernetes.io/managed-by: xdb-operator
      app.kubernetes.io/created-by: xdb-operator
"""

    account = yaml.safe_load(account)

    return account



def prepare_cluster_pod_disruption_budget(spec: XDBProxyClusterSpec) -> dict:
    """
      创建proxy使用的pdb
    """
    tmpl = f"""
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {spec.name}-pdb
spec:
  maxUnavailable: 25%
  selector:
    matchLabels:
      component: xdbproxy
      cloudbed.abcstack.com/dbproxy-cluster: {spec.name}
"""
    pdb = yaml.safe_load(tmpl.replace("\n\n", "\n"))
    return pdb

def update_stateful_set_spec(sts: api_client.V1StatefulSet, patch: dict) -> None:
    """
      更新statefulset的spec
    """
    api_apps.patch_namespaced_stateful_set(
        sts.metadata.name, sts.metadata.namespace, body=patch)

def reconcile_stateful_set(cluster: XDBProxyCluster, logger: Logger) -> None:
    """
    重新调谐statefulset
    """
    logger.info("reconcile_stateful_set")
    patch = prepare_xdbproxy_statefulset(cluster.parsed_spec, logger)

    logger.info(f"reconcile_stateful_set: patch={patch}")
    api_apps.patch_namespaced_stateful_set(
        cluster.name, cluster.namespace, body=patch)
    

def update_size(cluster: XDBProxyCluster, size: int, logger: Logger) -> None:
    """
    更新statefulset的size
    """
    deploy = cluster.get_stateful_set()
    if deploy:
        if size:
            patch = {"spec": {"replicas": size}}
            api_apps.patch_namespaced_stateful_set(
                deploy.metadata.name, deploy.metadata.namespace, body=patch)
        else:
            logger.info(f"Deleting dbproxy stateful_set")
            api_apps.delete_namespaced__stateful_set(
                f"{cluster.name}", cluster.namespace)
    else:
        if size:
            logger.info(f"Creating xdbproxy stateful_set with replicas={size}")
            router_deployment = prepare_xdbproxy_statefulset(cluster, logger)
            kopf.adopt(router_deployment)
            api_apps.create_namespaced_stateful_set(
                namespace=cluster.namespace, body=router_deployment)