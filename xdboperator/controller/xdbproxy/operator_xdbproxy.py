#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
@File    :   operato_xdbproxy.py
@Time    :   2024/8/27 15:34
<AUTHOR>   lixian<PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
@License :   Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
@Desc    :   None
"""

import kopf
from kopf._cogs.structs.bodies import Body
from logging import Logger
from typing import Optional
import kubernetes.client.rest

from xdboperator.controller import consts, diagnose
from xdboperator.controller import utils, config
from xdboperator.controller.api_utils import ApiSpecError, ignore_404
from xdboperator.controller.xdbproxy import xdbproxy_objects
from xdboperator.controller.xdbproxy.xdbproxy_api import XDBProxyCluster, XDBProxyPod
from xdboperator.controller.xdbproxy.xdbproxy_controller import XDBProxyController, XDBProxyClusterMutex
from xdboperator.controller.kubeutils import api_core, api_apps, api_policy, api_rbac, api_customobj, api_cron_job, \
    k8s_version


def update_status_with_retry(cluster, status_data, max_retries=5, logger=None):
    """
    使用重试机制更新XDBProxy状态，处理冲突问题
    
    Args:
        cluster: XDBProxyCluster对象实例
        status_data: 要更新的状态数据
        max_retries: 最大重试次数
        logger: 日志记录器
    
    Returns:
        bool: 状态更新是否成功
    """
    retries = 0
    while retries < max_retries:
        try:
            cluster.set_status(status_data)
            return True
        except kubernetes.client.rest.ApiException as e:
            if e.status == 409:  # Conflict
                if logger:
                    logger.warning(f"发生冲突，重新获取资源并重试，第{retries+1}次重试")
                try:
                    cluster.refresh()
                    retries += 1
                    continue
                except Exception as refresh_err:
                    if logger:
                        logger.error(f"重新获取资源失败: {refresh_err}")
                    return False
            else:
                if logger:
                    logger.error(f"更新状态时发生API错误: {e}")
                return False
        except Exception as e:
            if logger:
                logger.error(f"更新状态时发生未知错误: {e}")
            return False
    
    if logger:
        logger.error(f"更新状态重试次数已达上限 ({max_retries}次)")
    return False


@kopf.on.create(consts.GROUP, consts.VERSION, consts.XDBPROXY_PLURAL)
def on_xdbproxy_create(name: str, namespace: Optional[str], body: Body,
                       logger: Logger, **kwargs) -> None:
    """
    Create a new XDBProxyCluster CRD object.
    """
    logger.info(
        f"Initializing Proxy Cluster name={name} \
            namespace={namespace} on K8s {k8s_version()}")
    cluster = XDBProxyCluster(body)

    try:
        cluster.parse_spec()
        cluster.parsed_spec.validate(logger)
    except ApiSpecError as e:
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }})
        cluster.error(action="CreateCluster",
                      reason="InvalidArgument", message=str(e))
        raise kopf.TemporaryError(f"Error in xdbproxy spec: {e}")

    # 检查关联的 XDB 集群状态
    logger.info(f"Checking associated XDB cluster status for proxy {name}")
    xdb_cluster = cluster.get_xdb_cluster()
    if not xdb_cluster:
        error_msg = f"Associated XDB cluster {cluster.xdb_cluster_ns}/{cluster.xdb_cluster_name} not found"
        logger.error(error_msg)
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }})
        cluster.error(action="CreateCluster",
                      reason="ClusterNotFound", message=error_msg)
        raise kopf.PermanentError(error_msg)
    
    # 获取集群状态
    cluster_status = xdb_cluster.cluster_status
    logger.info(f"XDB cluster {xdb_cluster.name} status: {cluster_status}")
    
    # 如果集群还在创建中，等待重试
    if cluster_status in [diagnose.ClusterDiagStatus.CREATING.value, 
                         diagnose.ClusterDiagStatus.WAITING_CLUSTER_READY.value]:
        wait_msg = f"XDB cluster {xdb_cluster.name} is still creating \
            (status: {cluster_status}), waiting for cluster to be ready"
        logger.info(wait_msg)
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.PENDING.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }})
        cluster.info(action="CreateCluster",
                     reason="WaitingForClusterReady", message=wait_msg)
        raise kopf.TemporaryError(wait_msg, delay=30)
    
    # 如果集群状态无效或失败，抛出永久错误
    if cluster_status in [diagnose.ClusterDiagStatus.INVALID.value,
                         diagnose.ClusterDiagStatus.CREATE_FAILED.value]:
        error_msg = f"XDB cluster {xdb_cluster.name} is in invalid state: {cluster_status}"
        logger.error(error_msg)
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }})
        cluster.error(action="CreateCluster",
                      reason="ClusterInvalid", message=error_msg)
        raise kopf.PermanentError(error_msg)
    
    # 如果集群正在运行，继续创建 proxy 资源
    if cluster_status == diagnose.ClusterDiagStatus.RUNNING.value:
        logger.info(f"XDB cluster {xdb_cluster.name} is ready, proceeding with proxy creation")
    else:
        # 其他状态也等待重试
        wait_msg = f"XDB cluster {xdb_cluster.name} is in unexpected state: {cluster_status}, waiting for stable state"
        logger.warning(wait_msg)
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.PENDING.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }})
        cluster.warning(action="CreateCluster",
                        reason="ClusterStateUnexpected", message=wait_msg)
        raise kopf.TemporaryError(wait_msg, delay=30)

    icspec = cluster.parsed_spec
    cluster.log_cluster_info(logger)
    if not cluster.ready:
        try:
            print("0. XDBProxy Cluster PodDisruptionBudget")
            if not ignore_404(cluster.get_disruption_budget):
                print("\tPreparing...")
                disruption_budget = xdbproxy_objects.prepare_cluster_pod_disruption_budget(icspec)
                print("\tCreating...")
                kopf.adopt(disruption_budget)
                api_policy.create_namespaced_pod_disruption_budget(namespace=namespace, body=disruption_budget)

            print("1. DBProxy Service")
            if not ignore_404(cluster.get_dbproxy_service):
                print("\tPreparing...")
                proxy_service = xdbproxy_objects.prepare_dbproxy_service(icspec)
                print("\tCreating...")
                kopf.adopt(proxy_service)
                api_core.create_namespaced_service(namespace=namespace, body=proxy_service)

            print("2. DBProxy headless Service")
            if not ignore_404(cluster.get_dbproxy_headless_service):
                print("\tPreparing...")
                proxy_service_hs = xdbproxy_objects.prepare_dbproxy_service_hs(icspec)
                print("\tCreating...")
                kopf.adopt(proxy_service_hs)
                api_core.create_namespaced_service(namespace=namespace, body=proxy_service_hs)

            print("3. DBProxy ServiceAccount")
            existing_sa = ignore_404(lambda: cluster.get_service_account(icspec))
            print(f"\tExisting SA: {existing_sa}")
            if not existing_sa:
                print("\tPreparing...")
                sa = xdbproxy_objects.prepare_service_account(icspec)
                print(f"\tCreating...{sa}")
                kopf.adopt(sa)
                api_core.create_namespaced_service_account(namespace=namespace, body=sa)
                
            print("4. DBproxy Statefulset")
            if not ignore_404(cluster.get_stateful_set):
                if icspec.instances > 0:
                    print("\tPreparing...") 
                    router_deployment = xdbproxy_objects.prepare_xdbproxy_statefulset(cluster, logger, init_only=True)
                    print(f"\tCreating...{router_deployment}")
                    kopf.adopt(router_deployment)
                    api_apps.create_namespaced_stateful_set(namespace=namespace, body=router_deployment)
                else:
                    print("\Proxy count is 0. No STS is created.")

        except Exception as exc:
            cluster.warn(action="CreateProxyCluster", reason="CreateResourceFailed",
                         message=f"{exc}")
            raise

        print(f"3. Setting operator version for the IC to {config.DEFAULT_OPERATOR_VERSION_TAG}")
        cluster.set_operator_version(config.DEFAULT_OPERATOR_VERSION_TAG)
        cluster.info(action="CreateCluster", reason="ResourcesCreated",
                     message="Dependency resources created, switching status to PENDING")
        cluster.set_create_time()
        cluster.set_status({
            "cluster": {
                "status": diagnose.ClusterDiagStatus.PENDING.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }})


@kopf.timer(consts.GROUP, consts.VERSION, consts.XDBPROXY_PLURAL,
            interval=consts.MINUTES, initial_delay=consts.MINUTES * 2)
def on_xdbproxy_check(name: str, namespace: Optional[str], body: Body, logger: Logger, **kwargs) -> None:
    """
    check the status of the cluster
    """
    logger.info(f"Checking Proxy Cluster name={name} namespace={namespace} on K8s {k8s_version()}")
    cluster = XDBProxyCluster(body)
    cluster_ctl = XDBProxyController(cluster)
    cluster_ctl.probe_status(logger)


@kopf.on.delete(consts.GROUP, consts.VERSION, consts.XDBPROXY_PLURAL)
def on_xdbproxy_delete(name: str, namespace: Optional[str], body: Body,
                       logger: Logger, **kwargs) -> None:
    """
    Delete a new XDBProxyCluster CRD object.
    """
    cluster = XDBProxyCluster(body)
    pods = cluster.get_pods()
    logger.info(f"Deleting xdbproxy cluster {name}")
    # Scale down routers to 0
    logger.info(f"Updating XDBProxy  Deployment.replicas to 0")
    sts = cluster.get_stateful_set()
    if sts:
        xdbproxy_objects.update_stateful_set_spec(sts, {"spec": {"replicas": 0}})
        if len(pods) == 1 and pods[0].deleting:
            logger.info(
                "on_xdbproxy_delete: The cluster's only one pod is already deleting. Removing cluster finalizer here")
            cluster.remove_cluster_finalizer()

        logger.info(f"Updating DBProxy StatefulSet.instances to 0")


# TODO add a busy state and prevent changes while on it
@kopf.on.field(consts.GROUP, consts.VERSION, consts.XDBPROXY_PLURAL, field="spec.instances")  # type: ignore
def on_xdbproxy_field_instances(old, new, body: Body,
                                logger: Logger, **kwargs):
    """
        只监听spec.instances 的变化，只关注副本扩容
    """
    cluster = XDBProxyCluster(body)

    if not cluster.ready:
        logger.debug(f"Ignoring spec.instances change for unready cluster")
        return

    # TODO - identify what cluster statuses should allow changes to the size of the cluster
    sts = cluster.get_stateful_set()
    if sts and old != new:
        logger.info(
            f"Updating InnoDB Cluster StatefulSet.replicas from {old} to {new}")
        cluster.parsed_spec.validate(logger)
        xdbproxy_objects.update_stateful_set_spec(
            sts, {"spec": {"replicas": new}})



@kopf.on.create("", "v1", "pods",  backoff=config.BOOTSTRAP_RETRY_DELAY,
                labels={"app.kubernetes.io/component": "xdbproxy", 
                        "component": "xdbproxy",
                        "app.kubernetes.io/created-by": "xdb-operator",
                        "app.kubernetes.io/name": "xdb"}) 
def on_pod_create(body: Body, logger: Logger, **kwargs):
    """
    Handle XDB Proxy server Pod creation, which can happen when:
    - cluster is being first created
    """

    # TODO ensure that the pod is owned by us
    pod = XDBProxyPod.from_json(body)
    # 检查主容器 xdbproxy 是否 ready,避免在 init 阶段就注入信息
    container_statuses = body.get('status', {}).get('containerStatuses', [])
    main_ready = False
    for c in container_statuses:
        if c.get('name') == 'xdbproxy' and c.get('ready'):
            main_ready = True
    if not main_ready:
        logger.info("Main container not ready, will retry on_pod_create.")
        raise kopf.TemporaryError("Main container not ready, will retry", delay=10)

    # check general assumption
    assert not pod.deleting
    logger.info(
        f"POD CREATED: pod={pod.name} ContainersReady={pod.check_condition('ContainersReady')} " + \
            f"Ready={pod.check_condition('Ready')} gate[registerZk]={pod.get_member_readiness_gate('registerZk')}" + \
            f"gate[grants]={pod.check_condition('grants')} gate[ready]={pod.get_member_readiness_gate('ready')}")
    xdbproxy_cluster = pod.get_xdbproxy_cluster()
    logger.info(f"DBPROXY CLUSTER DELETING={xdbproxy_cluster.deleting}")
    assert xdbproxy_cluster
    cluster_ctl = XDBProxyController(xdbproxy_cluster)
    cluster_ctl.on_pod_created(pod, logger)


@kopf.on.delete("", "v1", "pods", retries=config.BOOTSTRAP_RETRIES, backoff=config.BOOTSTRAP_RETRY_DELAY,
                labels={"app.kubernetes.io/component": "xdbproxy", 
                        "component": "xdbproxy",
                        "app.kubernetes.io/created-by": "xdb-operator",
                        "app.kubernetes.io/name": "xdb"}) 
def on_pod_delete(body: Body, logger: Logger, **kwargs):
    """
    Handle XDB Proxy server Pod deletion, which can happen when:
    - cluster is being scaled down (members being removed)
    - cluster is being deleted
    - user deletes a pod by hand
    """
    # TODO ensure that the pod is owned by us
    pod = XDBProxyPod.from_json(body)
    logger.info(f"Deleting xdbproxy pod ip address = {pod.pod_ip_address}")
    # check general assumption
    assert pod.deleting
    # removeInstance the pod
    xdbcluster = pod.get_cluster()
    xdbproxy_cluster = pod.get_xdbproxy_cluster()
    if xdbcluster and xdbproxy_cluster:
        with XDBProxyClusterMutex(xdbcluster, pod):
            cluster_ctl = XDBProxyController(xdbproxy_cluster)
            cluster_ctl.on_pod_deleted(pod, body, logger)
    elif pod.pod_ip_address:
        cluster_ctl = XDBProxyController(xdbproxy_cluster)
        cluster_ctl.on_pod_deleted(pod, body, logger)
    else:
        pod.remove_member_finalizer(body)
        logger.error(f"Owner cluster for {pod.name} does not exist anymore")


@kopf.on.resume(consts.GROUP, consts.VERSION, consts.XDBPROXY_PLURAL)
def on_xdbproxy_resume(name: str, namespace: Optional[str], body: Body,
                      logger: Logger, **kwargs) -> None:
    """
    处理 operator 重启时的 XDBProxy 资源恢复。
    确保所有已存在的 XDBProxy 资源状态一致。
    
    Args:
        name (str): XDBProxy 资源的名称
        namespace (Optional[str]): XDBProxy 资源所在的命名空间
        body (Body): 包含 XDBProxy 资源的请求体
        logger (Logger): 日志记录器
        kwargs: 其他可选参数
    """
    logger.info(f"Resume handling XDBProxy name={name} namespace={namespace}")
    cluster = XDBProxyCluster(body)
    cluster.info(action="ResumeXDBProxy", reason="StartResume", 
                 message=f"Resume handling XDBProxy name={name} namespace={namespace}")
    
    # 如果资源正在删除中，跳过处理
    if cluster.deleting:
        logger.debug(f"XDBProxy {name} is being deleted, skip resume handling")
        cluster.info(action="ResumeXDBProxy", reason="Deleting", 
                     message=f"XDBProxy {name} is being deleted, skip resume handling")
        return
        
    try:
        cluster.parse_spec()
        cluster.parsed_spec.validate(logger)
    except ApiSpecError as e:
        status_data = {
            "cluster": {
                "status": diagnose.ClusterDiagStatus.INVALID.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(cluster, status_data, logger=logger):
            logger.error(f"Failed to update proxy status to INVALID: {name}")
        cluster.error(action="ResumeXDBProxy", reason="SpecInvalid", message=str(e))
        return
        
    # 如果状态为空，说明是首次创建，设置为 PENDING 状态
    current_status = cluster.get_cluster_status("status")
    cluster.info(action="ResumeXDBProxy", reason="CurrentStatus", 
                 message=f"Current proxy status: {current_status}")
    if current_status is None:
        status_data = {
            "cluster": {
                "status": diagnose.ClusterDiagStatus.PENDING.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(cluster, status_data, logger=logger):
            logger.error(f"Failed to update proxy status to PENDING: {name}")
        cluster.info(action="ResumeXDBProxy", reason="SetPending", 
                     message="Set proxy status to PENDING during resume")
        return
        
    # 检查必要的资源是否存在
    icspec = cluster.parsed_spec
    missing_resources = []
    if not ignore_404(cluster.get_disruption_budget):
        missing_resources.append("PodDisruptionBudget")
    if not ignore_404(cluster.get_dbproxy_service):
        missing_resources.append("Service")
    if not ignore_404(cluster.get_dbproxy_headless_service):
        missing_resources.append("HeadlessService")
    if not ignore_404(cluster.get_service_account):
        missing_resources.append("ServiceAccount")
    if not ignore_404(cluster.get_stateful_set):
        missing_resources.append("StatefulSet")
    if missing_resources:
        logger.warning(f"Missing resources for XDBProxy {name}: {', '.join(missing_resources)}")
        status_data = {
            "cluster": {
                "status": diagnose.ClusterDiagStatus.PENDING.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(cluster, status_data, logger=logger):
            logger.error(f"Failed to update proxy status to PENDING: {name}")
        cluster.warn(action="ResumeXDBProxy", reason="MissingResources", 
                     message=f"Missing resources: {', '.join(missing_resources)} during resume")
        return
        
    # 检查 StatefulSet 状态
    sts = cluster.get_stateful_set()
    if sts:
        pods = cluster.get_pods()
        ready_pods = sum(1 for pod in pods if pod.check_condition('Ready'))
        if ready_pods == len(pods) and len(pods) > 0:
            status_data = {
                "cluster": {
                    "status": diagnose.ClusterDiagStatus.RUNNING.value,
                    "onlineInstances": ready_pods,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(cluster, status_data, logger=logger):
                logger.error(f"Failed to update proxy status to RUNNING: {name}")
            cluster.info(action="ResumeXDBProxy", reason="AllPodsReady", 
                         message="All pods are ready during resume, set status to RUNNING")
        else:
            status_data = {
                "cluster": {
                    "status": diagnose.ClusterDiagStatus.PENDING.value,
                    "onlineInstances": ready_pods,
                    "lastProbeTime": utils.isotime()
                }}
            if not update_status_with_retry(cluster, status_data, logger=logger):
                logger.error(f"Failed to update proxy status to PENDING: {name}")
            cluster.warn(action="ResumeXDBProxy", reason="PodsNotReady", 
                         message=f"Not all pods are ready during resume, ready: {ready_pods}, total: {len(pods)}")
    else:
        status_data = {
            "cluster": {
                "status": diagnose.ClusterDiagStatus.PENDING.value,
                "onlineInstances": 0,
                "lastProbeTime": utils.isotime()
            }}
        if not update_status_with_retry(cluster, status_data, logger=logger):
            logger.error(f"Failed to update proxy status to PENDING: {name}")
        cluster.warn(action="ResumeXDBProxy", reason="NoStatefulSet", 
                     message="StatefulSet not found during resume, set status to PENDING")
