#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
备份XDB相关的ZK数据,支持压缩、自动清理和恢复功能
"""
import sys
import os
import json
import time
import gzip
import shutil
import logging
import glob
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple


from xdboperator.lightxdb.zookeeper import ZookeeperService, cache_with_timeout

class XDBZKBackup:
    """
    XDB ZK数据备份工具
    """
    def __init__(self, zk_host: str, backup_dir: str, retention_days: int = 30):
        """
        初始化备份工具
        
        Args:
            zk_host: ZK地址
            backup_dir: 备份文件保存目录
            retention_days: 备份文件保留天数
        """
        self.logger = logging.getLogger(__name__)
        self.zk = ZookeeperService(zk_host, self.logger)
        self.backup_dir = backup_dir
        self.retention_days = retention_days
        
    def backup_application_data(self, app_id: str) -> Dict:
        """
        备份指定应用的数据
        
        Args:
            app_id: 应用ID
        """
        app_details = {}
            
        # 备份节点信息
        status, nodes = self.zk.zk_node_list(app_id)
        if status == 0:
            app_details["nodes"] = {}
            for node_id in nodes:
                status, node_info = self.zk.zk_node_show(app_id, node_id)
                if status == 0:
                    app_details["nodes"][node_id] = node_info
        
        # 备份账号信息和详情
        status, accounts = self.zk.zk_account_list(app_id)
        if status == 0:
            app_details["accounts"] = []
            for account in accounts:
                # 获取账户名称
                account_name = account.get('name') if isinstance(account, dict) else account
                if account_name:
                    # 获取账户详情
                    status, account_info = self.zk.zk_account_show(app_id, account_name)
                    if status == 0:
                        app_details["accounts"].append(account_info)
        
        # 备份数据库信息
        status, databases = self.zk.zk_database_list(app_id)
        if status == 0:
            app_details["databases"] = {}
            for db in databases:
                # 如果 db 是字符串，直接使用它作为数据库名
                db_name = db if isinstance(db, str) else db.get("name")
                if db_name:
                    # 获取数据库详细信息 {"dbname":"noahee_inventoryplus","default_charset":"utf8","remark":""}
                    status, db_info = self.zk.zk_database_show(app_id, db_name)
                    if status == 0:
                        app_details["databases"][db_name] = db_info
                    
                    # # 获取表信息,暂时没有
                    status, tables = self.zk.zk_table_list(app_id, db_name)
                    if status == 0:
                        app_details["databases"][db_name]["tables"] = {}
                        for table in tables:
                            status, table_info = self.zk.zk_table_show(app_id, db_name, table)
                            if status == 0:
                                app_details["databases"][db_name]["tables"][table] = table_info
        
        # 新增：备份 dbproxy 信息
        status, proxies = self.zk.zk_proxy_list(app_id)
        if status == 0:
            app_details["proxies"] = {}
            for proxy_id in proxies:
                status, proxy_info = self.zk.zk_proxy_show(app_id, proxy_id)
                if status == 0:
                    app_details["proxies"][proxy_id] = proxy_info
    
        return app_details

    def save_backup(self, app_id: str, data: Dict) -> str:
        """
        保存备份数据到文件
        
        Args:
            app_id: 应用ID
            data: 要保存的数据
            
        Returns:
            备份文件路径
        """
        # 创建应用专属备份目录
        app_backup_dir = os.path.join(self.backup_dir, app_id)
        if not os.path.exists(app_backup_dir):
            os.makedirs(app_backup_dir)
            
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(app_backup_dir, f"{app_id}_zk_backup_{timestamp}.json")
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        return backup_file

    def compress_backup(self, backup_file: str) -> str:
        """
        压缩备份文件
        
        Args:
            backup_file: 要压缩的文件路径
            
        Returns:
            压缩后的文件路径
        """
        compressed_file = f"{backup_file}.gz"
        try:
            with open(backup_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # 删除原始文件
            os.remove(backup_file)
            self.logger.info(f"Backup file compressed: {compressed_file}")
            return compressed_file
        except Exception as e:
            self.logger.error(f"Failed to compress backup file: {str(e)}")
            return backup_file

    def cleanup_old_backups(self, app_id: str) -> None:
        """
        清理指定应用超过保留期限的备份文件
        
        Args:
            app_id: 应用ID
        """
        try:
            app_backup_dir = os.path.join(self.backup_dir, app_id)
            if not os.path.exists(app_backup_dir):
                return
                
            cutoff_date = datetime.now() - timedelta(days=self.retention_days)
            backup_pattern = os.path.join(app_backup_dir, f"{app_id}_zk_backup_*.gz")
            
            for backup_file in glob.glob(backup_pattern):
                try:
                    timestamp_str = backup_file.split('_')[-1].replace('.gz', '')
                    file_date = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                    
                    if file_date < cutoff_date:
                        os.remove(backup_file)
                        self.logger.info(f"Removed old backup file: {backup_file}")
                except (ValueError, IndexError):
                    self.logger.warning(f"Could not parse date from filename: {backup_file}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"Failed to cleanup old backups for app {app_id}: {str(e)}")

    def restore_from_backup(self, backup_file: str, target_apps: Optional[List[str]] = None) -> bool:
        """
        从备份文件恢复数据
        
        Args:
            backup_file: 备份文件路径
            target_apps: 要恢复的应用列表，如果为None则恢复所有应用
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            # 读取备份文件
            if backup_file.endswith('.gz'):
                with gzip.open(backup_file, 'rt', encoding='utf-8') as f:
                    backup_data = json.load(f)
            else:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)

            # 获取要恢复的应用列表
            apps_to_restore = target_apps or backup_data['data']['applications']
            
            for app in apps_to_restore:
                if app not in backup_data['data']['details']:
                    self.logger.warning(f"Application {app} not found in backup")
                    continue
                    
                app_data = backup_data['data']['details'][app]
                
                # 1. 创建应用（如果不存在）
                status, _ = self.zk.zk_application_create(app)
                if status != 0 and "already exists" not in str(_).lower():
                    self.logger.error(f"Failed to create application {app}")
                    continue

                # 2. 恢复节点信息
                if 'nodes' in app_data:
                    for node_id, node_info in app_data['nodes'].items():
                        status, _ = self.zk.zk_node_update(app, node_id, json.dumps(node_info))
                        if status != 0:
                            self.logger.error(f"Failed to restore node {node_id} for app {app}")

                # 3. 恢复账号信息
                if 'accounts' in app_data:
                    for account_info in app_data['accounts']:
                        # 先尝试创建账号
                        account_name = account_info.get('name')
                        if not account_name:
                            continue
                            
                        # 检查账号是否已存在
                        status, existing_accounts = self.zk.zk_account_list(app)
                        if status == 0:
                            existing_account_names = [acc.get('name') if isinstance(acc, dict) else acc 
                                                   for acc in existing_accounts]
                            
                            if account_name in existing_account_names:
                                # 如果账号已存在，更新账号信息
                                status, _ = self.zk.zk_account_update(app, account_name, account_info)
                                if status != 0:
                                    self.logger.error(f"Failed to update account {account_name} for app {app}")
                            else:
                                # 如果账号不存在，创建新账号
                                status, _ = self.zk.zk_account_create(app, account_name, json.dumps(account_info))
                                if status != 0:
                                    self.logger.error(f"Failed to create account {account_name} for app {app}")

                # 4. 恢复数据库和表信息
                if 'databases' in app_data:
                    for db_name, db_info in app_data['databases'].items():
                        # 创建数据库
                        status, _ = self.zk.zk_database_create(app, db_name, json.dumps(db_info))
                        if status != 0 and "already exists" not in str(_).lower():
                            self.logger.error(f"Failed to restore database {db_name} for app {app}")
                            continue

                        # 恢复表信息
                        if 'tables' in db_info:
                            for table_name, table_info in db_info['tables'].items():
                                status, _ = self.zk.zk_table_create(app, db_name, table_name, table_info)
                                if status != 0:
                                    self.logger.error(f"Failed to restore table {table_name} in {db_name} for app {app}")

                # 新增：恢复 dbproxy 信息
                if 'proxies' in app_data:
                    for proxy_id, proxy_info in app_data['proxies'].items():
                        # 从 proxy_id 中提取 IP 和端口
                        # 假设 proxy_id 格式为 'dbproxy_ip_port'
                        parts = proxy_id.split('_')
                        if len(parts) >= 3:
                            ip = parts[1]
                            port = parts[2]
                            status, _ = self.zk.zk_proxy_create(app, ip, port, proxy_info)
                            if status != 0:
                                self.logger.error(f"Failed to restore proxy {proxy_id} for app {app}")

            self.logger.info("Restore completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to restore from backup: {str(e)}")
            return False

    def run_backup(self, app_id: str) -> Optional[str]:
        """
        执行备份操作
        
        Args:
            app_id: 应用ID
            
        Returns:
            备份文件路径，如果备份失败返回None
        """
        try:
            self.logger.info(f"Starting XDB ZK backup for application {app_id}...")
            
            # 收集应用数据
            backup_data = {
                "backup_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "zk_host": self.zk.zk_host,
                "app_id": app_id,
                "data": self.backup_application_data(app_id)
            }
            
            # 保存备份
            backup_file = self.save_backup(app_id, backup_data)
            
            # 压缩备份
            compressed_file = self.compress_backup(backup_file)
            
            # 清理旧备份
            self.cleanup_old_backups(app_id)
            
            self.logger.info(f"Backup completed successfully for app {app_id}. Backup file: {compressed_file}")
            return compressed_file
            
        except Exception as e:
            self.logger.error(f"Backup failed for app {app_id}: {str(e)}")
            return None

def main():
    """
    主函数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='XDB ZK Backup Tool')
    parser.add_argument('--action', choices=['backup', 'restore'], required=True,
                      help='Action to perform: backup or restore')
    parser.add_argument('--zk-host', required=True, help='ZooKeeper host:port')
    parser.add_argument('--backup-dir', required=True, help='Backup directory')
    parser.add_argument('--app-id', required=True, help='Application ID to backup')
    parser.add_argument('--retention-days', type=int, default=30,
                      help='Number of days to retain backups')
    parser.add_argument('--backup-file', help='Backup file to restore from')
    parser.add_argument('--apps', nargs='+', help='Applications to restore')
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    backup_tool = XDBZKBackup(args.zk_host, args.backup_dir, args.retention_days)
    
    if args.action == 'backup':
        backup_file = backup_tool.run_backup(args.app_id)
        if backup_file:
            print(f"Backup completed successfully. Backup file: {backup_file}")
            sys.exit(0)
        else:
            print("Backup failed!")
            sys.exit(1)
    else:  # restore
        if not args.backup_file:
            print("Backup file must be specified for restore action")
            sys.exit(1)
        
        success = backup_tool.restore_from_backup(args.backup_file, args.apps)
        if success:
            print("Restore completed successfully")
            sys.exit(0)
        else:
            print("Restore failed!")
            sys.exit(1)

if __name__ == "__main__":
    """
    执行备份：
python3 backup_zk_data.py --action backup \
    --zk-host zookeeper-global-system.base.svc.cluster.local:2181 \
    --backup-dir /path/to/backup \
    --retention-days 30 \
    --app-id your_app_id

    执行恢复：
    #    恢复所有应用
    python3 backup_zk_data.py --action restore \
        --zk-host localhost:2181 \
        --backup-dir /path/to/backup \
        --backup-file /path/to/backup/xdb_zk_backup_20240321_103045.gz

    # 只恢复指定应用
    python3 backup_zk_data.py --action restore \
        --zk-host localhost:2181 \
        --backup-dir /path/to/backup \
        --backup-file /path/to/backup/xdb_zk_backup_20240321_103045.gz \
        --apps app1 app2
        
    """
    main() 