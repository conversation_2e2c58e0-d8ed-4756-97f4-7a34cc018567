#!/usr/bin/env python3
# coding=utf-8

"""

# Author       : z<PERSON><PERSON><PERSON><PERSON>@baidu.com
# BuildDate    : 2016-07-13
# Description  : the library for the account resource management, including:
#                AccountCreate
# Modified     : 2016.07.13 Init By zengguowei
#                2016.11.22 Modifed by z<PERSON><PERSON><PERSON><PERSON>, change account_update to async model, but only support add db auth.

"""
import json
import os
import pymysql
import random
import string
from datetime import datetime
from logging import Logger

from xdboperator.lightxdb.http_client import HttpService
from xdboperator.lightxdb.xagent import XagentService
from xdboperator.lightxdb.zookeeper import ZookeeperService



class AccountExistsError(Exception):
    """账户已存在异常"""
    pass

def get_password(length=16, chars=string.ascii_letters + string.digits):
    """
    生成指定长度的密码字符串，默认为16位，包含大小写字母和数字。
    
    Args:
        length (int, optional, default=16): 密码字符串的长度。默认为16位。
        chars (str, optional, default=string.ascii_letters + string.digits): 可选的字符集合，包含大小写字母和数字。默认为字母和数字组合。
    
    Returns:
        str: 返回一个由指定字符集合中随机选择的字符组成的字符串，长度为length。
    """
    return ''.join([random.choice(chars) for i in range(length)])


class AccountService(object):
    """AccountService"""
    __version__ = '1.0.0.0'
    HTTP_TIMEOUT_S = 60

    def __init__(self, zk_domain, db_version, app_id, logger: Logger):
        """
            初始化XagentClient类的实例，并设置相关属性值。
        
        Args:
            zk_domain (str): ZooKeeper域名，用于连接ZooKeeper服务器。
            db_version (int): 数据库版本号，表示当前使用的数据库版本。
            app_id (str): 应用ID，用于标识当前应用。
            logger (Logger): Logger类型的日志记录器对象，用于记录日志信息。
        
        Returns:
            None.
        
        Raises:
            无.
        """
        self.app_id = app_id
        self.xagent_service = XagentService(logger)
        self.http_service = HttpService()
        self.zk_service = ZookeeperService(zk_domain, logger)
        self.db_version = db_version
        self.logger = logger

    def get_instance_by_app(self, app_id):
        """
            根据应用ID获取实例列表，返回一个包含三个字段的列表：'ip', 'mysql_port', 'xagent_port'。
        如果获取失败，则返回空列表。
        
        Args:
            app_id (str): 应用ID，类型为str。
        
        Returns:
            list, dict: 返回一个列表，每个元素是一个字典，包含三个键值对：'ip'(str), 'mysql_port'(int), 'xagent_port'(int)。如果获取失败，则返回空列表。
        """
        ret = []
        status, node_list = self.zk_service.zk_node_list(app_id)
        if status != 0:
            self.logger.warning("Get app[%s] node list from zk failed: %d[%s].",
                                app_id, status, node_list)
            return []
        for each_node in node_list:
            status, output = self.zk_service.zk_node_show(app_id, each_node)
            if status != 0:
                self.logger.warning("Get node[%s:%s] from zk failed: %d[%s].",
                                    app_id, each_node, status, output)
                return []
            ret.append({
                'ip': output['ip'],
                'mysql_port': output['port'],
                'xagent_port': output['xagent_port']
            })
        return ret

    def get_dbproxy_by_app(self, app_id):
        """
            根据应用ID获取数据库代理节点列表。
        如果获取失败，将记录警告日志并返回空列表。
        
        Args:
            app_id (str): 应用ID，类型为str。
        
        Returns:
            List[Dict[str, str]]: 包含字典 {'ip': 'IP地址'} 的列表，类型为List[Dict[str, str]]。如果获取失败，则返回空列表。
        """
        ret = []
        status, proxy_list = self.zk_service.zk_proxy_list(app_id)
        if status != 0:
            self.logger.warning("Get app[%s] node list from zk failed: %d[%s].",
                                app_id, status, proxy_list)
            return []
        for each_proxy in proxy_list:
            status, output = self.zk_service.zk_proxy_show(app_id, each_proxy)
            if status != 0:
                self.logger.warning("Get node[%s:%s] from zk failed: %d[%s].",
                                    app_id, each_proxy, status, output)
                return []
            ret.append({
                'ip': output['ip']
            })
        return ret

    def compare_account_create(self, account_name, account_info):
        """
            对比账号创建，返回类型和执行的sql语句列表。
        如果数据库版本为8.0，则不会使用密码创建账号。
        
        Args:
            account_name (str): 账号名称。
            account_info (dict): 账号信息字典，包含以下键值：
                db_auth (list, optional): 账号授权信息列表，每个元素是一个字典，包含以下键值：
                    dbname (str, optional): 数据库名称，默认为空。
                    tables (list, optional): 表名列表，默认为空。
                    privileges (str, optional): 权限，默认为'SELECT'。
                groups (list, optional): 组名列表，默认为['group_0000']。
                db_password (str, optional): 账号密码，默认为空。
                db_username (str, optional): 账号用户名，默认为账号名称。
                type (str, optional): 账号类型，可选值为'online'或'rdview'，默认为'online'。
        
        Returns:
            tuple (int, list): 返回一个元组，第一个元素是账号类型，第二个元素是执行的sql语句列表，如果出现错误，返回-1。
        """
        if 'db_password' in account_info and account_info['db_password']:
            password = account_info['db_password']
        else:
            self.logger.warning("Db_password of account[%s] is empty.",
                                account_name)
            return -1
        db_username = account_name
        if 'db_username' in account_info and account_info['db_username']:
            db_username = account_info['db_username']
        else:
            account_info['db_username'] = db_username
        if 'groups' not in account_info:
            account_info['groups'] = ["group_0000"]

        result_sql = []
        type = 'online'
        if 'type' in account_info and account_info['type'] == 'rdview':
            type = account_info['type']

        if '8.0' == self.db_version:
            sql = "CREATE USER '%s'@'%%s' IDENTIFIED BY '%s';" % (
                db_username, password)
            result_sql.append(sql)
            if account_info.get("mysql_max_connections"):
                sql = "ALTER USER '%s'@'%%s' MAX_USER_CONNECTIONS %s;" % \
                        (db_username, account_info["mysql_max_connections"])
                result_sql.append(sql)
            pass

        for key, value in account_info.items():
            if key == "db_auth":
                for each_dbauth in value:
                    privileges = "SELECT"
                    if 'dbname' not in each_dbauth or not each_dbauth['dbname']:
                        self.logger.warning("Dbname is empty.")
                        return -1
                    if 'privileges' in each_dbauth and each_dbauth['privileges']:
                        privileges = each_dbauth['privileges']
                    if 'tables' in each_dbauth and each_dbauth['tables']:
                        for each_table in each_dbauth['tables']:
                            if '8.0' == self.db_version:
                                sql = ("GRANT %s ON %s.%s TO '%s'@'%%s'") % \
                                      (privileges, each_dbauth['dbname'], each_table, db_username)
                            else:
                                sql = ("GRANT %s ON %s.%s TO '%s'@'%%s' IDENTIFIED BY '%s'") % \
                                      (privileges, each_dbauth['dbname'], each_table, db_username, password)
                            result_sql.append(sql)
                    else:
                        if '8.0' == self.db_version:
                            sql = ("GRANT %s ON %s.* TO '%s'@'%%s'") % \
                                  (privileges, each_dbauth['dbname'], db_username)
                        else:
                            sql = ("GRANT %s ON %s.* TO '%s'@'%%s' IDENTIFIED BY '%s' WITH MAX_USER_CONNECTIONS %s") % \
                                  (privileges, each_dbauth['dbname'], db_username, password,
                                   account_info.get("mysql_max_connections", 0))
                        result_sql.append(sql)
                        # 只给xdb-schema xdb-data 两个账户授权_skeema
                        if  db_username.startswith("xsi") or db_username.startswith("xdi"):
                            sql = ("GRANT %s ON %s.* TO '%s'@'%%s'") % \
                                (privileges, f"_skeema_tmp_{each_dbauth['dbname']}", db_username)
                            result_sql.append(sql)
        return type, result_sql
    
    @staticmethod
    def generate_new_proxy_node_account_sql(proxy_ip, account_name, account_password):
        """
        生成新代理节点账号SQL语句
        Args:
            proxy_ip (_type_): _description_
            account_name (_type_): _description_
            account_password (_type_): _description_

        Returns:
            _type_: _description_
        """
        return "CREATE USER '%s'@'%s' IDENTIFIED BY '%s';" % (account_name, proxy_ip, account_password)
    @staticmethod
    def generate_user_grant_sqls( username, host, dbname, table, privileges="ALL"):
        """
        返回用户授权SQL语句列表

        Args:
            username (_type_): _description_
            host (_type_): _description_
            dbname (_type_): _description_
            table (_type_): _description_
            privileges (str, optional): _description_. Defaults to "ALL".

        Returns:
            _type_: _description_
        """
        grants = ["GRANT %s ON %s.* TO '%s'@'%s'" % (privileges, dbname, username, host)]
        
        if username.startswith("xsi") or username.startswith("xdi"):
            grants.append("GRANT %s ON %s.* TO '%s'@'%s'" % (privileges, f"_skeema_tmp_{dbname}", username, host))
        
        return grants

    def update_account_rw_flag_to_zk(self, account_info, wr_flag):
        """
            修改账户db读写分离权限
        """
                        
        account_name = account_info['account_info']['username']
        status, zk_account_info = self.zk_service.zk_account_show(self.app_id, account_name)   
        zk_account_info['wr_flag'] = wr_flag
        status, output = self.zk_service.zk_account_update(self.app_id,
                                                           account_name, zk_account_info)
        if status != 0:
            self.logger.warning("update account[%s] wr_flag to zk failed: %d[%s].",
                                account_name, status, output)
            return -1
        self.logger.info("update account[%s:%s] wr_flag success.",
                         account_name, account_info['account_info']['password'])
        print("update account[%s:%s] wr_flag success." % \
              (account_name, account_info['account_info']['password']))
        return 0
    
    def account_modify(self, account_info, allowedHosts) -> int:
        
        """
            修改账户权限，增加、删除用户关联的db
        """
        self.logger.debug('account_create: appId: %s, account_info: %s', self.app_id, json.dumps(account_info))
        status, account_list = self.zk_service.zk_account_list(self.app_id)
        if status != 0:
            self.logger.warning("Get account list of app[%s] from zk failed: %d.",
                                self.app_id, status)
            return -1
        account_name = ""
        if 'account_info' in account_info and \
                'username' in account_info['account_info']:
            account_name = account_info['account_info']['username']
        if account_name in account_list:
            self.logger.warning("Account[%s] is already in app[%s], can be modified.",
                                account_name, self.app_id)

        type, compare_result = self.compare_account_create(account_name, account_info['account_info'])

        final_sql = ""
        if type == "rdview":
            # instance = self.get_offline_instance(self.app_id)
            instance = []
            for each_instance in instance:
                if 'authip' not in account_info['account_info'] or \
                        not account_info['account_info']['authip']:
                    self.logger.warning("Authip is empty.")
                    return -1
                for each_authip in account_info['account_info']['authip']:
                    for sql in compare_result:
                        final_sql = sql % each_authip
                        ret = self.xagent_service.xagent_sql(each_instance['ip'], each_instance['xagent_port'],
                                                             each_instance['mysql_port'], final_sql, 0)
                        if ret is None and not sql.startswith('CREATE'):
                            self.logger.warning("Execute sql[%s] failed: %s, %s, %s.", final_sql,
                                                each_instance['ip'], each_instance['xagent_port'],
                                                each_instance['mysql_port'])
                            return -1
        else:
            appId = self.app_id
            clusterId = self.app_id + "_0000"
            status, output = self.zk_service.zk_replicate_topo_show(appId, clusterId)
            if status != 0:
                self.logger.error("Get zookeeper cluster[%s/%s] topology failed: %s[%s].",
                                  appId, clusterId, status, output)
                return -1
            topo_info = json.loads(output)
            if clusterId not in topo_info or \
                    'master' not in topo_info[clusterId] or \
                    topo_info[clusterId]['master'] == '':
                self.logger.error("No master exist now in zookeeper cluster[%s.%s] topology: %s",
                                  appId, clusterId, output)
                return -1
            master_node = topo_info[clusterId]['master']
            self.logger.debug('Cluster: %s.%s, Master: %s', appId, clusterId, master_node)
            status, output = self.zk_service.zk_node_info(appId, master_node)
            if status != 0:
                self.logger.error("Get zookeeper node[%s.%s] info failed: %s[%s].",
                                  appId, master_node, status, output)
                return -1
            node_info = json.loads(output)
            dbproxys = self.get_dbproxy_by_app(self.app_id)
            # 如果允许的hosts为空，则默认使用所有dbproxy的ip
            hosts = allowedHosts if allowedHosts else [i['ip'] for i in dbproxys] 
            each_instance = node_info
            for host in hosts:
                for sql in compare_result:
                    final_sql = sql % host
                    ret = self.xagent_service.xagent_sql(each_instance['ip'], each_instance['xagent_port'],
                                                         each_instance['port'], final_sql, 1)
                    if ret is None and not sql.startswith('CREATE'):
                        self.logger.warning("Execute sql[%s] failed: %s, %s, %s.", final_sql,
                                            each_instance['ip'], each_instance['xagent_port'], each_instance['port'])
                        return -1    
        zk_account_info = account_info['account_info']
        
        # file_data = json.dumps(zk_account_info)
        status, output = self.zk_service.zk_account_update(self.app_id,
                                                           account_name, zk_account_info)
        if status != 0:
            self.logger.warning("update account[%s] to zk failed: %d[%s].",
                                account_name, status, output)
            return -1
        self.logger.info("update account[%s:%s] wr_flag db success.",
                         account_name, account_info['account_info']['password'])
        print("update account[%s:%s] db success." % \
              (account_name, account_info['account_info']['password']))
        return 0



    def account_create(self, account_info, allowedHosts):
        """
            创建账号，并将账号信息注册到ZK中。
        
        Args:
            account_info (dict): 包含账号信息的字典，格式如下：
                {
                    "account_info": {
                        "username": "test",  # 必须，账号名称
                        "password": "123456",  # 必须，账号密码
                        "authip": ["***********"]  # 可选，账号授权IP列表，默认为空列表
                    }
                }
        
        Returns:
            int: 返回值为0表示成功，-1表示失败。
        
        Raises:
            无。
        """
        self.logger.debug('account_create: appId: %s, account_info: %s', self.app_id, json.dumps(account_info))
        status, account_list = self.zk_service.zk_account_list(self.app_id)
        if status != 0:
            self.logger.warning("Get account list of app[%s] from zk failed: %d.",
                                self.app_id, status)
            return -1
        account_name = ""
        if 'account_info' in account_info and \
                'username' in account_info['account_info']:
            account_name = account_info['account_info']['username']
        if account_name in account_list:
            status, zk_account = self.zk_service.zk_account_show(self.app_id, account_name)
            if status != 0:
                self.logger.warning("Failed to get account info from zk for [%s]: %d", account_name, status)
                raise AccountExistsError(f"Account {account_name} already exists, but failed to fetch zk info")
            input_password = account_info['account_info'].get('password')
            zk_password = zk_account.get('password') or zk_account.get('db_password')
            if input_password == zk_password:
                self.logger.info("Account[%s] exists and password matches, updating info in zk.", account_name)
                return self.account_modify(account_info, allowedHosts)
            else:
                self.logger.warning("Account[%s] exists but password mismatch.", account_name)
                raise AccountExistsError(f"Account {account_name} already exists with a different password")

        type, compare_result = self.compare_account_create(account_name, account_info['account_info'])

        final_sql = ""
        if type == "rdview":
            # instance = self.get_offline_instance(self.app_id)
            instance = []
            for each_instance in instance:
                if 'authip' not in account_info['account_info'] or \
                        not account_info['account_info']['authip']:
                    self.logger.warning("Authip is empty.")
                    return -1
                for each_authip in account_info['account_info']['authip']:
                    for sql in compare_result:
                        final_sql = sql % each_authip
                        ret = self.xagent_service.xagent_sql(each_instance['ip'], each_instance['xagent_port'],
                                                             each_instance['mysql_port'], final_sql, 0)
                        if ret is None and not sql.startswith('CREATE'):
                            self.logger.warning("Execute sql[%s] failed: %s, %s, %s.", final_sql,
                                                each_instance['ip'], each_instance['xagent_port'],
                                                each_instance['mysql_port'])
                            return -1
        else:
            appId = self.app_id
            clusterId = self.app_id + "_0000"
            status, output = self.zk_service.zk_replicate_topo_show(appId, clusterId)
            if status != 0:
                self.logger.error("Get zookeeper cluster[%s/%s] topology failed: %s[%s].",
                                  appId, clusterId, status, output)
                return -1
            topo_info = json.loads(output)
            if clusterId not in topo_info or \
                    'master' not in topo_info[clusterId] or \
                    topo_info[clusterId]['master'] == '':
                self.logger.error("No master exist now in zookeeper cluster[%s.%s] topology: %s",
                                  appId, clusterId, output)
                return -1
            master_node = topo_info[clusterId]['master']
            self.logger.debug('Cluster: %s.%s, Master: %s', appId, clusterId, master_node)
            status, output = self.zk_service.zk_node_info(appId, master_node)
            if status != 0:
                self.logger.error("Get zookeeper node[%s.%s] info failed: %s[%s].",
                                  appId, master_node, status, output)
                return -1
            node_info = json.loads(output)
            dbproxys = self.get_dbproxy_by_app(self.app_id)
            # 如果允许的hosts为空，则默认使用所有dbproxy的ip
            hosts = allowedHosts if allowedHosts else [i['ip'] for i in dbproxys] 
            each_instance = node_info
            for host in hosts:
                for sql in compare_result:
                    final_sql = sql % host
                    ret = self.xagent_service.xagent_sql(each_instance['ip'], each_instance['xagent_port'],
                                                         each_instance['port'], final_sql, 1)
                    if ret is None and not sql.startswith('CREATE'):
                        self.logger.warning("Execute sql[%s] failed: %s, %s, %s.", final_sql,
                                            each_instance['ip'], each_instance['xagent_port'], each_instance['port'])
                        return -1

        file_data = json.dumps(account_info['account_info'])
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            temp_file = "./temp_%s.conf" % (timestamp)
            fp = open(temp_file, 'w')
            fp.write(file_data)
            fp.close()
        except Exception as e:
            return -1

        file_tag = True
        status, output = self.zk_service.zk_account_create(self.app_id,
                                                           account_name, temp_file, file_tag)
        if status != 0:
            self.logger.warning("Register account[%s] to zk failed: %d[%s].",
                                account_name, status, output)
            return -1

        if os.path.exists(temp_file):
            os.remove(temp_file)

        self.logger.info("Create account[%s:%s] success.",
                         account_name, account_info['account_info']['password'])
        print("Create account[%s:%s] success." % \
              (account_name, account_info['account_info']['password']))
        return 0

    def get_master_node_from_zookeeper(self, app_id, cluster_id):
        """
            从ZooKeeper中获取指定应用的Master节点信息。
        
        Args:
            app_id (str): 应用ID。
        
        Returns:
            tuple: 包含状态码（status）和输出结果（output）的元组。
                
                - status (int): 状态码，0表示成功，其他表示失败。
                - output (str): 输出结果，如果状态码为0，则为JSON格式的Master节点信息；否则为错误信息。
        
        Raises:
            None.
        """
        status, output = self.zk_service.zk_replicate_topo_show(app_id, app_id + '_0000')
        if status != 0:
            self.logger.error("Get zookeeper cluster[%s.%s] topology failed: %s[%s].",
                              app_id, app_id + '_0000', status, output)
            return status, output
        status, output = self.zk_service.zk_replicate_topo_show(app_id, cluster_id)
        if status != 0:
            self.logger.error("Get zookeeper cluster[%s/%s] topology failed: %s[%s].",
                            app_id, cluster_id, status, output)
            return -1, {}
        topo_info = json.loads(output)
        if cluster_id not in topo_info or \
                'master' not in topo_info[cluster_id] or \
                topo_info[cluster_id]['master'] == '':
            self.logger.error("No master exist now in zookeeper cluster[%s.%s] topology: %s",
                            app_id, cluster_id, output)
            return -1, {}
        master_node = topo_info[cluster_id]['master']
        self.logger.debug('Cluster: %s.%s, Master: %s', app_id, cluster_id, master_node)
        status, output = self.zk_service.zk_node_info(app_id, master_node)
        if status != 0:
            self.logger.error("Get zookeeper node[%s.%s] info failed: %s[%s].",
                            app_id, master_node, status, output)
            return -1, {}
        node_info = json.loads(output)
        each_instance = node_info
        return 0, each_instance

    def account_auth_db_compare(self, authdb_old, authdb_new):
        """
        account_auth_db_compare
        """
        diff_dict = {}
        for each_new in authdb_new:
            diff_dict[each_new["dbname"]] = {
                'grant': [],
                'revoke': []
            }
            for each_old in authdb_old:
                if each_new['dbname'] == each_old['dbname']:
                    if each_new['privileges'] == "":
                        pri_new = []
                    else:
                        pri_new = each_new['privileges'].replace(' ', '').split(',')
                    if each_old['privileges'] == "":
                        pri_old = []
                    else:
                        pri_old = each_old['privileges'].replace(' ', '').split(',')
                    if pri_new:
                        for each_pri_new in pri_new:
                            if each_pri_new not in pri_old:
                                diff_dict[each_new['dbname']]['grant'].append(each_pri_new)
                    if pri_old:
                        for each_pri_old in pri_old:
                            if each_pri_old not in pri_new:
                                diff_dict[each_new['dbname']]['revoke'].append(each_pri_old)
                        break
                    break
            else:
                if each_new['privileges'] != "":
                    diff_dict[each_new['dbname']]['grant'] = each_new['privileges'].replace(' ', '').split(',')
        return diff_dict

    def account_auth_db_add(self, data):
        """
            新增账号授权数据库操作，包括获取账号信息、比对授权信息、执行授权操作和更新ZK中的账号授权信息等。
        
        Args:
            data (dict): 包含账号ID（accountId）、应用ID（appId）和需要授权的数据库列表（db_auth）的字典。
        
                - accountId (str): 账号ID。
                - appId (str): 应用ID。
                - db_auth (list[dict]): 需要授权的数据库列表，每个元素为一个字典，包含以下键值对：
                    - dbname (str): 数据库名称。
                    - privileges (str, optional): 授予的权限，默认为空字符串。
        
        Returns:
            int: 返回值为0表示成功；返回值为-1表示失败。
        
        Raises:
            None.
        """
        # new: data, zk old: account_info
        self.logger.info('account_auth_db_add start: %s', data)
        account_id = data['accountId']
        app_id = data['appId']
        status, account_info = self.zk_service.zk_account_show(app_id, account_id)
        if status != 0:
            self.logger.warning("Get account[%s] of app[%s] from zk failed: %d[%s].",
                                account_id, self.app_id, status, account_info)
            return -1
        self.logger.info('Get account info success: %s', account_info)
        if len(data['db_auth']) == 0:
            return 0
        ip_list = []
        instances = []
        if account_info['type'] == "rdview":
            # instances = self.get_offline_instance(self.app_id)
            for each_instance in instances:
                # get ip of account
                sql = "SELECT host FROM mysql.user WHERE user = '%s'" % (account_id)
                ret = self.xagent_service.xagent_sql(each_instance['ip'], each_instance['xagent_port'],
                                                     each_instance['mysql_port'], sql)
                if ret is None:
                    self.logger.warning("Execute sql[%s] failed." % sql)
                    return -1
                for each_host in ret['data']:
                    ip_list.append(each_host['host'])
        else:
            instances = self.get_instance_by_app(self.app_id)
            dbproxys = self.get_dbproxy_by_app(self.app_id)
            for each_dbproxy in dbproxys:
                ip_list.append(each_dbproxy['ip'])

        ip_list = list(set(ip_list))
        if len(ip_list) == 0 or len(instances) == 0:
            self.logger.info('No ip list[%s] or instances[%s] exist, will skip grants.', ip_list, instances)
            return 0

        diff_auth_dict = self.account_auth_db_compare(account_info['db_auth'], data['db_auth'])
        self.logger.info('db auth diff: %s', diff_auth_dict)
        for each_instance in instances:
            for each_db in diff_auth_dict:
                for each_authip in ip_list:
                    ## grant
                    if diff_auth_dict[each_db]['grant']:
                        sql = "GRANT %s ON %s.%s TO '%s'@'%s'" % \
                              (','.join(diff_auth_dict[each_db]['grant']),
                               each_db, '*', account_id, each_authip)
                        ret = self.xagent_service.xagent_sql(each_instance['ip'],
                                                             each_instance['xagent_port'],
                                                             each_instance['mysql_port'], sql, 0)
                        if ret is None:
                            self.logger.warning("Execute sql[%s] failed: ip[%s], port[%s], "
                                                "xagent_port[%s].", sql, each_instance['ip'],
                                                each_instance['mysql_port'],
                                                each_instance['xagent_port'])
                            return -1
                        self.logger.debug('Execute sql[%s] success: ip[%s], port[%s], xagent_port[%s].',
                                          sql, each_instance['ip'], each_instance['mysql_port'],
                                          each_instance['xagent_port'])
                    # revoke
                    if diff_auth_dict[each_db]['revoke']:
                        sql = "REVOKE %s ON %s.%s FROM '%s'@'%s'" % \
                              (','.join(diff_auth_dict[each_db]['revoke']),
                               each_db, '*', account_id, each_authip)
                        ret = self.xagent_service.xagent_sql(each_instance['ip'],
                                                             each_instance['xagent_port'],
                                                             each_instance['mysql_port'], sql, 0)
                        if ret is None:
                            self.logger.warning("Execute sql[%s] failed: ip[%s], port[%s], "
                                                "xagent_port[%s].", sql, each_instance['ip'],
                                                each_instance['mysql_port'], each_instance['xagent_port'])
                            return -1
                        self.logger.debug('Execute sql[%s] success: ip[%s], port[%s], xagent_port[%s].',
                                          sql, each_instance['ip'],
                                          each_instance['mysql_port'],
                                          each_instance['xagent_port'])
        # alter zk db auth info
        new_db_list = []
        db_auth_zk = []
        for each_new_dbpri in data['db_auth']:
            new_db_list.append(each_new_dbpri['dbname'])
            if each_new_dbpri['privileges'] == "":
                continue
            db_auth_zk.append(each_new_dbpri)

        for each_old_dbpri in account_info['db_auth']:
            if each_old_dbpri['dbname'] in new_db_list:
                continue
            db_auth_zk.append(each_old_dbpri)
        status, output = self.zk_service.zk_account_update(self.app_id,
                                                           account_id, {'db_auth': db_auth_zk})
        if status != 0:
            self.logger.warning("Update account[%s:%s] to zk failed: %d[%s].",
                                account_id, account_info, status, output)
            return -1
        self.logger.info("Update account[%s:%s] to zk success: %d[%s].",
                         account_id, account_info, status, output)

        return 0

    @staticmethod
    def check_mysql_account_connection(user, password, host, database, port) -> bool:
        """
            检查MySQL账户连接是否正常。
        如果连接成功，则返回True；否则返回False。
        
        Args:
            user (str): MySQL用户名。
            password (str): MySQL密码。
            host (str): MySQL主机地址。默认为localhost。
            database (str, optional): MySQL数据库名称。默认为None。
            port: xdb 代理端口
        
        Returns:
            bool: 如果连接成功，则返回True；否则返回False。

        """
        try:
            connection = pymysql.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=database
            )

            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result and result[0] == 1:
                    return True
                else:
                    return False
        except pymysql.MySQLError as e:
            print(f"Error: {e}")
            return False
        finally:
            if 'connection' in locals() and connection.open:
                connection.close()
