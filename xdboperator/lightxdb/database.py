#!/usr/bin/env python3
# coding=utf-8
"""
# Author       : z<PERSON><PERSON>ow<PERSON>@baidu.com
# BuildDate    : 2016-07-13
# Description  : the library for the database resource management, including:
#                   DatabaseCreate
# Modified     : 2016.07.13 Init By zengguowei
#                2016.11.22 Modifed by zengguowei, add accountAuth for create database.
"""

import json
from logging import Logger

from xdboperator.controller import config
from xdboperator.controller.consts import ResourceDeletePolicy
from xdboperator.lightxdb.xagent import XagentService
from xdboperator.lightxdb.zookeeper import ZookeeperService


class DatabaseService(object):
    """DatabaseService"""
    __version__ = '*******'
    HTTP_TIMEOUT_S = 60

    def __init__(self, zk_domain, app_id, logger: Logger, cluster_name: str):
        """
            初始化ZookeeperXagentService类的实例，用于操作zookeeper和xagent。
        
        Args:
            zk_domain (str): zookeeper域名，格式为"ip1:port1,ip2:port2,..."。
            app_id (str): 应用ID，用于区分不同的应用。
            logger (Logger): 日志记录器，用于记录日志信息。
        
        Returns:
            None.
        
        Raises:
            None.
        """
        self.app_id = app_id
        self.zk_service = ZookeeperService(zk_domain, logger)
        self.xagent_service = XagentService(logger)
        self.logger = logger
        self.cluster_name = cluster_name

    def _get_master_db_port(self, appId, clusterId):
        """
        获取主节点的数据库端口
        
        Args:
            appId (str): ZooKeeper应用ID
            clusterId (str): ZooKeeper集群ID
            
        Returns:
            int: 数据库端口，如果获取失败返回默认端口3203
        """
        try:
            status, output = self.zk_service.zk_replicate_topo_show(appId, clusterId)
            if status == 0:
                topo_info = json.loads(output)
                if clusterId in topo_info and 'master' in topo_info[clusterId] and topo_info[clusterId]['master']:
                    master_node = topo_info[clusterId]['master']
                    status, output = self.zk_service.zk_node_info(appId, master_node)
                    if status == 0:
                        node_info = json.loads(output)
                        return node_info.get('port', 3203)
        except Exception as e:
            self.logger.warning(f"Failed to get master db port: {e}")
        
        # 返回默认端口
        return 3203

    def master_mysql_execute_sql(self, appId, clusterId, sql):
        """
            执行MySQL语句，优先通过固定域名访问主节点，失败后查询ZooKeeper获取主节点信息。
        如果获取失败或者没有Master节点，则返回-1。
        
        Args:
            appId (str): ZooKeeper应用ID。
            clusterId (str): ZooKeeper集群ID。
            sql (str): MySQL语句字符串。
        
        Returns:
            int: 0表示执行成功，-1表示执行失败。
        """
        # 首先尝试通过固定域名访问主节点
        master_svc_domain = f"{self.cluster_name}-mysql-master.base.svc.cluster.local"
        xagent_port = 8500
        
        # 测试固定域名的8500端口连通性
        if self.xagent_service.test_port(master_svc_domain, xagent_port):
            self.logger.info(f"Using fixed master service domain: {master_svc_domain}:{xagent_port}")
            # 获取数据库端口
            db_port = 3203
            ret = self.xagent_service.xagent_sql(master_svc_domain, xagent_port, db_port, sql)
            if ret is not None:
                self.logger.info("Execute sql[%s] success via fixed domain: %s:%s, port[%s], %s.",
                                 sql, master_svc_domain, xagent_port, db_port, ret)
                return 0
            else:
                self.logger.warning("Execute sql failed via fixed domain: %s:%s, port[%s], sql[%s].",
                                    master_svc_domain, xagent_port, db_port, sql)
                # 固定域名失败，继续使用ZK查询逻辑
        
        # 通过ZooKeeper查询主节点信息
        self.logger.info(f"Falling back to ZK topology query for cluster: {appId}/{clusterId}")
        node_info = {}

        status, output = self.zk_service.zk_replicate_topo_show(appId, clusterId)
        if status != 0:
            self.logger.error("Get zookeeper cluster[%s/%s] topology failed: %s[%s].",
                              appId, clusterId, status, output)
            return -1
        topo_info = json.loads(output)
        if clusterId not in topo_info or \
                'master' not in topo_info[clusterId] or \
                topo_info[clusterId]['master'] == '':
            self.logger.error("No master exist now in zookeeper cluster[%s.%s] topology: %s",
                              appId, clusterId, output)
            return -1
        master_node = topo_info[clusterId]['master']
        self.logger.debug('Cluster: %s.%s, Master: %s', appId, clusterId, master_node)
        status, output = self.zk_service.zk_node_info(appId, master_node)
        if status != 0:
            self.logger.error("Get zookeeper node[%s.%s] info failed: %s[%s].",
                              appId, master_node, status, output)
            return -1
        node_info = json.loads(output)

        ret = self.xagent_service.xagent_sql(node_info['ip'], node_info['xagent_port'],
                                             node_info['port'], sql)
        if ret is None:
            self.logger.warning("Execute sql failed: xagent ip[%s:%s], port[%s], sql[%s], error[%s].",
                                node_info['ip'], node_info['port'], node_info['xagent_port'], sql, ret)
            return -1
        self.logger.info("Execute sql[%s] success: xagent ip[%s:%s], port[%s], %s.",
                         sql, node_info['ip'], node_info['port'],
                         node_info['xagent_port'], ret)
        return 0

    def mysql_database_create(self, app_id, database_name, charset):
        """
            创建MySQL数据库，如果不存在则创建。
        
        Args:
            app_id (int): 应用ID。
            database_name (str): 数据库名称。
            charset (str): 字符集，例如：utf8、gbk等。
        
        Returns:
            int: 返回值为0表示成功，-1表示失败。
        
        Raises:
            无。
        """
        sql = ("create database if not exists %s default charset %s") % (database_name, charset)
        status, output = self.zk_service.zk_cluster_list(app_id)
        if status != 0:
            self.logger.warning("Get app[%s]'s cluster list failed: %d[%s].",
                                app_id, status, output)
            return -1
        cluster_list = json.loads(output)
        for cluster_id in cluster_list:
            ret = self.master_mysql_execute_sql(app_id, cluster_id, sql)
            if ret != 0:
                self.logger.error('Create database[%s.%s] failed: %s.', app_id, database_name, sql)
                return -1
        return 0

    def mysql_show_database(self, app_id, database_name):
        """
        主节点查询db是否存在,使用use 判断结果
        由于通过 xagent task 执行sql 不能返回结果，只返回 执行状况，如果db 不存在 也会返回 0会误判，所以这里使用use
        :param app_id:
        :param database_name:
        :return:
        """

        sql = f""" use {database_name}"""
        status, output = self.zk_service.zk_cluster_list(app_id)
        if status != 0:
            self.logger.warning("Get app[%s]'s cluster list failed: %d[%s].",
                                app_id, status, output)
            return -1
        cluster_list = json.loads(output)
        for cluster_id in cluster_list:
            ret = self.master_mysql_execute_sql(app_id, cluster_id, sql)
            if ret != 0:
                self.logger.error('show database[%s.%s] failed: %s.', app_id, database_name, sql)
                return -1
        return 0

    def zk_show_database(self, app_id, database_name):
        """
        查询zk 里面是否已经有db
        :param app_id:
        :param database_name:
        :return:
        """
        status, database_list = self.zk_service.zk_database_list(app_id)
        self.logger.debug('Get database list success: %s.', database_list)
        if status != 0:
            self.logger.warning("Get database list of app[%s] from zk failed: %d[%s].",
                                self.app_id, status, database_list)
            return -1
        return 0 if database_name in database_list else -1

    def mysql_database_delete(self, app_id, database_name):
        """delete database"""
        sql = ("drop database if exists %s") % (database_name)
        status, output = self.zk_service.zk_cluster_list(app_id)
        if status != 0:
            self.logger.warning("Get app[%s]'s cluster list failed: %d[%s].",
                                app_id, status, output)
            return -1
        cluster_list = json.loads(output)
        for cluster_id in cluster_list:
            ret = self.master_mysql_execute_sql(app_id, cluster_id, sql)
            if ret != 0:
                self.logger.error('Delete database[%s.%s] failed: %s.', app_id, database_name, sql)
                return -1
        return 0

    def database_create(self, database_info):
        """
            创建数据库，如果数据库已存在则返回-1。
        参数（必选）：
            database_info (dict) - 包含数据库信息的字典，格式如下：
                {
                    'database_info': {
                        'dbname': 'test_db',   # 数据库名称，str类型，不能为空
                        'charset': 'utf8mb4',  # 编码方式，str类型，默认utf8mb4
                        'remark': 'test db'    # 备注，str类型，可以为空
                    }
                }
        返回值（必须）：
            int - 创建成功返回0，创建失败返回-1，数据库已存在返回-1
        """
        self.logger.debug('Create database: app[%s], data[%s]', self.app_id, json.dumps(database_info))
        status, database_list = self.zk_service.zk_database_list(self.app_id)
        self.logger.debug('Get database list success: %s.', database_list)
        if status != 0:
            self.logger.warning("Get database list of app[%s] from zk failed: %d[%s].",
                                self.app_id, status, database_list)
            return -1
        database_name = ""
        if 'database_info' in database_info and 'dbname' in database_info['database_info']:
            database_name = database_info['database_info']['dbname']
            charset = database_info['database_info']['charset']
        if database_name == "":
            self.logger.warning("DB name is null of app[%s].", self.app_id)
            return -1
        if database_name in database_list:
            self.logger.warning("Database[%s] is already in app[%s].",
                                database_name, self.app_id)
            # 如果存在，也为成功
            return 0
        if 0 != self.mysql_database_create(self.app_id, database_name, charset):
            return -1
        dbinfo = {
            'dbname': database_info['database_info']['dbname'],
            'default_charset': database_info['database_info']['charset'],
            'remark': database_info['database_info']['remark']
        }
        status, output = self.zk_service.zk_database_create(self.app_id,
                                                            database_name, dbinfo)
        if status != 0:
            self.logger.warning("Register database[%s] to zk failed: %d[%s].",
                                dbinfo, status, output)
            return -1
        self.logger.info("Register database[%s] to zk success.", dbinfo)

        return 0

    def database_delete(self, database_info, delete_policy="retain"):
        """delete database"""
        self.logger.debug('Delete database: app[%s], data[%s]', self.app_id, json.dumps(database_info))
        status, database_list = self.zk_service.zk_database_list(self.app_id)
        self.logger.debug('Get database list: %s.', database_list)
        if status != 0:
            self.logger.warning("Get database list of app[%s] from zk failed: %d[%s].",
                                self.app_id, status, database_list)
            return -1
        database_name = ""
        if 'database_info' in database_info and 'dbname' in database_info['database_info']:
            database_name = database_info['database_info']['dbname']
        if database_name == "":
            self.logger.warning("DB name is null of app[%s].", self.app_id)
            return -1
        if database_name not in database_list:
            self.logger.warning("Database[%s] is not in app[%s].",
                                database_name, self.app_id)
            # 保证幂等性
            return 0
        # 只要全局开关 + 用户开关都为delete 才能删除
        if delete_policy == config.GLOBAL_RESOURCE_DELETE_POLICY == ResourceDeletePolicy.DELETE:
            self.logger.warning("Del policy[%s] Database[%s] is not in app[%s].",
                                delete_policy, database_name, self.app_id)
            if 0 != self.mysql_database_delete(self.app_id, database_name):
                return -1
        dbinfo = {
            'dbname': database_info['database_info']['dbname'],
            'default_charset': database_info['database_info']['charset'],
            'remark': database_info['database_info']['remark']
        }
        status, output = self.zk_service.zk_database_drop(self.app_id,
                                                          database_name)
        if status != 0:
            self.logger.warning("Drop database[%s] in zk failed: %d[%s].",
                                dbinfo, status, output)
            return -1
        self.logger.info("Drop database[%s] in zk success.", dbinfo)

        return 0
