#!/bin/evn python3

"""
xdb zk的封装
"""
import json
import os

import functools
import time
import threading
import copy
import subprocess
from logging import Logger

DIRNAME = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

ATUM = DIRNAME + '/lightxdb/atum'



def cache_with_timeout(timeout: int):
    """
    线程安全的缓存装饰器，支持缓存结果为dict类型，缓存函数结果，并在指定时间后失效。
    :param timeout: 缓存超时时间（秒）
    """
    def decorator(func):
        cache = {}
        lock = threading.RLock()  # 使用递归锁保证线程安全

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 如果是对象方法，第一个参数是self，使用self区分不同对象
            obj = args[0] if len(args) > 0 and hasattr(args[0], '__dict__') else None
            
            # 缓存的键值为 (对象实例, 函数参数) 的组合
            key = (obj, args[1:], frozenset(kwargs.items())) if obj else (args, frozenset(kwargs.items()))
            current_time = time.time()

            with lock:
                # 检查缓存是否存在且未过期
                if key in cache:
                    result, timestamp = cache[key]
                    if current_time - timestamp < timeout:
                        # 如果缓存的结果是dict，返回副本避免被修改
                        if isinstance(result, dict):
                            return copy.deepcopy(result)
                        return result  # 返回缓存的结果

            # 调用原函数并缓存结果
            result = func(*args, **kwargs)

            with lock:
                cache[key] = (result, current_time)  # 缓存结果和当前时间

            return result
        
        return wrapper
    
    return decorator


class ZookeeperService(object):
    """
    zk tools
    """

    def __init__(self, zk_host, logger: Logger):
        """
            初始化ZookeeperClient对象，用于与Zookeeper进行交互。
        
        Args:
            zk_host (str): Zookeeper集群的主机地址，多个地址之间使用逗号分隔。
            logger (Logger): 日志记录器，用于记录ZookeeperClient类中的日志信息。
        
        Returns:
            None.
        
        Raises:
            None.
        """
        self.zk_host = zk_host
        self.logger = logger

    def run_cmd_output_json(self, shell_cmd):
        """
        通过 subprocess 执行atum 命令,python3 版本不支持commands 
        """
        self.logger.info(shell_cmd)
        result = subprocess.run(shell_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        output = result.stdout
        status = result.returncode
        self.logger.info(f"run_cmd_output_json output: {output} status:{status}")
        if status == 0:
            output = json.loads(result.stdout) if output else {}
            return status, output
        return status, output

    def run_cmd(self, shell_cmd):
        """
        通过 subprocess 执行atum 命令,python3 版本不支持commands 
        """
        self.logger.info(f"run_cmd: {shell_cmd}")
        result = subprocess.run(shell_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        output = result.stdout
        status = result.returncode
        self.logger.info(f"run_cmd: {shell_cmd} status:{status} output: {output} ")
        return status, output

    def zk_application_list(self):
        """
        获取当前xdb product list
        ["xdbxiandefault","xdbglobaldefault"]
        """
        shell_cmd = "%s -h %s -v -t xdb list product" % (ATUM, self.zk_host)
        return self.run_cmd_output_json(shell_cmd)

    def zk_application_create(self, app_id):
        """
        创建集群在zk的路径
        """
        shell_cmd = "%s -h %s -v -t xdb create product %s" % (ATUM, self.zk_host, app_id)
        return self.run_cmd(shell_cmd)

    def zk_application_drop(self, app_id):
        """
        删除集群信息
        """
        shell_cmd = "%s -h %s -v -t xdb drop product %s" % (ATUM, self.zk_host, app_id)
        return self.run_cmd(shell_cmd)

    def zk_cluster_list(self, app_id):
        """
        ["bpcmysql_0000"]
        """
        shell_cmd = "%s -h %s -v -t xdb list tsc %s" % \
                    (ATUM, self.zk_host, app_id)
        return self.run_cmd(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_cluster_update(self, app_id, cluster_id, update_info):
        """
            更新ZooKeeper中指定应用的集群信息，包括集群名称和集群版本。
        如果集群版本不存在，则会创建一个新的版本。
        
        Args:
            app_id (int): 应用ID，可以从应用列表页面获取。
            cluster_id (str): 集群ID，可以从集群列表页面获取。
            update_info (dict): 需要更新的集群信息，包含两个字段：name（string）���version（string）。
                name为集群名称，version为集群版本号，格式为"v1.0"。
        
        Returns:
            tuple (int, str): 返回一个元组，第一个元素是状态码，第二个元素是输出结果或错误信息。
            状态码说明：0表示成功，其他值表示失败。
        
        Raises:
            None.
        """
        shell_cmd = "%s -h %s -v -t xdb update tsc %s %s '%s'" % \
                    (ATUM, self.zk_host, app_id, cluster_id, json.dumps(update_info))
        return self.run_cmd(shell_cmd)
        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_node_create(self, app_id, cluster_id, node_info, node_role, node_status, db_info):
        """
            创建一个ZK节点，用于保存应用的节点信息。
        如果节点信息中不包含xagentSyncPort和groups字段，则分别设置为xagentPort+100和['group_0000']。
        
        Args:
            app_id (str): 应用ID。
            cluster_id (int): 集群ID。
            node_info (dict): 节点信息，包括nodeId、serverId、ip、xagentPort等字段。
            node_role (str): 节点角色，可以是master或者slave。
            node_status (str): 节点状态，可以是online或者offline。
            db_info (dict): 数据库信息，包括port、basedir、datadir等字段。
        
        Returns:
            tuple (int, str): 返回值为一个二元组，第一个元素表示命令执行结果，第二个元素表示命令输出或错误信息。
            当命令执行成功时，第一个元素为0；当命令执行失败时，第一个元素为-1，第二个元素为错误信息。
        
        Raises:
            None.
        """
        try:
            if 'xagentSyncPort' not in node_info:
                node_info['xagentSyncPort'] = int(node_info['xagentPort']) + 100
            if 'groups' not in node_info:
                node_info['groups'] = ['group_0000']
            node = {
                'node_id': node_info['nodeId'],
                'cluster_id': cluster_id,
                'server_id': node_info['serverId'],
                'ip': node_info['ip'],
                'port': int(db_info['port']),
                'basedir': db_info['basedir'],
                'datadir': db_info['datadir'],
                'xagent_port': int(node_info['xagentPort']),
                'xagent_sync_port': int(node_info['xagentSyncPort']),
                'region': node_info['region'],
                'pool': node_info['pool'],
                'tags': node_info['tags'],
                "role": node_role,
                "dest_role": node_role,
                "status": node_status,
                "dest_status": node_status,
                "is_exchangeable": 1,
                "exchange_weight": 1,
                "max_connections": 4096,
                "connect_timeout": 200000,
                "connect_timeout_short": 50000,
                "weight": 1,
                "reserved_master_connections": 800,
                "time_reconnect_interval": 30,
                "service_enable_delay": 600,
                'groups': node_info['groups']
            }
            shell_cmd = "%s -h %s -v -t xdb create node %s %s '%s'" % (
                ATUM, self.zk_host, app_id, node_info['nodeId'], json.dumps(node))
            return self.run_cmd(shell_cmd)
            # status, output = commands.getstatusoutput(shell_cmd)
            # return status, output
        except Exception as e:
            return -1, "zk node creat failed: node_info:%s, db_info: %s, error: %s" % (node_info, db_info, e)

    def zk_node_show(self, app_id, node_id):
        """
        (0, {u'exchange_weight': 1, u'weight': 1, u'ip': u'************', u'cluster_id': u'xdb_lxz_dev_0000',
        u'port': 3203, u'max_connections': 1024, u'server_id': u'************', u'basedir': u'/home/<USER>/mysql_3203',
        u'datadir': u'/home/<USER>/mysql_3203/var', u'role': u'slave', u'dest_role': u'slave',
        u'xagent_sync_port': 8624, u'connect_timeout': 200000, u'status': u'serving', u'tags': [],
         u'node_id': u'node_************_3203', u'groups': [u'group_0000'],
        u'reserved_master_connections': 800, u'pool': u'yinshang-online', u'time_reconnect_interval': 30,
        u'service_enable_delay': 600, u'is_exchangeable': 1,
        u'dest_status': u'serving', u'connect_timeout_short': 50000, u'xagent_port': 8500, u'region': u'bj'})

        """
        shell_cmd = "%s -h %s -v -t xdb show node %s %s" % (ATUM, self.zk_host, app_id, node_id)
        return self.run_cmd_output_json(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # if status != 0:
        #     return status, output
        # return status, json.loads(output)

    def zk_node_list(self, app_id, cluster=None):
        """
            获取应用的节点列表，如果没有指定集群则返回所有节点的列表。
        参数：
            app_id (str) - 应用ID，例如 "xdb-test"
            cluster (str, optional) - 集群名称，默认为 None，不指定集群名称时返回所有节点的列表
        返回值（tuple）：
            (int, dict) - 状态码和节点列表，格式为 {<ip>: <port>, ...}，如果出错则返回��态码和错误信息
        """
        if cluster is None:
            shell_cmd = "%s -h %s -v -t xdb list node %s" % (ATUM, self.zk_host, app_id)
        else:
            shell_cmd = "%s -h %s -v -t xdb list node %s %s" % (ATUM, self.zk_host, app_id, cluster)
        return self.run_cmd_output_json(shell_cmd)
        # status, output = commands.getstatusoutput(shell_cmd)
        # if status != 0:
        #     return status, output
        # return status, json.loads(output)

    def zk_node_drop(self, app_id, node_id):
        """
        对于 node 节点，执行 drop 操作，删除节点信息
        """
        shell_cmd = "%s -h %s -v -t xdb drop node %s %s" % (ATUM, self.zk_host, app_id, node_id)
        return self.run_cmd_output_json(shell_cmd)
        # status, output = commands.getstatusoutput(shell_cmd)
        # if status != 0:
        #     return status, output
        # return status, json.loads(output)

    def zk_proxy_create(self, app_id, ip, port, proxy_info):
        """
            创建一个ZK代理节点，并返回状态码和输出信息。
        如果创建成功，则返回状态码为0，否则返回非零状态码。
        
        Args:
            app_id (str): 应用ID，唯一标识一个应用。
            ip (str): 代理服务器的IP地址。
            port (int): 代理服务器的端口号。
            proxy_info (dict): 代理服务器的其他信息，包括配置、版本等。
                key (str): 字段名，可选值有：config、version、version_name、version_code、version_build、version_desc��
                value (str): 字段对应的值，例如：config={"backlog": 1024}。
        
        Returns:
            tuple (int, str): 返回一个元组，第一个元素是状态码（int），第二个元素是输出信息（str）。
        """
        shell_cmd = "%s -h %s -v -t xdb create proxy %s 'dbproxy_%s_%s' '%s'" % \
                    (ATUM, self.zk_host, app_id, ip, port, json.dumps(proxy_info))
        return self.run_cmd(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_proxy_show(self, app_id, proxy_id):
        """
            显示指定应用的指定代理节点信息，返回一个字典对象。
        如果执行失败，将返回一个非零状态码和错误信息。
        
        Args:
            app_id (str): 应用ID，类型为str。
            proxy_id (str): 代理节点ID，类型为str。
        
        Returns:
            dict, (int, str): 返回一个字典对象，包含代理节点的信息，如果执行失败，则返回一个非零状态码和错误信息。
        """
        shell_cmd = "%s -h %s -v -t xdb show proxy %s %s" % (ATUM, self.zk_host, app_id, proxy_id)
        return self.run_cmd_output_json(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # if status != 0:
        #     return status, output
        # return status, json.loads(output)

    def zk_proxy_list(self, app_id):
        """
            获取应用的所有代理节点信息，返回一个字典列表，包含每个代理节点的ip和端口等信息。
        如果获取失败，则返回None。
        
        Args:
            app_id (str): 应用的ID，类型为str。
        
        Returns:
            dict or None: 返回一个字典列表，包含每个代理节点的ip和端口等信息；如果获取失败，则返回None。
        """
        shell_cmd = "%s -h %s -v -t xdb list proxy %s" % (ATUM, self.zk_host, app_id)
        return self.run_cmd_output_json(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # if status != 0:
        #     return status, output
        # return status, json.loads(output)

    def zk_proxy_drop(self, app_id, proxy_id):
        """
        删除指定应用的指定代理节点。
        """
        shell_cmd = "%s -h %s -v -t xdb drop proxy %s  %s" % (ATUM, self.zk_host, app_id, proxy_id)
        return self.run_cmd(shell_cmd)
        # logging.info(shell_cmd)
        # status, output = commands.getstatusoutput(shell_cmd)
        # if status != 0:
        #     return status, output
        # return status, json.loads(output)

    def zk_account_create(self, app, name, file_path, file_tag):
        """
            创建ZooKeeper账户，包括应用名称、账户名称、文件路径和标签等信息。
        
        Args:
            app (str): 应用名称，例如："xdb"。
            name (str): 账户名称，例如："admin"。
            file_path (str): 账户配置文件的路径，例如："/opt/app/xdb/conf/account.xml"。
            file_tag (str): 账户配置文件的标签，例如："xdb"。
        
        Returns:
            tuple (int, str): 返回一个元组，第一个元素是状态码（int），表示命令执行的结果；第二个元素是字符串类型，表示命令的输出结果。
        
        Raises:
            无。
        """
        shell_cmd = "%s -h %s -f %s -v -t xdb create account %s %s" % \
                    (ATUM, self.zk_host, file_path, app, name)
        return self.run_cmd(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_account_list(self, app):
        """
            获取指定应用的账号列表，返回值为dict类型，包含以下字段：
            - account: str, 账号名称
            - password: str, 密码
            - type: int, 账号类型，0表示普通账号，1表示管理员账号
            - is_active: bool, True表示该账号已经激活，False表示未激活
            - create_time: str, 创建时间，格式为'%Y-%m-%d %H:%M:%S'
            - update_time: str, 更新时间，格式为'%Y-%m-%d %H:%M:%S'
        参数:
            - app (str) - 应用名称
        返回值:
            - dict - 包含账号信息的字典，如果无账号则返回空字典 {}
        """
        shell_cmd = "%s -h %s -v -t xdb show account_list %s" % (ATUM, self.zk_host, app)
        return self.run_cmd_output_json(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, json.loads(output)

    def zk_account_show(self, app, account):
        """
            显示指定应用的指定账号信息
        
        Args:
            app (str): 应用名称
            account (str): 账号名称
        
        Returns:
            dict, int: 返回一个字典对象，包含账号信息，如果执行失败则返回错误码（int），错误原因（str）
            $ ./atum -h zookeeper-xa-base.base.svc.cluster.local:2181  show account  xdbglobaldefault xsi_hestia
            {"allowed_hosts":["%"],"authbns":[],"authip":[],"authip_enable":0,
            "db_auth":[{"dbname":"hestia","privileges":"ALL"}],
            "db_password":"kusbzxnqd2kc","db_password_d":"kusbzxnqd2kc",
            "db_username":"xsi_hestia","default_charset":"utf8","default_db":
            "hestia","groups":["group_0000"],"max_connections":500,
            "mysql_max_connections":0,"password":"kusbzxnqd2kc","password_d":"kusbzxnqd2kc",
            "remark":"pro","type":"online","username":"xsi_hestia","wr_flag":2}
        """
        shell_cmd = "%s -h %s -v -t xdb show account %s %s" % (ATUM, self.zk_host, app, account)
        return self.run_cmd_output_json(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # if status != 0:
        #     return status, output
        # return status, json.loads(output)

    def zk_account_update(self, app, account, account_info):
        """
            更新账户信息，包括余额、冻结金额等。
        
        Args:
            app (str): 应用名称。
            account (str): 账号名称。
            account_info (dict):。
        
        Returns:
            tuple (int, str): 返回一个元组，第一个元素是状态码（int），表示命令的执行结果；第二个元素是字符串类型，表示命令的输出结果。
        
        Raises:
            无。
        """
        shell_cmd = "%s -h %s -v -t xdb update account %s %s '%s'" % \
                    (ATUM, self.zk_host, app, account, json.dumps(account_info))
        return self.run_cmd(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_account_delete(self, app, account):
        """
            删除指定应用下的账号信息
        
        Args:
            app (str): 应用名称，例如："app1"
            account (str): 需要删除的账号名称，例如："user1"
        
        Returns:
            tuple (int, str): 返回一个元组，第一个元素是状态码，0表示成功；第二个元���是输出结果，可能为空字符串。
        
        Raises:
            无
        """
        shell_cmd = "%s -h %s -v -t xdb drop account %s %s " % \
                    (ATUM, self.zk_host, app, account)
        return self.run_cmd(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_node_info(self, app, node_id):
        """
            获取指定应用和节点ID的ZK信息，返回一个字典，包含节点状态、版本号等信息。如果节点不存在，则返回None。
        参数：
            app (str) - 应用名称
            node_id (str) - 节点ID
        返回值（dict）：
            {
                'state': str,  # 节点状态，例如 "leader", "follower"
                'version': int,  # 节点版本号
                ...  # 其他属性，根据实际情况而定
            }
            如果节点不存在，则返回None
        """
        shell_cmd = "%s -h %s -v -t xdb show node %s %s" % (ATUM, self.zk_host, app, node_id)
        return self.run_cmd(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_database_create(self, app, name, database_info):
        """
            创建数据库，返回值为一个元组，包含两个元素：第一个是执行命令的状态码，第二个是命令执行后的输出结果
        
        Args:
            app (str): 应用名称
            name (str): 数据库名称
            database_info (dict): 数据库信息，包括数据库类型、版本号等，格式为字典类型，例如{'type': 'mysql', 'version': '5.6'}
        
        Returns:
            tuple (int, str): 第一个元素是执行命令的状态码，第二个元素是命令执行后的输出结果
        """
        shell_cmd = "%s -h %s -v -t xdb create database %s %s '%s'" % \
                    (ATUM, self.zk_host, app, name, json.dumps(database_info))
        return self.run_cmd(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_database_list(self, app):
        """
            获取指定应用的数据库列表，返回值为dict类型，包含两个字段：
            "status": int, 状态码，0表示成功，其他表示失败；
            "data": list, 数据库列表，每个元素为一个字典，包含以下字段：
                "name": str, 数据库名称；
                "size": int, 数据库大小，单位为byte；
                "createTime": str, 创建时间，格式为yyyy-MM-dd HH:mm:ss；
                "updateTime": str, 更新时间，格式为yyyy-MM-dd HH:mm:ss。
        
        Args:
            app (str): 应用名称
        
        Returns:
            dict, {
                    "status": int,
                    "data": [
                        {
                            "name": str,
                            "size": int,
                            "createTime": str,
                            "updateTime": str
                        },
                        ...
                    ]
                }
            状态码和数据库列表信息
        
        Raises:
            None
        """
        shell_cmd = "%s -h %s -v -t xdb show database_list %s" % (ATUM, self.zk_host, app)
        return self.run_cmd_output_json(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, json.loads(output)

    def zk_database_drop(self, app, name):
        """delete database in zk"""
        shell_cmd = "%s -h %s -v -t xdb drop database %s %s" % \
                    (ATUM, self.zk_host, app, name)
        return self.run_cmd(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_table_create(self, app, dbname, table_name, table_info):
        """
            创建ZooKeeper中的表，参数为应用名、数据库名、表名和表信息（字典）。
        返回值为一个元组，第一个元素为状态码（0表示成功，非零表示失败），第二个元素为输出结果（可能为空）。
        
        Args:
            app (str): 应用名，例如 "app1"。
            dbname (str): 数据库名，例如 "db1"。
            table_name (str): 表名，例如 "table1"。
            table_info (dict): 表信息，包括字段名、类型、长度、是否主键等，以字典形式传入，例如：
                {
                    "fields": [{"field_name": "id", "type": "int", "length": 11},
                               {"field_name": "name", "type": "varchar", "length": 255}],
                    "primary_key": ["id"],
                    "indexes": [],
                    "unique_keys": [],
                    "schema": {}
                }
        
        Returns:
            tuple: 一个元组，第一个元素为状态码（int），第二个元素为输出结果（str，可能为空）。
        """
        table_info['schema'] = table_info['schema'].replace('"', '').replace("'", "")
        shell_cmd = '%s -h %s -v -t xdb create table %s %s %s \'%s\'' % \
                    (ATUM, self.zk_host, app, dbname, table_name,
                     json.dumps(table_info).replace('`', ''))
        return self.run_cmd(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_table_show(self, app, dbname, table_name):
        """
            显示指定应用下的指定数据库中的表结构信息
        
        Args:
            app (str): 应用名称，例如 "xdb"
            dbname (str): 数据库名称，例如 "test"
            table_name (str): 表名称，例如 "user"
        
        Returns:
            tuple (int, str): 返回一个元组，第一个元素为状态码（0表示成功），第二个元素为命令执行的输出结果（字符串类型）
        
        Raises:
            无
        """
        shell_cmd = "%s -h %s -v -t xdb show table %s %s %s" % \
                    (ATUM, self.zk_host, app, dbname, table_name)
        return self.run_cmd_output_json(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_table_list(self, app, dbname):
        """
            获取指定应用下的指定数据库中所有表格列表，返回值为字符串类型，��含了每个表格名称。
        
        Args:
            app (str): 应用名称，例如 "app1"。
            dbname (str): 数据库名称，例如 "db1"。
        
        Returns:
            str: 返回一个字符串，包含了每个表格名称，以换行分隔。如果没有表格，则返回空字符串。
        
        Raises:
            无。
        """
        shell_cmd = "%s -h %s -v -t xdb list table %s %s" % (ATUM, self.zk_host, app, dbname)
        return self.run_cmd_output_json(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_table_drop(self, app, dbname, table_name):
        """
            删除ZooKeeper上指定应用的数据库中的表。
        
        Args:
            app (str): 应用名称，例如 "app1"。
            dbname (str): 数据库名称，例如 "mydb"。
            table_name (str): 要删除的表名称，例如 "mytable"。
        
        Returns:
            tuple (int, str): 返回一个元组，第一个元素是命令执行状态码（0表示成功），第二个元素是命令输出结果。
        
        Raises:
            无。
        """
        shell_cmd = "%s -h %s -v -t xdb drop table %s %s %s" % \
                    (ATUM, self.zk_host, app, dbname, table_name)
        return self.run_cmd(shell_cmd)

        # status, output = commands.getstatusoutput(shell_cmd)
        # return status, output

    def zk_replicate_topo_update(self, app_id, cluster_id, topo, type=None):
        """
            更新拓扑信息到Zookeeper中，可以指定类型为'service'或者不传入。默认为'replica'。
        参数:
            app_id (int) - 应用ID
            cluster_id (int) - 集群ID
            topo (str) - 拓扑信息，JSON格式的字符串
            type (str, optional) - 类型，默认为'replica', 可选值为'service'
        返回值:
            tuple (int, str) - 返回状态码和错误消息，如果成功则状态码为0，否则为非零值，错误消息为空字符串
        """
        shell_cmd = "%s -h %s -v -t xdb update replica_topo %s %s '%s'" % (ATUM, self.zk_host, app_id, cluster_id, topo)
        if type == 'service':
            shell_cmd = "%s -h %s -v -t xdb update service_topo %s %s '%s'" % (
                ATUM, self.zk_host, app_id, cluster_id, topo)
        return self.run_cmd(shell_cmd)
        # return commands.getstatusoutput(shell_cmd)

    def zk_replicate_topo_show(self, app_id, cluster_id, type=None):
        """
            显示应用的拓扑信息，包括主从关系和服务关系。
        
        Args:
            app_id (int): 应用ID，可以通过调用`app_list`获取。
            cluster_id (str): 集群ID，可以通过调用`cluster_list`获取。
            type (str, optional): 类型，默认为None，可选值为'master'或'service'，分别表示主从关系和服务关系。如果不指定，则返回所有类型的拓扑信息。 Default is None.
        
        Returns:
            tuple: 返回一个二元组，第一个元素是命令执行状态码（0表示成功），第二个元素是命令执行结果。
        
        Raises:
            None.
        {"xdbglobaltest_0000":{"backup":["node_10.6.86.5_3203"],"exchange_time":1725871277,
        "master":"node_10.6.82.10_3203","offline":[],"slave":["node_10.6.82.11_3203"],"version":26}}
        """
        shell_cmd = "%s -h %s -v -t xdb show replica_topo %s '[\"%s\"]'" % (ATUM, self.zk_host, app_id, cluster_id)
        if type == 'service':
            shell_cmd = "%s -h %s -v -t xdb show service_topo %s %s" % (ATUM, self.zk_host, app_id, cluster_id)
        return self.run_cmd(shell_cmd)

        # return commands.getstatusoutput(shell_cmd)

    def zk_replicate_topo_add(self, app_id, cluster_id, node_id, role, type=None):
        """
        将节点添加到拓扑中，并更新拓扑信息。
        
        Args:
            app_id (int): 应用ID。
            cluster_id (str): 集群ID。
            node_id (str): 节点ID。
            role (str): 角色类型，可以是'master'、'slave'、'backup'或'offline'之一。
            type (str, optional): 类型，默认为None，表示不指定类型。
        
        Returns:
            tuple (int, str): 返回值为一个元组，第一个元素为状态码，0表示成功；第二个元素为状态信息，为空字符串表示操作成功。
        
        Raises:
            无。
        """
        status, output = self.zk_replicate_topo_show(app_id, cluster_id, type)
        if status != 0:
            return status, output
        repl = json.loads(output.strip())
        if role == 'master':
            repl[cluster_id][role] = node_id

        for each_role in ['slave', 'backup', 'offline']:
            if each_role == role:
                if each_role not in repl[cluster_id]:
                    repl[cluster_id][each_role] = [node_id]
                elif node_id not in repl[cluster_id][each_role]:
                    repl[cluster_id][each_role].append(node_id)
            else:
                if each_role not in repl[cluster_id] or not repl[cluster_id][each_role]:
                    continue
                repl[cluster_id][each_role] = list(set(repl[cluster_id][each_role]))
                for idx in range(len(repl[cluster_id][each_role])):
                    if repl[cluster_id][each_role][idx] == node_id:
                        del (repl[cluster_id][each_role][idx])
                        break
        return self.zk_replicate_topo_update(app_id, cluster_id, json.dumps(repl[cluster_id]))

    def zk_database_show(self, app, dbname):
        """
        显示指定应用的数据库详细信息
        
        Args:
            app (str): 应用名称
            dbname (str): 数据库名称
            
        Returns:
            tuple (int, dict): 返回状态码和数据库信息。状态码0表示成功，数据库信息为字典格式
        """
        shell_cmd = "%s -h %s -v -t xdb show database %s %s" % (ATUM, self.zk_host, app, dbname)
        return self.run_cmd_output_json(shell_cmd)
