#!/usr/bin/env python3
# coding=utf-8

"""
# Author       : <PERSON><PERSON><PERSON><PERSON><PERSON>@baidu.com
# BuildDate    : 2016-03-05
# Description  : the library for http request
# Modified     : 2015.03.05 Init By zengguowei

"""

import datetime
import http.client as httplib
import json
import sys
from urllib.parse import urlparse


class HttpService(object):
    """HttpService"""
    __version__ = '*******'
    HTTP_TIMEOUT_S = 200

    def __init__(self, timeout=200):
        """
            Initializes the class with a default timeout of 200 seconds.
        
        Args:
            timeout (int, optional): Timeout in seconds for each request. Defaults to 200.
        
        Returns:
            None: No return value.
        """
        self.debug = 0
        self.timeout = timeout

    def set_debug_level(self, level=0):
        """
            设置调试等级，默认为0。
        当等级大于0时，将打开调试信息的输出。
        
        Args:
            level (int, optional): 调试等级，默认为0. 默认为0.
        
        Returns:
            NoneType: 无返回值，直接修改了对象的属性debug。
        """
        self.debug = level
        return

    def http_request(self, method, url, data, headers):
        """
            发送HTTP请求，返回响应对象。
        如果请求方法为GET或DELETE，则不需要body数据；否则，将data转换为JSON字符串作为body数据。
        
        Args:
            method (str): HTTP请求方法，可选值包括"GET", "POST", "PUT", "PATCH", "DELETE"等。
            url (str): URL地址，必须是绝对路径。
            data (dict, optional): 请求体数据，默认为None。如果请求方法为GET或DELETE，此参数无效。
            headers (dict, optional): HTTP头信息，默认为None，表示使用默认的头信息。
        
        Returns:
            http.client.HTTPResponse: HTTP响应对象，包含了状态码、响应头和响应体等信息。
        """
        res = None
        urlobj = urlparse(url)
        body = None
        if (method != 'GET' and method != 'DELETE'):
            body = json.dumps(data)
        uri = urlobj.path
        if (urlobj.query):
            uri += '?' + urlobj.query

        if headers is None:
            headers = {}
        conn = self.__get_http_connection(urlobj.netloc)
        conn.request(method, uri, body, headers)
        res = conn.getresponse()
        # conn.close()
        return res

    def __get_http_connection(self, host=None):
        """
            获取HTTP连接，支持指定主机和端口。如果不指定主机，则使用默认的主机。
        如果指定了端口，则使用该端口；否则使用默认的80端口。
        
        Args:
            host (str, optional): 主机名或主机名:端口号，默认为None，表示使用默认的主机。 Defaults to None.
        
        Returns:
            httplib.HTTPConnection: HTTP连接对象。
        """
        port = 80
        host_port_list = host.split(":")
        if len(host_port_list) == 1:
            host = host_port_list[0].strip()
        elif len(host_port_list) == 2:
            host = host_port_list[0].strip()
            port = int(host_port_list[1].strip())
        if sys.version_info >= (2, 6):
            conn = httplib.HTTPConnection(host=host, port=port, timeout=self.timeout)
        else:
            conn = httplib.HTTPConnection(host=host, port=port)
        conn.set_debuglevel(self.debug)
        return conn

    def get_canonical_time(self, timestamp=0):
        """
            获取标准时间，如果timestamp为0则使用当前时间，否则使用指定的时间。
        返回值是一个字符串格式的UTC时间，格式为'%Y-%m-%dT%H:%M:%SZ'。
        
        Args:
            timestamp (int, optional): 指定时间戳，默认为0，表示当前时间。默认为0。
        
        Returns:
            str: 标准时间字符串，格式为'%Y-%m-%dT%H:%M:%SZ'。
        """
        if timestamp == 0:
            utctime = datetime.datetime.utcnow()
        else:
            utctime = datetime.datetime.utcfromtimestamp(timestamp)
        return "%04d-%02d-%02dT%02d:%02d:%02dZ" % (
            utctime.year, utctime.month, utctime.day,
            utctime.hour, utctime.minute, utctime.second)
