#!/usr/bin/env python3
# coding=utf-8

"""


# Author       : z<PERSON><PERSON><PERSON><PERSON>@baidu.com
# BuildDate    : 2016-03-05
# Description  : the library for xagent service, including:
#                   * http request
#                   * sql request
# Modified     : 2015.03.05 Init By zengguowei
"""

import json
import os
import sys
import time
import asyncio
import aiohttp
import socket
from logging import Logger

from xdboperator.lightxdb.http_client import HttpService

DIRNAME = os.path.dirname(os.path.realpath(__file__))
XAGENT_SQL_CLIENT = DIRNAME + '/xagent_sql_client'


class XagentService(object):
    """
    xagent script sdk
    """

    def __init__(self, logger: Logger, timeout=600):
        """
            初始化函数，设置日志器和超时时间。
        
        Args:
            logger (Logger): 日志器对象，用于记录日志信息。
            timeout (int, optional): 默认为600，表示等待的最长时间（秒），超过该时间将会抛出TimeoutError异常。默认值为600秒。
        
        Returns:
            None, 无返回值。
        """
        self.logger = logger
        self.timeout = timeout
        self.retry = 10

    def xagent_task(self, ip, xagent_port, task, data):
        """
        xagent_task
        """
        http_service = HttpService(self.timeout)
        xagent_url = 'http://%s:%s/%s' % (ip, xagent_port, task)
        headers = {
            'Accept': 'application/json'
        }
        retry_count = 0
        retry_interval = 30
        retry_timeout = 180
        ret_data = ''
        while retry_count < retry_timeout / retry_interval:
            try:
                res = http_service.http_request(
                    'POST', xagent_url, data, headers)
                if (res.status / 100 != 2):
                    self.logger.error('[url:%s][data:%s]Request xagent failed: code[%s], return[%s]',
                                      xagent_url, data, res.status, res.read())
                    return -1, None
                ret_data = res.read()
                self.logger.info(ret_data)
                json_data = json.loads(ret_data)
                # data = {"status":-1, "statusinfo":"", "data":{}}
                if json_data['status'] != 0:
                    self.logger.error('[url:%s][data:%s]Request xagent failed: code[%s], return[%s]',
                                      xagent_url, data, res.status, ret_data)
                    return -1, json_data
                return 0, json_data
            except Exception as e:
                self.logger.warning('[url:%s][data:%s][receive:%s]Request xagent failed: %s',
                                    xagent_url, data, ret_data, e)
                retry_count += 1
                time.sleep(retry_interval)
        else:
            self.logger.error('[url:%s]Request xagent[%s:%s] timeout, Connection refused',
                              xagent_url, ip, xagent_port)
        return -1, None

    def create_xagent_async_task(self, ip, xagent_port, task_name, data):
        """
        create_xagent_async_task

        """
        start_time = time.time()
        http_service = HttpService()
        xagent_url = 'http://%s:%s/task' % (ip, xagent_port)
        headers = {
            'Accept': 'application/json'
        }
        post_data = {
            "task_type": "async",
            "data": data,
            "task_name": task_name,
        }
        retry_interval = 3
        ret_data = ''
        ret_code = -1
        for retry in [1, 2, 3]:
            try:
                # request xagent
                res = http_service.http_request(
                    'POST', xagent_url, post_data, headers)

                # parse result
                response = res.read()
                json_data = json.loads(response)
                # failed
                if (res.status / 100 != 2 or json_data['error_code'] != 0):
                    self.logger.warning('create xagent async task[%s] failed: code[%s], try[%s], return[%s]',
                                        task_name, res.status, retry, response)
                    continue
                # success
                ret_code = 0
                ret_data = json_data['task_id']
                break
            except Exception as e:
                self.logger.warning('create xagent async task[%s] with exception: %s, try[%s], return[%s]',
                                    task_name, e, retry, response)
            retry_interval = retry_interval * retry
            time.sleep(retry_interval)
        current_time = time.time()
        elapsed_time = round((current_time - start_time), 2)
        if ret_code != 0:
            self.logger.debug('create async task xagent url: %s, post: %s',
                              xagent_url, json.dumps(post_data))
            self.logger.debug('create xagent async task[%s] elapsed[%s], ret_code[%s], ret_data[%s]',
                              task_name, elapsed_time, ret_code, ret_data)
        return ret_code, ret_data

    def check_xagent_async_task(self, ip, xagent_port, task_id, timeout=86400):
        """
        check_xagent_async_task
        """
        # prepare xagent request
        http_service = HttpService()
        xagent_url = 'http://%s:%s/task/%s' % (ip, xagent_port, task_id)
        headers = {
            'Accept': 'application/json'
        }
        get_data = {}

        # prepare return
        ret_code = -1
        ret_data = ''

        # loop control vars
        start_time = time.time()
        retry_interval = 3
        count = 0
        error_count = 0
        max_error_count = 100
        elapsed_time = 0
        response = ''
        json_data = {}

        while True:
            time.sleep(retry_interval)
            count = count + 1
            current_time = time.time()
            elapsed_time = round((current_time - start_time), 2)

            # check error count
            if error_count >= max_error_count:
                self.logger.error('check async task reach max_error_count(%s), elapsed[%s], count[%s]',
                                  max_error_count, elapsed_time, count)
                return -1, json_data

            # check timeout
            if elapsed_time > timeout:
                self.logger.debug('checking async task status url: %s', xagent_url)
                self.logger.error('check async task timeout[%s], elapsed[%s], count[%s]',
                                  timeout, elapsed_time, count)
                return -1, None

            # print elapsed seconds every 100 loops
            if (count % 100) == 0:
                sys.stderr.write('\n')
                self.logger.debug('checking async task status url: %s', xagent_url)
                self.logger.warning(
                    "elapsed: %s seconds, last response: %s", elapsed_time, response)

            # request xagent task
            try:
                res = http_service.http_request(
                    'GET', xagent_url, get_data, headers)
                # parse result
                response = res.read()
                json_data = json.loads(response)

                # request xagent script to get async task status
                if (res.status / 100 != 2 or json_data['error_code'] != 0):
                    error_count = error_count + 1
                    self.logger.debug(
                        'checking async task status url: %s', xagent_url)
                    self.logger.warning('check async task temp failed: code[%s], elapsed[%s], count[%s], return[%s]',
                                        res.status, elapsed_time, count, response)

                    continue

                # check task status
                task_status = json_data['task_status']
                if task_status == 'failed':
                    self.logger.debug(
                        'checking async task status url: %s', xagent_url)
                    self.logger.error('async task failed, elapsed[%s], count[%s]',
                                      elapsed_time, count)
                    return -1, json_data
                elif task_status == 'running' or task_status == 'initiating':
                    # reset error count
                    error_count = 0
                    # print count to log
                    sys.stderr.write('%s ' % (count))
                    continue
                elif task_status == 'success':
                    sys.stderr.write('\n')
                    self.logger.info('async task success, elapsed[%s], count[%s]',
                                     elapsed_time, count)
                    return 0, json_data
                else:
                    self.logger.error("unsupport async task status[%s], elapsed[%s], count[%s]",
                                      task_status, elapsed_time, count)
                    return -1, json_data
            except Exception as e:
                error_count = error_count + 1
                self.logger.debug('checking async task status url: %s', xagent_url)
                self.logger.warning(
                    'check async task with exception[%s], return[%s]', e, response)
        return -1, None

    def run_xagent_async_task(self, ip, xagent_port, task_name, data, timeout=86400):
        """
        run_xagent_async_task
        """
        # create async task
        ret, task_id = self.create_xagent_async_task(
            ip, xagent_port, task_name, data)
        if (ret != 0):
            return ret, task_id
        # check async task
        ret, task_result = self.check_xagent_async_task(
            ip, xagent_port, task_id, timeout)
        return ret, task_result

    def xagent_asyn_task(self, ip, xagent_port, task_name, data):
        """
        xagent asyn task
        """
        http_service = HttpService(self.timeout)
        # 1. send create asyn task request
        xagent_url = 'http://%s:%s/task' % (ip, xagent_port)
        headers = {
            'Accept': 'application/json'
        }
        post_data = {
            "task_type": "async",
            "data": data,
            "task_name": task_name,
            # "callback_url": '',
            "timeout": self.timeout
        }
        retry_count = 0
        retry_interval = 3
        retry_timeout = self.timeout
        ret_data = ''
        self.logger.warning(self.timeout)
        while retry_count < retry_timeout / retry_interval:
            try:
                self.logger.warning(xagent_url)
                self.logger.warning(json.dumps(post_data))
                res = http_service.http_request(
                    'POST', xagent_url, post_data, headers)
                if (res.status / 100 != 2):
                    self.logger.error('[url:%s][data:%s]Request xagent failed: code[%s], return[%s]',
                                      xagent_url, post_data, res.status, res.read())
                    return -1, None
                ret_data = res.read()
                json_data = json.loads(ret_data)
                if json_data['error_code'] != 0:
                    self.logger.error('[url:%s][data:%s]Request xagent failed: code[%s], return[%s]',
                                      xagent_url, post_data, res.status, ret_data)
                    return -1, json_data
                # return 0, json_data
                task_id = json_data['task_id']
                break
            except Exception as e:
                self.logger.warning('[url:%s][data:%s][receive:%s]Request xagent failed: %s',
                                    xagent_url, post_data, ret_data, e)
                retry_count += 1
                time.sleep(retry_interval)
        else:
            self.logger.error('[url:%s]Request xagent[%s:%s] timeout, Connection refused',
                              xagent_url, ip, xagent_port)

        # 2. query asyn task status
        xagent_url = 'http://%s:%s/task/%s' % (ip, xagent_port, task_id)
        headers = {
            'Accept': 'application/json'
        }
        get_data = {}
        retry_count = 0
        retry_interval = 3
        retry_timeout = self.timeout
        ret_data = ''
        while retry_count < retry_timeout / retry_interval:
            try:
                res = http_service.http_request(
                    'GET', xagent_url, get_data, headers)
                if (res.status / 100 != 2):
                    self.logger.error('[url:%s][data:%s]Request xagent failed: code[%s], return[%s]',
                                      xagent_url, get_data, res.status, res.read())
                    return -1, None
                ret_data = res.read()
                json_data = json.loads(ret_data)
                if json_data['error_code'] != 0:
                    self.logger.error('[url:%s][data:%s]Request xagent failed: code[%s], return[%s]',
                                      xagent_url, get_data, res.status, ret_data)
                    return -1, json_data
                if json_data['task_status'] == 'success':
                    return 0, json_data
                elif json_data['task_status'] == 'failed':
                    return -1, json_data
                else:
                    self.logger.info('[url:%s][data:%s]Request xagent running: code[%s], return[%s]',
                                     xagent_url, get_data, res.status, res.read())
                    retry_count += 1
                    time.sleep(retry_interval)
            except Exception as e:
                self.logger.warning('[url:%s][data:%s][receive:%s]Request xagent failed: %s',
                                    xagent_url, get_data, ret_data, e)
                retry_count += 1
                time.sleep(retry_interval)
        else:
            self.logger.error('[url:%s]xagent[%s:%s] task execute timeout',
                              xagent_url, ip, xagent_port)
        return -1, None

    def xagent_sql(self, ip, xagent_port, db_port, sql_statement, log_bin=1):
        """
        xagent_sql
        """
        basedir = "/home/<USER>/mysql_%d" % int(db_port)
        if basedir is None:
            return None

        task_name = 'execute_sql'
        data = {
            'basedir': basedir,
            'sql': sql_statement,
            'sql_log_bin': log_bin
        }
        ret, output = self.xagent_asyn_task(ip, xagent_port, task_name, data)
        # format result
        try:
            result = {
                "error_no": output['error_code'],
                "reason": output['error_info'],
            }
            # format data
            output_data = output['task_result']
            output_data = output_data.replace('[NOTICE] ', '')
            output_data = output_data.replace('\n', '')
            if output_data.strip() == '':
                result['data'] = output_data
            else:
                result['data'] = json.loads(output_data)
        except Exception as e:
            self.logger.warning(
                "Execute sql failed: format result failed, result: [%s], error: [%s]", output, e)
            return None
        # if ret is None:
        if ret != 0:
            self.logger.warning("Execute sql failed: xagent ip[%s:%s], port[%s], sql[%s], error[%s].",
                                ip, db_port, xagent_port, sql_statement, ret)
            return None
        self.logger.info("Execute sql [%s] success: xagent ip[%s:%s], port[%s], %s.",
                         sql_statement, ip, db_port, xagent_port, ret)
        return result
        """
        shell_cmd = '%s -d %s -x %s:%s -i %s -e \'%s\'' % \
                    (XAGENT_SQL_CLIENT, db_type, ip, xagent_port, db_port, sql_statement.replace("'", "\""))

        if db_type == 'mysql' and log_bin == 0:
            shell_cmd = '%s -d %s --sql-log-bin=0 -x %s:%s -i %s -e \'%s\'' % \
                        (XAGENT_SQL_CLIENT, db_type, ip, xagent_port, db_port, sql_statement.replace("'", "\""))
        try:
            i = 0
            while i < self.retry: 
                (status, result) = commands.getstatusoutput(shell_cmd)
                if status != 0:
                    time.sleep(1)
                    i += 1
                    continue
                json_ret = json.loads(result)
                if 'error_no' not in json_ret or int(json_ret['error_no']) != 0:
                    #self.logger.warning('Exec shell cmd %s failed: %s[%s], count[%d]', shell_cmd, status, result, i)
                    time.sleep(1)
                    i += 1
                    continue
                self.logger.info('Exec shell cmd %s success: %s[%s]', shell_cmd, status, result)
                return json_ret
            self.logger.warning('Exec shell cmd %s failed: %s[%s].', shell_cmd, status, result)
            return None
        except Exception as e:
            self.logger.error('Exec shell cmd %s failed: %s', shell_cmd, e)
            return None
        """

    def test_port(self, ip, xagent_port):
        """
        Test port connectivity
        """
        try:
            socket.create_connection((ip, xagent_port), timeout=5)
            return True
        except Exception as e:
            self.logger.warning('Port test failed: %s', e)
            return False


class AsyncXagentService:
    """
   xagent的异步客户端,计划后续替换原来的同步客户端，提高sql执行效率
    """

    def __init__(self, logger: Logger, timeout=600):
        self.logger = logger
        self.timeout = timeout
        self.retry = 10

    async def _http_request(self, session, method, url, data=None, headers=None):
        """
        异步http请求
        Args:
            session (_type_): _description_
            method (_type_): _description_
            url (_type_): _description_
            data (_type_, optional): _description_. Defaults to None.
            headers (_type_, optional): _description_. Defaults to None.

        Returns:
            _type_: _description_
        """
        async with session.request(method, url, json=data, headers=headers) as response:
            res_data = await response.text()
            return response.status, res_data

    async def xagent_task(self, ip, xagent_port, task, data):
        """
        xagent异步任务
        Args:
            ip (_type_): _description_
            xagent_port (_type_): _description_
            task (_type_): _description_
            data (_type_): _description_

        Returns:
            _type_: _description_
        """
        async with aiohttp.ClientSession() as session:
            xagent_url = f'http://{ip}:{xagent_port}/{task}'
            headers = {'Accept': 'application/json'}
            retry_count = 0
            retry_interval = 30
            retry_timeout = 180
            
            while retry_count < retry_timeout / retry_interval:
                try:
                    status, ret_data = await self._http_request(session, 'POST', xagent_url, data, headers)
                    if status // 100 != 2:
                        self.logger.error('[url:%s][data:%s]Request xagent failed: code[%s], return[%s]',
                                          xagent_url, data, status, ret_data)
                        return -1, None
                    json_data = json.loads(ret_data)
                    if json_data['status'] != 0:
                        self.logger.error('[url:%s][data:%s]Request xagent failed: code[%s], return[%s]',
                                          xagent_url, data, status, ret_data)
                        return -1, json_data
                    return 0, json_data
                except Exception as e:
                    self.logger.warning('[url:%s][data:%s]Request xagent failed: %s',
                                        xagent_url, data, e)
                    retry_count += 1
                    await asyncio.sleep(retry_interval)
            self.logger.error('[url:%s]Request xagent[%s:%s] timeout', xagent_url, ip, xagent_port)
            return -1, None

    async def create_xagent_async_task(self, ip, xagent_port, task_name, data):
        """
        创建xagent异步任务
        Args:
            ip (_type_): _description_
            xagent_port (_type_): _description_
            task_name (_type_): _description_
            data (_type_): _description_

        Returns:
            _type_: _description_
        """
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            xagent_url = f'http://{ip}:{xagent_port}/task'
            headers = {'Accept': 'application/json'}
            post_data = {
                "task_type": "async",
                "data": data,
                "task_name": task_name,
            }
            retry_interval = 3
            ret_code = -1
            ret_data = ''
            
            for retry in range(3):
                try:
                    status, response = await self._http_request(session, 'POST', xagent_url, post_data, headers)
                    json_data = json.loads(response)
                    if status // 100 != 2 or json_data['error_code'] != 0:
                        self.logger.warning('create xagent async task[%s] \
                                            failed: code[%s], try[%s], return[%s]',
                                            task_name, status, retry, response)
                        continue
                    ret_code = 0
                    ret_data = json_data['task_id']
                    break
                except Exception as e:
                    self.logger.warning('create xagent async task[%s] with exception: %s, try[%s], return[%s]',
                                        task_name, e, retry, response)
                await asyncio.sleep(retry_interval)
                retry_interval *= retry + 1

            current_time = time.time()
            elapsed_time = round(current_time - start_time, 2)
            if ret_code != 0:
                self.logger.debug('create async task xagent url: %s, post: %s', xagent_url, json.dumps(post_data))
                self.logger.debug('create xagent async task[%s] elapsed[%s], ret_code[%s], ret_data[%s]',
                                  task_name, elapsed_time, ret_code, ret_data)
            return ret_code, ret_data

    async def check_xagent_async_task(self, ip, xagent_port, task_id, timeout=86400):
        """
        检查xagent异步任务
        Args:
            ip (_type_): _description_
            xagent_port (_type_): _description_
            task_id (_type_): _description_
            timeout (int, optional): _description_. Defaults to 86400.

        Returns:
            _type_: _description_
        """
        async with aiohttp.ClientSession() as session:
            xagent_url = f'http://{ip}:{xagent_port}/task/{task_id}'
            headers = {'Accept': 'application/json'}
            retry_interval = 3
            count = 0
            error_count = 0
            max_error_count = 100
            start_time = time.time()
            elapsed_time = 0
            
            while True:
                await asyncio.sleep(retry_interval)
                count += 1
                current_time = time.time()
                elapsed_time = round(current_time - start_time, 2)

                if error_count >= max_error_count:
                    self.logger.error('check async task reach max_error_count(%s), elapsed[%s], count[%s]',
                                      max_error_count, elapsed_time, count)
                    return -1, {}

                if elapsed_time > timeout:
                    self.logger.error('check async task timeout[%s], elapsed[%s], count[%s]',
                                      timeout, elapsed_time, count)
                    return -1, None

                if count % 100 == 0:
                    self.logger.debug('checking async task status url: %s', xagent_url)

                try:
                    status, response = await self._http_request(session, 'GET', xagent_url, headers=headers)
                    json_data = json.loads(response)

                    if status // 100 != 2 or json_data['error_code'] != 0:
                        error_count += 1
                        self.logger.warning('check async task temp failed: code[%s], \
                                            elapsed[%s], count[%s], return[%s]',
                                            status, elapsed_time, count, response)
                        continue

                    task_status = json_data['task_status']
                    if task_status == 'failed':
                        self.logger.error('async task failed, elapsed[%s], count[%s]', elapsed_time, count)
                        return -1, json_data
                    elif task_status in ['running', 'initiating']:
                        error_count = 0
                        continue
                    elif task_status == 'success':
                        self.logger.info('async task success, elapsed[%s], count[%s]', elapsed_time, count)
                        return 0, json_data
                    else:
                        self.logger.error("unsupported async task status[%s], elapsed[%s], count[%s]",
                                          task_status, elapsed_time, count)
                        return -1, json_data
                except Exception as e:
                    error_count += 1
                    self.logger.warning('check async task with exception[%s], return[%s]', e, response)
            return -1, None

    async def run_xagent_async_task(self, ip, xagent_port, task_name, data, timeout=86400):
        """
        运行xagent异步任务
        Args:
            ip (_type_): _description_
            xagent_port (_type_): _description_
            task_name (_type_): _description_
            data (_type_): _description_
            timeout (int, optional): _description_. Defaults to 86400.

        Returns:
            _type_: _description_
        """
        ret, task_id = await self.create_xagent_async_task(ip, xagent_port, task_name, data)
        if ret != 0:
            return ret, task_id
        ret, task_result = await self.check_xagent_async_task(ip, xagent_port, task_id, timeout)
        return ret, task_result

    async def xagent_asyn_task(self, ip, xagent_port, task_name, data):
        """
        异步任务
        Args:
            ip (_type_): _description_
            xagent_port (_type_): _description_
            task_name (_type_): _description_
            data (_type_): _description_

        Returns:
            _type_: _description_
        """
        async with aiohttp.ClientSession() as session:
            xagent_url = f'http://{ip}:{xagent_port}/task'
            headers = {'Accept': 'application/json'}
            post_data = {
                "task_type": "async",
                "data": data,
                "task_name": task_name,
                "timeout": self.timeout
            }
            retry_count = 0
            retry_interval = 3
            retry_timeout = self.timeout
            
            while retry_count < retry_timeout / retry_interval:
                try:
                    status, ret_data = await self._http_request(session, 'POST', xagent_url, post_data, headers)
                    if status // 100 != 2:
                        self.logger.error('[url:%s][data:%s]Request xagent failed: code[%s], return[%s]',
                                          xagent_url, post_data, status, ret_data)
                        return -1, None
                    json_data = json.loads(ret_data)
                    if json_data['error_code'] != 0:
                        self.logger.error('[url:%s][data:%s]Request xagent failed: code[%s], return[%s]',
                                          xagent_url, post_data, status, ret_data)
                        return -1, json_data
                    task_id = json_data['task_id']
                    break
                except Exception as e:
                    self.logger.warning('[url:%s][data:%s]Request xagent failed: %s',
                                        xagent_url, post_data, e)
                    retry_count += 1
                    await asyncio.sleep(retry_interval)
            else:
                self.logger.error('[url:%s]Request xagent[%s:%s] timeout, Connection refused',
                                  xagent_url, ip, xagent_port)
            
            # Check async task status
            return await self.check_xagent_async_task(ip, xagent_port, task_id)

    async def xagent_sql(self, ip, xagent_port, db_port, sql_statement, log_bin=1):
        """
        执行SQL语句
        """
        basedir = f"/home/<USER>/mysql_{db_port}"
        if basedir is None:
            return None

        task_name = 'execute_sql'
        data = {
            'basedir': basedir,
            'sql': sql_statement,
            'sql_log_bin': log_bin
        }
        ret, output = await self.xagent_asyn_task(ip, xagent_port, task_name, data)

        try:
            result = {
                "error_no": output['error_code'],
                "reason": output['error_info'],
            }
            output_data = output['task_result'].replace('[NOTICE] ', '').replace('\n', '').strip()
            result['data'] = json.loads(output_data) if output_data else output_data
        except Exception as e:
            self.logger.warning(
                "Execute sql failed: format result failed, result: [%s], error: [%s]", output, e)
            return None

        if ret != 0:
            self.logger.warning("Execute sql failed: xagent ip[%s:%s], port[%s], sql[%s], error[%s].",
                                ip, db_port, xagent_port, sql_statement, ret)
            return None

        self.logger.info("Execute sql [%s] success: xagent ip[%s:%s], port[%s], %s.",
                         sql_statement, ip, db_port, xagent_port, ret)
        return result
    
    
    
